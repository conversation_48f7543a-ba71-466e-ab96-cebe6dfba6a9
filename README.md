# baoyan-booking-platform
宝燕购票预约平台



```sql
CREATE TABLE `by_order_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单日志表主键',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `order_goods_id` int(11) DEFAULT NULL COMMENT '订单商品编号',
  `goods_id` int(11) DEFAULT NULL COMMENT '操作的商品id',
  `goods_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '商品名',
  `log_type` tinyint(4) DEFAULT NULL COMMENT '操作类型 1.创建订单 2.付款 3.申请售后 4.同意售后/退款 5.核销 6.赠送给好友 7.删除订单',
  `operator_type` tinyint(4) DEFAULT NULL COMMENT '操作人类型 1.用户 2.管理员',
  `operator_count` int(4) DEFAULT NULL COMMENT '操作的数量',
  `logs` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '日志记录',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `order_no` (`order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

```



淘潮开发添加的权限数据

```sql
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (332, 0, '[1]', '', 'FOLDER', '', '淘潮玩管理', '淘潮玩', '管理', '', 2, '2021-07-13 14:42:56', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (333, 332, '[1]', '', 'PAGE', '', '品牌管理', '', '', '/baoyan/tc/brand/tc_brand', 0, '2021-07-13 14:42:56', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (336, 332, '[1]', '', 'PAGE', '', '品类管理', '', '', '/baoyan/tc/cate/tc_cate', 1, '2021-07-15 00:51:04', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (337, 332, '[1]', '', 'PAGE', '', '商品管理', '', '', '/baoyan/tc/goods/tc_goods', 2, '2021-07-15 13:54:38', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (339, 332, '[1]', '', 'FOLDER', '', '推荐管理', '推荐', '管理', '', 3, '2021-07-25 14:12:35', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (340, 339, '[1]', '', 'PAGE', '', '板块管理', '', '', '/baoyan/tc/home/<USER>/index', 0, '2021-07-25 14:13:29', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (341, 339, '[1]', '', 'PAGE', '', '推荐系列', '', '', '/baoyan/tc/home/<USER>/index', 0, '2021-07-28 15:18:40', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (342, 0, '[]', '', 'FOLDER', '', '淘潮订单', '淘潮', '订单', '', 4, '2021-08-02 22:25:37', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (343, 342, '[]', '', 'PAGE', '', '订单管理', '', '', '/baoyan/tc/order/order', 0, '2021-08-02 22:31:10', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (344, 342, '[]', '', 'PAGE', '', '售后管理', '', '', '/baoyan/tc/order/orders_after', 0, '2021-08-07 12:03:53', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (345, 339, '[1]', '', 'PAGE', '', '首页设置', '', '', '/baoyan/tc/home/<USER>/index', 0, '2021-08-08 15:31:16', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (346, 342, '[]', '', 'PAGE', '', '淘潮核销码', '', '', '/baoyan/tc/order/writeoff', 0, '2021-08-15 18:14:36', NULL);
INSERT INTO `baoyan`.`sys_menu`(`id`, `pid`, `apps_id`, `icon`, `menu_type`, `menu_code`, `menu_name`, `first_name`, `second_name`, `route`, `score`, `gmt_create`, `gmt_modified`) VALUES (347, 342, '[]', '', 'PAGE', '', '淘潮核销记录', '', '', '/baoyan/tc/order/writeofflog', 0, '2021-08-15 18:15:11', NULL);

UPDATE `sys_policy` SET `apps_id` = '[1]', `instance_id` = 0, `policy_name` = '宝燕管理系统', `remark` = '1.0', `policy_type` = 'SYSTEM', `menus_id` = '[287, 288, 289, 291, 293, 329, 324, 294, 295, 310, 311, 312, 325, 326, 296, 315, 317, 322, 297, 307, 298, 308, 299, 309, 318, 319, 320, 321, 300, 302, 303, 304, 305, 306, 301, 313, 314, 330, 331, 328, 207, 327]', `permissions` = '[]', `created_username` = 'sys', `gmt_create` = '2019-06-29 16:58:01', `gmt_modified` = '2020-07-09 20:31:43' WHERE `id` = 25;

```



淘潮开发增加的表

```sql


/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

-- USE `baoyan-back`;

/* Create table in target */
CREATE TABLE `by_classify_recommend`(
	`id` int(10) NOT NULL  auto_increment COMMENT 'id自增,无意义' , 
	`text` varchar(20) COLLATE utf8_bin NULL  COMMENT '推荐标题' , 
	`jump_type` int(20) NULL  COMMENT '跳转类型(1.商品分类2.联票列表3.次卡列表 4.拼团列表5.文章6.普通商品7.联票商品 8.次卡商品9.拼团商品10.优惠券11.淘潮玩' , 
	`target` text COLLATE utf8_bin NULL  COMMENT '跳转content' , 
	`sort` int(10) NULL  COMMENT '排序值' , 
	`goods_id` int(20) NULL  COMMENT '商品id' , 
	`cover_img` text COLLATE utf8_bin NULL  COMMENT '封面图' , 
	`cate_id` int(20) NULL  COMMENT '品类id' , 
	`brand_id` int(20) NULL  COMMENT '品牌id' , 
	`is_shelves` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '0下架 1上架 默认上架' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '0删除 1 未删除 默认1' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_update` datetime NULL  COMMENT '修改时间' , 
	`label_id` varchar(50) COLLATE utf8_bin NULL  COMMENT '标签id' , 
	`tc_flag` int(4) NULL  COMMENT '标识符 标记 淘潮玩推荐的类别' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_banner`(
	`id` int(11) unsigned NOT NULL  auto_increment , 
	`img_url` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '图片地址' , 
	`status` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '是否上架 0 不上架 1上架' , 
	`jump_type` int(11) NULL  COMMENT '跳转类型(1.淘潮玩普通商品 2.淘潮玩品类 3.淘潮玩品牌 4淘潮玩标签)' , 
	`target` text COLLATE utf8mb4_general_ci NULL  COMMENT '跳转content\r\n跳转类型 不是 普通商品时 target ==-1 跳转内容是 全部(品牌/标签/品类)' , 
	`sort` int(11) NULL  COMMENT '排序' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_update` datetime NULL  COMMENT '修改时间' , 
	`is_del` tinyint(1) NULL  DEFAULT 0 COMMENT '是否删除' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_pl_0900_ai_ci' COMMENT='Banner';


/* Create table in target */
CREATE TABLE `tc_brand`(
	`id` int(11) NOT NULL  auto_increment COMMENT 'id自增，无意义' , 
	`name` varchar(50) COLLATE utf8_bin NULL  COMMENT '品牌名' , 
	`remark` varchar(200) COLLATE utf8_bin NULL  COMMENT '备注' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '是否删除(1:已经删除 0:未删除)' , 
	`img` text COLLATE utf8_bin NULL  COMMENT '品牌图片' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩品牌表';


/* Create table in target */
CREATE TABLE `tc_cate`(
	`id` int(11) NOT NULL  auto_increment COMMENT 'id自增，无意义' , 
	`name` varchar(50) COLLATE utf8_bin NULL  COMMENT '品类名' , 
	`remark` varchar(200) COLLATE utf8_bin NULL  COMMENT '备注' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '逻辑删除(1:删除,0:未删除)' , 
	`img` text COLLATE utf8_bin NULL  COMMENT '品类图片' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩品类表';


/* Create table in target */
CREATE TABLE `tc_goods`(
	`id` int(11) NOT NULL  auto_increment COMMENT '自增id，无意义' , 
	`goods_name` varchar(200) COLLATE utf8_bin NULL  COMMENT '商品名' , 
	`brand_id` int(11) NULL  COMMENT '品牌id' , 
	`cate_id` int(11) NULL  COMMENT '品类id' , 
	`title` json NULL  COMMENT '标签  ,json字符串数组' , 
	`sales_area` varchar(100) COLLATE utf8_bin NULL  COMMENT '销售区域' , 
	`sale_price` decimal(10,2) NULL  COMMENT '销售价格' , 
	`door_price` decimal(10,2) NULL  COMMENT '门市价格' , 
	`stock` int(10) unsigned NULL  COMMENT '剩余库存' , 
	`code_img` varchar(300) COLLATE utf8_bin NULL  COMMENT '小程序二维码图片' , 
	`cover_img` text COLLATE utf8_bin NULL  COMMENT '封面图片' , 
	`banner_img` text COLLATE utf8_bin NULL  COMMENT '轮播图片' , 
	`detail` text COLLATE utf8_bin NULL  COMMENT '商品详情，富文本' , 
	`introduction` text COLLATE utf8_bin NULL  COMMENT '服务介绍，富文本' , 
	`is_deleted` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '是否上架' , 
	`is_home` tinyint(1) unsigned NOT NULL  DEFAULT 0 COMMENT '是否推荐首页' , 
	`is_integral` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '能否使用积分抵扣0否，1能' , 
	`is_coupon` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '能否使用优惠卷抵扣0否，1能' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`status` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '商品状态 ' , 
	`sell_count` int(10) NULL  DEFAULT 0 COMMENT '销售数量' , 
	`order_by` int(10) unsigned NOT NULL  DEFAULT 10 COMMENT '商品排序，越小越前' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_goods_shoping`(
	`id` int(11) NOT NULL  auto_increment COMMENT '主键自增无意义' , 
	`goods_id` int(11) NULL  COMMENT '淘潮玩商品id' , 
	`goods_count` int(10) unsigned NULL  COMMENT '商品数量' , 
	`user_id` int(11) NULL  COMMENT '所属用户id' , 
	`gmt_create` datetime NULL  COMMENT '添加购物车时间' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '逻辑删除，是否删除' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩购物车表';


/* Create table in target */
CREATE TABLE `tc_goods_stock`(
	`id` int(11) NOT NULL  auto_increment COMMENT '主键自增无意义' , 
	`goods_id` int(11) NULL  COMMENT '商品id' , 
	`stock` int(10) unsigned NULL  COMMENT '库存数量' , 
	`warning_count` int(10) unsigned NULL  COMMENT '预警数量' , 
	`warning_msg` varchar(300) COLLATE utf8_bin NULL  COMMENT '预警消息' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_img_module`(
	`id` int(4) NOT NULL  auto_increment , 
	`img_url` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '图片地址' , 
	`layout` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '布局方式' , 
	`jump_type` int(4) NULL  COMMENT '跳转类型 (1.普通商品，2品牌，3品类，4.标签)' , 
	`target` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '跳转类容（跳转类型为2/3时 等于-1时 跳转全部品牌/品类显示）' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '1为删除，0为未删除' , 
	`status` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '1上架，0下架' , 
	`sort` int(4) NULL  COMMENT '排序值' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_0900_ai_ci';


/* Create table in target */
CREATE TABLE `tc_label`(
	`id` int(10) NOT NULL  auto_increment , 
	`label_name` varchar(50) COLLATE utf8_bin NULL  , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_order`(
	`id` int(11) NOT NULL  auto_increment COMMENT '主键自增，无意义' , 
	`order_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '订单编号' , 
	`pay_order_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '支付订单号，微信支付时的商家订单号' , 
	`gtm_create` datetime NULL  COMMENT '创建时间，下单时间' , 
	`pay_time` datetime NULL  COMMENT '支付时间' , 
	`pay_type` varchar(30) COLLATE utf8_bin NULL  COMMENT '支付方法，目前微信支付' , 
	`user_id` int(11) NULL  COMMENT '下单用户id' , 
	`consignee` varchar(50) COLLATE utf8_bin NULL  COMMENT '收货人' , 
	`mobile_phone` varchar(50) COLLATE utf8_bin NULL  COMMENT '收货人联系电话' , 
	`address` varchar(300) COLLATE utf8_bin NULL  COMMENT '收货地址' , 
	`remark` varchar(200) COLLATE utf8_bin NULL  COMMENT '订单备注' , 
	`delivery_mode` int(11) NULL  COMMENT '配送方式。1.到店自提，2，邮寄' , 
	`estimated_time` datetime NULL  COMMENT '预计到店时间' , 
	`logistics_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '物流单号' , 
	`express_name` varchar(50) COLLATE utf8_bin NULL  COMMENT '快递公司名' , 
	`deliver_time` datetime NULL  COMMENT '发货时间' , 
	`finish_time` datetime NULL  COMMENT '完成时间' , 
	`pay_flow_no` varchar(255) COLLATE utf8_bin NULL  COMMENT '支付流水号' , 
	`status` int(11) NULL  COMMENT '订单状态:-1订单超时自动取消 1-待付款, 2-已付款(待发货),  3-已发货, 4-已完成, 5-已退款' , 
	`origin_amount` decimal(10,2) NULL  COMMENT '订单原价，抵扣前价格' , 
	`actual_amount` decimal(10,2) NULL  COMMENT '实际支付金额' , 
	`integral_amount` decimal(10,2) NULL  DEFAULT 0.00 COMMENT '积分抵扣金额' , 
	`coupon_amount` decimal(10,2) NULL  DEFAULT 0.00 COMMENT '优惠券抵扣金额' , 
	`goods_count` int(11) NULL  COMMENT '商品数量' , 
	PRIMARY KEY (`id`) , 
	KEY `order_no`(`order_no`) , 
	KEY `pay_order_no`(`pay_order_no`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩订单表';


/* Create table in target */
CREATE TABLE `tc_order_after`(
	`id` int(11) NOT NULL  auto_increment COMMENT '主键自增' , 
	`user_id` int(11) NULL  COMMENT '申请售后的用户' , 
	`after_type` int(11) unsigned NULL  COMMENT '1.仅退款，2.退款退货' , 
	`goods_id` int(11) NULL  COMMENT '退款的商品id' , 
	`refund_count` int(11) NULL  COMMENT '退款数量' , 
	`refund_price` decimal(10,2) NULL  COMMENT '退款金额' , 
	`detail_id` int(11) NULL  COMMENT '订单详情id' , 
	`order_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '订单编号' , 
	`refund_addr_id` int(11) NULL  COMMENT '退款地址id' , 
	`refund_consignee` varchar(50) COLLATE utf8_bin NULL  COMMENT '退货寄回时的收件人' , 
	`refund_phone` varchar(50) COLLATE utf8_bin NULL  COMMENT '退货寄回时的收货电话' , 
	`refund_addr` varchar(300) COLLATE utf8_bin NULL  COMMENT '退货寄回的地址' , 
	`reason` varchar(300) COLLATE utf8_bin NULL  COMMENT '退款原因' , 
	`status` int(11) NULL  COMMENT '1.申请中 2.退款中 3.已退款 4.已拒绝' , 
	`apply_time` datetime NULL  COMMENT '申请时间' , 
	`handle_time` datetime NULL  COMMENT '处理时间' , 
	`remark` varchar(300) COLLATE utf8_bin NULL  COMMENT '备注' , 
	`refund_express` varchar(100) COLLATE utf8_bin NULL  COMMENT '退货的快递公司' , 
	`logistics_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '退款退货时，用户寄出的物流单号' , 
	`reject_msg` varchar(200) COLLATE utf8_bin NULL  COMMENT '拒绝原因' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩订单售后';


/* Create table in target */
CREATE TABLE `tc_order_goods`(
	`id` int(11) NOT NULL  auto_increment COMMENT 'id自增，无意义' , 
	`tc_order_id` int(11) NULL  COMMENT '淘潮玩订单id' , 
	`tc_order_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '淘潮玩订单号' , 
	`tc_goods_id` int(11) NULL  COMMENT '淘潮玩商品id' , 
	`tc_goods_name` varchar(300) COLLATE utf8_bin NULL  COMMENT '淘潮玩商品名' , 
	`tc_goods_img` varchar(300) COLLATE utf8_bin NULL  COMMENT '淘潮玩商品图片' , 
	`goods_count` int(11) NULL  COMMENT '商品数量' , 
	`goods_price` decimal(10,2) NULL  COMMENT '购买时的商品价格' , 
	`unit_price` decimal(10,2) NULL  COMMENT '实付商品单价' , 
	`status` int(11) NULL  COMMENT '订单状态:-1订单超时自动取消 1-待付款, 2-已付款(待发货),  3-已发货, 4-已完成 5,已退款' , 
	`can_after` tinyint(1) NULL  DEFAULT 0 COMMENT '是否可以售后' , 
	`refund_count` int(11) NOT NULL  DEFAULT 0 COMMENT '退款数量，已经退款了多少个' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='淘潮玩订单详情，订单商品表';


/* Create table in target */
CREATE TABLE `tc_order_parent`(
	`id` int(11) NOT NULL  auto_increment COMMENT '主键自增id' , 
	`order_parent_no` varchar(100) COLLATE utf8_bin NULL  COMMENT '商家订单流水号' , 
	`order_amount` decimal(10,2) NULL  COMMENT '订单原价' , 
	`pay_amount` decimal(10,2) NULL  COMMENT '实际支付金额' , 
	`coupon_amount` decimal(10,2) NULL  DEFAULT 0.00 COMMENT '优惠券抵扣金额' , 
	`integral_amount` decimal(10,2) NULL  DEFAULT 0.00 COMMENT '积分抵扣金额' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	PRIMARY KEY (`id`) , 
	UNIQUE KEY `order_parent_no`(`order_parent_no`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin' COMMENT='支付时用来聚合淘潮玩和门票';


/* Create table in target */
CREATE TABLE `tc_recommend`(
	`id` int(10) NOT NULL  auto_increment COMMENT 'id自增,无意义' , 
	`template_id` int(20) NULL  COMMENT '模板id' , 
	`text` varchar(20) COLLATE utf8_bin NULL  COMMENT '推荐标题' , 
	`jump_type` int(20) NULL  COMMENT '跳转类型(1.商品分类2.联票列表3.次卡列表 4.拼团列表5.文章6.普通商品7.联票商品 8.次卡商品9.拼团商品10.优惠券11.淘潮玩品类12.淘潮玩品牌 13.淘潮玩标签 14.淘潮玩首页头部)' , 
	`target` text COLLATE utf8_bin NULL  COMMENT '跳转content' , 
	`sort` int(10) NULL  COMMENT '排序值' , 
	`goods_id` int(20) NULL  COMMENT '商品id' , 
	`cover_img` text COLLATE utf8_bin NULL  COMMENT '封面图' , 
	`cate_id` int(20) NULL  COMMENT '品类id' , 
	`brand_id` int(20) NULL  COMMENT '品牌id' , 
	`is_shelves` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '0下架 1上架 默认上架' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '0删除 1 未删除 默认1' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_update` datetime NULL  COMMENT '修改时间' , 
	`label_id` varchar(50) COLLATE utf8_bin NULL  COMMENT '标签id' , 
	`tc_flag` int(4) NULL  COMMENT '标识符 标记 淘潮玩推荐的类别' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_template`(
	`id` int(10) NOT NULL  auto_increment COMMENT '推荐模块id' , 
	`name` varchar(50) COLLATE utf8_bin NULL  COMMENT '模块名称' , 
	`way` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '0横向1纵向默认横向' , 
	`sort` int(20) NULL  COMMENT '排序值' , 
	`jump_type` int(20) NULL  COMMENT '跳转类型(1.商品分类2.联票列表3.次卡列表 4.拼团列表5.文章6.普通商品7.联票商品 8.次卡商品9.拼团商品10.优惠券11.淘潮玩品类12.淘潮玩品牌 13.淘潮玩标签 14.淘潮玩首页头部)' , 
	`target` text COLLATE utf8_bin NULL  COMMENT '跳转content' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_update` datetime NULL  COMMENT '修改时间' , 
	`is_shelves` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '1上架 0下架默认上架' , 
	`is_del` tinyint(1) NOT NULL  DEFAULT 0 COMMENT '1删除 0未删除' , 
	`is_home` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '1 商城首页 0 淘潮玩主页' , 
	PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COLLATE='utf8_bin';


/* Create table in target */
CREATE TABLE `tc_write_off_code`(
	`id` int(10) unsigned NOT NULL  auto_increment , 
	`write_off_name` varchar(500) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '核销卡名称' , 
	`cust_user_id` int(11) NULL  COMMENT '用户id' , 
	`goods_id` int(11) NULL  COMMENT '来源商品ID' , 
	`order_no` varchar(100) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '来源订单编号' , 
	`surplus_num` int(11) unsigned NULL  COMMENT '剩余次数' , 
	`total_num` int(11) unsigned NULL  COMMENT '总次数' , 
	`status` int(1) NULL  DEFAULT 0 COMMENT '状态(0 待处理 1已使用 2已过期 3已退款)' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_modified` datetime NULL  COMMENT '修改时间' , 
	`detail_id` int(11) NULL  COMMENT '订单详情id' , 
	`code` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL  COMMENT '核销码图片链接' , 
	PRIMARY KEY (`id`) , 
	KEY `order_no`(`order_no`) , 
	KEY `cust_user_id`(`cust_user_id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_0900_ai_ci' COMMENT='淘潮玩核销码表';


/* Create table in target */
CREATE TABLE `tc_write_off_code_log`(
	`id` int(10) unsigned NOT NULL  auto_increment , 
	`write_off_id` int(11) NULL  COMMENT '核销卡id' , 
	`store_id` int(11) NULL  COMMENT '核销门店' , 
	`staff_id` int(11) NULL  COMMENT '核销员工' , 
	`write_off_date` datetime NULL  COMMENT '核销时间' , 
	`gmt_create` datetime NULL  COMMENT '创建时间' , 
	`gmt_modified` datetime NULL  COMMENT '修改时间' , 
	`cust_user_id` int(11) NULL  COMMENT '用户id' , 
	`write_off_num` int(11) NULL  COMMENT '核销次数' , 
	PRIMARY KEY (`id`) , 
	KEY `idx_write_off_id`(`write_off_id`) , 
	KEY `idx_store_id`(`store_id`) , 
	KEY `idx_staff_id`(`staff_id`) 
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_0900_ai_ci' COMMENT='核销码记录表';

/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;


/* Alter table in target */
-- ALTER TABLE base_navigation_conf ADD COLUMN `is_home` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1 商品首页导航 0 全部分类导航 默认首页';

ALTER TABLE `by_goods_info` 
	ADD COLUMN `code_img` varchar(255)  COLLATE utf8mb4_0900_ai_ci NULL COMMENT '小程序二维码' after `spec_name` ;
	
ALTER TABLE tc_goods ADD COLUMN is_mailing TINYINT(1) COMMENT '是否支持邮寄';
```

```
//小程序appid
fastboot.wechat.appid=wxbf206dac4f2fc9ef
//小程序 secret
fastboot.wechat.secret=25276a7eecc7dbf96b65f9b8c1124f59

fastboot.wechat-pay.appid=wxbf206dac4f2fc9ef
```

