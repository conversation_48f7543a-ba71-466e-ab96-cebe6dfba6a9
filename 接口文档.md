## 淘潮玩商品

### 接口名称：查看全部商品

接口描述：查看全部商品 可以根据查询条件进行筛选

#### 请求地址：tc/tcGoods/

#### 请求方式：GET

#### 请求参数：

| 参数名     | 参数类型 | 说明                                                         | 必含有 |
| :--------- | -------- | ------------------------------------------------------------ | ------ |
| pageIndex  | int      | 分页起始位置                                                 | n      |
| pageSize   | int      | 每页条数                                                     | n      |
| searchName | string   | 根据商品名筛选                                               | n      |
| brandId    | int      | 品牌id 进行筛选                                              | n      |
| cateId     | int      | 品类id 进行筛选                                              | n      |
| title      | int[]    | 根据商品标签id 数组进行筛选                                  | n      |
| minPrice   | double   | 商品销售价格最小金额进行筛选                                 | n      |
| maxPrice   | double   | 商品销售价格最小金额进行筛选                                 | n      |
| isIntegral | boolean  | 根据商品是否支持使用积分抵扣 0否，1能                        | n      |
| isCoupon   | boolean  | 根据商品是否支持使用优惠卷抵扣 0否，1能                      | n      |
| sortSearch | int      | 排序规则 1  销量降序 2 销量升序 3 销售价 降序 4 销售价 升序 5 创建时间 降序  为空 默认(排序值升序) | n      |

#### 返回参数：

| total                | 数据总条数                 | y      |
| -------------------- | -------------------------- | ------ |
| 参数名               | 说明                       | 必含有 |
| list                 | 数据结果集                 | y      |
| pageNum              | 当前页码                   | y      |
| pageSize             | 每页数量                   | y      |
| list                 | 结果商品对象结果集         | y      |
| list[0].id           | 商品id                     | y      |
| list[0].goodsName    | 商品名称                   | y      |
| list[0].brandId      | 品牌id                     | y      |
| list[0].cateId       | 品类id                     | y      |
| list[0].title        | 标签列表(标签名数组)       | y      |
| list[0].salesArea    | 销售区域                   | y      |
| list[0].salePrice    | 销售价格                   | y      |
| list[0].doorPrice    | 门市价格                   | y      |
| list[0].stock        | 库存                       | y      |
| list[0].codeImg      | 小程序二维码               | y      |
| list[0].coverImg     | 封面图                     | y      |
| list[0].bannerImg    | 轮播图片(图片地址数组)     | y      |
| list[0].detail       | 商品详情，富文本           | y      |
| list[0].introduction | 服务介绍，富文本           | y      |
| list[0].isDeleted    | 是否上架(1上架0下架)       | y      |
| list[0].isCoupon     | 能否使用优惠卷抵扣0否，1能 | y      |
| list[0].isIntegral   | 能否使用积分抵扣0否，1能   | y      |
| list[0].gmtCreate    | 创建时间                   | y      |
| list[0].status       | 状态(0删除1未删除)         | y      |
| list[0].sellCount    | 销售数量                   | y      |
| list[0].tcBrand      | 品牌对象                   | y      |
| list[0].tcBrand.id   | 品牌对象id                 | y      |
| list[0].tcBrand.name | 品牌对象名称               | y      |
| list[0].tcCate       | 品类对象                   | y      |
| list[0].tcCate.id    | 品类对象id                 | y      |
| list[0].tcCate.name  | 品类对象名称               | y      |

请求结果：

```json
{
    "total": 4,
    "list": [
        -
    ],
    "pageNum": 1,
    "pageSize": 1,
    "size": 0,
    "startRow": 0,
    "endRow": 0,
    "pages": 0,
    "prePage": 0,
    "nextPage": 0,
    "isFirstPage": false,
    "isLastPage": false,
    "hasPreviousPage": false,
    "hasNextPage": false,
    "navigatePages": 0,
    "navigatepageNums": null,
    "navigateFirstPage": 0,
    "navigateLastPage": 0
}
```



### 接口名称：查看指定id 的商品信息

接口描述：根据商品id查询商品信息

#### 请求地址：tc/tcGoods/{id}

#### 请求方式：GET

#### 请求参数：

| 参数名 | 参数类型 | 说明   | 必含有 |
| ------ | -------- | ------ | ------ |
| id     | int      | 商品id | n      |

#### 返回参数：

| 参数名       | 说明                       | 必含有 |
| ------------ | -------------------------- | ------ |
| id           | 商品id                     | y      |
| goodsName    | 商品名称                   | y      |
| brandId      | 品牌id                     | y      |
| cateId       | 品类id                     | y      |
| title        | 标签列表(标签名数组)       | y      |
| salesArea    | 销售区域                   | y      |
| salePrice    | 销售价格                   | y      |
| doorPrice    | 门市价格                   | y      |
| stock        | 库存                       | y      |
| codeImg      | 小程序二维码               | y      |
| coverImg     | 封面图                     | y      |
| bannerImg    | 轮播图片(图片地址数组)     | y      |
| detail       | 商品详情，富文本           | y      |
| introduction | 服务介绍，富文本           | y      |
| isDeleted    | 是否上架(1上架0下架)       | y      |
| isCoupon     | 能否使用优惠卷抵扣0否，1能 | y      |
| isIntegral   | 能否使用积分抵扣0否，1能   | y      |
| gmtCreate    | 创建时间                   | y      |
| status       | 状态(0删除1未删除)         | y      |
| sellCount    | 销售数量                   | y      |
| tcBrand      | 品牌对象                   | y      |
| tcBrand.id   | 品牌对象id                 | y      |
| tcBrand.name | 品牌对象名称               | y      |
| tcCate       | 品类对象                   | y      |
| tcCate.id    | 品类对象id                 | y      |
| tcCate.name  | 品类对象名称               | y      |

请求结果：

```json
{
    "id": 1,
    "goodsName": "小鱼儿玩具",
    "brandId": 7,
    "cateId": 5,
    "title": [
        "最新",
        "小东西"
    ],
    "salesArea": "重庆",
    "salePrice": 22.00,
    "doorPrice": 13.00,
    "stock": 888888,
    "codeImg": null,
    "coverImg": "https://baoyan.oss-cn-shanghai.aliyuncs.com/img/CQxsCSAzscHRp4GcEnczXrhpW7ArZQtC.jpg",
    "bannerImg": "",
    "detail": "<h1>？？？？？？？？<a style=\"font-size: 14px;\"><img src=\"https://baoyan.oss-cn-shanghai.aliyuncs.com/img/EzmBXchwRXPWBjbwNbBfpW8MmjTJXJTx.jpg\" style=\"max-width:100%;\"></a></h1>",
    "introduction": "<p>小小</p>",
    "isDeleted": true,
    "isCoupon": true,
    "isIntegral": true,
    "isHome": false,
    "gmtCreate": "2021-07-16",
    "status": true,
    "sellCount": 100,
    "orderBy": 12,
    "tcBrand": {
        "id": 7,
        "name": "孩之宝",
        "remark": "测试",
        "isDel": null
    },
    "tcCate": {
        "id": 5,
        "name": "科技",
        "remark": "测试",
        "isDel": null
    },
    "tcGoodsStock": {
        "id": 4,
        "goodsId": 1,
        "stock": 888888,
        "warningCount": 10,
        "warningMsg": "测试   ===>",
        "gmtCreate": "2021-07-19"
    },
    "sortSearch": null,
    "searchName": null,
    "maxPrice": null,
    "minPrice": null,
    "gmtCreateEnd": null
}
```

## 淘潮玩品牌

### 接口名称：查看指定id 的品牌

接口描述：根据商品id查询品牌信息

#### 请求地址：tc/tcBrand/{id}

#### 请求方式：GET

#### 请求参数： 

| 参数名 | 参数类型 | 说明   | 必含有 |
| ------ | -------- | ------ | ------ |
| id     | int      | 品牌id | y      |

#### 返回参数：

| 参数名 | 说明     | 必含有 |
| ------ | -------- | ------ |
| id     | 品牌id   | y      |
| name   | 品牌名称 | y      |
| remark | 备注     | y      |

```json
{
    "id": 1,
    "name": "星辉玩具",
    "remark": "测试",
    "isDel": false
}
```



### 接口名称：查看全部品牌

接口描述：根据商品id查询品牌信息

#### 请求地址：tc/tcBrand/

#### 请求方式：GET

#### 请求参数： 

| 参数名    | 参数类型 | 说明           | 必含有 |
| :-------- | -------- | -------------- | ------ |
| pageIndex | int      | 分页起始位置   | n      |
| pageSize  | int      | 每页条数       | n      |
| name      | string   | 根据品牌名筛选 | n      |

#### 返回参数：

| 参数名         | 说明               | 必含有 |
| -------------- | ------------------ | ------ |
| total          | 数据总条数         | y      |
| list           | 数据结果集         | y      |
| pageNum        | 当前页码           | y      |
| pageSize       | 每页数量           | y      |
| list           | 结果品牌对象结果集 | y      |
| list[0].id     | 品牌id             | y      |
| list[0].name   | 品牌名称           | y      |
| list[0].remark | 备注               | y      |

```json
{
    "total": 1,
    "list": [
        {
            "id": 1,
            "name": "星辉玩具",
            "remark": "测试",
            "isDel": false
        }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "size": 1,
    "startRow": 1,
    "endRow": 1,
    "pages": 1,
    "prePage": 0,
    "nextPage": 0,
    "isFirstPage": true,
    "isLastPage": true,
    "hasPreviousPage": false,
    "hasNextPage": false,
    "navigatePages": 8,
    "navigatepageNums": [
        1
    ],
    "navigateFirstPage": 1,
    "navigateLastPage": 1
}
```

## 淘潮玩品类

### 接口名称：查看指定id 的品牌

接口描述：根据商品id查询品类信息

#### 请求地址：tc/tcCate/{id}

#### 请求方式：GET

#### 请求参数： 

| 参数名 | 参数类型 | 说明   | 必含有 |
| ------ | -------- | ------ | ------ |
| id     | int      | 品类id | y      |

#### 返回参数：

| 参数名 | 说明     | 必含有 |
| ------ | -------- | ------ |
| id     | 品类id   | y      |
| name   | 品类名称 | y      |
| remark | 备注     | y      |

```json
{
    "id": 1,
    "name": "奥特曼",
    "remark": "测试",
    "isDel": false
}
```

### 接口名称：查看全部品类

接口描述：根据商品id查询类牌信息

#### 请求地址：tc/tcCate/

#### 请求方式：GET

#### 请求参数： 

| 参数名    | 参数类型 | 说明           | 必含有 |
| :-------- | -------- | -------------- | ------ |
| pageIndex | int      | 分页起始位置   | n      |
| pageSize  | int      | 每页条数       | n      |
| name      | string   | 根据品类名筛选 | n      |

#### 返回参数：

| 参数名         | 说明               | 必含有 |
| -------------- | ------------------ | ------ |
| total          | 数据总条数         | y      |
| list           | 数据结果集         | y      |
| pageNum        | 当前页码           | y      |
| pageSize       | 每页数量           | y      |
| list           | 结果品类对象结果集 | y      |
| list[0].id     | 品类id             | y      |
| list[0].name   | 品牌名称           | y      |
| list[0].remark | 备注               | y      |

```json
{
    "total": 1,
    "list": [
        {
            "id": 1,
            "name": "奥特曼",
            "remark": "测试",
            "isDel": false
        }
    ],
    "pageNum": 1,
    "pageSize": 20,
    "size": 1,
    "startRow": 1,
    "endRow": 1,
    "pages": 1,
    "prePage": 0,
    "nextPage": 0,
    "isFirstPage": true,
    "isLastPage": true,
    "hasPreviousPage": false,
    "hasNextPage": false,
    "navigatePages": 8,
    "navigatepageNums": [
        1
    ],
    "navigateFirstPage": 1,
    "navigateLastPage": 1
}
```



## 淘潮玩标签

### 接口名称：查看淘潮玩全部标签

### 接口名称：查看全部品类

接口描述：查看淘潮玩全部标签

#### 请求地址：tc/label/

#### 请求方式：GET

#### 请求参数： 

#### 返回参数：

| 参数名    | 说明   | 必含有 |
| --------- | ------ | ------ |
| id        | 标签id | y      |
| labelName | 标签名 | y      |

```json
[
    {
        "id": 1,
        "labelName": "最新"
    },
    {
        "id": 2,
        "labelName": "小东西"
    },
    {
        "id": 3,
        "labelName": "热门"
    },
    {
        "id": 5,
        "labelName": "ZUIX"
    },
    {
        "id": 6,
        "labelName": "ZUI"
    },
    {
        "id": 7,
        "labelName": "乐玩"
    },
    {
        "id": 8,
        "labelName": "消息"
    },
    {
        "id": 9,
        "labelName": "zuix"
    },
    {
        "id": 10,
        "labelName": "潮玩"
    },
    {
        "id": 11,
        "labelName": "热卖"
    },
    {
        "id": 12,
        "labelName": "畅销"
    },
    {
        "id": 13,
        "labelName": "布制"
    }
]
```



## 淘潮玩板块

### 接口名称：查看淘潮玩顶部首页模块

接口描述：查看淘潮玩顶部首页模块

#### 请求地址：/tc/template/tcHomeTop

#### 请求方式：GET

#### 请求参数： 

#### 返回参数：

| 参数名 | 说明                      | 必含有 |
| ------ | ------------------------- | ------ |
| id     | 板块id                    | y      |
| target | json数组 顶部显示的标签id | y      |

```json
{
    "id": 11,
    "name": "淘潮玩首页头部",
    "way": false,
    "sort": 0,
    "jumpType": 14,
    "target": "[\"1\",\"8\",\"10\"]",
    "gmtCreate": 1627230170000,
    "gmtUpdate": 1627540755000,
    "isShelves": true,
    "isDel": false,
    "isHome": false
}
```

### 接口名称：查看商城首页模块

接口描述：查看商城首页模块

#### 请求地址：/tc/template/home

#### 请求方式：GET

#### 请求参数： 

#### 返回参数：

| 参数名   | 说明                                                         | 必含有 |
| -------- | ------------------------------------------------------------ | ------ |
| id       | 板块id                                                       | y      |
| name     | 板块名称                                                     | y      |
| way      | 显示方式   0横向 1纵向                                       | y      |
| jumpType | 更多链接的 跳转类型( 1.商品分类2.联票列表3.次卡列表  4.拼团列表5.文章6.普通商品7.联票商品 8.次卡商品9.拼团商品10.优惠券11.淘潮玩品类  12.淘潮玩品牌 13.淘潮玩标签 ) | y      |
| target   | 根据跳转类型(jumpType)判定  1：门票分类id  2/3/4：图片地址  5：文章的id  6：门票的商品id 7：联票商品 id   8：次卡商品id 9：拼团商品id  10：优惠券id 11:(target 是 -1 查询全部淘潮玩品类 否之 淘潮玩品类id)  <br/>12:(target 是 -1 查询全部淘潮玩品牌 否之 淘潮玩品牌id) <br/>13:(target 是 -1 查询全部淘潮玩标签 否之 淘潮玩标签 id) | y      |
| isHome   | 标识板块显示位置 1 在商城首页 0 在淘潮玩首页                 | y      |

```json
[
    {
        "id": 8,
        "name": "测试6",
        "way": false,
        "sort": 10,
        "jumpType": 5,
        "target": "40",
        "gmtCreate": 1627221986000,
        "gmtUpdate": 1627223431000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 26,
        "name": "热门品牌",
        "way": false,
        "sort": 10,
        "jumpType": 12,
        "target": "-1",
        "gmtCreate": 1627477578000,
        "gmtUpdate": 1627822127000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 27,
        "name": "热门品类",
        "way": false,
        "sort": 10,
        "jumpType": 11,
        "target": "-1",
        "gmtCreate": 1627478115000,
        "gmtUpdate": 1627822117000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 28,
        "name": "测试2020",
        "way": false,
        "sort": 10,
        "jumpType": 1,
        "target": "56",
        "gmtCreate": 1627540690000,
        "gmtUpdate": 1627750433000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 4,
        "name": "测试2",
        "way": false,
        "sort": 14,
        "jumpType": 1,
        "target": "56",
        "gmtCreate": 1627213283000,
        "gmtUpdate": null,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 10,
        "name": "测试7",
        "way": false,
        "sort": 1213,
        "jumpType": 2,
        "target": "https://baoyan.oss-cn-shanghai.aliyuncs.com/img/wdbaQbf3pJTr3e4DCKPYCa5fDcReXsTe.jpg",
        "gmtCreate": 1627230153000,
        "gmtUpdate": 1627286115000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    },
    {
        "id": 6,
        "name": "测试4",
        "way": false,
        "sort": 3131,
        "jumpType": 2,
        "target": "https://baoyan.oss-cn-shanghai.aliyuncs.com/img/JcDEDMxJbBKXHY7HeSJsAyt6RamxbyEk.jpg",
        "gmtCreate": 1627215336000,
        "gmtUpdate": 1627223649000,
        "isShelves": true,
        "isDel": false,
        "isHome": true
    }
]
```

### 接口名称：查看淘潮玩首页模块

接口描述：查看淘潮玩首页模块

#### 请求地址：/tc/template/tcHome

#### 请求方式：GET

#### 请求参数： 

#### 返回参数：查看商城首页模块 类型一致



```json
[
    {
        "id": 1,
        "name": "最新活动",
        "way": false,
        "sort": 10,
        "jumpType": 11,
        "target": "14",
        "gmtCreate": 1627205958000,
        "gmtUpdate": 1627750482000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 3,
        "name": "测试",
        "way": false,
        "sort": 10,
        "jumpType": 13,
        "target": "7",
        "gmtCreate": 1627207038000,
        "gmtUpdate": 1627302929000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 9,
        "name": "测试5",
        "way": false,
        "sort": 10,
        "jumpType": 11,
        "target": "11",
        "gmtCreate": 1627230100000,
        "gmtUpdate": 1627822179000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 23,
        "name": "测试9",
        "way": false,
        "sort": 10,
        "jumpType": 11,
        "target": "14",
        "gmtCreate": 1627387474000,
        "gmtUpdate": 1627750447000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 7,
        "name": "测试5",
        "way": true,
        "sort": 13,
        "jumpType": 11,
        "target": "12",
        "gmtCreate": 1627221915000,
        "gmtUpdate": 1627750401000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 29,
        "name": "热门标签",
        "way": false,
        "sort": 19,
        "jumpType": 13,
        "target": "-1",
        "gmtCreate": 1627822150000,
        "gmtUpdate": null,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    },
    {
        "id": 2,
        "name": "热门活动",
        "way": true,
        "sort": 21,
        "jumpType": 11,
        "target": "6",
        "gmtCreate": 1627206952000,
        "gmtUpdate": 1627750471000,
        "isShelves": true,
        "isDel": false,
        "isHome": false
    }
]
```

## 推荐系列

### 接口名称：查看某个板块的推荐系列

接口描述：根据商品id查询板块的推荐系列

#### 请求地址：/tc/recommend/template/{id}

#### 请求方式：GET

#### 请求参数： 

| 参数名 | 参数类型 | 说明   | 必含有 |
| :----- | -------- | ------ | ------ |
| id     | int      | 板块id | y      |

#### 返回参数：

| 参数名          | 说明                                                         | 必含有 |
| --------------- | ------------------------------------------------------------ | ------ |
| id              | 推荐系列id                                                   | y      |
| templateId      | 板块id                                                       | y      |
| text            | 系列名称                                                     | y      |
| coverImg        | 封面图                                                       | n      |
| sort            | 排序值                                                       | y      |
| isShelves       | 1上架 0下架                                                  | y      |
| jumpType        | 跳转类型( 1.商品分类2.联票列表3.次卡列表  4.拼团列表5.文章6.普通商品7.联票商品 8.次卡商品9.拼团商品10.优惠券11.淘潮玩 ) | y      |
| tcFlag          | 淘潮玩标记  当跳转类型是淘潮玩才会出现 1时表示系列商品 (goodsId 不为空时 表示当个商品 反之跳转内容时一个系列的商品列表)  2 时表示单个系列 | n      |
| goodsId         | 商品id  跳转类型为 (普通商品/联票商品/次卡商品/拼团商品/淘潮玩) | n      |
| labelId         | 淘潮玩商品标签  （tcFlag==2 && labelId / cateId / brandId == -1 表示 查询 标签/品类/品牌 里面的全部信息）反之不为空  就是具体（标签/品类/品牌）的 id | n      |
| cateId          | 淘潮玩商品品类  （同上 淘潮玩商品标签解释）                  | n      |
| brandId         | 淘潮玩商品品牌  （同上 淘潮玩商品标签解释）                  | n      |
| target          | 根据跳转类型判断定 jumpType（1:门票的分类 2/3/4:图片地址 5:文章id 10:优惠券id | n      |
| tcTemplate      | 所属板块对象                                                 | y      |
| tcTemplate.id   | 所属板块对象id                                               | y      |
| tcTemplate.name | 所属板块对象名称                                             | y      |

```json
[
    {
        "id": 5,
        "templateId": 29,
        "text": "测试--淘潮玩标签",
        "jumpType": 11,
        "target": "",
        "sort": 12,
        "goodsId": null,
        "coverImg": "https://baoyan.oss-cn-shanghai.aliyuncs.com/img/Tzd8tGrmD57cfyiaJys7645fmej2zBX7.jpg",
        "cateId": null,
        "brandId": null,
        "isShelves": true,
        "isDel": true,
        "gmtCreate": 1627823426000,
        "gmtUpdate": null,
        "labelId": "-1",
        "tcFlag": 2,
        "tcTemplate": {
            "id": 29,
            "name": "热门标签",
            "way": null,
            "sort": null,
            "jumpType": null,
            "target": null,
            "gmtCreate": null,
            "gmtUpdate": null,
            "isShelves": true,
            "isDel": null,
            "isHome": null
        },
        "searchTemplateId": null
    }
]
```

## 购物车

### 接口名称：查看全部购物车

接口描述：获得当前用户的淘潮玩购物车和门票购物车

#### 请求地址：/shop/showShopAll

#### 请求方式：GET

#### 请求参数：

| 参数名 | 说明 | 必含有 |
| ------ | ---- | ------ |
| 无参数 |      |        |

#### 返回参数：

| 参数名                      | 说明                          | 必含有 |
| --------------------------- | ----------------------------- | ------ |
| left                        | 门票购物车信息                | n      |
| left[0].id                  | 门票购物车id                  | y      |
| left[0].goodsId             | 门票商品id                    | y      |
| left[0].goodsType           | 商品类型 1 普通 2 次卡 3 联票 | y      |
| left[0].goodsCount          | 数量                          | y      |
| left[0].goodsImg            | 图片                          | y      |
| left[0].marketPrice         | 市场价                        | y      |
| left[0].sellPrice           | 零售价                        | y      |
| left[0].goodsName           | 商品名称                      | y      |
| middle                      | 淘潮玩购物车信息              | n      |
| middle[0].id                | 淘潮购物车id                  | y      |
| middle[0].goodsId           | 淘潮商品id                    | y      |
| middle[0].goodsCount        | 购物车中商品数量              | y      |
| middle[0].tcGoods           | 淘潮商品信息                  | y      |
| middle[0].tcGoods.goodsName | 淘潮商品名                    | y      |
| middle[0].tcGoods.salePrice | 淘潮商品售价                  | y      |
| middle[0].tcGoods.coverImg  | 淘潮商品预览图片              | y      |

#### 请求结果：

```json
{
    "left":[
        {
            "id":21049,
            "goodsId":202,
            "goodsType":1,
            "goodsCount":1,
            "goodsImg":"https://baoyan.oss-cn-shanghai.aliyuncs.com/img/WQbpFSGfnmJ7zKpwHe7aaz2XsXQC2CQP.png",
            "userId":null,
            "gmtCreate":"2021-08-02 20:36:19",
            "type":1,
            "marketPrice":"2535.00",
            "sellPrice":"500.00",
            "goodsName":"豹豹乐园长江店亲子次卡（付款后请到店开卡，本价格不含10元开卡费）",
            "goodsStock":"9978",
            "amount":null,
            "isDel":null,
            "inAmount":null,
            "limited":0
        }
    ],
    "middle":[
        {
            "id":6,
            "goodsId":22,
            "tcGoods":{
                "id":22,
                "goodsName":"神奇女侠手办",
                "brandId":1,
                "cateId":14,
                "title":null,
                "salesArea":null,
                "salePrice":79,
                "doorPrice":129,
                "stock":100,
                "codeImg":null,
                "coverImg":"暂无图片",
                "gmtCreate":"2021-07-24",

            },
            "goodsCount":1,
            "userId":100754,
            "gmtCreate":"2021-07-21 00:00:00",
            "isDel":false
        }
    ],
    "right":null
}
```

### 接口名称：删除购物车

接口描述：根据id删除淘潮玩购物车和门票购物车

#### 请求地址：/shop/deleteCart

#### 请求方式：DELETE

#### 请求参数：

| 参数名    | 参数类型 | 说明                     | 必含有 |
| --------- | -------- | ------------------------ | ------ |
| ticketIds | array    | 删除的门票购物车id数组   | n      |
| tcIds     | array    | 删除的淘潮玩购物车id数组 | n      |

#### 返回参数：

| 参数名     | 说明       | 必含有 |
| ---------- | ---------- | ------ |
| code       | 响应状态码 | y      |
| msg        | 提示消息   | n      |
| jsonObject | json对象   | n      |

接口如果出现异常状态码为 418。成功为0

#### 请求结果：

```json
{"code":0,"msg":"删除成功","jsonObject":null}
```

### 接口名称：添加淘潮购物车

接口描述：添加商品到淘潮购物车

#### 请求地址：/shop/insertTc

#### 请求方式：POST

#### 请求参数：

| 参数名     | 说明                   | 必含有 |
| ---------- | ---------------------- | ------ |
| goodsId    | 添加的商品id           | y      |
| goodsCount | 商品数量，负数就是减少 | y      |

#### 返回参数：

| 参数名     | 说明       | 必含有 |
| ---------- | ---------- | ------ |
| code       | 响应状态码 | y      |
| msg        | 提示消息   | n      |
| jsonObject | json对象   | n      |

接口如果出现异常状态码为 418。成功为0

#### 请求结果：

```json
{"code":0,"msg":"添加成功","jsonObject":null}
```

## 淘潮支付

### 接口名称：单个淘潮玩查看商品详情

接口描述：支付前的准备工作，查看支付相关的信息

#### 请求地址：/pay/payTcDetail

#### 请求方式：POST

#### 请求参数：

| 参数名                | 数据类型 | 说明                 | 必含有 |
| --------------------- | -------- | -------------------- | ------ |
| tcGoods               | array    | 需要购买的商品信息   | y      |
| tcGoods[0].goodsId    | number   | 商品id               | y      |
| tcGoods[0].goodsCount | number   | 商品数量             | y      |
| justAloneTc           | bool     | 是否详情页进行购买， | y      |

#### 返回参数：

| 参数名                       | 数据类型 | 说明                                                         | 必含有 |
| ---------------------------- | -------- | ------------------------------------------------------------ | ------ |
| coupon                       | array    | 可用的优惠券                                                 | n      |
| coupon[0].id                 | number   | 用户的优惠券id                                               | y      |
| coupon[0].name               | string   | 优惠券名                                                     | y      |
| coupon[0].discount           | number   | 折扣金额                                                     | y      |
| coupon[0].full               | number   | 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券） | y      |
| couponType                   | bool     | 是否可用优惠券                                               | y      |
| goodsAmount                  | number   | 订单价格                                                     | y      |
| count                        | number   | 积分可抵扣的金额                                             | y      |
| tcGoods                      | array    | 淘潮玩商品信息                                               | y      |
| tcGoods[0].goodsId           | number   | 淘潮玩商品id                                                 | y      |
| tcGoods[0].goodsCount        | number   | 淘潮玩商品数量                                               | y      |
| tcGoods[0].tcGoods.goodsName | string   | 淘潮玩商品名                                                 | y      |
| tcGoods[0].tcGoods.coverImg  | string   | 淘潮玩商品图片                                               | y      |
| goodsCount                   | number   | 商品数量                                                     | y      |
| type                         | bool     | 是否可用积分                                                 | y      |
| fraction                     | number   | 可使用的积分                                                 | y      |
| userPoint                    | number   | 用户的积分数量                                               | y      |

#### 请求结果：

```json
{
    "coupon":[
        {
            "id":84,
            "userId":100754,
            "couponId":26,
            "targetId":0,
            "type":7,
            "name":"宝燕全场可用",
            "discount":15,
            "full":15,
            "startDate":"2021-07-24",
            "endDate":"2021-08-05",
            "isUse":0,
            "isGive":0,
            "inBatch":null,
            "getType":2,
            "useDate":null,
            "gmtCreate":"2021-07-24 10:54:28",
            "gmtUpdate":null,
            "auditStatus":1,
            "auditUserId":null,
            "issuerUserId":null,
            "auditTime":"2021-08-02 07:26:33"
        }
    ],
    "couponType":true,
    "goodsAmount":237,
    "count":50,
    "couponCount":null,
    "tcGoods":[
        {
            "id":null,
            "goodsId":22,
            "tcGoods":{
                "id":22,
                "goodsName":"神奇女侠手办",
                "brandId":1,
                "cateId":14,
                "title":null,
                "salesArea":null,
                "salePrice":79,
                "doorPrice":129,
                "stock":100,
                "codeImg":null,
                "coverImg":"暂无图片"
                "isDeleted":true,
                "isCoupon":true,
                "isIntegral":true,
                "isHome":false,
                "gmtCreate":"2021-07-24",
                "status":true
                "orderBy":10
            },
            "goodsCount":3,
            "userId":null,
            "gmtCreate":null,
            "isDel":null
        }
    ],
    "goods":null,
    "goodsCount":3,
    "type":true,
    "fraction":50,
    "userPoint":50
}
```

### 接口名称：计算订单优惠后的价格

接口描述：计算订单优惠后的价格

#### 请求地址：/collect/calculationAll

#### 请求方式：GET

#### 请求参数：

| 参数名      | 数据类型 | 说明               | 必含有 |
| ----------- | -------- | ------------------ | ------ |
| orderAmount | number   | 订单原价           | y      |
| scoreAmount | number   | 使用积分抵扣的金额 | n      |
| couponId    | number   | 优惠券id           | n      |

couponId和scoreAmount不能一起使用，否则会抛出异常

#### 返回参数：

| 参数名         | 说明                           | 必含有 |
| -------------- | ------------------------------ | ------ |
| type           | 是否可用积分                   | y      |
| discountAmount | 优惠券抵扣的金额               | n      |
| orderAmount    | 计算完优惠券和积分后的订单金额 | n      |

#### 请求结果：

```json
{
    "type": false,
    "discountAmount": 15.00,
    "orderAmount": 103.00
}
```

### 接口名称：淘潮玩商品单独付款

接口描述：淘潮玩订单详情页支付请调用这个方法

#### 请求地址：/pay/payTc

#### 请求方式：POST

#### 请求参数：

| 参数名             | 数据类型 | 说明                                              | 必含有 |
| ------------------ | -------- | ------------------------------------------------- | ------ |
| tcGoods            | array    | 淘潮玩商品信息                                    | y      |
| tcGoods[0].goodsId | number   | 淘潮玩商品id                                      | y      |
| tcGoods[0].count   | number   | 淘潮玩商品数量                                    | y      |
| couponId           | number   | 优惠券id(不能同时使用优惠券和积分)                | n      |
| scoreAmount        | number   | 想要使用的积分数量                                | n      |
| justAloneTc        | bool     | 请为true。是否只有淘潮玩商品，如果含有门票为false | y      |
| fromId             | string   | 参考以前订单                                      | n      |
| deliveryMode       | number   | 配送方式  1：到店自提  2：快递邮寄                | y      |
| address            | string   | 收货地址                                          | n      |
| mobilePhone        | string   | 收货人电话                                        | n      |
| consignee          | string   | 收货人姓名                                        | n      |
| remark             | string   | 备注                                              | n      |

#### 返回参数：

| 参数名        | 类型   | 说明                                    | 必含有 |
| ------------- | ------ | --------------------------------------- | ------ |
| pay           | object | 唤醒小程序微信的参数                    | y      |
| pay.appId     | string |                                         | y      |
| pay.timeStamp | string |                                         | y      |
| pay.nonceStr  | string |                                         | y      |
| pay.pkg       | string |                                         | y      |
| pay.signType  | string |                                         | y      |
| pay.paySign   | string |                                         | y      |
| orderId       | string | 当前订单id                              | y      |
| orderNo       | string | 支付时的商家订单号                      | y      |
| type          | number | 含义不清楚，以前响应结果有。为1或者null | n      |
| payAmount     | number | 需要支付的金额                          | y      |

#### 请求结果：

```json
{
    "pay":{
        "appId":"wxbf206dac4f2fc9ef",
        "timeStamp":"1627996944",
        "nonceStr":"cat34sevwrrx8ouf",
        "pkg":"prepay_id=wx03212225207010094cc7612385b76b0000",
        "signType":"MD5",
        "paySign":"E0D1FFB23534192DACCC02D58443B463"
    },
    "orderId":"104551",
    "orderNo":"202127996940785464",
    "type":null,
    "payAmount":487
}
```

### 接口名称：购物车查看支付详情

接口描述：从购物车跳转到支付页面后显示的信息

#### 请求地址：/pay/payAllDetail

#### 请求方式：POST

#### 请求参数：

| 参数名        | 数据类型 | 说明                                                       | 必含有 |
| ------------- | -------- | ---------------------------------------------------------- | ------ |
| goods         | object   | 需要支付的门票信息                                         | n      |
| goods.id      | string   | 需要支付的门票购物车id，多个用","隔开                      | y      |
| goods.type    | number   | /*1  商品支付 2 购物车支付 3 未支付订单支付*/  这里必须为2 | y      |
| tcGoods       | array    | 需要支付的淘潮玩信息                                       | n      |
| tcGoods[0].id | number   | 淘潮玩购物车id                                             | y      |
| justAloneTc   | bool     | true: 淘潮玩商品页进行购买  false：购物车进行购买          | y      |

#### 返回参数：

| 参数名                       | 数据类型 | 说明                                                         | 必含有 |
| ---------------------------- | -------- | ------------------------------------------------------------ | ------ |
| coupon                       | array    | 可用的优惠券                                                 | n      |
| coupon[0].id                 | number   | 用户的优惠券id                                               | y      |
| coupon[0].name               | string   | 优惠券名                                                     | y      |
| coupon[0].discount           | number   | 折扣金额                                                     | y      |
| coupon[0].full               | number   | 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券） | y      |
| couponType                   | bool     | 是否可用优惠券                                               | y      |
| goodsAmount                  | number   | 订单价格                                                     | y      |
| count                        | number   | 积分可抵扣的金额                                             | y      |
| tcGoods                      | array    | 淘潮玩商品信息                                               | y      |
| tcGoods[0].goodsId           | number   | 淘潮玩商品id                                                 | y      |
| tcGoods[0].goodsCount        | number   | 淘潮玩商品数量                                               | y      |
| tcGoods[0].tcGoods.goodsName | string   | 淘潮玩商品名                                                 | y      |
| tcGoods[0].tcGoods.coverImg  | string   | 淘潮玩商品图片                                               | y      |
| tcGoods[0].tcGoods.salePrice | number   | 售价                                                         |        |
| goods                        | array    | 门票商品信息                                                 | y      |
| goods[0].id                  | number   | 门票购物车id                                                 | y      |
| goods[0].goodsCount          | number   | 门票购物车数量                                               | y      |
| goods[0].goodsImg            | string   | 门票图片                                                     | y      |
| goods[0].sellPrice           | number   | 门票售价                                                     | y      |
| goods[0].goodsName           | string   | 门票名                                                       | y      |
| goodsCount                   | number   | 商品数量                                                     | y      |
| type                         | bool     | 是否可用积分                                                 | y      |
| fraction                     | number   | 可使用的积分                                                 | y      |
| userPoint                    | number   | 用户的积分数量                                               | y      |

#### 请求结果：

```json
{
    "coupon":Array[4],
    "couponType":true,
    "goodsAmount":1017,
    "count":136,
    "couponCount":null,
    "tcGoods":Array[2],
    "goods":Array[2],
    "goodsCount":5,
    "type":true,
    "fraction":136,
    "userPoint":null
```

### 接口名称：购物车进行支付

接口描述：购物车进行支付的接口

#### 请求地址：/pay/payAll

#### 请求方式：POST

#### 请求参数：

| 参数名           | 数据类型 | 说明                                                         | 必含有 |
| ---------------- | -------- | ------------------------------------------------------------ | ------ |
| goods            | object   | 支付时门票信息                                               | n      |
| goods.goodsCount | number   | 门票总数                                                     | y      |
| goods.id         | string   | 需要支付的门票购物车id，多个用","隔开                        | y      |
| goods.type       | number   | /*1  商品支付 2 购物车支付 3 未支付订单支付*/  这里必须为2   | y      |
| goods.goods      | array    | 需要支付的门票购物车对象数组                                 | y      |
| tcGoods          | array    | 需要支付的淘潮玩购物车信息                                   | n      |
| tcGoods[0].id    | number   | 淘潮玩购物车id                                               | y      |
| deliveryMode     | number   | 配送方式。1.到店自提，2，邮寄 （如果有淘潮玩商品就必须要填） | y      |
| address          | string   | 收货地址 (如果是邮寄就必须填写)                              | n      |
| consignee        | string   | 收货人姓名                                                   | n      |
| mobilePhone      | string   | 收货人电话号码                                               | n      |
| orderAmount      | number   | 订单原价，优惠前的价格                                       | y      |
| payAmount        | number   | 订单价格，优惠后的价格                                       | y      |
| scoreAmount      | number   | 积分抵扣的价格，没有使用积分请填0                            | y      |
| couponAmount     | number   | 优惠券抵扣的金额，没有使用优惠券请填0                        | y      |
| couponId         | number   | 想要使用的优惠券id                                           | n      |
| fromId           | string   | 请参考二期的参数                                             | y      |
| goodsCount       | number   | 商品的总数量                                                 | n      |
| justAloneTc      | bool     | * true: 淘潮玩商品页进行购买 * false：购物车进行购买         | y      |
| remark           | string   | 订单备注                                                     | n      |
| type             | number   | /*1  商品支付 2 购物车支付 3 未支付订单支付*/  请为2         | y      |

#### 返回参数：

| 参数名        | 类型   | 说明                                    | 必含有 |
| ------------- | ------ | --------------------------------------- | ------ |
| pay           | object | 唤醒小程序微信的参数                    | y      |
| pay.appId     | string |                                         | y      |
| pay.timeStamp | string |                                         | y      |
| pay.nonceStr  | string |                                         | y      |
| pay.pkg       | string |                                         | y      |
| pay.signType  | string |                                         | y      |
| pay.paySign   | string |                                         | y      |
| orderId       | string | 当前订单id                              | y      |
| orderNo       | string | 支付时的商家订单号                      | y      |
| type          | number | 含义不清楚，以前响应结果有。为1或者null | n      |
| payAmount     | number | 需要支付的金额                          | y      |

#### 请求结果：

```json
{
    "pay":{
        "appId":"wxbf206dac4f2fc9ef",
        "timeStamp":"1627996944",
        "nonceStr":"cat34sevwrrx8ouf",
        "pkg":"prepay_id=wx03212225207010094cc7612385b76b0000",
        "signType":"MD5",
        "paySign":"E0D1FFB23534192DACCC02D58443B463"
    },
    "orderId":"104551",
    "orderNo":"202127996940785464",
    "type":null,
    "payAmount":487
}
```

## 淘潮订单

### 接口名称：查看用户订单列表

接口描述：分页查看当前用户的订单

#### 请求地址：/tc/order

#### 请求方式：GET

#### 请求参数：

| 参数名    | 数据类型 | 说明                                                         | 必含有 |
| --------- | -------- | ------------------------------------------------------------ | ------ |
| pageIndex | number   | 页码，不写默认第一页                                         | n      |
| pageSize  | number   | 一页多少条记录。默认20，超过50为50                           | n      |
| status    | number   | 订单状态，不写为查看全部。 * -1订单超时自动取消 1-待付款, * 2-已付款(待发货\|待自提),  3-已发货, 4-已完成 | n      |

#### 返回参数：

| 参数名                            | 类型   | 说明                                                         | 必含有 |
| --------------------------------- | ------ | ------------------------------------------------------------ | ------ |
| total                             | number | 总记录数                                                     | y      |
| pageNum                           | number | 当前页码                                                     | y      |
| pageSize                          | number | 每页的数量                                                   | y      |
| pages                             | number | 总页数                                                       | y      |
| hasNextPage                       | bool   | 是否有下一页                                                 | y      |
| list                              | array  | 订单列表                                                     | y      |
| list[0].orderNo                   | string | 订单号                                                       | y      |
| list[0].payOrderNo                | string | 商家支付订单号                                               | y      |
| list[0].status                    | number | 订单状态。* -1订单超时自动取消 1-待付款, * 2-已付款(待发货待自提),  3-已发货, 4-已完成 |        |
| list[0].originAmount              | number | 订单原价，优惠前的价格                                       | y      |
| list[0].actualAmount              | number | 实付价格，应付价格                                           | y      |
| list[0].integralAmount            | number | 积分抵扣的金额                                               | y      |
| list[0].couponAmount              | number | 优惠券抵扣的金额                                             | y      |
| list[0].goodsCount                | number | 商品数量                                                     | y      |
| list[0].gtmCreate                 | string | 创建订单时间                                                 | y      |
| list[0].payTime                   | string | 支付时间                                                     | y      |
| list[0].payType                   | string | 支付类型                                                     | y      |
| list[0].deliveryMode              | number | 配送方式。1.到店自提，2，邮寄 （如果有淘潮玩商品就必须要填） | y      |
| list[0].address                   | string | 收货地址                                                     | y      |
| list[0].mobilePhone               | string | 收货人电话                                                   | y      |
| list[0].consignee                 | string | 收货人姓名                                                   | y      |
| list[0].logisticsNo               | string | 快递单号                                                     | y      |
| list[0].expressName               | string | 快递名                                                       | y      |
| list[0].finishTime                | string | 订单完成时间                                                 | y      |
| list[0].payFlowNo                 | string | 微信支付流水号                                               | y      |
| list[0].orderGoods                | array  | 订单详情列表                                                 | y      |
| list[0].orderGoods[0].tcOrderId   | number | 订单id                                                       | y      |
| list[0].orderGoods[0].tcGoodsName | string | 商品名                                                       | y      |
| list[0].orderGoods[0].tcGoodsImg  | string | 图片路径                                                     | y      |
| list[0].orderGoods[0].goodsPrice  | number | 支付时的售价                                                 | y      |
| list[0].orderGoods[0].goodsCount  | number | 商品数量                                                     | y      |
| list[0].orderGoods[0].status      | number | 订单详情状态。* -1订单超时自动取消 1-待付款, * 2-已付款(待发货 |        |

#### 请求结果：

```json
{
    "total":3,
    "list":{
            "id":43,
            "orderNo":"202128068188942511",
            "payOrderNo":"202128068188900364",
            "gtmCreate":"2021-08-04 17:09:49",
            "payTime":null,
            "payType":"微信支付",
            "userId":100754,
            "consignee":null,
            "mobilePhone":null,
            "address":null,
            "remark":"",
            "deliveryMode":1,
            "logisticsNo":null,
            "expressName":null,
            "finishTime":null,
            "payFlowNo":null,
            "status":1,
            "originAmount":157,
            "actualAmount":137,
            "integralAmount":20,
            "couponAmount":0,
            "goodsCount":3,
            "orderGoods":[
                {
                    "id":72,
                    "tcOrderId":43,
                    "tcOrderNo":null,
                    "tcGoodsId":21,
                    "tcGoodsName":"美国队长盾牌",
                    "tcGoodsImg":"",
                    "goodsCount":2,
                    "unitPrice":34.03,
                    "goodsPrice":39,
                    "status":1,
                    "tempUnitPrice":null
                }
            ]
        },
    "pageNum":1,
    "pageSize":20,
    "size":3,
    "pages":1,
    "hasNextPage":false
}
```

### 接口名称：查看用户单个订单

接口描述：根据订单id查看订单

#### 请求地址：/tc/order/{orderId}

#### 请求方式：GET

#### 请求参数：

| 参数名 | 数据类型 | 说明 | 必含有 |
| ------ | -------- | ---- | ------ |
| 无参数 |          |      |        |

#### 返回参数：

| 参数名                    | 类型   | 说明                                                         | 必含有                       |
| ------------------------- | ------ | ------------------------------------------------------------ | ---------------------------- |
| orderNo                   | string | 订单号                                                       | y                            |
| payOrderNo                | string | 商家支付订单号                                               | y                            |
| status                    | number | 订单状态。* -1订单超时自动取消 1-待付款, * 2-已付款(待发货   | 待自提),  3-已发货, 4-已完成 |
| originAmount              | number | 订单原价，优惠前的价格                                       | y                            |
| actualAmount              | number | 实付价格，应付价格                                           | y                            |
| integralAmount            | number | 积分抵扣的金额                                               | y                            |
| couponAmount              | number | 优惠券抵扣的金额                                             | y                            |
| goodsCount                | number | 商品数量                                                     | y                            |
| gtmCreate                 | string | 创建订单时间                                                 | y                            |
| payTime                   | string | 支付时间                                                     | y                            |
| payType                   | string | 支付类型                                                     | y                            |
| deliveryMode              | number | 配送方式。1.到店自提，2，邮寄 （如果有淘潮玩商品就必须要填） | y                            |
| address                   | string | 收货地址                                                     | y                            |
| mobilePhone               | string | 收货人电话                                                   | y                            |
| consignee                 | string | 收货人姓名                                                   | y                            |
| logisticsNo               | string | 快递单号                                                     | y                            |
| expressName               | string | 快递名                                                       | y                            |
| finishTime                | string | 订单完成时间                                                 | y                            |
| payFlowNo                 | string | 微信支付流水号                                               | y                            |
| orderGoods                | array  | 订单详情列表                                                 | y                            |
| orderGoods[0].tcOrderId   | number | 订单详情id                                                   | y                            |
| orderGoods[0].tcGoodsName | string | 商品名                                                       | y                            |
| orderGoods[0].tcGoodsImg  | string | 图片路径                                                     | y                            |
| orderGoods[0].goodsPrice  | number | 支付时的售价                                                 | y                            |
| orderGoods[0].goodsCount  | number | 商品数量                                                     | y                            |
| orderGoods[0].status      | number | 订单详情状态。* -1订单超时自动取消 1-待付款, * 2-已付款(待发货 |                              |

#### 请求结果：

```json
{
    "id": 43,
    "orderNo": "202128068188942511",
    "payOrderNo": "202128068188900364",
    "gtmCreate": "2021-08-04 17:09:49",
    "payTime": null,
    "payType": "微信支付",
    "userId": 100754,
    "consignee": null,
    "mobilePhone": null,
    "address": null,
    "remark": "",
    "deliveryMode": 1,
    "logisticsNo": null,
    "expressName": null,
    "finishTime": null,
    "payFlowNo": null,
    "status": 1,
    "originAmount": 157.00,
    "actualAmount": 137.00,
    "integralAmount": 20.00,
    "couponAmount": 0.00,
    "goodsCount": 3,
    "startDate": null,
    "endDate": null,
    "keyWord": null,
    "orderGoods": [
        {
            "id": 72,
            "tcOrderId": 43,
            "tcOrderNo": null,
            "tcGoodsId": 21,
            "tcGoodsName": "美国队长盾牌",
            "tcGoodsImg": "",
            "goodsCount": 2,
            "unitPrice": 34.03,
            "goodsPrice": 39.00,
            "status": 1,
            "tempUnitPrice": null
        },
        {
            "id": 73,
            "tcOrderId": 43,
            "tcOrderNo": null,
            "tcGoodsId": 22,
            "tcGoodsName": "神奇女侠手办",
            "tcGoodsImg": "暂无图片",
            "goodsCount": 1,
            "unitPrice": 68.93,
            "goodsPrice": 79.00,
            "status": 1,
            "tempUnitPrice": null
        }
    ]
}
```

### 接口名称：申请淘潮玩商品退款

接口描述：申请淘潮玩商品退款

#### 请求地址：/tc/after/apply

#### 请求方式：POST

#### 请求参数：

| 参数名     | 参数类型 | 说明                          | 必含有 |
| ---------- | -------- | ----------------------------- | ------ |
| after_type |          | 退款类型 1.仅退款，2.退款退货 | y      |
| detail_id  |          | 订单详情id                    |        |
| reason     |          | 退款原因                      | y      |

#### 返回参数：

| 参数名 | 说明 | 必含有 |
| ------ | ---- | ------ |
|        |      |        |

返回bool，是否申请成功

#### 请求结果：

```json

```

