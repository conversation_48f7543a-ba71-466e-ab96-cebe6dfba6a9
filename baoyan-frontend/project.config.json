{"description": "项目配置文件。", "setting": {"urlCheck": false, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "miniprogramRoot": "dist/wx/", "compileType": "miniprogram", "appid": "wxbf206dac4f2fc9ef", "projectname": "baoyan", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "libVersion": "2.20.2", "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"list": []}}}