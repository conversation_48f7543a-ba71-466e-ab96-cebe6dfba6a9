import {
  getTicket,
  getTicketDetail,
  queryTeam,
  getTeamDetail,
  getTeamGoods
} from '@/api'

export default {
  namespaced: true,
  state: {
    ticket: [],
    ticketDetail: [],
    teams: [],
    teamDetail: [],
    teamGoods: []
  },
  mutations: {
    //  拼团活动
    RECORD_TICKET: (state, ticket) => {
      state.ticket = ticket
    },
    //  拼团详情
    RECORD_TICKET_DETAIL: (state, ticketDetail) => {
      state.ticketDetail = ticketDetail
    },
    //  正在拼团
    RECORD_TEAM: (state, teams) => {
      state.teams = teams
    },
    //  正在参与拼团详情
    RECORD_TEAM_DETAIL: (state, teamDetail) => {
      state.teamDetail = teamDetail
    },
    //  拼团订单详情
    RECORD_TEAM_GOODS: (state, teamGoods) => {
      state.teamGoods = teamGoods
    }
  },
  actions: {
    //  拼团活动
    getTicket ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getTicket(params)
        let arr = [...state.ticket, ...data.list]
        commit('RECORD_TICKET', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  拼团详情
    getTicketDetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getTicketDetail(params)
        commit('RECORD_TICKET_DETAIL', data)
        resolve(data)
      })
    },
    //  正在拼团
    queryTeam ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryTeam(params)
        commit('RECORD_TEAM', data)
        resolve(data)
      })
    },
    //  正在参与拼团详情
    getTeamDetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getTeamDetail(params)
        commit('RECORD_TEAM_DETAIL', data)
        resolve(data)
      })
    },
    //  拼团订单详情
    getTeamGoods ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getTeamGoods(params)
        commit('RECORD_TEAM_GOODS', data)
        resolve(data)
      })
    }
  },
  getters: {

  }
}
