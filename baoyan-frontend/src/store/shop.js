import {
  queryShop,
  getShopcar,
  deleteShop,
  changeShopcar,
  payDetail
} from '@/api'

export default {
  namespaced: true,
  state: {
    shopcar: [],
    paydetail: []
  },
  mutations: {
    //  我的购物车
    RECORD_SHOPCAR: (state, shopcar) => {
      state.shopcar = shopcar
    },
     //  提交订单
     RECORD_PAYDETAIL: (state, paydetail) => {
      state.paydetail = paydetail
    }
  },
  actions: {
    //  添加购物车
    queryShop ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryShop(params)
        resolve(data)
      })
    },
    //  我的购物车
    getShopcar ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getShopcar(params)
        commit('RECORD_SHOPCAR', data)
        resolve(data)
      })
    },
    //  删除购物车
    deleteShop ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await deleteShop(params)
        resolve(data)
      })
    },
    //  修改购物车
    changeShopcar ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await changeShopcar(params)
        resolve(data)
      })
    },
    //  提交订单
    payDetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await payDetail(params)
        commit('RECORD_PAYDETAIL', data)
        resolve(data)
      })
    }
  },
  getters: {

  }
}
