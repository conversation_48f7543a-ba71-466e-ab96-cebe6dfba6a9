import {
  getOrderList,
  getOrderDetail,
  cancelOrder,
  querApplySale,
  getOrderNum,
  queryEvalOrder,
  delOrder
} from '@/api'

export default {
  namespaced: true,
  state: {
    orderList: [],
    orderDetail: [],
    orderNum: []
  },
  mutations: {
    //  我的订单列表
    RECORD_ORDERLIST: (state, orderList) => {
      state.orderList = orderList
    },
    //   获取订单详情
    RECORD_ORDER_DETAIL: (state, orderDetail) => {
      state.orderDetail = orderDetail
    },
    //  我的订单中显示订单 （待支付，已付款，已完成）数量
    RECORD_ORDERNUM: (state, orderNum) => {
      state.orderNum = orderNum
    }
  },
  actions: {
    //  我的订单列表
    getOrderList ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getOrderList(params)
        let arr = [...state.orderList, ...data.list]
        commit('RECORD_ORDERLIST', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  获取订单详情
    getOrderDetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getOrderDetail(params)
        commit('RECORD_ORDER_DETAIL', data)
        resolve(data)
      })
    },
    //  取消订单
    cancelOrder ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await cancelOrder(params)
        resolve(data)
      })
    },
    //  提交申请售后
    querApplySale ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await querApplySale(params)
        resolve(data)
      })
    },
    //  我的订单中显示订单 （待支付，已付款，已完成）数量
    getOrderNum ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getOrderNum(params)
        commit('RECORD_ORDERNUM', data)
        resolve(data)
      })
    },
    //  评价
    queryEvalOrder ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryEvalOrder(params)
        resolve(data)
      })
    },
    //  删除订单
    delOrder ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await delOrder(params)
        resolve(data)
      })
    }
  },
  getters: {

  }
}
