import {
  getIndexinfo,
  register,
  queryPhone,
  queryInfo,
  getActivity,
  getbyarticle,
  getClassification,
  getGoodsdetail,
  getEvaluate
} from '@/api'

export default {
  namespaced: true,
  state: {
    indexinfo: [],
    activity: [],
    byarticle: [],
    classification: [],
    ticketGoods: [],
    cardGoods: [],
    goodsDetail: [],
    evaluate: []
  },
  mutations: {
    RECORD_INDEXINFO: (state, indexinfo) => {
      state.indexinfo = indexinfo
    },
    RECORD_ACTIVITY: (state, activity) => {
      state.activity = activity
    },
    RECORD_BYARTICLE: (state, byarticle) => {
      state.byarticle = byarticle
    },
    RECORD_CLASSIFICATION: (state, classification) => {
      state.classification = classification
    },
    RECORD_GOODS_DETAIL: (state, goodsDetail) => {
      state.goodsDetail = goodsDetail
    },
    RECORD_EVALUATE: (state, evaluate) => {
      state.evaluate = evaluate
    }
  },
  actions: {
    //  首页
    getIndexinfo ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getIndexinfo(params)
        commit('RECORD_INDEXINFO', data)
        resolve(data)
      })
    },
    //  注册
    register ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await register(params)
        resolve(data)
      })
    },
     //  发送验证码
     queryPhone ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryPhone(params)
        resolve(data)
      })
    },
    //  完善个人资料
     queryInfo ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryInfo(params)
        resolve(data)
      })
    },
    //  首页活动
    getActivity ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getActivity(params)
        let arr = [...state.activity, ...data.list]
        commit('RECORD_ACTIVITY', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  文章详情
    getbyarticle ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getbyarticle(params)
        commit('RECORD_BYARTICLE', data)
        resolve(data)
      })
    },
    //  分类
    getClassification ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getClassification(params)
        commit('RECORD_CLASSIFICATION', data)
        resolve(data)
      })
    },
    //  普通商品详情
    getGoodsdetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getGoodsdetail(params)
        commit('RECORD_GOODS_DETAIL', data)
        resolve(data)
      })
    },
    //  更多评价
    getEvaluate ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getEvaluate(params)
        let arr = [...state.evaluate, ...data.list]
        arr.map(item => {
          if (item.img) {
            item.img = item.img.split(',')
          } else {
            item.img = []
          }
          if (item.isAnonymous) {
            item.userName = item.userName ? item.userName.substr(0, 1)+'****' : ''
          }
        })
        commit('RECORD_EVALUATE', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
  },
  getters: {

  }
}
