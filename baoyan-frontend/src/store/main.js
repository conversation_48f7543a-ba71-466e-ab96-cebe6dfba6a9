import {
  getOrderafter,
  getOrderafterDetail,
  getIntegral,
  getCouponUser
} from '@/api'

export default {
  namespaced: true,
  state: {
    orderafter: [],
    orderafterDetail: [],
    integral: [],
    couponUser: []
  },
  mutations: {
    //  我的售后列表
    RECORD_ORDERAFTER: (state, orderafter) => {
      state.orderafter = orderafter
    },
    //  查看售后详情
    RECORD_ORDERAFTER_DETAIL: (state, orderafterDetail) => {
      state.orderafterDetail = orderafterDetail
    },
    //  我的积分
    RECORD_INTEGRAL: (state, integral) => {
      state.integral = integral
    },
    //  我的优惠券
    RECORD_COUPONUSER: (state, couponUser) => {
      state.couponUser = couponUser
    }
  },
  actions: {
    //  我的售后列表
    getOrderafter ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getOrderafter(params)
        let arr = [...state.orderafter, ...data.list]
        commit('RECORD_ORDERAFTER', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
     //  查看售后详情
     getOrderafterDetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getOrderafterDetail(params)
        commit('RECORD_ORDERAFTER_DETAIL', data)
        resolve(data)
      })
    },
    //  我的积分
    getIntegral ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getIntegral(params)
        commit('RECORD_INTEGRAL', data)
        resolve(data)
      })
    },
    //  我的优惠券
    getCouponUser ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getCouponUser(params)
        let arr = [...state.couponUser, ...data.list]
        commit('RECORD_COUPONUSER', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    }
  },
  getters: {

  }
}
