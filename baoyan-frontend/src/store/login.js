import {
  register,
  queryPhone,
  queryInfo,
  updateUser
} from '@/api'

export default {
  namespaced: true,
  state: {
  },
  mutations: {
  },
  actions: {
    //  注册
    register ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await register(params)
        resolve(data)
      })
    },
     //  发送验证码
     queryPhone ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryPhone(params)
        resolve(data)
      })
    },
    //  完善个人资料
     queryInfo ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryInfo(params)
        resolve(data)
      })
    },
    // 修改用户信息
    updateUser ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await updateUser(params)
        resolve(data)
      })
    }
  },
  getters: {

  }
}
