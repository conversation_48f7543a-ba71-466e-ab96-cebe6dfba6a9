import {
  getAppointment,
  getcustApp,
  getcancel,
  queryDelete,
  getAllstore,
  getBespdetail,
  getshowProduct,
  getInsert,
  getCheckDate,
  getClassProduct
} from '@/api'

export default {
  namespaced: true,
  state: {
    appointment: [],
    custApp: [],
    allstore: [],
    bespdetail: [],
    product: [],
    checkDate: [],
    products: []
  },
  mutations: {
    //  我的预约列表
    RECORD_APPOINTMENT: (state, appointment) => {
      state.appointment = appointment
    },
    //  预约详情
    RECORD_CUSTAPP: (state, custApp) => {
      state.custApp = custApp
    },
    //  所有门店
    RECORD_ALLSTORE: (state, allstore) => {
      state.allstore = allstore
    },
    //  门店详情
    RECORD_BESP_DETAIL: (state, bespdetail) => {
      state.bespdetail = bespdetail
    },
    //  门店产品
    RECORD_PRODUCT: (state, product) => {
      state.product = product
    },
    //  查询日历
    RECORD_CHECKDATE: (state, checkDate) => {
      state.checkDate = checkDate
    },
    //  商品
    RECORD_PRODUCTS: (state, products) => {
      state.products = products
    }
  },
  actions: {
    //  我的预约列表
    getAppointment ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getAppointment(params)
        let arr = [...state.appointment, ...data.list]
        commit('RECORD_APPOINTMENT', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  预约详情
    getcustApp ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getcustApp(params)
        commit('RECORD_CUSTAPP', data)
        resolve(data)
      })
    },
    //  取消预约
    getcancel ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getcancel(params)
        resolve(data)
      })
    },
    //  预约删除
    queryDelete ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await queryDelete(params)
        resolve(data)
      })
    },
    //  所有门店
    getAllstore ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getAllstore(params)
        if (data.code == 418) {
            wx.showModal({
              content: '您还没有完善资料', 
              cancelText: '取消', 
              confirmText: '去完善',
              success: res => {
                if (res.confirm) {
                    wx.navigateTo({
                        url: '/pages/login/info/main'
                    })
                }
              }
            })
            return
        }
        let arr = [...state.allstore, ...data.list]
        commit('RECORD_ALLSTORE', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  门店详情
    getBespdetail ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBespdetail(params)
        commit('RECORD_BESP_DETAIL', data)
        resolve(data)
      })
    },
    //  门店产品
    getshowProduct ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getshowProduct(params)
        let arr = [...state.product, ...data.list]
        commit('RECORD_PRODUCT', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    },
    //  添加预约
    getInsert ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getInsert(params)
        resolve(data)
      })
    },
     //  查询日历
     getCheckDate ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getCheckDate(params)
        commit('RECORD_CHECKDATE', data)
        resolve(data)
      })
    },
    //  商品
    getClassProduct ({state, commit, dispatch}, params) {
      return new Promise(async (resolve, reject) => {
        const { data } = await getClassProduct(params)
        let arr = [...state.products, ...data.list]
        commit('RECORD_PRODUCTS', arr)
        if (arr.length >= data.total) {
          resolve(true)
        } else {
          resolve(false)
        } 
      })
    }
  },
  getters: {

  }
}
