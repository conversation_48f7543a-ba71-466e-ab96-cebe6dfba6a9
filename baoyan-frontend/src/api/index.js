import request from '@/utils/fetch'

//  获取token
export const queryToken = data => request.get('/wx/wx-token', data)
//  注册
export const register = data => request.get('/user/register', data)
//  发送验证码
export const queryPhone = data => request.get('/common/reg-sms', data)
//  完善个人资料
export const queryInfo = data => request.post('/user/completeUser', data)
//  首页
export const getIndexinfo = data => request.get('/home/<USER>', data)
//  首页活动
export const getActivity = data => request.get('/home/<USER>', data)
//  文章详情
export const getbyarticle = data => request.get('/home/<USER>', data)
//  分类
export const getClassification = data => request.get('/home/<USER>', data)
//  普通商品详情
export const getGoodsdetail = data => request.get('/goods/detail', data)
//  更多评价
export const getEvaluate = data => request.get('/goods/evaluate', data)

//  我的售后列表
export const getOrderafter = data => request.get('/orderafter/list', data)
//  查看售后详情
export const getOrderafterDetail = data => request.get(`/orderafter/${data.id}`, {})
//  我的积分
export const getIntegral = data => request.get('/integraLog/list', data)
//  我的优惠券
export const getCouponUser = data => request.get('/couponUser/list', data)
//  我的预约列表
export const getAppointment = data => request.get('/custApp/list', data)
//  预约详情
export const getcustApp = data => request.get(`/custApp/${data.id}`, {})
//  取消预约
export const getcancel = data => request.get(`/custApp/cancel`, data)
//  预约删除
export const queryDelete = data => request.delete(`/custApp/${data.id}`, {})
//  所有门店
export const getAllstore = data => request.get('/activity/showActivity', data)
//  门店详情
export const getBespdetail = data => request.get('/activity/detail', data)
//  门店产品
export const getshowProduct = data => request.get('/activity/showProduct', data)
//  添加预约
export const getInsert = data => request.post('/activity/insert', data)
//  查询日历
export const getCheckDate = data => request.get('/activity/checkDate', data)
//  商品
export const getClassProduct = data => request.get('/activity/classProduct', data)

//  添加购物车
export const queryShop = data => request.post('/shop/insert', data)
//  我的购物车
export const getShopcar = data => request.get('/shop/showShop', data)
//  删除购物车
export const deleteShop = data => request.delete(`/shop/delete/${data.ids}`, {})
//  修改购物车
export const changeShopcar = data => request.put('/shop/update', data)
//  提交订单
export const payDetail = data => request.post('/pay/payDetail', data)

// 查看当前订单信息
export const payAllDetail = data => request.post('/pay/payAllDetail', data)

//  我的订单列表
export const getOrderList = data => request.get('/order/list', data)
//  获取订单详情
export const getOrderDetail = data => request.get(`/order/userOrderDetail/${data.id}`, {})
//  取消订单
export const cancelOrder = data => request.post(`/order/cancelOrder/${data.id}`, {})
//  提交申请售后
export const querApplySale = data => request.post('/order/applySale', data)
//  我的订单中显示订单 （待支付，已付款，已完成）数量
export const getOrderNum = data => request.get('/order/num', data)
//  评价
export const queryEvalOrder = data => request.post('/order/evalOrder', data)
// 获取阿里云配置
export const getossToken = data => request.get('/common/oss-token', data)
//  删除订单
export const delOrder = data => request.delete(`/order/delOrder/${data.id}`, {})

//  拼团活动
export const getTicket = data => request.get('/collect/ticket', data)
//  拼团详情
export const getTicketDetail = data => request.get('/collect/ticketDetail', data)
//  正在拼团
export const queryTeam = data => request.get('/collect/team', data)
//  正在参与拼团详情
export const getTeamDetail = data => request.get('/collect/teamDetail', data)
//  拼团订单详情
export const getTeamGoods = data => request.get('/collect/teamGoodsDetail', data)
//  个人资料
export const getUser = data => request.get('/user/user', data)
// 计算金额
export const calculation = data => request.get('/collect/calculationAll', data)
// 支付
export const collectPay = data => request.get('/collect/pay', data)
// 商城支付
export const shoppingPay = data => request.post('/pay/pay', data)
// 淘潮和门票支付都支持
export const shoppingPayAll = data => request.post('/pay/payAll', data)
// 购物车金额计算
export const shopCalculation = data => request.post('/shop/calculation', data)
// 领取优惠券
export const receiveCoupon = data => request.post('/couponUser/receiveCoupon', data)
// 核销码列表
export const codeList = data => request.get('/write/checkWrite', data)
// 核销详情
export const writeCheck = data => request.get('/write/check', data)
// 确认核销
export const writeGoods = data => request.get('/write/writeGoods', data)
// 领取优惠券列表
export const couponList = data => request.get('/couponUser/couponList', data)
// 评价
export const evalOrder = data => request.post('/order/evalOrder', data)
// 申请售后
export const getAfterDetail = data => request.get('/orderafter/getAfterDetail', data)

// 拼团列表
export const presonTeam = data => request.get('/collect/presonTeam', data)
// 拼团列表
export const teamDetail = data => request.get('/collect/detail', data)

// 联票商品
export const ticketGoods = data => request.get('/home/<USER>', data)

// 删除售后订单
export const deleteOrderAfter = data => request.post('/orderafter/deleteOrderAfter', data)

// 禁用
export const checkUserDisable = data => request.post('/user/checkUserDisable', data)

//
export const checkGoodsShelf = data => request.get('/home/<USER>', data)

// 拼团评价
export const teamEvalTeam = data => request.post('/team/evalTeam', data)

export const showOrderRedDot = data => request.get('/user/showOrderRedDot', data)

// 获取首页广告

export const advertising = data => request.get('/home/<USER>', data)
// 获取消息滚动

export const byArticles = data => request.get('/home/<USER>', data)
// 获取消息滚动

export const homeImgs = data => request.get('/home/<USER>', data)
// 获取商品优惠券

export const couponListByGoods = data => request.get('/couponUser/couponListByGoods', data)
// 赠送好友

export const gifts = data => request.post('/pay/gifts', data)
// 赠送好友
export const privacy = data => request.get('/home/<USER>', data)
// 赠送好友
export const purchase = data => request.get('/home/<USER>', data)
// 修改用户信息
export const updateUser = data => request.post('/user/updateByCustUser', data)
