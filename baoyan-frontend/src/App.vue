<!--
 * @Author: your name
 * @Date: 2020-09-22 14:04:07
 * @LastEditTime: 2020-10-30 13:59:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \baoyan-frontend\src\App.vue
-->
<script>
export default {
    // 解析链接
  onLoad: function(options) {
    // 页面创建时执行
    if (options.scene != undefined) {   //微信直接扫码
      var scan_url = decodeURIComponent(options.scene);
      console.log(scan_url);
      that.setData({
        id: this.getQueryString(scan_url,'id'),
        type: this.getQueryString(scan_url, 'type')
      })
    } else {    //微信内部扫一扫
      that.setData({
        // goods_id: options.goodsid,
        code: options.gcode
      })
    }
  },
getQueryString: function (url, name) {
  // console.log("url = " + url);
  // console.log("name = " + name);
  var reg = new RegExp('(^|&|/?)' + name + '=([^&|/?]*)(&|/?|$)', 'i');
  var r = url.substr(1).match(reg);
  if (r != null) {
    // console.log("r = " + r)
    // console.log("r[2] = " + r[2])
    return r[2];
  }
  return null;
},onLaunch() {
    // this._launch()
    const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function (res) {
                if (res.confirm) {
                  //清楚缓存
                  // wx.clearStorageSync()
                  updateManager.applyUpdate()
                }
              }
            })
          })
          updateManager.onUpdateFailed(function () {
            wx.showModal({
              title: '已经有新版本了哟~',
              content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
            })
          })
        }
      })
  },
  methods: {
    _launch() {
      if (!wx.getStorageSync('token')) {
        wx.reLaunch({
          url: '/pages/authorize/main'
        })
      }
    }
  }
}
</script>

<style lang="less">
// @import url("~mpvue-wxparse2/src/wxParse.css");
page {
  min-height: 100vh;
}
button {
  margin-left: 0;
  margin-right: 0;
  &::after {
    content: none;
  }
}
.container {
  min-height: 100vh;
  box-sizing: border-box;
  background: #FAFAFA;
}
.noneData {
   color:#9B9B9B;
   font-size:14px;
   text-align: center;
   margin-top: 10px;
}
/* this rule will be remove */
* {
  transition: width 2s;
  -moz-transition: width 2s;
  -webkit-transition: width 2s;
  -o-transition: width 2s;
}
</style>
