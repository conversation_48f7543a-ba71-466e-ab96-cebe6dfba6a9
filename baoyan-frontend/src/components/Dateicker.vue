<template>
  <div class="canlendarBgView">
    <div class="canlendarView">
      <div class="canlendarTopView">
        <div class="centerView">{{cur_year ? cur_year : "--"}} 年 {{cur_month ? cur_month : "--"}} 月</div>
        <div class="arrow">
          <div class="leftBgView" @click.stop="queryMonth" data-handle="prev">
            <img class="leftView" mode="aspectFit" src="/static/images/icon-date-left.png" alt />
          </div>
          <div class="leftBgView" @click.stop="queryMonth" data-handle="next">
            <img class="leftView" mode="aspectFit" src="/static/images/icon-date-right.png" alt />
          </div>
        </div>
      </div>
      <div class="weekBgView">
        <div class="weekView" v-for="(item, index) in weeks_ch" :key="index">{{item}}</div>
      </div>
      <div class="dateBgView">
        <div v-if="hasEmptyGrid" class="dateEmptyView" v-for="(item, index) in empytGrids">{{item}}</div>
        <div
          class="dateView"
          v-for="(item, index2) in days"
          @click.stop="dateSelectAction(item, index2)"
        >
          <div class="datesView" v-if="!item.type">{{item.time}}</div>
          <div
            class="datesView"
            v-if="item.type"
            :class="item.state == 1 ? 'success'
            :item.state == 2 ? 'yellow'
            :item.state == 3 ? 'red' : ''"
          >{{item.time}}</div>
        </div>
      </div>
      <div class="tibs">
        <p class="tibs-text" style="margin-right: 15px;">
          <span style="color:#FD5D61;">红色：</span>指当天已被预约满，不可再预约
        </p>
        <p class="tibs-text">
          <span style="color:#EEAC34;">黄色：</span>表示即将满员
        </p>
        <p class="tibs-text">
          <span style="color:#4AA343;">绿色：</span>表示预约名额充足
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import toast from "@/plugins/toast";
export default {
  props: ["info"],
  data() {
    return {
      cur_year: "",
      cur_month: "",
      cur_day: "",
      weeks_ch: [],
      empytGrids: [],
      hasEmptyGrid: false,
      days: [],
      todayIndex: 0,
      nowDate: ""
    };
  },
  computed: {
    checkDate() {
      return this.$store.state.besp.checkDate;
    }
  },
  methods: {
    dateSelectAction(item, index) {
      //   let tibs = this.cur_year+'-'+this.cur_month+'-'+item.time
      let newMonth = this.cur_month;
      let newTime = item.time;
      if (newMonth < 10) {
        newMonth = "0" + newMonth;
      }
      if (newTime < 10) {
        newTime = "0" + newTime;
      }
      let time = this.cur_year + "-" + newMonth + "-" + newTime;
      //   let time = moment(tibs).format('YYYY-MM-DD')
      if (item.state && item.state == 3) {
        toast.text("已被预约满，不可再预约");
        this.$emit("isShow", true, item);
      } else {
        if (time < this.nowDate) {
          toast.text("不可预约");
          this.$emit("isShow", true, item);
        } else {
          item.date = time;
          this.$emit("isShow", false, item);
        }
      }
    },
    setNowDate() {
      const date = new Date();
      this.cur_year = date.getFullYear();
      this.cur_month = date.getMonth() + 1;
      //   this.cur_day = date.getDate();
      this.todayIndex = date.getDate() - 1;

      this.weeks_ch = ["日", "一", "二", "三", "四", "五", "六"];
      this.calculateEmptyGrids(this.cur_year, this.cur_month);
      this.calculateDays(this.cur_year, this.cur_month);
    },
    // getThisMonthDays(year, month) {
    //   return new Date(year, month, 0).getDate();
    // },
    // getFirstDayOfWeek(year, month) {
    //   return new Date(Date.UTC(year, month - 1, 1)).getDay();
    // },
    calculateEmptyGrids(year, month) {
      this.empytGrids = [];
      let firstDayOfWeek = new Date(Date.UTC(year, month - 1, 1)).getDay();
      if (firstDayOfWeek > 0) {
        for (let i = 0; i < firstDayOfWeek; i++) {
          this.empytGrids.push(i);
        }
        this.hasEmptyGrid = true;
      } else {
        this.hasEmptyGrid = false;
        this.empytGrids = [];
      }
    },
    calculateDays(year, month, day) {
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      let thisMonthDays = new Date(year, month, 0).getDate() + 1;

      //   let date = moment(new Date()).format('DD')
      let tday = [];

      //   let teday = moment(this.nowDate).format('YYYY-MM')
      let teday = this.nowDate.slice(0, -3);

      let newTeday = this.nowDate.slice(0, -3);
      //   let endtime = moment(year + '-' + month).format('YYYY-MM')
      let endtime = year + "-" + month;

      // type: 是否可预约

      for (let i = 1; i < thisMonthDays; i++) {
        let _i = i < 10 ? "0" + i : i;
        let obj = { time: i, type: false };
        if (!this.checkDate.length && teday <= endtime) {
          let _today = newTeday + "-" + _i;
          let _endtime = "";

          if (teday == endtime) {
            //判断是否是当前月份
            _endtime = endtime + "-" + this.cur_day;
          }

          obj.type = true;
          obj.state = 1;

          if (_today < _endtime) {
            obj.type = false;
          }
        } else {
          this.checkDate.map(item => {
            if (i == parseInt(moment(item.time).format("DD"))) {
              obj.type = true;
              obj.date = item.time;
              if (item.type) {
                obj.state = item.type;
              }
            }
          });
        }
        tday.push(obj);
      }
      this.days = tday;
    },
    queryMonth(e) {
      let handle = e.currentTarget.dataset.handle;
      if (handle === "prev") {
        this.cur_month = this.cur_month - 1;
        if (this.cur_month < 1) {
          this.cur_year = this.cur_year - 1;
          this.cur_month = 12;
        }
      } else {
        this.cur_month = this.cur_month + 1;
        if (this.cur_month > 12) {
          this.cur_year = this.cur_year + 1;
          this.cur_month = 1;
        }
      }
      this.getInfo();
    },
    getInfo() {
      this.checkDate = [];
      let time = moment(this.cur_year + "-" + this.cur_month).format(
        "YYYY-MM-DD"
      );
      let params = {
        id: this.info.id,
        date: time,
        nowDate: this.nowDate
      };
      this.$store.dispatch("besp/getCheckDate", params).then(res => {
        this.checkDate = res;
        this.calculateDays(this.cur_year, this.cur_month);
        this.calculateEmptyGrids(this.cur_year, this.cur_month);
      });
    }
  },
  onLoad() {
    const newDate = new Date();

    this.cur_day = newDate.getDate();
    this.cur_day = this.cur_day < 10 ? "0" + this.cur_day : this.cur_day;

    this.checkDate = [];
    this.nowDate = moment(new Date()).format("YYYY-MM-DD");
    let date = moment(new Date()).format("YYYY-MM");
    let time = moment(date).format("YYYY-MM-DD");
    this.$store
      .dispatch("besp/getCheckDate", {
        id: this.info.id,
        date: time,
        nowDate: this.nowDate
      })
      .then(res => {
        this.checkDate = res;
        this.setNowDate();
      });
  }
};
</script>

<style lang="less" scope>
.canlendarBgView {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  .canlendarView {
    color: #171717;
    display: flex;
    flex-direction: column;
    padding: 0 0 10px;
    box-sizing: border-box;
    .canlendarTopView {
      height: 58px;
      font-size: 16px;
      display: flex;
      padding: 0 16px 0 27px;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid #d6d6d6;
      .arrow {
        display: flex;
        align-items: center;
        .leftBgView {
          width: 38px;
          height: 34px;
          margin-left: 33px;
          display: flex;
          align-items: center;
          justify-content: center;
          &:last-child {
            margin: 0;
          }
          .leftView {
            width: 8px;
            height: 14px;
          }
        }
      }
    }
    .weekBgView {
      padding: 20px 0 10px;
      box-sizing: border-box;
      font-size: 14px;
      color: #9b9b9b;
      line-height: 20px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .weekView {
        flex-grow: 1;
        text-align: center;
      }
    }
    .dateBgView {
      height: 230px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      .dateEmptyView {
        width: 107.1428571429rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .dateView {
        width: 107.1428571429rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        .datesView {
          width: 33px;
          height: 33px;
          font-size: 14px;
          font-weight: 400;
          color: #171717;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .red {
          border-radius: 50%;
          color: #fff;
          background: rgba(253, 93, 97, 1);
          box-shadow: 0px 3px 4px 0px rgba(253, 93, 97, 0.3);
        }
        .success {
          border-radius: 50%;
          color: #fff;
          background: rgba(74, 163, 67, 1);
          box-shadow: 0px 3px 4px 0px rgba(208, 217, 197, 1);
        }
        .yellow {
          border-radius: 50%;
          color: #fff;
          background: rgba(253, 190, 77, 1);
          box-shadow: 0px 3px 4px 0px rgba(255, 224, 173, 1);
        }
      }
    }
    .tibs {
      width: 355px;
      color: #fafafa;
      padding: 9px 25px;
      box-sizing: border-box;
      font-size: 11px;
      color: #7a7a7a;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      line-height: 14px;
      background: #fafafa;
      margin: 10px auto 0;
    }
  }
}
</style>