<template>
    <div class="container bgw">
        <div class="main">
            <p class="inventory">购物清单</p>
            <div class="content" v-for="(item,index) in dataList" :key="index">
                <img :src="item.goodsImg" alt="">
                <div class="right">
                <h5>{{item.goodsName||goods.name}}</h5>
                <div class="num">
                    <p>¥{{item.sellPrice||goods.price}}</p>
                    <p class="count">x {{item.goodsCount}}</p>
                </div>
                </div>
            </div>
            <div class="deduction first" v-if="type&&fraction&&!discountAmount&&num>1">
                <p class="text">可使用{{fraction}}积分抵扣 ¥{{count}}</p>
                <img @click="getSelect" class="select" :src="'/static/images/'+(isSelect ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            </div>
            <div class="deduction" v-if="coupon.length&&coupon&&!isSelect" @click="getCoupon">
                <p class="text">优惠券</p>
                <div class="right">
                    <p class="tibs" v-if="discountAmount">-￥{{discountAmount}}</p>
                    <p class="tibs" v-else>{{coupon.length}}个优惠券可用</p>
                    <img src="/static/images/icon-gray-arrow.png" alt="" class="arrow">
                </div>
            </div>
            <p class="goods">共{{goodsCount}}件商品，合计：￥{{goodsAmount}}</p>
        </div>
        <!-- <div class="section">
            <p>买家留言</p>
            <input type="text" v-model="text" placeholder="输入留言">
        </div> -->
        <div class="section">
            <p>支付方式</p>
            <p>微信支付</p>
        </div>
        <div class="agree">
            <p class="text"><checkbox class='checkbox' value="cb" color='#EE8200' :checked="checked" @click="checked=!checked"/><span @click='toread'>我已阅读并同意《商品购买协议》</span></p>
            <p><img class="arrow" src="/static/images/icon-gray-arrow.png" alt /></p>
        </div>
        <form @submit="orderPay" report-submit='true'>
            <cover-view class="footer">
                <cover-view class="total">总计：<cover-view class="span">¥{{num}}　</cover-view></cover-view>
                <cover-image class="btnBg" src="/static/images/btnBg.png"></cover-image>
                <button form-type="submit">提交订单</button>
            </cover-view>
        </form>
    </div>
</template>

<script>
console.log('购物车进入支付页====================')
import { payDetail,calculation,getTeamGoods,shoppingPay,shoppingPayAll,payAllDetail } from '@/api'
export default {
    data () {
        return {
            checked:false,
            id: '',
            types: '',
            goodsType: '',
            goodsCount: '',
            fraction: '',

            dataList: [],
            text: '',
            isSelect: false,
            num: 0,
            goods: {},
            count: 0,
            type: false,
            false: 0,
            couponType: false,
            coupon: [],
            discount: 0,
            discountAmount: '',
            // goodsCount: 1,
            goodsAmount: 0,
            param: {
                orderAmount: '',
                couponId: '',
                scoreAmount: 0
            }
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
      toread(){
          wx.navigateTo({ url: `/pages/main/agreepay/main` })
      },
      orderPay(e) {
            if(!this.checked){
                      wx.showToast({
                        title: "请先阅读并同意商品购买协议",
                        icon: "none",
                        duration: 1000
                      });
                      return
            }
            wx.showLoading({
                mask: true
            })
            let formId = e.target.formId
            let param = {
                remark: this.text,
                type: this.types,
                goodsType: this.goodsType*1,
                id: this.id,
                goodsCount: this.goodsCount*1,
                goods: this.dataList,
                orderAmount: this.goodsAmount,
                payAmount: this.num,
                couponId: this.param.couponId,
                couponAmount: this.discountAmount,
                scoreAmount: this.param.scoreAmount,
                fromId: formId,
                coupon: this.fraction,
            }

            let allOrder = {
              remark: this.text,
              // type: this.types,
              // goodsType: this.goodsType*1,
              // id: this.id,
              goodsCount: this.goodsCount*1,
              orderAmount: this.goodsAmount,
              // payAmount: this.num,
              // couponId: this.param.couponId,
              // couponAmount: this.discountAmount,
              // scoreAmount: this.param.scoreAmount,
              fromId: formId,
              // coupon: this.fraction,
              deliveryMode:1,
              address:"",
              mobilePhone:"",
              consignee:"",
              // goods : {
              //   id : this.id,
              //   goods: this.dataList,
              //   type : 2,
              //   goodsCount: this.goodsCount*1,
              // },
              "tcGoods":[
                  {
                    "id":5
                  },
                  {
                    "id":6
                  }
              ],
              "justAloneTc":false,

            }
            shoppingPayAll(allOrder).then(res => {
                if(!res.data.code) {
                    let orderId = res.data.orderId;
                    if (res.data.type == 0) {
                        wx.redirectTo({
                            url: "/pages/ticket/orders/success/main"
                        })
                        return
                    }
                    wx.hideLoading()
                    wx.requestPayment({
                        timeStamp: res.data.pay.timeStamp,
                        nonceStr: res.data.pay.nonceStr,
                        package: res.data.pay.pkg,
                        signType: res.data.pay.signType,
                        paySign: res.data.pay.paySign,
                        success() {
                            wx.redirectTo({
                                url: "/pages/ticket/orders/success/main"
                            });
                        },
                        fail() {
                            wx.showToast({ title: '支付失败', icon:'none', duration: 1000})
                            setTimeout(() => {
                                wx.redirectTo({
                                    url: `/pages/main/Order/orderDetails/main?id=${res.data.orderId}`
                                })
                            },1000)
                        }
                    })
                } else {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
                }
            })
        },
        // 选择优惠券
        getCoupon () {
            wx.setStorageSync("calculatePriceObj",this.param)
            wx.navigateTo({
                url: `/pages/ticket/orders/coupon/main?type=2&id=${this.id}`
            })
        },
        // 积分抵扣
        getSelect () {
            this.isSelect = !this.isSelect
            if(this.isSelect) {
                this.param.scoreAmount = this.count;
                //如果订单金额和积分一样多，提示一下
                if(this.param.scoreAmount===this.param.orderAmount){
                    wx.showToast({
                      title: '折扣后最低0.01元',
                      icon: 'none',
                      duration: 2000
                    });
                }
            } else {
                this.param.scoreAmount = 0;
            }
            //商品数量
            this.param.goodsCount = this.goodsCount;
            this.param.orderAmount = this.goodsAmount
            this.calculatePrice()
        },
        calculatePrice() {
            calculation(this.param).then(res => {
                if(res.data.code) {
                    wx.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 1000
                    })
                    this.param.couponId = ''
                } else {
                    this.num = res.data.orderAmount
                    this.discountAmount = res.data.discountAmount
                }
            })
        }
    },
    onLoad() {
        if (!this.$mp.query.isBack) {
            this.param.scoreAmount = 0
            this.isSelect = false
        }
        this.id = this.$root.$mp.query.id
        this.types = this.$root.$mp.query.type
        this.goodsType = this.$root.$mp.query.goodsType
        this.param.couponId = this.$root.$mp.query.couponId || ''
        this.text = ''
    },
    onShow() {
        let that = this
        this.goodsCount = 0
        let param = {
            type: this.$root.$mp.query.type*1,
            goodsType: this.$root.$mp.query.goodsType,
            id: this.$root.$mp.query.id
        }

        let payAllOrder = {
          // goods : param,
          "tcGoods":[
              {
                "id":5
              },
              {
                "id":6
              }
          ],
          "justAloneTc" : false
        };

        payAllDetail(payAllOrder).then(res => {
        // payDetail(param).then(res => {
            if(res.data) {
              if(res.data.code==418){
                wx.showToast({
                  title: res.data.msg,
                  icon: 'none',
                  duration: 1000
                })
                setTimeout(() => {
                  wx.navigateBack()
                }, 1000);
                return
              }
                this.dataList = res.data.goods || []
                this.coupon = res.data.coupon
                this.type = res.data.type  //是否可使用积分
                this.couponType = res.data.couponType //是否可使用优惠券
                this.fraction = res.data.fraction
                this.count = res.data.count
                this.goodsAmount = res.data.goodsAmount
                let coupon = JSON.stringify(res.data.coupon)
                this.param.orderAmount = res.data.goodsAmount
                res.data.goods.filter(item => {
                    this.goodsCount += item.goodsCount
                })
                wx.setStorageSync('wx_coupon',coupon)
                that.calculatePrice()
            }
        })
    }
}
</script>

<style lang="less">
.bgw{
    background: white!important;
}
.container {
  padding-bottom: 50px;
  .main {
    width: 100%;
    background: #fff;
    padding: 0 12px 12px;
    box-sizing: border-box;
    // margin-bottom: 10px;
    border-bottom: 10px solid #F6F6F6;
    .inventory {
    //   padding: 0 11px;
      box-sizing: border-box;
      font-size: 13px;
      color: #090203;
      line-height: 18px;
    }
    .content {
      padding: 11px 0 10px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      img {
        width: 80px;
        height: 80px;
        border-radius: 4px;
        margin-right: 10px;
      }
      .right {
        width: 255px;
        h5 {
          width: 100%;
          font-size: 16px;
          color: #171717;
          line-height: 22px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        //   margin-bottom: 28px;
    border-bottom: 28px solid #fff;
        }
        .num {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #FF3333;
          line-height: 20px;
          p {
            font-size: 16px;
            color: #FF3434;
            line-height: 22px;
          }
          .count {
            font-size: 12px;
            color: #FF3434;
          }
        }
      }
    }
    .deduction {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid #E5E5E5;
      .text {
        font-size: 14px;
        color: #4A4A4A;
        line-height: 20px;
      }
      .select {
        width: 16px;
        height: 16px;
      }
      .right {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9B9B9B;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .first {
      border-top: 1rpx solid #E5E5E5;
    }
    .goods {
      font-size: 14px;
      color: #4A4A4A;
      line-height: 20px;
      text-align: right;
      margin-top: 5px;
    }
  }
  .section {
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    font-size: 14px;
    color: #4A4A4A;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 10px;
    border-bottom: 10px solid #F6F6F6;
    input {
      width: 270px;
      height: 100%;
      line-height: 50%;
      font-size: 14px;
    }
  }
  .agree{
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    font-size: 14px;
    color: #686868;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text{
      .checkbox .wx-checkbox-input {
        width: 20rpx; /* 背景的宽 */
        height: 20rpx; /* 背景的高 */
        border-color: #EE8200;
        border-radius: 50%;
      }
      .checkbox  .wx-checkbox-input.wx-checkbox-input-checked::before {
        font-size: 8px;
        background: #EE8200;
        transform: translate(-50%, -50%) scale(1);
        -webkit-transform: translate(-50%, -50%) scale(1);
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        color: transparent;
      }
      span{
        font-size: 9px;
        color: #A6A6A6;
        line-height: 8px;
      }
    }
    .arrow {
          width: 7px;
          height: 13px;
          margin-right: 13px;
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 49px;
    line-height: 49px;
    padding: 0 0 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    z-index: 999;
    box-shadow: -1px -1px 10px 0px rgba(152, 152, 152, 0.33);
    .total {
      font-size: 14px;
      color: #7F7F7F;
      min-width: 150px;
      .span {
        display: inline;
        color: #FF3434;
        font-weight: 500;
      }
    }
    .btnBg {
        width: 110px;
        height: 49px;
        position: absolute;
        right: 0;
        bottom: 0;
    }
    button {
      width: 110px;
      height: 49px;
      line-height: 49px;
      padding: 0;
      border-radius: 0;
      text-align: center;
      font-size: 14px;
      color: #fff;
      background: transparent;
    }
  }
}
</style>
