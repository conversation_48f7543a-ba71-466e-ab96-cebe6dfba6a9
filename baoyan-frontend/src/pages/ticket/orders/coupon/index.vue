<template>
  <div class="container">
    <scroll-view class="scrollView">
      <div class="main">
        <div class="volume-box" v-for="(item, index) in coupon" :key="index" @click.stop="getUse(item)">
          <img v-if="item.couponType == 1 && item.full != 0 && item.full !=  -1" src="/static/images/icon-Coupon.png" alt="">
          <img v-if="item.couponType == 1 && item.full == 0" src="/static/images/icon-Deduction-Volume.png" alt="">
          <img v-if="item.couponType == 2" src="/static/images/icon-Discount-Volume.png" alt="">
          <div class="volume">
            <div class="many">
              <h5>¥{{item.couponType == 2 ? item.discount+'折' : item.discount}}</h5>
              <!-- 1优惠券，2折扣券 -->
              <p class="text" v-if="item.couponType == 1">{{!item.full ? '抵扣券' : '优惠券'}}</p>
              <p class="text" v-if="item.couponType == 2">折扣券</p>
            </div>
            <div class="content">
              <div>
                  <h6>{{item.name}}</h6>
                  <p v-if="item.full ">满{{item.full}}可用</p>
              </div>
              <p class="time">{{item.startDate}}～{{item.endDate}}</p>
            </div>
          </div>
        </div>
      </div>
    </scroll-view>
    <div class="btn">
      <button @click="goback">不使用优惠券</button>
    </div>
  </div>
</template>

<script>
import { calculation } from '@/api'
export default {
    data () {
        return {
            id: '',
            types: '',
            goodsType: '',
            goodsCount: '',
            coupon: []
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        // 选择优惠券
        getUse(item) {
            console.log(item)
            let param = wx.getStorageSync('calculatePriceObj')
            param.couponId = item.id
            calculation(param).then(res => {
                if (res.data.code) {
                    wx.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 1000
                    })
                } else {
                    let pages = getCurrentPages()
                    let prevPage = pages[pages.length - 2]
                    prevPage.onLoad({
                        id: this.id,
                        type: this.types,
                        goodsType: this.goodsType,
                        goodsCount: this.goodsCount,
                        couponId: item.id,
                        isBack: true
                    })
                    wx.navigateBack({delta: 1})
                }
            })
        },
        // 不使用优惠券
        goback () {
            let pages = getCurrentPages()
            let prevPage = pages[pages.length - 2]
            prevPage.onLoad({
                id: this.id,
                type: this.types,
                goodsType: this.goodsType,
                goodsCount: this.goodsCount,
                couponId: "",
                isBack: true
            })
            wx.navigateBack({delta: 1})
        }
    },
    onLoad() {
        this.id = this.$root.$mp.query.id
        this.types = this.$root.$mp.query.type
        this.goodsType = this.$root.$mp.query.goodsType
        this.goodsCount = this.$root.$mp.query.goodsCount
    },
    onShow () {
        this.coupon = JSON.parse(wx.getStorageSync('wx_coupon'))
    }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 10px 15px 0;
    box-sizing: border-box;
    margin-bottom: 70px;
    .volume-box {
      width: 345px;
      height: 93px;
      margin-bottom: 10px;
      position: relative;
      img {
        width: 345px;
        height: 93px;
      }
      .volume {
        position: absolute;
        width: 345px;
        height: 93px;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        .many {
          box-sizing: border-box;
          margin-right: 15px;
          width: 95px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-direction: column;
          padding: 24px 0;
          h5 {
            font-size: 20px;
            line-height: 28px;
            color: #fff;
          }
          .text {
            font-size: 12px;
            color: #fff;
          }
        }
        .content {
          height: 100%;
          font-size: 12px;
          color: #746C6C;
          margin-right: 26px;
          padding: 13px 0;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          h6 {
            font-size: 14px;
            line-height: 20px;
            color: #290B0B;
            margin-bottom: 8px;
          }
          .time {
            margin-top: 17px;
            font-size: 10px;
            line-height: 14px;
            color: #ADA8A8;
          }
        }
      }
    }
  }
  .btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 15px 12px;
    box-sizing: border-box;
    background: #fff;
    button {
      width: 100%;
      height: 40px;
      line-height: 40px;
      padding: 0;
      text-align: center;
      border-radius: 22px;
      background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      font-size: 16px;
      color: #fff;
    }
  }
}
</style>
