<template>
    <div class="container" :class="{'overflow':guanggaoshow===true}">
      <cover-view class = "guideBackground" v-if="isTiptrue" @click = "closeGuide">
          <cover-view class="arrow"></cover-view>
        <cover-view class = "guideInfo">
          <cover-view class = "guideText"> 点击</cover-view>
          <cover-view class = "curcleL"></cover-view>
          <cover-view class = "curcleM"></cover-view>
          <cover-view class = "curcleL"></cover-view>
          <cover-view class = "guideText">,添加到我的小程序 </cover-view>
        </cover-view>
      </cover-view>
      <div class="header">
        <div class="bannerbg">
          <!-- <div class="borderbg"></div> -->
          <!-- <img src="/static/images/bannerbg.png" class="borderbg" alt=""> -->
          <div class="swiperbox">
            <swiper
              class="swiper"
              @change="swiperChange"
              indicator-color="rgba(255,255,255,0.5)"
              indicator-active-color="#fff"
              :autoplay="true"
              :interval="3000"
              :duration="1000"
              :circular="true"
              :current="curr"
            >
              <a href v-for="(item, index) in indexinfo.banner" @click="tabPage(item)" :key="index">
                <swiper-item>
                  <image :src="item.bannerImgUrl" class="slide-image" mode="aspectFill" />
                </swiper-item>
              </a>
            </swiper>
          </div>
        </div>
<!-- 指示点-->
            <view class="dots">
                <block v-for="(item, index) in indexinfo.banner" :key="index">
                    <view class="dot" :class="{'active' : index == curr}"></view>
                </block>
            </view>
        </div>
        <div class="section">
          <div class="newbox">
            <ul class="list">
                <li v-for="(item, index) in indexinfo.navigation" :key="index" @click="tabPage(item)">
                    <img :src="item.url" alt />
                    <p>{{item.title}}</p>
                </li>
            </ul>
            <div class="gonggao" v-if="gonggao.length">
              <div class="left_title">
                <img src="/static/images/gonggao_img.png" alt="" class="gonggao_img">
                <div class="text">宝燕头条</div>
              </div>
              <swiper
                class="gonggao_swiper"
                indicator-color="rgba(255,255,255,0.5)"
                indicator-active-color="#fff"
                :autoplay="true"
                :interval="gonggao[0].time*1000"
                :duration="500"
                :circular="true"
            >
            <swiper-item v-for="(item,index) in gonggao" :key="index">
              <div class="message" @click="toArticle(item)">
                {{item.articleName}}
              </div>
            </swiper-item>
            </swiper>
            </div>
          </div>
        </div>
        <!-- 多图 -->
        <div class="moreimg" v-if="homeImgs.length">
          <div :class="item.layout===1?'maximg':'minimg'" v-for="(item,index) in homeImgs" :key="index" @click="tabPage(item)">
            <img :src="item.imgUrl" alt="">
          </div>
        </div>
        <div class="coupon" v-if="coupon">
            <div class="title-box">
                <div class="special">
                <img src="/static/images/icon-spot.png" alt />
                <h6>优惠券专区</h6>
                <img src="/static/images/icon-spot.png" alt />
                </div>
                <div class="more" @click="getCoupon">
                <p class="text">查看更多</p>
                <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
                </div>
            </div>
            <div class="box">
                <img class="back" src="/static/images/icon-coupon-background.png" alt />
                <div class="content">
                <div class="left">
                    <div class="num-box">
                    <p class="num" v-if="coupon.couponType==2">
                        {{coupon.discount}}<span>折</span>
                    </p>
                    <p class="num" v-else>
                        <span>¥</span>{{coupon.discount}}
                    </p>
                    <div class="volume-box">
                        <img class="coupon-back" src="/static/images/icon-coupon-back.png" alt />
                        <p class="volume">{{coupon.couponType==1 ? (!coupon.full?'抵扣券':'优惠券') :'折扣券'}}</p>
                        <p ></p>
                    </div>
                    </div>
                    <p class="goods" v-if="coupon.type==0">全部商品可用</p>
                    <p class="goods" v-else>限{{coupon.limitation}}类商品可用</p>
                </div>
                <div class="right" @click="receive(coupon.id)">
                    <img src="/static/images/icon-receive.png" alt />
                </div>
                </div>
            </div>
        </div>
        <div class="tab-box">
            <div class="item" v-for="(item, index) in activity" :key="index" @click.stop="tabPage(item)">
                <img :src="item.targetImgUrl" mode="aspectFill" alt />
                <p class="tibs">{{item.activityName}}</p>
            </div>
        </div>
        <!-- 优惠券 -->
        <div class="mask" v-if="isShow">
        <div class="main">
            <h6>恭喜您获得优惠券</h6>
            <div class="img-box">
            <img src="/static/images/icon-volume-back.png" alt="" class="back">
            <div class="content">
                <div class="money">
                <p class="num"><span>￥</span>{{birthday.couponType == 2 ? birthday.discount+'折' : birthday.discount}}</p>
                <p class="coupon" v-if="birthday.couponType == 1">{{birthday.full == 0 ? '抵扣券' : '优惠券'}}</p>
                <p class="coupon" v-if="birthday.couponType == 2">折扣券</p>
                </div>
                <div class="right">
                <p v-if="birthday.full">满{{birthday.full}}元可用</p>
                <p v-if="birthday.couponType != 2 && birthday.goodsName">限{{birthday.goodsName}}类商品可用</p>
                <p v-if="!birthday.goodsName">全部商品可用</p>
                <p>{{birthday.startDate}}~{{birthday.endDate}}</p>
                </div>
            </div>
            </div>
            <div class="btn" @click="isShow = false">确定</div>
        </div>
        </div>
        <!-- 广告 -->
        <div class="guanggao" v-show="guanggaoshow">
          <icon class="cancel" type="cancel" size="40" color='rgb(255,255,255)' @click="guanggaoshow=false"/>
          <img class="image" mode="aspectFit" :src="guanggao.imgUrl" @click="tabPage(guanggao)" alt="">
        </div>
    </div>
</template>

<script>
import toast from '@/plugins/toast'
import { receiveCoupon, checkGoodsShelf,advertising,byArticles,homeImgs } from '@/api'
export default {
    data() {
        return {
            isTiptrue: true,
            curr: 0,
            isShow: false,
            birthday: {}, // 优惠券弹窗
            coupon: null, // 优惠券
            page: 1,
            pageSize: 20,
            noService: false,
            gonggao:'',// 公告
            homeImgs:'',//多图
            guanggao:'',//广告
            guanggaoshow:false,
        }
    },
    computed: {
        bannerLen() {
            return this.$store.state.home.indexinfo.length || 0;
        },
        indexinfo() {
            return this.$store.state.home.indexinfo;
        },
        activity () {
            return this.$store.state.home.activity
        }
    },
    created () {
        // this.loadInfo()
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onPullDownRefresh() {
        console.log('==')
    },
    onPullDownRefresh() {
        wx.showNavigationBarLoading() //在标题栏中显示加载
        this.page = 1
        this.noService = false
        this.$store.commit('home/RECORD_ACTIVITY', [])
        this.$store.dispatch("home/getIndexinfo", {}).then(res => {
            if (res.code) {
                toast.text(res.msg)
            } else {
                this.coupon = this.indexinfo.coupon
                if (this.indexinfo.type) {
                    this.isShow = true
                    this.birthday = this.indexinfo.birthday
                } else {
                    this.isShow = false
                }
                this.getactivity()
            }
        })
    },
    methods: {
      toArticle(item){
                        wx.navigateTo({
                            url: `/pages/home/<USER>/main?id=${item.id}`
                        })
      },
        loadMore () {
            if (this.noService) {
                return
            }
            this.page++
            this.getactivity()
        },
        // 经纬度
        loadInfo () {
            let _this = this
            wx.getLocation({
                type: 'gcj02', // 返回可以用于wx.openLocation的经纬度
                success(res) {
                    let latArr = { lat: res.latitude, lng: res.longitude }
                    wx.setStorageSync('latArr', latArr)
                }
            })
        },
        // banner
        swiperChange(e) {
            this.curr = e.target.current;
        },
        // 查看更多优惠券
        getCoupon () {
            wx.navigateTo({
                url: '/pages/home/<USER>/main'
            })
        },
        // 跳转类型 1 商品分类 2 联票列表 3次卡列表 4拼团列表 5 文章 6 普通商品 7联票商品 8次卡商品 9 拼团商品	10规格商品 11优惠券
        tabPage (item) {
            wx.showLoading({
                title: '',
                mask: true
            })
            let url
            // wx.setStorageSync('imgUrl', item.imgUrl)
            console.log("======>",item)
            wx.setStorageSync('baseBanner', item.baseBanner)
            if(!item.targetImgUrl){
              wx.setStorageSync('imgUrl', item.imgUrl)
            }else{
              wx.setStorageSync('imgUrl', item.targetImgUrl)

            }
            if (item.jumpType == 4) {
                wx.navigateTo({
                    url: '/pages/home/<USER>/main'
                })
                return
            }
            if (item.jumpType == 1 || item.jumpType == 2 || item.jumpType == 3) {
                let title=item.title
                if(item.classifyTitle){
                  title=item.classifyTitle
                }
                wx.navigateTo({
                    url: `/pages/home/<USER>/main?type=${item.jumpType}&target=${item.target}&classId=${item.id}&title=${title}`
                })
                return
            }
            if(item.jumpType == 11){
              this.receive(item.target)
              wx.hideLoading()
              return
            }
            checkGoodsShelf({jumpType: item.jumpType,id: item.target}).then(res => {
                if (res.data.code == 0) {
                        if (item.jumpType == 5) {
                            url = `/pages/home/<USER>/main?id=${item.target}&jumpType=${item.jumpType}`
                        } else if (item.jumpType == 6 || item.jumpType == 7 || item.jumpType == 8) {
                          if(item.jumpType == 8){
                            url = `/pages/home/<USER>/classDetails/main?id=${item.target}&type=3`
                          }else{
                            url = `/pages/home/<USER>/classDetails/main?id=${item.target}&type=${item.jumpType}`
                          }
                        } else if (item.jumpType == 9) {
                            url = `/pages/home/<USER>/clusterGoods/main?id=${item.target}`
                        }
                        wx.navigateTo({
                            url: url
                        })
                } else {
                    toast.text(res.data.msg)
                }
            })
            wx.hideLoading()
        },
        // 领取优惠券
        receive (couponId) {
            let token = wx.getStorageSync('token')
            let registerFlag = wx.getStorageSync('registerFlag')
            if (!token) {
                wx.showModal({
                    content: '您还没有登录',
                    cancelText: '取消',
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            }
            if (!registerFlag) {
                wx.showModal({
                    content: '您还没有注册',
                    cancelText: '取消',
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }
            receiveCoupon({couponId}).then(res => {
                if (res.data.code) {
                    toast.text(res.data.msg)
                } else {
                    toast.text('领取成功')
                }
            })
        },
        // 获取活动信息
        getactivity () {
            let params = {
                pageSize: 500,
                pageIndex: 1
            }
            this.$store.dispatch('home/getActivity', params).then(res => {
                this.noService = res
                setTimeout(() => {
                    wx.hideNavigationBarLoading() //完成停止加载
                    wx.stopPullDownRefresh()
                },1000)
            })
        },


  // Page中添加关闭引导
  closeGuide(e) {
    wx.setStorage({
      key: 'loadOpen',
      data: 'OpenTwo'
    })
    this.isTiptrue=false
  },
    },
    onShow() {
        wx.setStorageSync('appointmentOrderId', '')
        wx.hideLoading()
    },
    onLoad() {
        this.page = 1
        this.noService = false
        this.$store.commit('home/RECORD_ACTIVITY', [])
        this.$store.dispatch("home/getIndexinfo", {}).then(res => {
            if (res.code) {
                toast.text(res.msg)
            } else {
                this.coupon = this.indexinfo.coupon
                if (this.indexinfo.type) {
                    this.isShow = true
                    this.birthday = this.indexinfo.birthday
                } else {
                    this.isShow = false
                }
                this.getactivity()
            }
        })
        byArticles({}).then(res=>{
                if (res.data) {
                  this.gonggao=res.data
                }
        })
        homeImgs({}).then(res=>{
          if(res.data){
            this.homeImgs=res.data
          }
        })
        advertising({}).then(res => {
                if (res.data) {
                  this.guanggaoshow=true
                  this.guanggao=res.data
                }
            })
  // onLoad中添加以下代码
      let firstOpen = wx.getStorageSync("loadOpen")
      if (firstOpen == undefined || firstOpen == '') { // 根据缓存周期决定是否显示新手引导
        this.isTiptrue=true
        setTimeout(() => {
          this.isTiptrue=false
        }, 5000);
      }
      else {
        this.isTiptrue=false
      }
    }
}
</script>

<style scoped lang="less">
.overflow{
  overflow: hidden!important;
  height: 100vh!important;
}
.container {
  padding-bottom: 20px;
  position: relative;
  .header {
    position: relative;
    .bannerbg {
      position: relative;
      .borderbg {
        position: absolute;
        z-index: 0;
        width: 100%;
        height: 180px;
      }
    }
    .swiperbox{
      box-sizing: border-box;
      padding: 5px 15px;
      width: 100%;
      height: 182px;
      margin-bottom: 15px;
    }
    .swiper {
      width: 100%;
      height: 182px;
      border-radius: 10px;
      overflow: hidden;
      ._swiper-item {
        border-radius: 10px;
        overflow: hidden;
      }
      .slide-image {
        width: 100%;
      height: 182px;
      }
    }
    .dots {
      width: 100%;
      height: 3px;
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 15px;
      .dot {
        width: 20px;
        height: 7px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.5);
        margin-right: 6px;
        &:last-child {
          margin: 0;
        }
      }
      .active {
        background: #fff;
      }
    }
  }
  .section {
    width: 100%;
    padding: 3px 15px 16px;
    box-sizing: border-box;
    margin-top: 5px;
    .newbox {
      width: 100%;
      box-shadow: 1px 1px 10px 0px rgba(152, 152, 152, 0.33);
      border-radius: 6px;
      .list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        li {
          width: 20%;
          margin-top: 12px;
          margin-bottom: 20px;
          display: flex;
          text-align: center;
          align-items: center;
          // box-sizing: border-box;
          // margin-left: 40px;
          flex-direction: column;
          &:nth-child(1) {
            margin-left: 0;
          }
          &:nth-child(5) {
            margin-left: 0 !important;
          }
          img {
            width: 41px;
            height: 41px;
            margin-bottom: 10px;
          }
          p {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 13px;
            color: #999999;
            line-height: 18px;
          }
        }
      }
      .gonggao {
        height: 40px;
        position: relative;
        &::after {
          content: " ";
          display: block;
          width: 100%;
          height: 1px;
          background: linear-gradient(
            to right,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0) 10%,
            rgba(239, 235, 235, 0.3) 20%,
            rgba(239, 235, 235, 0.4) 30%,
            rgba(239, 235, 235, 0.5) 40%,
            rgba(239, 235, 235, 0.8) 50%,
            rgba(239, 235, 235, 0.5) 60%,
            rgba(239, 235, 235, 0.4) 70%,
            rgba(239, 235, 235, 0.3) 80%,
            rgba(0, 0, 0, 0) 90%,
            rgba(0, 0, 0, 0) 100%
          );
        }
        .left_title {
          float: left;
          display: flex;
          justify-content: space-around;
          width: 28%;
          align-items: center;
          height: 40px;
          .text {
            font-size: 12px;
            padding-right: 9px;
            position: relative;
            font-weight: bold;
            &::after {
              position: absolute;
              top: 0px;
              right: 0px;
              content: " ";
              width: 1px;
              height: 16px;
              display: block;
              background: #9d9d9d;
            }
          }
          .gonggao_img {
            display: inline-block;
            height: 18px;
            width: 18px;
            margin-left: 10px;
          }
        }
        .gonggao_swiper {
          float: left;
          height: 100%;
          width: 70%;
          .message {
            font-size: 12px;
            line-height: 40px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .moreimg {
    padding: 0 15px;
    .maximg {
      width: 100%;
      height: 123px;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 15px;
    }
    .minimg {
      width: 168px;
      height: 100px;
      border-radius: 10px;
      overflow: hidden;
      float: left;
      margin-bottom: 15px;
      margin-right: 8px;
      &:nth-child(2n + 1) {
        margin-right: 0;
      }
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .coupon {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    .title-box {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      margin-bottom: 8px;
      .special {
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 18px;
          height: 8px;
        }
        h6 {
          font-size: 16px;
          color: #090203;
          line-height: 22px;
          font-weight: 500;
          margin: 0 7px;
        }
      }
      .more {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(0, -50%);
        display: flex;
        align-items: center;
        .text {
          font-size: 12px;
          color: #9b9b9b;
          line-height: 17px;
          margin-right: 5px;
        }
        .arrow {
          width: 8px;
          height: 12px;
        }
      }
    }
    .box {
      width: 345px;
      height: 90px;
      position: relative;
      margin-bottom: 18px;
      .back {
        width: 345px;
        height: 90px;
      }
      .content {
        position: absolute;
        top: 0;
        left: 0;
        width: 345px;
        height: 90px;
        padding: 21px 8px 0 133px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 0;
        .left {
          color: #fff;
          width: 150px;
          .num-box {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            .num {
              font-size: 34px;
              line-height: 22px;
              margin-right: 6px;
              span {
                font-size: 16px;
              }
            }
            .volume-box {
              width: 69px;
              height: 20px;
              position: relative;
              .coupon-back {
                width: 69px;
                height: 20px;
              }
              .volume {
                position: absolute;
                top: 0;
                left: 0;
                width: 69px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                overflow: hidden;
                font-size: 13px;
                color: #de5064;
              }
            }
          }
          .goods {
            width: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 13px;
            color: #fff;
          }
        }
        .right {
          width: 54px;
          height: 54px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .tab-box {
    width: 100vw;
    padding: 0 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    box-sizing: border-box;
    // .itemBox {
    //     width: 100%;
    //     display: flex;
    //     padding: 0 20px;
    // }
    .item {
      width: 166px;
      //   margin-right: 13px;
      margin-bottom: 20px;
      display: inline-block;
      font-weight: 500;
      img {
        width: 166px;
        height: 102px;
        margin-bottom: 5px;
        border-radius: 6px;
      }
      .tibs {
        width: 160px;
        height: auto;
        font-size: 14px;
        color: #4a4a4a;
        overflow: hidden;
        text-overflow: ellipsis;
        text-overflow: -o-ellipsis-lastline;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
      }
    }
  }
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10;
    .main {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 295px;
      border-radius: 6px;
      background: #fff;
      z-index: 11;
      h6 {
        width: 100%;
        text-align: center;
        padding: 22px 0;
        box-sizing: border-box;
        font-size: 18px;
        line-height: 25px;
        color: #4a4a4a;
      }
      .img-box {
        width: 271px;
        height: 95px;
        margin: 0 auto 27px;
        position: relative;
        .back {
          width: 271px;
          height: 95px;
        }
        .content {
          position: absolute;
          top: 0;
          left: 0;
          width: 271px;
          height: 95px;
          display: flex;
          align-items: center;
          padding: 0 0 0 19px;
          box-sizing: border-box;
          color: #fff;
          .money {
            margin-right: 34px;
            .num {
              font-size: 36px;
              line-height: 36px;
              margin-bottom: 6px;
              span {
                font-size: 13px;
                line-height: 18px;
                vertical-align: top;
              }
            }
            .coupon {
              font-size: 13px;
              line-height: 18px;
              text-align: right;
            }
          }
          .right {
            margin-bottom: 4px;
            p {
              font-size: 12px;
              line-height: 20px;
              width: 147px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
      .btn {
        width: 100%;
        padding: 0;
        text-align: center;
        background: #fff;
        color: #ed813a;
        font-size: 15px;
        height: 49px;
        line-height: 49px;
        border-top: 1rpx solid #e5e5e5;
      }
    }
  }
  .guanggao {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    .cancel {
      position: absolute;
      top: 22%;
      right: 5%;
    }
    .image {
      width: 85%;
      height: 300px;
      position: absolute;
      top: 30%;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .guideBackground {
  width: 168px;
  height: 30px;
  line-height: 16px;
  z-index: 99999;
  position: fixed;
  top: 3px;
  right: 15px;
  // display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .arrow{
    position: relative;
    left: 100px;
    z-index: 9;
    right: 55px;
    margin-left: 100px;
    width: 0;
    height: 0;
    padding: 0;
    margin: 0;
    border: 5px solid;
    border-color: transparent transparent rgba(0, 0, 0, 0.6) transparent;
  }
}

.guideInfo {
  width: 100%;
  height: 20px;
  border-radius:35rpx;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.guideText {
  margin-top: -2px;
  font-size: 12px;
  color: white;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.curcleL {
  line-height: 15px;
  height: 6rpx;
  width: 6rpx;
  border-radius: 3rpx;
  background-color:white;
  margin:0 6rpx;
}
.curcleM {
  line-height: 15px;
  height: 10rpx;
  width: 10rpx;
  // margin-left: 6rpx;
  // margin-right: 6rpx;
  border-radius: 5rpx;
  background-color:white;
}
}
</style>
