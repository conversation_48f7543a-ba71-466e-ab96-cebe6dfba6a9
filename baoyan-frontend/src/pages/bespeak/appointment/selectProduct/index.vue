<template>
  <div class="container">
    <div class="header">
      <div class="query">
        <input type="text" v-model="text" placeholder="输入商品名称">
        <img mode="aspectFit" src="/static/images/icon-input-query.png" alt="" class="query-icon">
      </div>
      <p class="queryinfo" @click="searchHandle">搜索</p>
    </div>
    <scroll-view class="scrollView" :style="{height: scrollViewHeight}" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
      <div class="main" v-for="(item, index) in product" :key="index" @click.stop="queryProduct(item)">
        <img mode="aspectFill" :src="item.goodsImg" alt="">
        <p class="title">{{item.goodsName}}</p>
      </div>
    </scroll-view>
  </div>
</template>

<script>
import { getshowProduct } from '@/api'
export default {
    data () {
        return {
            text: '',
            page: 1,
            pageSize: 20,
            noService: false,
            product: []
        }
    },
    computed: {
        scrollViewHeight () {
            if (this.$store.state.windowHeight) {
                return `${this.$store.state.windowHeight - 55}px`
            }
            return '100%'
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        searchHandle() {
            this.product = []
            this.getinfo()
        },
        loadMore () {
            if (this.noService) {
                return
            }
            this.page ++
            this.getinfo()
        },
        // info 
        getinfo () {
            let params = {
                id: this.$root.$mp.query.id,
                productName: this.text,
                pageIndex: this.page,
                pageSize: this.pageSize
            }
            getshowProduct(params).then(res => {
                if(res.data.code) {
                    wx.showToast({
                        title: res.data.msg, //提示的内容,
                        icon: 'none', //图标,
                        duration: 1500
                    })
                } else {
                    this.product = this.product.concat(res.data.list)
                    this.product.forEach(item => {
                        if (item.goodsName.length > 30) {
                            item.goodsName = item.goodsName.substr(0,30) + '...'
                        }
                    })
                    this.noService = res.data.isLastPage
                }
            })
        },
        // 选择产品
        queryProduct (item) {
            wx.setStorageSync('product', {name: item.goodsName, id: item.id})
            wx.navigateBack({
                delta: 1
            })
        }
    },
    onLoad () {
        this.product = []
        this.$store.commit('getWindowHeight')
        this.noService = false
        this.page = 1
        this.text = ''
        this.$store.commit('besp/RECORD_PRODUCT', [])
        this.getinfo()
    }
}
</script>

<style lang="less">
.container {
  .header {
    width: 100%;
    height: 54px;
    background: #fff;
    border-bottom: 1rpx solid #E6E3DC;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 0 15px;
    box-sizing: border-box;
    .query {
      width: 302px;
      height: 30px;
      line-height: 30px;
      position: relative;
      input {
        width: 100%;
        height: 30px;
        line-height: 30px;
        background: #F6F6F6;
        border-radius: 16px;
        padding: 0 0 0 29px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .query-icon {
        position: absolute;
        top: 50%;
        left: 9px;
        transform: translate(0, -50%);
        width: 16px;
        height: 16px;
      }
    }
    .queryinfo {
      font-size: 14px;
      color: #171717;
      line-height: 20px;
      padding: 0 15px;
      box-sizing: border-box;
    }
  }
  .main {
    width: 100%;
    padding: 10px 15px;
    box-sizing: border-box;
    background: #fff;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    img {
      width: 60px;
      height: 60px;
    }
    .title {
      width: 274px;
      font-size: 16px;
      color: #171717;
      line-height: 22px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }
}
</style>
