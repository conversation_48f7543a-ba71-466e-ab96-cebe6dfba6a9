<template>
  <div class="appointment">
    <div class="Store">
      <img mode="aspectFill" :src="stores.storeImg" alt class="Store_img" />
      <div class="Store_right">
        <h3>{{stores.name}}</h3>
        <div class="Store_msg">
          <img mode="aspectFit" src="/static/images/icon-phone.png" alt class="phone_img" />
          <span>{{stores.mobile}}</span>
        </div>
        <div class="Store_msg">
          <img mode="aspectFit" src="/static/images/icon-address.png" alt class="phone_img" />
          <span>{{stores.address}}</span>
        </div>
      </div>
    </div>
    <ul class="make">
      <li @click="getProduct">
        <p>预约产品</p>
        <div class="Gender">
          <div class="poi_picker goodsName">{{genderitem}}</div>
          <img mode="aspectFit" class="right" src="/static/images/icon-gray-arrow.png" alt />
        </div>
      </li>
      <!-- 日历 -->
      <li @click="isShow = true">
        <p>预约日期</p>
        <div class="Gender">
          <div class="poi_picker">{{date}}</div>
          <img mode="aspectFit" class="right" src="/static/images/icon-gray-arrow.png" alt />
        </div>
      </li>
      <li>
        <p>购票渠道</p>
        <div class="Gender">
          <picker
            @change="bindticketchange($event)"
            mode="selector"
            :value="index"
            :range="ticketList"
            class="poi_picker"
          >
            <div class="poi_picker">{{ticketitem}}</div>
          </picker>
          <img mode="aspectFit" class="right" src="/static/images/icon-gray-arrow.png" alt />
        </div>
      </li>
      <li>
        <p>预约数量</p>
        <div class="Gender">
          <img
            mode="aspectFit"
            @click="getreduce"
            class="symbol"
            src="/static/images/icon-reduce.png"
            alt
          />
          <div class="num">{{num}}</div>
          <img
            mode="aspectFit"
            @click="getplus"
            src="/static/images/icon-plus.png"
            alt
            class="symbol"
          />
        </div>
      </li>
    </ul>
    <div class="title">
      <div></div>
      <h3>家长信息</h3>
    </div>
    <ul class="make">
      <li>
        <p class="Name">家长姓名</p>
        <div class="Gender">
          <input type="text" v-model="parentName" />
        </div>
      </li>
      <li>
        <p class="Name">联系方式</p>
        <div class="Gender">
          <input type="text" v-model="parentPhone" />
        </div>
      </li>
    </ul>
    <div class="title">
      <div></div>
      <h3>小朋友信息</h3>
    </div>
    <ul class="make">
      <li>
        <p class="Name">姓名</p>
        <div class="Gender">
          <input type="text" v-model="childName" />
        </div>
      </li>
      <li>
        <p>性别</p>
        <div class="sex">
          <div class="box" @click="sex = 0">
            <img :src="'/static/images/'+( sex == 0 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>男</span>
          </div>
          <div class="box" @click="sex = 1">
            <img :src="'/static/images/'+( sex == 1 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>女</span>
          </div>
        </div>
      </li>
      <li>
        <p class="Name">生日</p>
        <div class="Gender">
          <picker mode="date" :value="date" @change="bindbirthdayChange($event)" class="poi_picker">
            <div class="poi_picker">{{birthday}}</div>
          </picker>
          <img mode="aspectFit" class="right" src="/static/images/icon-gray-arrow.png" alt />
        </div>
      </li>
    </ul>
    <div class="btn">
      <div @click="addBesp">预约</div>
    </div>
    <div class="mask-box" catchtouchmove="ture" v-if="isShow" @click.stop="isShow=false">
      <div class="mask" catchtouchmove="ture"></div>
      <div class="main">
        <Dateicker :info='{id: id, isShow: true}' @isShow='getType'></Dateicker>
      </div>
    </div>
  </div>
</template>

<script>
import toast from "@/plugins/toast";
import Dateicker from "@/components/Dateicker";
export default {
  data() {
    return {
      id: '',
      ticketList: ["大众点评", "麦淘亲子", "抖音", "闲鱼"], //购票渠道
      ticketCannel: 0,
      genderitem: "未选择",
      ticketitem: "小程序",
      date: "未选择",
      birthday: "未选择",
      num: 1,
      parentName: "",
      parentPhone: "",
      childName: "",
      sex: "",
      stores: {},
      user: {},
      productId: "",
      isShow: false
    };
  },
  components: {
    Dateicker
  },
  computed: {
    bespdetail() {
      return this.$store.state.besp.bespdetail;
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    getType (event, item) {
        // this.isShow = true
        if(item.state == 1 || item.state == 2) {
            this.isShow = false
            this.date = item.date
        }
    },
    // 预约产品
    getProduct() {
      wx.navigateTo({
        url: `/pages/bespeak/appointment/selectProduct/main?id=${
          this.stores.id
        }`
      });
    },
    // 购票渠道
    bindticketchange(e) {
      let i = e.target.value;
      this.ticketitem = this.ticketList[i];
      this.ticketCannel = i
    },
    // 预约日期
    bindDateChange(e) {
      this.date = e.target.value;
    },
    // 生日
    bindbirthdayChange(e) {
      this.birthday = e.target.value;
    },
    // 减
    getreduce() {
      if (this.num > 1) {
        this.num--;
      }
    },
    // 加
    getplus() {
      if (this.num >= this.stores.appointmentNum) {
          toast.text("已到达最大预约数量");
          return
      }
      this.num++;
    },
    // 提交预约
    addBesp() {
      if (this.genderitem == "未选择") {
        toast.text("请选择预约产品");
        return;
      }
      if (this.date == "未选择") {
        toast.text("请选择预约日期");
        return;
      }
      if (this.ticketitem == "未选择") {
        toast.text("请选择购票渠道");
        return;
      }
      let params = {
        storeId: this.stores.id,
        productId: this.productId,
        apponintmentDate: this.date,
        apponintmentNum: this.num,
        parentName: this.parentName,
        tel: this.parentPhone,
        childName: this.childName,
        childSex: this.sex,
        birthday: this.birthday,
        ticketChannel: this.ticketCannel*1
      };
      this.$store.dispatch("besp/getInsert", params).then(res => {
        if (res.code == 0) {
          wx.redirectTo({
            url: "/pages/bespeak/appointment/subSuccess/main"
          });
        } else {
          toast.text(res.msg);
        }
      });
    }
  },
  onLoad() {
    wx.setStorageSync("product",'');
    this.num = 1;
    this.genderitem = "未选择"
    this.ticketitem = "小程序"
    this.date = "未选择"
    this.birthday = "未选择"
    this.id = this.$root.$mp.query.id
    this.$store.dispatch("besp/getBespdetail", { id: this.id }).then(res => {
      this.stores = this.bespdetail.store;
      this.user = this.bespdetail.user;
      this.birthday = this.user.birthday;
      this.parentName = this.user.parentName;
      this.parentPhone = this.user.parentPhone;
      this.childName = this.user.childName;
      this.sex = this.user.childSex
    })
  },
  onShow() {
    this.isShow = false 
    wx.setStorageSync('backFlag', true)
    if (wx.getStorageSync("product")) {
      let obj = wx.getStorageSync("product");
      this.genderitem = obj.name;
      this.productId = obj.id;
    } else {
      this.genderitem = "未选择";
    }
  }
   
};
</script>

<style lang="less" scoped>
.appointment {
  width: 100%;
  padding-bottom: 70px;
  box-sizing: border-box;
  background: #fafafa;
  overflow: hidden;
  .Store {
    width: 100%;
    background: #fff;
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    box-sizing: border-box;
    margin-bottom: 10px;
    &::before {
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      height: 1px;
      content: "";
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #e6e3dc;
    }
    .Store_img {
      width: 96px;
      height: 96px;
      border-radius: 4px;
    }
    .Store_right {
      width: 70%;
      h3 {
        width: 240px;
        font-size: 16px;
        color: #171717;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 13px;
      }
      .Store_msg {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 9px;
        .phone_img {
          width: 18px;
          height: 18px;
          margin-right: 3px;
        }
        span {
          width: 202px;
          font-size: 12px;
          color: #7a7a7a;
          line-height: 17px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
      }
    }
  }
  .make {
    width: 100%;
    background: #fff;
    .sex {
        display: flex;
        align-items: center;
        .box {
          display: flex;
          align-items: center;
          font-size: 15px;
          color: #4A4A4A;
          &:last-child {
            margin-left: 36px;
          }
          img {
            width: 16px;
            height: 16px;
            margin-right: 10px;
          }
        }
    }
    li {
      width: 100%;
      height: 45px;
      line-height: 45px;
      display: flex;
      justify-content: space-between;
      position: relative;
      padding: 0 15px;
      box-sizing: border-box;
      &:last-child::before {
        height: 0px;
      }
      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      .Name {
        color: #171717;
        font-weight: bold;
      }
      p {
        font-size: 14px;
        color: #4a4a4a;
        flex:1;
      }
      .Gender {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        input {
          font-size: 15px;
          color: #4a4a4a;
          text-align: right;
        }
        .poi_picker {
          font-size: 14px;
          color: #727272;
        }
        .goodsName {
          width: 260px;
          text-align: right;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .right {
          width: 8px;
          height: 14px;
          margin-left: 6px;
        }
        .symbol {
          width: 20px;
          height: 20px;
        }
        .num {
          width: 30px;
          text-align: center;
          font-size: 13px;
          color: #333333;
          line-height: 18px;
        }
      }
    }
  }
  .title {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 41px;
    padding: 0 15px;
    div {
      width: 4px;
      height: 13px;
      background: rgba(74, 163, 67, 1);
    }
    h3 {
      font-size: 13px;
      color: #7a7a7a;
      margin-left: 6px;
    }
  }
  .btn {
    width: 375px;
    height: 58px;
    background: #fff;
    padding-top: 7px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0px;
    left: 0px;
    z-index: 999;
    &::before {
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      height: 1px;
      content: "";
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #cccccc;
    }
    div {
      width: 345px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      color: 16px;
      text-align: center;
      color: #fff;
      background: linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      margin: 0 auto;
      border-radius: 50px;
      margin-top: 2.5px;
    }
  }
  .mask-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    z-index: 10;
    .mask {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      overflow: hidden;
      z-index: 10;
      background:rgba(0,0,0, .3);
    }
    .main {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      z-index: 11;
      border-radius:8px 8px 0px 0px;
      box-shadow:0px 2px 18px 0px rgba(0,0,0,0.04);
      background: #fff;
    }
  }
}
</style>
