<!--
 * @Author: your name
 * @Date: 2020-10-09 09:53:45
 * @LastEditTime: 2020-10-30 20:51:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \baoyan-frontend\src\pages\wish\index.vue
-->
<template>
    <div class="wish">
        <!-- 好友赠送 -->
    </div>
</template>

<script>
import wxParse from 'mpvue-wxparse'
import { gifts,getUser } from "@/api";
import toast from "@/plugins/toast";

export default {
  data () {
    return {}
    },
    methods: {
        // 领取
        receive () {
            let token = wx.getStorageSync('token')
            let registerFlag = wx.getStorageSync('registerFlag')
            if (!token) {
                wx.showModal({
                    content: '您还没有登录', 
                    cancelText: '取消', 
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            }else if (!registerFlag) {
                wx.showModal({
                    content: '您还没有注册', 
                    cancelText: '取消', 
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }
            getUser().then(res => {
                if(res) {
                let params = {
                    orderId: this.$root.$mp.query.orderId,
                    userId: res.data.id,
                    random:this.$root.$mp.query.random,
                }
                    gifts(params).then(res => {
                        if (res.data.code) {

                            wx.reLaunch({ url: `/pages/main/Order/main?type=0&tip=领取失败:${res.data.msg}` })
                            // toast.text('领取失败:'+res.data.msg)
                            // setTimeout(() => {
                            //     // wx.reLaunch({ url: `/pages/index/main` })
                            //     wx.reLaunch({ url: `/pages/main/Order/main?type=0&tip=领取失败:${res.data.msg}` })
                            // }, 3000);
                        } else {
                            wx.reLaunch({ url: `/pages/main/Order/main?type=0&tip=领取${this.$root.$mp.query.name}成功` })
                            // toast.text('领取'+this.$root.$mp.query.name+'成功')
                            // console.log(res)
                            // setTimeout(() => {
                            //     wx.reLaunch({ url: `/pages/main/Order/main?type=0` })
                            // }, 3000);
                        }
                    })
                }
            })
        wx.hideLoading()
        },
    },
    onShow(){
        // wx.showLoading({
        //         title: '',
        //         mask: true
        //     })
            this.receive()
    }
}
</script>
<style lang="less">
.wish{

}
</style>