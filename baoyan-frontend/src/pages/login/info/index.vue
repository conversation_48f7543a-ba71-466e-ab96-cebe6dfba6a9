<template>
  <div class="container">
    <p class="title">家长信息</p>
    <ul class="list">
      <li>
        <p>家长姓名</p>
        <input v-if="!isShow" type="text" v-model="fname" placeholder="请输入家长姓名" placeholder-style="color:#C6C6C6;">
        <p class="replace" v-else >请输入家长姓名</p>
      </li>
      <li>
        <p>联系方式</p>
        <input v-if="!isShow" type="text" v-model="phone" maxlength="11" placeholder="请输入家长联系方式" placeholder-style="color:#C6C6C6;">
        <p class="replace" v-else >请输入家长联系方式</p>
      </li>
    </ul>
    <p class="title">小朋友信息</p>
    <ul class="list">
      <li>
        <p>姓名</p>
        <input v-if="!isShow" type="text" v-model="name" placeholder="请输入小朋友姓名" placeholder-style="color:#C6C6C6;">
        <p class="replace" v-else >请输入小朋友姓名</p>
      </li>
      <li>
        <p>性别</p>
        <div class="sex">
          <div class="box" @click="sex = 0">
            <img :src="'/static/images/'+( sex == 0 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>男</span>
          </div>
          <div class="box" @click="sex = 1">
            <img :src="'/static/images/'+( sex == 1 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>女</span>
          </div>
        </div>
      </li>
      <li>
        <p>生日</p>
        <picker mode="date" @change="bindDateChange"  :end="end">
          <div class="picker">
            <p class="time">{{birthday}}</p>
            <img class="arrow" src="/static/images/icon-right-arrow.png" alt="">
          </div>
        </picker>
      </li>
    </ul>
    <button @click="getTabs">确定</button>

    <div class="mask" v-if="isShow">
      <div class="main">
        <h6>恭喜您获得优惠券</h6>
        <div class="img-box">
          <img src="/static/images/icon-volume-back.png" alt="" class="back">
          <div class="content">
            <div class="money">
              <p class="num"><span>￥</span>{{info.discount}}</p>
              <p class="coupon" v-if="info.couponType == 1">{{info.full === 0 ? '抵扣券' : '优惠券'}}</p>
              <p class="coupon" v-if="info.couponType == 2">折扣券</p>
            </div>
            <div class="right">
              <p>满{{info.full}}元可用</p>
              <p v-if="info.type===0">全部商品可用</p>
              <p v-else>限{{info.goodsName}}类商品可用</p>
              <p>{{info.startDate}}~{{info.endDate}}</p>
            </div>
          </div>
        </div>
        <div class="btn" @click="tabpage">确定</div>
      </div>
    </div>


     <!-- <div class="mask">
      <div class="main">
        <h6>恭喜您获得优惠券</h6>
        <div class="img-box">
          <img src="/static/images/icon-volume-back.png" alt="" class="back">
          <div class="content">
            <div class="money">
              <p class="num"><span>￥</span>99</p>
              <p class="coupon">折扣券</p>
            </div>
            <div class="right">
              <p>满100元可用</p>
              <p class="p-two">是可用是可用是可用是可用是可用是可用是可用</p>
              <p>2019-10-10 ~ 1092-90-19</p>
            </div>
          </div>
        </div>
        <div class="btn" @click="tabpage">确定</div>
      </div>
    </div> -->

  </div>
</template>

<script>
import toast from '@/plugins/toast'
import { formatTime2 } from '@/utils/index'
export default {
  data () {
    return {
      end: formatTime2(new Date()),
      fname: '',
      phone: '',
      name: '',
      sex: 0,
      birthday: '选择日期',
      isShow: false,
      info: {} //优惠券数据
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    // 选择日期
    bindDateChange (e) {
      this.birthday = e.target.value;
    },
    // 点击确认按钮
    getTabs () {
      let _this = this;
      if (!this.fname) {
        toast.text('请输入家长姓名');
        return false
      }
      if (!this.phone) {
        toast.text('请输入家长联系方式');
        return false
      }
      const reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!(reg.test(_this.phone))) {
        toast.text('请输入正确的手机号');
        return false
      }
      if (!this.name) {
        toast.text('请输入小朋友姓名');
        return false
      }
      if (this.birthday === '选择日期') {
        toast.text('请选择日期');
        return false
      }
      wx.showModal({
        content: '请确认资料信息',
        confirmColor: '#ED813A',
        success (res) {
          if (res.confirm) {
            let params = {
              parentName: _this.fname,
              parentPhone: _this.phone,
              childName: _this.name,
              birthday: _this.birthday,
              childSex: _this.sex
            }
            _this.$store.dispatch('login/queryInfo', params).then(res => {
              if (!res.code) {
                if (!res.byCouponTemp.couponType) {
                    wx.switchTab({
                        url: "/pages/index/main"
                    })
                    return
                }
                _this.info = res.byCouponTemp
                _this.isShow = true
              } else {
                toast.text(res.msg)
              }
            })
          } else if (res.cancel) {
          }
        }
      })
    },
    // 领取优惠券
    tabpage () {
      this.isShow = false
      wx.navigateBack({
        delta: 1
      })
    }
  },
  onShow () {
    this.fname = ''
    this.phone = ''
    this.name = ''
    this.sex = 0
    this.birthday = '选择日期'
    this.isShow = false
  }
}
</script>

<style lang="less">
.container {
  .title {
    width: 100%;
    height: 41px;
    line-height: 41px;
    padding: 0 15px 0 25px;
    box-sizing: border-box;
    font-size: 13px;
    color: #7A7A7A;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 15px;
      width: 4px;
      height: 13px;
      transform: translate(0, -50%);
      background: #4AA343;
    }
  }
  .list {
    background: #fff;
    li {
      width: 100%;
      height: 49px;
      line-height: 49px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
      box-sizing: border-box;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #D9D5CC;
      }
      &:last-child::after {
        background-color: transparent;
      }
      p {
        font-size: 14px;
        color: #171717;
      }
      input {
        height: 100%;
        text-align: right;
        width: 70%;
        font-size: 15px;
      }
      .replace {
        font-size: 15px;
        color: #C6C6C6;
      }
      .sex {
        display: flex;
        align-items: center;
        .box {
          display: flex;
          align-items: center;
          font-size: 15px;
          color: #4A4A4A;
          &:last-child {
            margin-left: 36px;
          }
          img {
            width: 16px;
            height: 16px;
            margin-right: 10px;
          }
        }
      }
      .picker {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #727272;
        .time {
          color: #727272;
          font-size: 14px;
        }
        .arrow {
          width: 6px;
          height: 12px;
          margin-left: 6px;
        }
      }
    }
  }
  button {
    width: 315px;
    height: 44px;
    line-height: 44px;
    background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
    border-radius:22px;
    margin: 60px auto 0;
    text-align: center;
    font-size: 16px;
    color: #FFFFFF;
  }
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background:rgba(0,0,0, .3);
    z-index: 10;
    .main {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 295px;
      border-radius: 6px;
      background: #fff;
      z-index: 11;
      h6 {
        width: 100%;
        text-align: center;
        padding: 22px 0;
        box-sizing: border-box;
        font-size: 18px;
        line-height: 25px;
        color: #4A4A4A;
      }
      .img-box {
        width: 271px;
        height: 95px;
        margin: 0 auto 27px;
        position: relative;
        .back {
          width: 271px;
          height: 95px;
        }
        .content {
          position: absolute;
          top: 0;
          left: 0;
          // width: 271px;
          // height: 95px;
          display: flex;
          // align-items: center;
          // padding: 0 0 0 19px;
          // box-sizing: border-box;
          color: #fff;
          width: 100%;
          height: 100%;
          .money {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding-left: 15px;
            .num {
              font-size: 36px;
              line-height: 36px;
              margin-bottom: 6px;
              span {
                font-size: 13px;
                line-height: 18px;
                vertical-align: top;
              }
            }
            .coupon {
              font-size: 13px;
              line-height: 18px;
              margin-left: 10px;
              // text-align: right;
            }
          }
          .right {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 40px;
            // margin-bottom: 4px;
            p {
              font-size: 12px;
              line-height: 20px;
              // width: 147px;
              &:last-child {
                margin-bottom: 0;
              }
            }
            .p-two {
                background: red;
                width: 150px;
            }
          }
        }
      }
      .btn {
        width: 100%;
        padding: 0;
        text-align: center;
        background: #fff;
        color: #ED813A;
        font-size: 15px;
        height: 49px;
        line-height: 49px;
        border-top: 1rpx solid #E5E5E5;
      }
    }
  }
}
</style>
