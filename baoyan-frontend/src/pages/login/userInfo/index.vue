<template>
  <div class="container">
    <p class="title">家长信息</p>
    <ul class="list">
      <li>
        <p>家长姓名</p>
        <input v-if="!isShow" type="text" v-model="updateUser.parentName" placeholder="请输入家长姓名"
               placeholder-style="color:#C6C6C6;"/>
        <p class="replace" v-else>请输入家长姓名</p>
      </li>
      <li>
        <p>联系方式</p>
        <input type="text" v-if="!isShow" v-model="updateUser.parentPhone" maxlength="11" placeholder="请输入家长联系方式"
               placeholder-style="color:#C6C6C6;"/>
        <p class="replace" v-else>请输入家长联系方式</p>
      </li>
    </ul>
    <p class="title">小朋友信息</p>
    <ul class="list">
      <li>
        <p>姓名</p>
        <input type="text" v-if="!isShow" v-model="updateUser.childName" placeholder="请输入小朋友姓名"
               placeholder-style="color:#C6C6C6;">
        <p class="replace" v-else>请输入小朋友姓名</p>
      </li>
      <li>
        <p>性别</p>
        <div class="sex">
          <div class="box" @click="updateUser.childSex =0">
            <img :src="'/static/images/'+( updateUser.childSex === 0 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>男</span>
          </div>
          <div class="box" @click="updateUser.childSex =1">
            <img :src="'/static/images/'+( updateUser.childSex === 1 ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            <span>女</span>
          </div>
        </div>
      </li>
      <li>
        <p>生日</p>
        <picker mode="date" @change="bindDateChange" :end="end">
          <div class="picker">
            <p class="time">{{ updateUser.birthday==null ? '选择日期' : updateUser.birthday}}</p>
            <img class="arrow" src="/static/images/icon-right-arrow.png" alt="">
          </div>
        </picker>
      </li>
    </ul>
    <button @click="getTabs">确定</button>
  </div>
</template>

<script>
    import toast from '@/plugins/toast'
    import {formatTime2} from '@/utils/index'
    import {getUser} from '@/api'

    export default {
        data() {
            return {
                updateUser: {},
                end: formatTime2(new Date()),
                isShow: false,
                clickFlag:null,
            }
        },

        watch: {
            updateUser: {
                handler(newValue, oldValue) {
                    this.clickFlag = (newValue === oldValue);
                },
                deep: true
            }
        },

        onShareAppMessage(res) {
            return {
                title: '宝燕乐园',
                path: `/pages/index/main`
            }
        },
        methods: {
            // 选择日期
            bindDateChange(e) {
                this.updateUser.birthday = e.target.value;
                this.updateUser.childAge = this.calculateAge(this.updateUser.birthday);
            },
            calculateAge(strBirthday) {
                let returnAge;
                let strBirthdayArr = strBirthday.split("-");
                let birthYear = strBirthdayArr[0];
                let birthMonth = strBirthdayArr[1];
                let birthDay = strBirthdayArr[2];

                let d = new Date();
                let nowYear = d.getFullYear();
                let nowMonth = d.getMonth() + 1;
                let nowDay = d.getDate();

                if (nowYear == birthYear) {
                    returnAge = 0;//同年 则为0岁
                } else {
                    var ageDiff = nowYear - birthYear; //年之差
                    if (ageDiff > 0) {
                        if (nowMonth == birthMonth) {
                            var dayDiff = nowDay - birthDay;//日之差
                            if (dayDiff < 0) {
                                returnAge = ageDiff - 1;
                            } else {
                                returnAge = ageDiff;
                            }
                        } else {
                            var monthDiff = nowMonth - birthMonth;//月之差
                            if (monthDiff < 0) {
                                returnAge = ageDiff - 1;
                            } else {
                                returnAge = ageDiff;
                            }
                        }
                    } else {
                        returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
                    }
                }

                return returnAge;//返回周岁年龄

            },
            // 点击确认按钮
            getTabs() {
                if (this.updateUser.parentName == null) {
                    toast.text('请输入家长姓名');
                    return false;
                }
                if (this.updateUser.parentPhone == null) {
                    toast.text('请输入家长联系方式');
                    return false;
                }
                const reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
                if (!(reg.test(this.updateUser.parentPhone))) {
                    toast.text('请输入正确的手机号');
                    return false;
                }
                if (this.updateUser.childName == null) {
                    toast.text('请输入小朋友姓名');
                    return false;
                }
                if (this.updateUser.birthday === '选择日期') {
                    toast.text('请选择日期');
                    return false;
                }
                let _this = this;
                wx.showModal({
                    content: '确定是否要修改',
                    confirmColor: '#ED813A',
                    success(res) {
                        // if (!_this.clickFlag){
                        //     return;
                        // }
                        if (res.confirm) {
                            _this.$store.dispatch('login/updateUser', _this.updateUser).then(res => {
                                if (res) {
                                    wx.navigateTo({
                                        url: "/pages/main/personal/main",
                                        success:function () {
                                            toast.text("修改成功");
                                        }
                                    });
                                    return;
                                }
                                // toast.text(res.msg)
                            })
                        }
                    }
                });
            }
        },
        onShow: function () {
            let that = this;
            getUser().then(res => {
                that.updateUser = res.data;

            });
        }
    }
</script>

<style lang="less">
  .container {
    .title {
      width: 100%;
      height: 41px;
      line-height: 41px;
      padding: 0 15px 0 25px;
      box-sizing: border-box;
      font-size: 13px;
      color: #7A7A7A;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 15px;
        width: 4px;
        height: 13px;
        transform: translate(0, -50%);
        background: #4AA343;
      }
    }

    .list {
      background: #fff;

      li {
        width: 100%;
        height: 49px;
        line-height: 49px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;
        position: relative;

        &::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(.5);
          transform: scaleY(.5);
          background-color: #D9D5CC;
        }

        &:last-child::after {
          background-color: transparent;
        }

        p {
          font-size: 14px;
          color: #171717;
        }

        input {
          height: 100%;
          text-align: right;
          width: 70%;
          font-size: 15px;
        }

        .replace {
          font-size: 15px;
          color: #C6C6C6;
        }

        .sex {
          display: flex;
          align-items: center;

          .box {
            display: flex;
            align-items: center;
            font-size: 15px;
            color: #4A4A4A;

            &:last-child {
              margin-left: 36px;
            }

            img {
              width: 16px;
              height: 16px;
              margin-right: 10px;
            }
          }
        }

        .picker {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #727272;

          .time {
            color: #727272;
            font-size: 14px;
          }

          .arrow {
            width: 6px;
            height: 12px;
            margin-left: 6px;
          }
        }
      }
    }

    button {
      width: 315px;
      height: 44px;
      line-height: 44px;
      background: linear-gradient(90deg, rgba(255, 151, 56, 1) 0%, rgba(254, 116, 61, 1) 100%);
      border-radius: 22px;
      margin: 60px auto 0;
      text-align: center;
      font-size: 16px;
      color: #FFFFFF;
    }

    .mask {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background: rgba(0, 0, 0, .3);
      z-index: 10;

      .main {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 295px;
        border-radius: 6px;
        background: #fff;
        z-index: 11;

        h6 {
          width: 100%;
          text-align: center;
          padding: 22px 0;
          box-sizing: border-box;
          font-size: 18px;
          line-height: 25px;
          color: #4A4A4A;
        }

        .img-box {
          width: 271px;
          height: 95px;
          margin: 0 auto 27px;
          position: relative;

          .back {
            width: 271px;
            height: 95px;
          }

          .content {
            position: absolute;
            top: 0;
            left: 0;
            // width: 271px;
            // height: 95px;
            display: flex;
            // align-items: center;
            // padding: 0 0 0 19px;
            // box-sizing: border-box;
            color: #fff;
            width: 100%;
            height: 100%;

            .money {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              padding-left: 15px;

              .num {
                font-size: 36px;
                line-height: 36px;
                margin-bottom: 6px;

                span {
                  font-size: 13px;
                  line-height: 18px;
                  vertical-align: top;
                }
              }

              .coupon {
                font-size: 13px;
                line-height: 18px;
                margin-left: 10px;
                // text-align: right;
              }
            }

            .right {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: center;
              margin-left: 40px;
              // margin-bottom: 4px;
              p {
                font-size: 12px;
                line-height: 20px;
                // width: 147px;
                &:last-child {
                  margin-bottom: 0;
                }
              }

              .p-two {
                background: red;
                width: 150px;
              }
            }
          }
        }

        .btn {
          width: 100%;
          padding: 0;
          text-align: center;
          background: #fff;
          color: #ED813A;
          font-size: 15px;
          height: 49px;
          line-height: 49px;
          border-top: 1 rpx solid #E5E5E5;
        }
      }
    }
  }
</style>
