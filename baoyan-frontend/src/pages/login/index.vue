<template>
  <div class="container">
    <div class="logo">
      <img src="/static/images/icon-logo1.jpg" alt="">
    </div>
    <ul class="list">
      <li>
        <p class="name">手机号</p>
        <input type="text" v-model="phone" placeholder="输入手机号" placeholder-style="color: #C6C6C6;">
      </li>
      <li>
        <p class="name">验证码</p>
        <input type="text" v-model="code" placeholder="输入验证码" placeholder-style="color: #C6C6C6;">
        <button class="code" @click="getValidCode" :disabled="counting">{{text}}</button>
      </li>
    </ul>
    <button @click="next">绑定</button>
    <img class="bottom-img" src="/static/images/icon-bottom-back.png" alt="">
  </div>
</template>

<script>
import toast from '@/plugins/toast'
let clsTimeOut
export default {
  data () {
    return {
      phone: '',
      code: '',
      text: '获取验证码',
      limitTime: 60,
      counting: false, //  是否正在计时,
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    // 点击获取验证码
    getValidCode () {
      if (!this.verifyPhone(this.phone)) {
        return
      } else {
        // 验证码接口
        this.$store.dispatch('login/queryPhone', {mobile: this.phone}).then(res => {
          //if (res.code == 200) {
            toast.text('发送成功')
            this.countDown()
          //}
        })
      }
    },
    // 是否输入验证码
    hasEmpty () {
      if (!this.verifyPhone(this.phone)) {
        return true
      }
      if (!this.code) {
        toast.text('请输入验证码')
        return true
      }
      return false
    },
     //  验证手机号
    verifyPhone (num) {
      const reg = /^1(3|4|5|6|7|8|9)\d{9}$/
      if (!num) {
      toast.text('请输入手机号')
        return false
      }
      if (!(reg.test(num))) {
        toast.text('请输入正确的手机号')
        return false
      }
      return true
    },
    // 计时器
    countDown () {
      let self = this
      if (this.limitTime > 0) {
        this.limitTime--
        this.text = `${this.limitTime}s`
        this.counting = true

        clsTimeOut = setTimeout(() => {
          self.countDown()
        }, 1000)
      } else {
        clearTimeout(clsTimeOut)
        this.limitTime = 60
        this.text = '重新获取'
        this.counting = false
      }
    },
    // 点击绑定
    next () {
      const hasEmpty = this.hasEmpty()
      if (hasEmpty) {
        return
      } else {
        let _this = this
        this.$store.dispatch('login/register', {mobile: this.phone, verifyCode: this.code}).then(res => {
          if (!res.code) {
            wx.setStorageSync('registerFlag', true)
            wx.showModal({
              title: '注册成功',
              content: '是否继续完善资料，完善资料后方可正常下单购买商品',
              success (res) {
                if (res.confirm) {
                  wx.redirectTo({
                    url: "/pages/login/info/main"
                  })
                } else if (res.cancel) {
                  wx.navigateBack({
                    delta: 1
                  })
                }
              }
            })
          } else {
            toast.text(res.msg)
          }
        })
      }
    }
  },
  onLoad () {
    this.code = ''
    this.phone = ''
    this.limitTime = 60
  }
}
</script>

<style lang="less">
.container {
  .logo {
    width: 100%;
    padding: 34px 0 36px;
    box-sizing: border-box;
    text-align: center;
    img {
      width: 122px;
      height: 122px;
      border-radius: 50%;
    }
  }
  .list {
    width: 100%;
    padding: 0 30px 53px;
    box-sizing: border-box;
    li {
      width: 100%;
      height: 51px;
      line-height: 51px;
      display: flex;
      align-items: center;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #D9D5CC;
      }
      .name {
        height: 100%;
        width: 68px;
        font-size: 15px;
        color: #343333;
      }
      input {
        height: 100%;
        background: transparent;
        font-size: 15px;
      }
      .code {
        height: 100%;
        width: 72px;
        font-size: 14px;
        color: #EF7F39;
        border-radius: 0;
        background: none;
        padding: 0;
        line-height: 51px;
      }
    }
  }
  button {
    width: 315px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
    border-radius:22px;
    font-size: 16px;
    color: #fff;
    margin: 0 auto;
  }
  .bottom-img {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 89px;
  }
}
</style>
