<template>
    <div class="container">
        <div class="header">
            <div class="query">
                <input type="text" v-model="text" placeholder="输入门店名称" @input="queryText">
                <img mode="aspectFit" src="/static/images/icon-input-query.png" alt="" class="query-icon">
            </div>
        </div>
        <scroll-view class="scrollView" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
            <div class="section" v-for="(item, index) in allstore" :key="index">
                <div class="main">
                    <img mode="aspectFill" :src="item.storeImg" alt="">
                    <div class="right">
                        <p class="title">{{item.name}}</p>
                        <div class="info">
                            <div class="phone">
                                <img mode="aspectFit" src="/static/images/icon-phone.png" alt="" class="icon">
                                <p class="tel">{{item.mobile}}</p>
                            </div>
                            <p class="distance">{{item.distance}}km</p>
                        </div>
                        <div class="address">
                            <img mode="aspectFit" src="/static/images/icon-address.png" alt="" class="icon">
                            <p class="add">{{item.address}}</p>
                        </div>
                    </div>
                </div>
                <div class="btn">
                <!-- <button class="besp" @click="getDetail(item)">立即预约</button> -->
                <button @click="tabMap(item)">前往门店</button>
                </div>
            </div>
        </scroll-view>
        <div class="btnBox" v-if="positionFlag">
            <p class="p1">温馨提示：预约功能需要授权地理位置，您还未授权</p>
            <button open-type="openSetting">去授权</button>
        </div>
    </div>
</template>
<script>
import { getOrderNum, checkUserDisable } from '@/api'

export default {
    data () {
        return {
            text: '',
            lat: '',
            lng: '',
            page: 1,
            pageSize: 20,
            appointmentOrderId: '',
            noService: false,
            registerFlag: false,
            latArr: '',
            positionFlag: false,
            token: ''
        }
    },
    computed: {
        scrollViewHeight () {
            if (this.$store.state.windowHeight) {
                return `${this.$store.state.windowHeight - 55}px`
            }
            return '100%'
        },
        allstore () {
            return this.$store.state.besp.allstore
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onPullDownRefresh() {
        wx.showNavigationBarLoading() //在标题栏中显示加载
        this.appointmentOrderId = ''
        this.page = 1
        this.$store.commit('besp/RECORD_ALLSTORE', [])
        if (this.latArr) {
            this.getinfo()
        } else {
            this.loadInfo()
        }
    },
    methods : {
        // 经纬度
        loadInfo () {
            let _this = this
            // if (this.registerFlag) {
                wx.getLocation({
                    type: 'wgs84', // 返回可以用于wx.openLocation的经纬度
                    success(res) {
                        let latArr = { lat: res.latitude, lng: res.longitude }
                        wx.setStorageSync('latArr', latArr)
                        _this.latArr = wx.getStorageSync('latArr')
                        _this.positionFlag = false
                        _this.getinfo()
                    },
                    fail() {
                        _this.positionFlag = true
                    }
                })
            // }
        },
        loadMore () {
            if (this.noService) {
                return
            }
            this.page++
            this.getinfo()
        },
        // 跳地图导航
        tabMap (item) {
            let lat = Number(item.latitude)
            let lng = Number(item.longitude)
            let name = item.name
            // let add = this.detail.storeAddr
            wx.getLocation({
                type: 'wgs84', 
                success: function (res) {
                wx.openLocation({//​使用微信内置地图查看位置。
                    latitude: lat,//要去的纬度-地址
                    longitude: lng,//要去的经度-地址
                    name: name,
                })
                }
            })
        },
        // 搜索
        queryText () {
            if (!this.registerFlag) return
            this.noService = false
            this.page = 1
            this.$store.commit('besp/RECORD_ALLSTORE', [])
            this.getinfo()
        },
        // 立即预约
        getDetail (item) {
            if (!this.token) {
                wx.showModal({
                    content: '您还没有登录', 
                    cancelText: '取消', 
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            } else {
                if (!this.registerFlag) {
                    wx.showModal({
                        content: '您还没有注册', 
                        cancelText: '取消', 
                        confirmText: '去注册',
                        success: res => {
                            if (res.confirm) {
                                wx.navigateTo({ url: `/pages/login/main` })
                            }
                        }
                    })
                    return
                }
            }
            checkUserDisable({type: 0, ids: "0"}).then(data => {
                if (data.data.code == 0) {
                    getOrderNum().then(res => {
                        if (res.data.type == 2) {
                            wx.showModal({
                                content: '您还没有完善资料', 
                                cancelText: '取消', 
                                confirmText: '去完善',
                                success: res => {
                                    if (res.confirm) {
                                        wx.navigateTo({
                                            url: '/pages/login/info/main'
                                        })
                                    }
                                }
                            })
                            return
                        } else {
                            wx.navigateTo({
                                url: `/pages/bespeak/appointment/main?id=${item.id}`
                            })
                        }
                    })
                } else {
                    toast.text(data.data.msg)
                }
            })
        },
        getinfo () {
            let latArr = wx.getStorageSync('latArr')
            this.lat = latArr.lat
            this.lng = latArr.lng
            let params = {
                orderId: this.appointmentOrderId,
                storeName: this.text ? this.text : '',
                latitude: this.lat,
                longitude: this.lng,
                pageIndex: this.page,
                pageSize: this.pageSize
            }
            this.$store.dispatch('besp/getAllstore', params).then(res => {
                this.noService = res
                wx.setStorageSync('backFlag', false)
                setTimeout(() => {
                    wx.hideNavigationBarLoading() //完成停止加载
                    wx.stopPullDownRefresh()
                },1000)
            })
        }
    },
    onShow () {
        let latArr = wx.getStorageSync('latArr')
        this.lat = latArr.lat
        this.lng = latArr.lng
        this.$store.commit('getWindowHeight')

        let backFlag = wx.getStorageSync('backFlag')

        this.positionFlag = false
        this.token = wx.getStorageSync('token')
        this.appointmentOrderId = wx.getStorageSync('appointmentOrderId')
        this.registerFlag = wx.getStorageSync('registerFlag')
        this.noService = false
        this.page = 1
        this.text = ''
        if (this.latArr) {
            if (!backFlag) {
                this.$store.commit('besp/RECORD_ALLSTORE', [])
                this.getinfo()
            }
        } else {
            this.loadInfo()
        }
    }
}
</script>

<style lang="less">
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}
.container {
    // padding-bottom: 20px;
    .btnBox {
        position: fixed;
        top: 54px;
        width: 100%;
        font-size: 13px;
        .p1 {
            color: #FF5100;
            padding: 15px;
        }
        button {
            color: #fff;
            font-size: 14px;
            margin: 0 auto;
            width: 200px;
            padding: 0;
            background: #FF5100;
        }
    }
  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 54px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background: #fff;
    z-index: 999;
    border-bottom: 1rpx solid #E6E3DC;
    .query {
      width: 345px;
      height: 30px;
      position: relative;
      input {
        width: 100%;
        height: 30px;
        line-height: 30px;
        background: #F5F5F5;
        font-size: 13px;
        padding: 0 15px 0 32px;
        box-sizing: border-box;
        border-radius: 16px;
      }
      .query-icon {
        position: absolute;
        top: 50%;
        left: 8px;
        transform: translate(0, -50%);
        width: 16px;
        height: 16px;
      }
    }
  }
  .scrollView {
      margin-top: 55px;
      height: cacl(100vh - 55px);
  }
  .section {
    width: 100%;
    background: #fff;
    padding: 10px 15px 12px;
    box-sizing: border-box;
    margin-bottom: 10px;
    .main {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      img {
        width: 96px;
        height: 96px;
        border-radius: 4px;
      }
      .right {
        width: 240px;
        .title {
          width: 240px;
          font-size: 17px;
          line-height: 22px;
          color: #171717;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 13px;
        }
        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 9px;
          .phone {
            display: flex;
            align-items: center;
            .icon {
              width: 18px;
              height: 18px;
              margin-right: 3px;
            }
            .tel {
              font-size: 12px;
              color: #7A7A7A;
              line-height: 17px;
            }
          }
          .distance {
            font-size: 12px;
            color: #ED813A;
            line-height: 17px;
          }
        }
        .address {
          display: flex;
          // align-items: center;
          .icon {
            width: 18px;
            height: 18px;
            margin-right: 3px;
          }
          .add {
            width: 202px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 12px;
            color: #7A7A7A;
            line-height: 17px;
          }
        }
      }
    }
    .btn {
      width: 100%;
      height: 28px;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      button {
        background: #fff;
        width: 72px;
        height: 28px;
        line-height: 28px;
        padding: 0;
        text-align: center;
        border: 1px solid #4A4A4A;
        border-radius: 16px;
        font-size: 12px;
        color: #4A4A4A;
        margin-left: 10px;
      }
      .besp {
        border-color: #FF5100;
        color: #FF5100;
      }
    }
  }
}
</style>
