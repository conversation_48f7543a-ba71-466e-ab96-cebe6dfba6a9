<template>
    <div class="container">
        <div class="headerTop">
            <div class="header">
                <div class="query-box">
                    <input type="text" v-model="text" class="query" placeholder="输入商品名称" @input="getquery" />
                    <img src="/static/images/icon-query.png" alt class="icon-query" />
                </div>
                <!-- <div class="classification" @click="showHandler">
                    <img class="img-class" src="/static/images/icon-classfunction.png" alt />
                    <p class="class-text">分类</p>
                </div> -->
            </div>
            <ul class="type">
                <li>
                    <p :class="{active:sortType===1}" @click="tabInfo(1)">综合</p>
                </li>
                <li @click="tabInfo(2)">
                    <p :class="{active:sortType===2}">价格</p>
                    <div class="triangle-box">
                        <div :class="{'triangle-top':true,'triangle-top-select':isPriceAscending===false}"></div>
                        <div :class="{'triangle-bottom':true,'triangle-bottom-select':isPriceAscending===true}"></div>
                    </div>
                </li>
                <li @click="tabInfo(3)">
                    <p :class="{active:sortType===3}">距离</p>
                    <div class="triangle-box">
                        <div :class="{'triangle-top':true,'triangle-top-select':isDistanceAscending===true}"></div>
                        <div :class="{'triangle-bottom':true,'triangle-bottom-select':isDistanceAscending===false}"></div>
                    </div>
                </li>
                <li @click="tabInfo(4)">
                    <p :class="{active:sortType===4}">销量</p>
                    <div class="triangle-box">
                        <div :class="{'triangle-top':true,'triangle-top-select':isSalesAscending===true}"></div>
                        <div :class="{'triangle-bottom':true,'triangle-bottom-select':isSalesAscending===false}"></div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="scrollBox">
            <div class="leftScroll">
                <scroll-view scroll-y>
                    <p
                        v-for="(item, index) in logs"
                        :key="index"
                        class="type-item"
                        @click="changeType(index)"
                        :class="tabIndex == index ? 'current' : ''"
                    >{{item.classifyTitle}}</p>
                </scroll-view>
            </div>
            <div class="rightScroll">
                <scroll-view scroll-y @scrolltolower="loadMore">
                    <ul class="list">
                        <li v-for="(item, index) in products" :key="index" @click.stop="getDetail(item)">
                            <img :src="item.goodsImg"  alt />
                            <p class="title">{{item.goodsName}}</p>
                            <div class="tibs">
                                <span class="num" v-if="!item.isCombination">¥{{item.sellPrice}}</span>
                                <span class="num" v-else>¥{{item.lowPrice}}
                                    <span class="marketPrice1">起</span>
                                </span>
                                <span class="sell">已售：{{item.goodsCount}}</span>
                            </div>
                        </li>
                    </ul>
                    <div v-if="!products.length" class="noneData">没有更多数据啦...</div>
                </scroll-view>
            </div>
        </div>
        <!-- <div class="maskBg" :style="{'display': showHide}"></div>
        <div class="mask" @click.stop="hideHandler" :style="{'margin-left':marginLeft}">
            <scroll-view class="tab-box" scroll-y>
                <p
                    v-for="(item, index) in logs"
                    :key="index"
                    class="type-item"
                    @click="changeType(index)"
                    :class="tabIndex == index ? 'current' : ''"
                >{{item.classifyTitle}}</p>
            </scroll-view>
        </div> -->
    </div>
</template>

<script>
export default {
    data() {
        return {
            marginLeft: '-100%',
            showHide: 'none',
            opacity: 0,
            logs: [],
            tabIndex: 0,
            isShow: false,
            lat: "",
            lng: "",
            text: "",
            type: 1,
            jumpType:1,
            page: 1,
            pageSize: 20,
            noService: false,
            price: false, //价格
            distance: false, //距离
            sales: false, //销量
            sortType: 1, //排序类型
            isPriceAscending: '', //是否价格升序
            isDistanceAscending: '', //是否距离升序
            isSalesAscending: '' //是否距离升序
        }
    },
    computed: {
        classification() {
            return this.$store.state.home.classification;
        },
        products() {
            return this.$store.state.besp.products;
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onPullDownRefresh() {
        wx.showNavigationBarLoading() //在标题栏中显示加载
        this.$store.commit("besp/RECORD_PRODUCTS", [])
        this.$store.dispatch("home/getClassification", {}).then(res => {
            this.logs = [{ classifyTitle: "全部商品", id: "" }]
            this.classification.map(item => {
                this.logs.push(item)
            })
            this.getInfo()
        })
    },
    onReachBottom() {
        if (this.noService) {
            return
        }
        this.page ++
        this.getInfo()
    },
    methods: {
        // 经纬度
        loadInfo () {
            let _this = this
            wx.getLocation({
                type: 'wgs84', // 返回可以用于wx.openLocation的经纬度
                success(res) {
                    let latArr = { lat: res.latitude, lng: res.longitude }
                    _this.lat = res.latitude
                    _this.lng = res.longitude
                    wx.setStorageSync('latArr', latArr)
                    _this.latArr = wx.getStorageSync('latArr')
                    _this.page = 1
                    _this.noService = false
                    _this.$store.commit("besp/RECORD_PRODUCTS", [])
                    _this.getInfo()                },
                fail() {
                    wx.showModal({
                        title: '温馨提示', //提示的标题,
                        content: '距离筛选需要您授权地理位置',
                        confirmText: '去授权',
                        success: res => {
                            if (res.confirm) {
                                wx.openSetting({
                                    success(res) {
                                        _this.loadInfo()
                                    }
                                })                    
                            }
                        }
                    })
                }
            })
        },
        showHandler() {
            this.showHide = 'block'
            this.marginLeft = 0
        },
        hideHandler() {
            this.showHide = 'none'
            this.marginLeft = '-100%'
        },
        loadMore() {
            if (this.noService) {
                return
            }
            this.page ++
            this.getInfo()
        },
        getInfo() {
            let params = {
                latitude: this.lat,
                longitude: this.lng,
                type: this.type,
                jumpType: this.jumpType,
                classId: this.logs[this.tabIndex].id ? this.logs[this.tabIndex].id : "",
                goodsName: this.text,
                pageIndex: this.page,
                pageSize: this.pageSize
            }
            this.$store.dispatch("besp/getClassProduct", params).then(res => {
                this.noService = res;
                setTimeout(() => {
                    wx.hideNavigationBarLoading() //完成停止加载
                    wx.stopPullDownRefresh()
                },1000)
            })
        },
        // 更新数据/1 综合 2 价格 低 3 高 4 距离 近 5 远 6 销量 低 7 高
        tabInfo(e) {
            this.sortType = e
            if (e == 1) {
                this.type = 1
                this.isPriceAscending = '', //是否价格升序
                this.isDistanceAscending = '', //是否距离升序
                this.isSalesAscending = '' //是否距离升序
            } else if (e == 2) {
                this.price = !this.price;
                this.isPriceAscending = !this.isPriceAscending
                this.type = this.price ? 3 : 2
            } else if (e == 3) {
                this.isDistanceAscending = !this.isDistanceAscending
                this.distance = !this.distance
                this.type = this.distance ? 5 : 4
                if (!this.latArr) {
                    this.loadInfo()
                    return
                }
            } else if (e == 4) {
                this.isSalesAscending=!this.isSalesAscending
                this.sales = !this.sales
                this.type = this.sales ? 7 : 6
            }
            this.page = 1
            this.noService = false
            this.$store.commit("besp/RECORD_PRODUCTS", [])
            this.getInfo()
        },
        // 选择类型
        changeType(index) {
            this.tabIndex = index
            this.page = 1
            this.noService = false
            this.$store.commit("besp/RECORD_PRODUCTS", [])
            this.getInfo()
        },
        // 搜索
        getquery() {
            //   if (this.text == '') return
            this.page = 1
            this.noService = false
            this.$store.commit("besp/RECORD_PRODUCTS", [])
            this.getInfo()
        },
        // 商品详情
        getDetail(item) {
            wx.navigateTo({
                url: `/pages/home/<USER>/classDetails/main?id=${item.id}&type=1`
            })
        }
    },
    onShow() {
        wx.setStorageSync('appointmentOrderId', '')
    },
    onLoad() {
        this.$store.commit("getWindowHeight")
        let latArr = wx.getStorageSync("latArr")
        this.lat = latArr.lat
        this.lng = latArr.lng


        this.text = ""
        this.tabIndex = 0
        this.type = 1
        this.page = 1
        this.noService = false
        this.price = false //价格
        this.distance = false; //距离
        this.sales = false //销量
        this.$store.commit("besp/RECORD_PRODUCTS", [])
        this.$store.dispatch("home/getClassification", {}).then(res => {
            this.logs = [{ classifyTitle: "全部商品", id: "" }]
            this.classification.map(item => {
                this.logs.push(item)
            })
            this.getInfo()
        })
    }
}
</script>

<style lang="less">
page {
    background: #fafafa;
}
.container {
    .headerTop {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999;
        background: #fff;
    }
    .header {
        width: 100%;
        height: 49px;
        background: #fff;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .query-box {
            width: 100%;
            height: 32px;
            position: relative;
        .query {
            width: 100%;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            padding: 0 15px 0 30px;
            box-sizing: border-box;
            border-radius: 16px;
            background: #fafafa;
        }
        .icon-query {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translate(0, -50%);
            width: 14px;
            height: 14px;
        }
        }
        .classification {
            display: flex;
            align-items: center;
            .img-class {
                width: 13px;
                height: 13px;
                margin-right: 5px;
            }
            .class-text {
                font-size: 14px;
                color: #4a4a4a;
                line-height: 20px;
            }
        }
    }
    scroll-view ::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
        display: none;
    }
    .scrollBox {
        padding-top: 89px;
        display: flex;
        height: calc(100vh - 89px);
        .leftScroll {
            width: 117px;
            box-sizing: border-box;
            background: #fff;
            color: #8B8B8B;
            font-size: 14px;
            padding: 0 10px;
            text-align: center;
            p {
                height: auto;
                margin-top: 30px;
            }
            .current {
                position: relative;
            }
            .current:after {
                position: absolute;
                bottom: -6px;
                left: 50%;
                transform: translateX(-50%);
                display: block;
                content: '';
                width: 28px;
                height: 2px;
                background: #FB7C3C;
            }
            scroll-view {
                height: calc(100vh - 90px);
            }
        }
    }
    .rightScroll {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        background: #fafafa;
        // padding-top: 100px;
        background: #fafafa;
        width: calc(100vw - 117px);
        scroll-view {
            height: calc(100vh - 90px);
        }
    }
    .type {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 21px 0 25px;
        box-sizing: border-box;
        background: #fff;
        justify-content: space-between;
        margin-bottom: 10px;
        li {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #4a4a4a;
            line-height: 20px;
            img {
                width: 7px;
                height: 10px;
                margin-left: 3px;
            }
            .triangle-box {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: space-around;
                flex-direction: column;
                .triangle-top {
                width: 0;
                height: 0;
                border: 4px solid;
                border-color: transparent transparent rgba(0, 0, 0, 0.5);
                }
                .triangle-top-select {
                border-color: transparent transparent rgb(251,124,60,1);
                }
                .triangle-bottom {
                width: 0;
                height: 0;
                border: 4px solid;
                border-color: rgba(0, 0, 0, 0.5) transparent transparent;
                }
                .triangle-bottom-select {
                border-color: rgba(251,124,60,1) transparent transparent;
                }
            }
        }
        .active {
            color: #fb7c3c;
        }
    }
    .list {
        width: 228px;
        box-sizing: border-box;
        padding: 25px 15px 5px 15px;
        box-sizing: border-box;
        li {
            background: #fff;
            width: 228px;
            margin-bottom: 15px;
            padding-bottom: 8px;
            img {
                width: 228px;
                height: 233px;
                border-radius: 10px 10px 0 0;
            }
            .title {
                overflow: hidden; 
                text-overflow: ellipsis;
                display: -webkit-box; 
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2; 
                width: 100%;
                padding: 0 15px;
                box-sizing: border-box;
                // height: 40px;
                font-size: 14px;
                line-height: 20px;
                color: #000000;
                overflow: hidden;
            }
            .tibs {
                width: 100%;
                margin-top: 6px;
                padding: 0 15px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #9b9b9b;
                .num {
                    font-size: 19px;
                    line-height: 26px;
                    color: #ff5100;
                    .marketPrice1{
                        color:#666666;
                        font-size: 12px;
                    }
                }
                .sell {
                    font-size: 11px;
                }
            }
        }
    }
    .maskBg {
      animation: mymove .3s ease;
      background: rgba(0, 0, 0, 0.3);
      width: 100vw;
      height: 100vh;
      top: 0;
      left: 0;
      z-index: 999;
      display: none;
      position: fixed;
    }
    @keyframes mymove
    {
      from {background: rgba(0, 0, 0, 0);}
      to {background: rgba(0, 0, 0, 0.3);}
    }
  .mask {
    transition: .3s;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    // background: rgba(0, 0, 0, 0.3);
    position: fixed;
    top: 0;
    z-index: 9999;
    margin-left: -100%;
    .tab-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 158px;
      height: 100vh;
      background: #fff;
      z-index: 11;
      padding-top: 3px;
      box-sizing: border-box;
      .type-item {
        width: 100%;
        padding: 15px 10px 15px 30px;
        box-sizing: border-box;
        font-size: 14px;
        color: #4a4a4a;
        line-height: 20px;
        overflow: hidden;
      }
    }
  }
}
</style>
