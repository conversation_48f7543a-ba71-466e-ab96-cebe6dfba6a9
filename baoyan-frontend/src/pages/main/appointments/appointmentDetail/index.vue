<template>
  <div class="container">
    <div class="section">
      <div class="main">
        <img mode="aspectFill" v-if="custApp.storeImg" :src="custApp.storeImg" alt />
        <div class="right">
          <p class="title">{{custApp.storeName}}</p>
          <div class="info">
            <div class="phone">
              <img mode="aspectFit" src="/static/images/icon-phone.png" alt class="icon" />
              <p class="tel">{{custApp.storeMobile}}</p>
            </div>
            <!-- <p class="distance">{{custApp.}}km</p> -->
          </div>
          <div class="address">
            <img mode="aspectFit" src="/static/images/icon-address.png" alt class="icon" />
            <p class="add">{{custApp.address}}</p>
          </div>
        </div>
      </div>
    </div>
    <ul class="list">
      <li>
        <p>预约产品</p>
        <span class="goodsName">{{custApp.apponintmentProduct}}</span>
      </li>
      <li>
        <p>预约日期</p>
        <span>{{custApp.apponintmentDate}}</span>
      </li>
      <li>
        <p>购票渠道</p>
        <span>{{custApp.ticketChannelName}}</span>
      </li>
      <li>
        <p>预约数量</p>
        <span>{{custApp.apponintmentNum}}</span>
      </li>
      <li>
        <p>预约时间</p>
        <span>{{custApp.gmtCreate}}</span>
      </li>
      <li>
        <p>预约状态</p>
        <span>{{custApp.apponintmentStatusStr}}</span>
      </li>
      <li v-if="custApp.apponintmentStatusStr == '已取消'">
        <p>取消时间</p>
        <span>{{custApp.gmtModified}}</span>
      </li>
    </ul>
    <div class="box">
      <div class="info-box">
        <div class="name-box">
          <i class="line"></i>
          <p class="name">家长</p>
        </div>
        <div class="info-text">
          <p>家长姓名</p>
          <span>{{custApp.parentName}}</span>
        </div>
        <div class="info-text">
          <p>联系方式</p>
          <span>{{custApp.contactTel}}</span>
        </div>
      </div>
      <div class="info-box">
        <div class="name-box">
          <i class="line"></i>
          <p class="name">小朋友</p>
        </div>
        <div class="info-text">
          <p>姓名</p>
          <span>{{custApp.childName}}</span>
        </div>
        <div class="info-text">
          <p>性别</p>
          <span>{{custApp.childSex == 0 ? '男' : '女'}}</span>
        </div>
        <div class="info-text">
          <p>生日</p>
          <span>{{custApp.birthday}}</span>
        </div>
      </div>
    </div>

    <div class="footer">
      <button v-if="custApp.apponintmentStatusStr == '已取消'" @click="goBack">重新预约</button>
      <button v-if="custApp.apponintmentStatusStr == '已取消' || custApp.apponintmentStatusStr == '已结束'" @click.stop="deleteData">删除</button>
      <form @submit="cancel" report-submit='true' v-if="custApp.apponintmentStatusStr == '已预约'">
        <button form-type="submit">取消预约</button>
      </form>
    </div>
  </div>
</template>

<script>
import toast from '@/plugins/toast'
export default {
  data() {
    return {
      text: ""
    };
  },
  computed: {
    custApp () {
      return this.$store.state.besp.custApp
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    goBack() {
        wx.navigateTo({ 
            url: `/pages/bespeak/appointment/main?id=${this.custApp.storeId}` 
        })
    },
    // 取消
    cancel (e) {
      let formId = e.target.formId
      let that = this
      wx.showModal({
        content: '确认取消吗？',
        success: res => {
          if (res.confirm) {
            this.$store.dispatch('besp/getcancel', {id: this.id,fromId: formId}).then(res => {
                if (res.code == 0) {
                    this.$store.dispatch('besp/getcustApp', {id: this.id})
                    toast.text('已取消')
                } else {
                    toast.text('取消失败')
                }
            })
          }
        }
      });
    },
    // 删除
    deleteData () {
      wx.showModal({
        content: '确认删除吗？', //提示的内容,
        success: res => {
          if (res.confirm) {
            this.$store.dispatch('besp/queryDelete', {id: this.id}).then(res=> {
                if (res.code == 0) {
                    toast.text('已删除')
                    setTimeout(() => {
                        wx.navigateBack({
                            delta: 1
                        })
                    },500)
                } else {
                    toast.text('删除失败')
                }
            })
          }
        }
      });
    }
  },
  onLoad () {
    this.id = this.$root.$mp.query.id
    this.$store.dispatch('besp/getcustApp', {id: this.id})
  }
};
</script>

<style lang="less">
.container {
  padding-bottom: 49px;
        .goodsName {
          width: 260px;
          text-align: right;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
  .section {
    width: 100%;
    background: #fff;
    padding: 10px 15px 12px;
    box-sizing: border-box;
    margin-bottom: 10px;
    .main {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // margin-bottom: 12px;
      img {
        width: 96px;
        height: 96px;
        border-radius: 4px;
      }
      .right {
        width: 240px;
        .title {
          width: 240px;
          font-size: 17px;
          line-height: 22px;
          color: #171717;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 13px;
        }
        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 9px;
          .phone {
            display: flex;
            align-items: center;
            .icon {
              width: 18px;
              height: 18px;
              margin-right: 3px;
            }
            .tel {
              font-size: 12px;
              color: #7a7a7a;
              line-height: 17px;
            }
          }
          .distance {
            font-size: 12px;
            color: #ed813a;
            line-height: 17px;
          }
        }
        .address {
          display: flex;
          // align-items: center;
          .icon {
            width: 18px;
            height: 18px;
            margin-right: 3px;
          }
          .add {
            width: 202px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 12px;
            color: #7a7a7a;
            line-height: 17px;
          }
        }
      }
    }
  }
  .list {
    width: 100%;
    padding: 12px 15px 11px;
    box-sizing: border-box;
    background: #fff;
    margin-bottom: 10px;
    li {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      p {
        font-size: 12px;
        color: #4a4a4a;
        line-height: 17px;
      }
      span {
        font-size: 12px;
        color: #9b9b9b;
        line-height: 17px;
      }
    }
  }
  .box {
    margin-bottom: 10px;
    .info-box {
      width: 100%;
      padding: 12px 15px 12px;
      box-sizing: border-box;
      background: #fff;
      .name-box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .line {
          width: 4px;
          height: 13px;
          background: rgba(74, 163, 67, 1);
          margin-right: 6px;
        }
        .name {
          font-size: 13px;
          color: #7a7a7a;
          line-height: 18px;
        }
      }
      .info-text {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        &:last-child {
          margin-bottom: 0;
        }
        p {
          font-size: 12px;
          color: #4a4a4a;
          line-height: 17px;
        }
        span {
          font-size: 12px;
          color: #9b9b9b;
          line-height: 17px;
        }
      }
    }
  }

  .footer {
    width: 100%;
    height: 49px;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    padding: 0 15px;
    box-sizing: border-box;
    button {
      width: 72px;
      height: 28px;
      line-height: 28px;
      padding: 0;
      border-radius: 16px;
      border: 1px solid #4a4a4a;
      background: #fff;
      font-size: 12px;
      color: #4a4a4a;
      text-align: center;
      margin-left: 10px;
    }
  }
}
</style>
