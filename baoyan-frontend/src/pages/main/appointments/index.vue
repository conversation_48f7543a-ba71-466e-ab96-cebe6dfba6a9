<template>
  <div class="container">
    <scroll-view class="scrollView" style="height: 100vh" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
      <div class="main">
        <div class="section" v-for="(item, index) in appointment" :key="index" @click.stop="tabDetail(item)">
          <div class="title">
            <p class="time">{{item.gmtCreate}}</p>
            <p class="type">{{item.apponintmentStatusStr}}</p>
            <!-- 预约状态 -->
          </div>
          <ul class="list">
            <li>
              <p>预约门店</p>
              <span>{{item.storeName}}</span>
            </li>
            <li>
              <p>预约产品</p>
              <span>{{item.apponintmentProduct}}</span>
            </li>
            <li>
              <p>购买渠道</p>
              <span>{{item.ticketChannelName}}</span>
            </li>
            <li>
              <p>预约数量</p>
              <span>{{item.apponintmentNum}}</span>
            </li>
            <li>
              <p>预约时间</p>
              <span>{{item.apponintmentDate}}</span>
            </li>
          </ul>
        </div>
      </div>
      <div v-if="!appointment.length" class="noneData">没有更多数据啦...</div>
    </scroll-view>
  </div>
</template>

<script>
export default {
  data () {
    return {
      page: 1,
      pageSize: 20,
      noService: false,
    }
  },
  computed: {
    appointment () {
      return this.$store.state.besp.appointment
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    loadMore () {
      if (this.noService) {
        return
      }
      this.page++
      this.getinfo()
    },
    // 获取信息
    getinfo () {
      let params = {
        pageIndex: this.page,
        pageSize: this.pageSize,
      }
      this.$store.dispatch('besp/getAppointment', params).then(res => {
        this.noService = res
      })
    },
    // 跳转预约详情
    tabDetail (e) {
      wx.navigateTo({
        url: `/pages/main/appointments/appointmentDetail/main?id=${e.id}`
      })
    }
  },
  onShow () {
    this.page = 1
    this.noService = false
    this.$store.commit('besp/RECORD_APPOINTMENT', [])
    this.getinfo()
  }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 12px 10px 0;
    box-sizing: border-box;
    .section {
      width: 100%;
      background: #fff;
      border-radius: 6px;
      margin-bottom: 10px;
      .title {
        width: 100%;
        height: 42px;
        padding: 0 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        &::before {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 1px;
          content: "";
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #D6D6D6;
        }
        .time {
          font-size: 13px;
          color: #4A4A4A;
          line-height: 18px;
        }
        .type {
          font-size: 13px;
          color: #FF5100;
          line-height: 18px;
        }
      }
      .list {
        width: 100%;
        padding: 12px 10px 12px;
        box-sizing: border-box;
        li {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          &:last-child {
            margin-bottom: 0;
          }
          p {
            font-size: 14px;
            color: #4A4A4A;
            line-height: 20px;
            margin-right: 20px;
          }
          span {
            width: 259px;
            font-size: 14px;
            color: #7A7A7A;
            line-height: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
