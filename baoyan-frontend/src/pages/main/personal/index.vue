<template>
  <div class="appointment">
    <div class="Store">
      <img mode="aspectFill" :src="data.headImg" alt class="Store_img" />
      <ul class="userinfo">
        <li>
          <p class="Name">昵称</p>
          <p class="Gender">{{ data.nickName }}</p>
        </li>
        <!-- <li>
          <p class="Name">手机号</p>
          <p class="Gender">{{ data.mobile }}</p>
            <button @click="getUserAddress">选择收货地址</button>
        </li> -->
      </ul>
    </div>
    <div class="title">
      <div></div>
      <h3>家长信息</h3>
    </div>
    <ul class="make">
      <li>
        <p class="Name">家长姓名</p>
        <input
          type="text"
          placeholder="未完善"
          :value="data.parentName"
          disabled
        />
      </li>
      <li>
        <p class="Name">联系方式</p>
        <input
          type="text"
          placeholder="未完善"
          :value="data.parentPhone"
          disabled
        />
      </li>
    </ul>
    <div class="title">
      <div></div>
      <h3>小朋友信息</h3>
    </div>
    <ul class="make">
      <li>
        <p class="Name">姓名</p>
        <input
          type="text"
          placeholder="未完善"
          :value="data.childName"
          disabled
        />
      </li>
      <li>
        <p class="Name">性别</p>
        <input
          type="text"
          placeholder="未完善"
          :value=" data.childSex === 0 ? '男' : data.childSex === 1 ? '女' : '未完善' "
          disabled
        />
      </li>
      <li>
        <p class="Name">生日</p>
        <input
          type="text"
          placeholder="未完善"
          :value="data.birthday"
          disabled
        />
      </li>
    </ul>
    <cover-view @click="goPage" class="btn" v-if="showFlag">
      <cover-view>完善资料</cover-view>
    </cover-view>
    <cover-view @click="goUserinfo" class="btn" v-if="!showFlag">
      <cover-view>修改个人信息</cover-view>
    </cover-view>
  </div>
</template>

<script>
import { getUser, updateUser } from '@/api';
export default {
  data() {
    return {
      data: {},
      showFlag: true,
    };
  },
  onShareAppMessage(res) {
    return {
      title: "宝燕乐园",
      path: `pages/index/main`,
    };
  },
  methods: {
    getUserAddress(){
      wx.chooseAddress({
        success (res) {
          console.log(res.userName)
          console.log(res.postalCode)
          console.log(res.provinceName)
          console.log(res.cityName)
          console.log(res.countyName)
          console.log(res.detailInfo)
          console.log(res.nationalCode)
          console.log(res.telNumber)
        }
      })
    },
    goPage() {
      wx.navigateTo({
        url: "/pages/login/info/main",
      });
    },
    goUserinfo() {
      wx.navigateTo({
        url: "/pages/login/userInfo/main",
      });
    },
  },
  onShow() {
    getUser().then((res) => {
      if (res) {
        this.data = res.data;
        if (
          this.data.parentPhone &&
          this.data.parentName &&
          this.data.parentPhone &&
          this.data.childName &&
          (this.data.childSex === 0 || this.data.childSex === 1) &&
          this.data.birthday
        ) {
          this.showFlag = false;
        }
      }
    });
  },
};
</script>

<style lang="less" scoped>
.appointment {
  width: 100%;
  height: 100vh;
  background: #fafafa;
  overflow: hidden;
  .Store {
    width: 100%;
    background: #fff;
    text-align: center;
    padding-top: 20px;
    box-sizing: border-box;
    .Store_img {
      width: 66px;
      height: 66px;
      border-radius: 50%;
    }
  }
  .userinfo {
    width: 100%;
    background: #fff;
    li {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
      box-sizing: border-box;
      position: relative;
      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      &:last-child::before {
        background-color: transparent;
      }
      .Name {
        font-size: 14px;
        color: #0f0f0f;
      }
      .Gender {
        font-size: 15px;
        color: #4a4a4a;
        line-height: 21px;
      }
    }
  }
  .make {
    width: 100%;
    background: #fff;
    li {
      width: 100%;
      height: 50px;
      line-height: 50px;
      display: flex;
      justify-content: space-between;
      position: relative;
      padding: 0 15px;
      box-sizing: border-box;
      &:last-child::before {
        height: 0px;
      }
      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      .Name {
        font-weight: bold;
      }
      p {
        font-size: 14px;
        color: #4a4a4a;
      }
      input {
        font-size: 15px;
        height: 50px;
        line-height: 50px;
        text-align: right;
        color: #9b9b9b;
      }
    }
  }
  .title {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 41px;
    padding: 0 15px;
    div {
      width: 4px;
      height: 13px;
      background: rgba(74, 163, 67, 1);
    }
    h3 {
      font-size: 13px;
      color: #7a7a7a;
      margin-left: 6px;
    }
  }
  .btn {
    width: 375px;
    height: 58px;
    background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: fixed;
    bottom: 0px;
    z-index: 999;
    left: 0px;
    &::before {
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      height: 1px;
      content: "";
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #cccccc;
    }
    cover-view {
      width: 345px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      color: 16px;
      text-align: center;
      color: #fff;
      background: rgba(255, 151, 56, 1);
      border-radius: 50px;
    }
  }
}
</style>
