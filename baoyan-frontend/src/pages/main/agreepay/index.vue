<!--
 * @Author: your name
 * @Date: 2020-10-26 10:44:31
 * @LastEditTime: 2020-11-02 14:43:54
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \baoyan-frontend\src\pages\main\agreepay\index.vue
-->
<template>
    <div class="container">
      <wxParse v-if="info.text" :content="info.text"/> 
    </div>
</template>
<script>
import { purchase } from "@/api";
import wxParse from 'mpvue-wxparse'
export default {
    components:{
        wxParse
    },
  data () {
    return {
      info: {
        code: null,
        text:''
      }
    }
  },
    onLoad(){
                      purchase().then(res=>{
                        if (res.data) {
                            this.info.text=res.data
                        }
                      })
    }
}
</script>
<style lang="less">
.container {
    padding: 15px;
    box-sizing: border-box;
    .mt-20 {
        margin-top: 15px;
    }
}
</style>
