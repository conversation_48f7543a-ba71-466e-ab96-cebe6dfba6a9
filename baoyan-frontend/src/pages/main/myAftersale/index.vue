<template>
  <div class="container">
    <!-- <scroll-view class="scrollView" style="height: 100vh" :lower-threshold="100" @scrolltolower="loadMore" scroll-y> -->
      <ul class="sale_content">
        <li v-for="(item, index) in orderafter" :key="index">
          <div class="sale_title">
            <span>{{item.gmtCreate}}</span>
            <p>{{item.afterStatus == 0 ? '待审核'
              :item.afterStatus == 1 ? '已通过'
              :item.afterStatus == 2 ? '已拒绝' : ''}}</p>
          </div>
          <div class="sale_msg">
            <img mode="aspectFill" :src="item.goodsImg" alt="">
            <div class="commodity">
              <p>{{item.goodsName}}</p>
              <div>x{{item.goodsNum}}</div>
            </div>
          </div>
          <div class="details">
              <p @click="tabDetail(item)">查看详情</p>
              <p v-if="item.afterStatus == 1 || item.afterStatus == 2" @click="delHandler(item)">删除</p>
          </div>
        </li>
      </ul>
      <div v-if="!orderafter.length" class="noneData">没有更多数据啦...</div>
    <!-- </scroll-view> -->
  </div>  
</template>

<script>
import { deleteOrderAfter } from '@/api'
export default {
  data () {
    return {
      page: 1,
      pageSize: 20,
      noService: false,
    }
  },
  computed: {
    orderafter () {
      return this.$store.state.main.orderafter
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onReachBottom() {
        if (this.noService) {
            return
        }
        this.page ++
        this.getInfo()
    },
  methods: {
    delHandler(item) {
        wx.showModal({
          content: '确认删除吗？',
          success: res => {
            if (res.confirm) {
                deleteOrderAfter({id: item.id}).then(res => {
                    if(res.data.code) {
                        wx.showToast({
                          title: res.data.msg, //提示的内容,
                          icon: 'none', //图标,
                          duration: 1000
                        })
                    } else {
                       this.$store.commit('main/RECORD_ORDERAFTER', [])
                       this.getinfo()
                    }
                })            
            }
          }
        })
    },
    loadMore () {
      if (this.noService) {
        return
      }
      this.page++
      this.getinfo()
    },
    // 获取信息
    getinfo () {
      let params = {
        pageIndex: this.page,
        pageSize: this.pageSize,
      }
      this.$store.dispatch('main/getOrderafter', params).then(res => {
        this.noService = res
        setTimeout(() => {
            wx.hideNavigationBarLoading() //完成停止加载
            wx.stopPullDownRefresh()
        },500)
      })
    },
    // 跳转详情
    tabDetail (item) {
      wx.navigateTo({
        url: `/pages/main/myAftersale/salesDetails/main?id=${item.id}`
      })
    }
  },
  onPullDownRefresh() {
        wx.showNavigationBarLoading() //在标题栏中显示加载
        this.page = 1
        this.noService = false
        this.$store.commit('main/RECORD_ORDERAFTER', [])
        this.getinfo()
  },
  onLoad () {
    this.page = 1
    this.noService = false
    this.$store.commit('main/RECORD_ORDERAFTER', [])
    this.getinfo()
  }
}
</script>

<style lang="less" scoped>
  .container{
    .sale_content{
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      li{
        width: 100%;
        padding: 12px 10px;
        box-sizing: border-box;
        background: #fff;
        margin-bottom: 10px;
        .sale_title{
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 21px;
          span{
            font-size: 13px;
            color: #4A4A4A;
          }
          p{
            font-size: 13px;
            color: #FF5100;
          }
        }
        .sale_msg{
          width: 100%;
          display: flex;
          justify-content: space-between;
          img{
            width: 60px;
            height: 60px;
            border-radius: 4px;
          }
          .commodity{
            width: 263px;
            p{
              width: 100%;
              font-size: 14px;
              color: #171717;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;  //设置两行溢出显示省略号
              overflow: hidden;
            }
            div{
              font-size: 14px;
              color: #090203;
              float: right;
            }
          }
        }
        .details{
          width: 100%;
          display: flex;
          flex-direction:row-reverse;
          p{
            font-size: 12px;
            color: #4A4A4A;
            width:72px;
            height:28px;
            line-height: 28px;
            text-align: center;
            border-radius:16px;
            border:1px solid rgba(74,74,74,1);
            margin-top: 21px;
            margin-left: 10px;
          }
        }
      }
    }
  }
</style>
