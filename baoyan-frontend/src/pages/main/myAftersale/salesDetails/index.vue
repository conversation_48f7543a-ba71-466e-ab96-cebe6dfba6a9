<template>
  <div class="container">
    <div class="sales_content">
      <div class="sales_top">
        <img src="/static/images/icon-Aftersale-back.png" alt="" class="sales_img">
        <h3 class="sales_title">
          <img mode="aspectFit" src="/static/images/icon-time.png" alt="">
          <p>{{ detail.afterStatus == 0 ? '待审核'
            :detail.afterStatus == 1 ? '已通过' 
            :detail.afterStatus == 2 ? '已拒绝' : ''}}</p>
        </h3>
        <div class="sales_time">
          <p>申请时间：{{detail.gmtCreate}}</p>
          <p v-if="detail.afterStatus == 0">(请耐心等待，我们会在3~7个工作日内进行审核)</p>
        </div>
        <!-- <div class="adopt_time">申请时间：2019-07-11 12:00:00</div> -->
      </div>
      <div class="refund">
        <p>退款金额</p>
        <p>￥{{detail.afterAmount}}</p>
      </div>
      <div class="sales_msg">
        <div class="details" @click="goDetail">
          <img mode="aspectFill" :src="detail.goodsImg" alt="">
          <div class="details_title">
            <h3>{{detail.goodsName}}</h3>
            <p>x{{detail.goodsNum}}</p>
          </div>
        </div>
        <div class="Application_time">
          <p>申请时间</p>
          <span>{{detail.gmtCreate}}</span>
        </div>
        <div class="Application_time">
          <p>退款说明</p>
          <div class="Reimbursement">{{detail.afterReason}}</div>
        </div>
      </div>
      <div class="examine" v-if="detail.afterStatus != 0">
        <div class="Application_time">
          <p>审核时间</p>
          <span>{{detail.auditTime}}</span>
        </div>
        <div class="Application_time">
          <p>审核结果</p>
          <span>{{detail.afterStatus == 1 ? '已通过' 
            :detail.afterStatus == 2 ? '已拒绝' : item.afterStatus == 0 ? '待审核' : ''}}</span>
        </div>
        <div class="Application_time" v-if="detail.afterStatus == 2">
          <p>拒绝理由</p>
          <div class="Reimbursement">{{detail.refuseReason}}</div>
        </div>
      </div>
    </div>
    <div class="btn" v-if="detail.afterStatus == 2 || detail.afterStatus == 1">
      <div @click="delHandler">删除</div>
    </div>
  </div>
</template>

<script>
import { deleteOrderAfter } from '@/api'

export default {
  data () {
    return {

    }
  },
  computed: {
    detail () {
      return this.$store.state.main.orderafterDetail
    }
  },
  methods: {
        delHandler() {
            wx.showModal({
            content: '确认删除吗？',
            success: res => {
                if (res.confirm) {
                    deleteOrderAfter({id: this.$root.$mp.query.id}).then(res => {
                        if(res.data.code) {
                            wx.showToast({
                            title: res.data.msg, //提示的内容,
                            icon: 'none', //图标,
                            duration: 1000
                            })
                        } else {
                            setTimeout(() => {
                                wx.navigateBack({
                                    delta: 1
                                })
                            },500)
                        }
                    })            
                }
            }
            })
        },
      goDetail() {
          wx.navigateTo({ 
              url: `/pages/home/<USER>/classDetails/main?type=${this.detail.productType}&id=${this.detail.orderGoodsId}`
          })
      }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  onLoad () {
    this.$store.dispatch('main/getOrderafterDetail', {id: this.$root.$mp.query.id})
  }
}
</script>

<style lang="less" scoped>
  .container{
    width: 100%;
    height: 100vh;
    background: #FAFAFA;
    overflow: hidden;
    .btn {
    width: 375px;
    height: 48px;
    background: #fff;
    padding-top: 7px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0px;
    left: 0px;
    z-index: 999;
    div {
        font-size: 12px;
        color: #4A4A4A;
        width:72px;
        height:28px;
        line-height: 28px;
        text-align: center;
        border-radius:16px;
        border:1px solid rgba(74,74,74,1);
        margin-right: 10px;
        margin-top: 4px;
        float: right;
    }
  }
    .sales_content{
      width: 100%;
      .sales_top{
        width: 100%;
        height: 93px;
        position: relative;
        .sales_img{
          width: 100%;
          height: 93px;
          position: absolute;
          top: 0px;
          left: 0px;
        }
        .sales_title{
          width: 100%;
          position: absolute;
          top: 15px;
          left: 20px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          img{
            width: 15px;
            height: 15px;
            margin-right: 2px;
          }
          p{
            font-size: 15px;
            color: #fff;
          }
        }
        .sales_time{
          width: 100%;
          position: absolute;
          top: 47px;
          left: 20px;
          p{
            font-size: 11px;
            color: #FFFFFF;
            margin-bottom: 3px;
          }
        }
        .adopt_time{
          position: absolute;
          top: 44px;
          left: 20px;
          font-size: 14px;
          color: #FFFFFF;
        }
      }
      .refund{
        width: 100%;
        height: 51px;
        background: #FFF;
        display: flex;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;
        p{
          font-size: 14px;
          color: #7A7A7A;
          line-height: 51px;
        }
      }
      .sales_msg{
        width: 100%;
        margin-top: 10px;
        .details{
          width: 100%;
          background: #fff;
          display: flex;
          justify-content: space-between;
          padding: 10px 15px 15px 15px;
          box-sizing: border-box;
          img{
            width: 96px;
            height: 96px;
            border-radius: 4px;
          }
          .details_title{
            width: 240px;
            h3{
              width: 100%;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;  //设置两行溢出显示省略号
              overflow: hidden;
              font-size: 16px;
              color: #171717;
            }
            p{
              font-size: 14px;
              color: #615454;
              margin-top: 30px;
              float: right;
            }
          }
        }
        .Application_time{
          width: 100%;
          display: flex;
          justify-content: space-between;
          background: #fff;
          padding: 15px;
          box-sizing: border-box;
          position: relative;
          &::before{
            position: absolute;
            right: 0;
            top: 0;
            left: 0;
            height: 1px;
            content: '';
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
            background-color: #D6D6D6;
          }
          p{
            font-size: 14px;
            color: #4a4a4a;
          }
          span{
            display: block;
            color: #7A7A7A;
            font-size: 14px;
          }
          .Reimbursement {
            width: 266px;
            color: #7A7A7A;
            font-size: 14px;
            line-height: 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
        }
      }
      .examine{
        width: 100%;
        margin-top: 10px;
        .Application_time{
          width: 100%;
          display: flex;
          justify-content: space-between;
          background: #fff;
          padding: 15px;
          box-sizing: border-box;
          position: relative;
          &:last-child::before{
            height: 0;
          }
          &::before{
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            height: 1px;
            content: '';
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
            background-color: #D6D6D6;
          }
          p{
            font-size: 14px;
            color: #4a4a4a;
          }
          span{
            display: block;
            color: #7A7A7A;
            font-size: 14px;
          }
           .Reimbursement {
            width: 266px;
            color: #7A7A7A;
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
