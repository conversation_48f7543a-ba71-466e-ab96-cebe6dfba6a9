<!-- 拼团详情  暂不使用 -->
<template>
    <div class="container">
        <div class="section">
            <img :src="goods.goodsImg" alt="">
            <div class="right">
                <h6>{{goods.goodsName}}</h6>
                <p class="num">{{goods.teamNum}}人团 <span>￥{{goods.teamPrice}}</span></p>
            </div>
        </div>
        <div class="main">
            <ul class="head-list">
                <li v-for="(item, index) in goods.team" :key="index">
                    <img mode="aspectFill" :src="item.userImg" alt="">
                    <span v-if="item.isTeam" class="captain">团长</span>
                </li>  
                <li v-if="false">
                    <img mode="aspectFill" src="/static/images/icon-Unassembled-Group.png" alt="">
                </li> 
            </ul>
            <p class="tibs">还差{{goods.lessNum}}人团购成功，赶快召唤小伙伴来参团吧</p>
            <div class="time-box">
                <p class="time">剩余</p>
                <span>{{goods.endDateStr.HH}}</span>
                <p>:</p>
                <span>{{goods.endDateStr.mm}}</span>
                <p>:</p>
                <span>{{goods.endDateStr.ss}}</span>
            </div> 
        </div>
        <ul class="list">
            <li v-for="(item, index) in goods.team" :key="index">
                <div class="img-box">
                    <img mode="aspectFill" :src="item.userImg" alt="">
                    <span v-if="item.isTeam" class="captain">团长</span>
                </div>
                <p class="text">{{item.payTime}}开团</p>
            </li>
        </ul>
        <div class="btn">
            <button open-type="share" v-if="goods.textFlag && type==1">邀请好友参团</button>
            <button @click="goDetail" v-if="goods.textFlag && type!=1">参与拼团</button>
            <button v-if="!goods.textFlag">拼团已结束</button>
        </div>
    </div>
</template>

<script>
import { teamDetail } from '@/api'
export default {
    data () {
        return {
            id: '',
            type: '',
            goods: {
                endDateStr: {
                    HH: '00',
                    mm: '00',
                    ss: '00'
                }
            }
        }
    },
    computed: {
        teamDetail () {
            return this.$store.state.team.teamDetail
        }
    },
    onShareAppMessage(res) {
        return {
            title: '拼团详情',
            path: 'pages/main/assembles/clusterDetail/main?id=' + this.id
        }
    },
    methods: {
        goDetail() {
            wx.navigateTo({
                url: `/pages/home/<USER>/clusterGoods/orders/main?id=${this.id}&type=2`
            })
        },
        timer() {
            let that = this
            let endDateStr = {}
            let timer = setInterval(function(){
                let timestamp = Date.parse(new  Date())
                if(that.goods.endDateStr) {
                    var time = that.goods.endDate2  //取出时间
                    //时间差
                    let timeDifference = time - timestamp
                    if(timeDifference <= 0) {
                        endDateStr.HH = '00'
                        endDateStr.mm = '00'
                        endDateStr.ss = '00'
                        that.$set(that.goods,'textFlag',false)
                    } else {
                        //hour
                        let HH = parseInt( timeDifference / 3600000 )
                        if(HH < 10) {
                            HH = '0' + HH
                        }
                        //minute
                        let mm = parseInt( (timeDifference % 3600000) / 60000 )
                        if(mm < 10) {
                            mm = '0' + mm
                        }
                        //second
                        let ss = parseInt( (timeDifference % 3600000 % 60000) / 1000 )
                        if(ss < 10) {
                            ss = '0' + ss
                        }
                        endDateStr.HH = HH
                        endDateStr.mm = mm
                        endDateStr.ss = ss
                        that.$set(that.goods,'textFlag',true)
                    }
                    that.$set(that.goods,'endDateStr',endDateStr)
                }
            },1000)
        }
    },
    onLoad () {
        let id = this.$root.$mp.query.id
        this.id = this.$root.$mp.query.id
        teamDetail({id}).then(res => {
            if (res.data.code) {
                wx.showToast({ title: res.data.msg, icon:'none', duration: 1000})
            } else {
                this.goods = res.data
                this.type = res.data.type
                this.$set(this.goods,'textFlag',true)
                let obj = {
                    HH: '00',
                    mm: '00',
                    ss: '00'
                }
                this.$set(this.goods,'endDateStr', obj)
                this.timer()
            }
        })
    }
}
</script>

<style lang="less">
.container {
  .section {
    width: 100%;
    padding: 10px 15px 12px;
    box-sizing: border-box;
    margin-bottom: 10px;
    background: #fff;
    display: flex;
    align-items: center;
    img {
      width: 96px;
      height: 96px;
      border-radius: 4px;
      margin-right: 10px;
    }
    .right {
      h6 {
        width: 100%;
        font-size: 16px;
        color: #171717;
        line-height: 22px;
        margin-bottom: 30px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .num {
        font-size: 14px;
        line-height: 20px;
        color: #343333;
        span {
          color: #FF5100;
        }
      }
    }
  }
  .main {
    width: 100%;
    margin-bottom: 10px;
    padding: 20px 0;
    box-sizing: border-box;
    background: #fff;
    .head-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 19px;
      li {
        width: 50px;
        height: 50px;
        position: relative;
        margin-right: 20px;
        &:last-child{
          margin-right: 0;
        }
        img {
          width: 50px;
          height: 50px;
          border-radius: 50%;
        }
        .captain{
          position: absolute;
          bottom: 0;
          right: -13px;
          width: 32px;
          height: 14px;
          line-height: 14px;
          background:rgba(254,124,60,1);
          border-radius:7px;
          font-size: 10px;
          color: #fff;
          text-align: center;
        }
      }
    }
    .tibs {
      font-size: 13px;
      line-height: 18px;
      color: #343333;
      text-align: center;
      margin-bottom: 20px;
    }
    .time-box {
      text-align: center;
      font-size: 12px;
      line-height: 17px;
      color: #343333;
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        margin: 0 5px;
      }
      .time {
        margin: 0 10px 0 0;
      }
      span {
        padding: 0 8px;
        box-sizing: border-box;
        height: 17px;
        color: #fff;
        background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
        border-radius:4px;
      }
    }
  }
  .list {
    margin-bottom: 52px;
    li {
      width: 100%;
      height: 50px;
      padding: 0 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      background: #fff;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #EDEBEB;
      }
      &:last-child::after{
        background-color: transparent;
      }
      .img-box {
        position: relative;
        img {
          width: 36px;
          height: 36px;
          border-radius: 50%;
        }
        .captain{
          position: absolute;
          bottom: 0;
          right: -13px;
          width: 32px;
          height: 14px;
          line-height: 14px;
          background:rgba(254,124,60,1);
          border-radius:7px;
          font-size: 10px;
          color: #fff;
          text-align: center;
        }
      }
      .text {
        font-size: 13px;
        color: #4A4A4A;
        line-height: 18px;
      }
    }
  }
  .btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 15px 12px;
    box-sizing: border-box;
    button {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      color: #fff;
      background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      border-radius:22px;
    }
  }
}
</style>
