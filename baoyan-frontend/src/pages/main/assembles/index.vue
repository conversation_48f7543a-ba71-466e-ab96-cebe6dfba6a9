<!-- 我的拼团 -->
<template>
    <div class="container">
        <div class="main">
            <div class="section" v-for="(item,index) in dataList" :key="index" @click="goDetail(item.id)">
                <p class="tibs" v-if="item.orderStatus==1">拼团成功</p>
                <p class="tibs" v-else-if="item.orderStatus==2">拼团失败</p>
                <p class="tibs" v-else>还差{{item.lessNum }}人，剩余时间 {{item.endDateStr}}</p>
                <div class="content">
                    <img mode="aspectFill" :src="item.goodsImg" alt="">
                    <div class="right">
                        <p class="title">{{item.teamName}}</p>
                        <div class="num">
                            <p class="many">￥{{item.teamPrice}}</p>
                            <p>{{item.teamNum}}人成团</p>
                        </div>
                    </div>
                </div>
                <div class="btn">
                    <button>查看详情</button>
                </div>
            </div>
            <div v-if="!dataList.length" class="noneData">没有更多数据啦...</div>
        </div>
    </div>
</template>

<script>
import { presonTeam } from '@/api'

export default {
    data () {
        return {
            timer: null,
            dataList: [],
            pageIndex: 1,
            pageSize: 2000
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        getList() {
            let param = {
                pageIndex: this.pageIndex,
                pageSize: this.pageSize
            }

            presonTeam(param).then(res => {
                if (res.data.code) {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1000})
                } else {
                    this.dataList = res.data.list
                    if(this.dataList.length && this.dataList) {
                        this.dataList.filter((item,index) => {
                            this.$set(item,'textFlag',true)
                            this.$set(item,'endDateStr','00:00:00')
                        })
                        this.timerHandler()
                    }
                }
            })
        },
        goDetail(id) {
            wx.navigateTo({ 
                url: '/pages/home/<USER>/clusterGoods/collaging/partakeCluster/main?id=' + id 
            })
        },
        timerHandler() {
            let that = this
            let endDateStr = ''
            this.timer = setInterval(function(){
                let timestamp = Date.parse(new  Date())
                if(that.dataList.length && that.dataList) {
                    for(let i=0;i<that.dataList.length;i++) {
                        var time = that.dataList[i].endDate2  //取出时间
                        //时间差
                        let timeDifference = time - timestamp
                        if(timeDifference <= 0) {
                            endDateStr = '00:00:00'
                            that.$set(that.dataList[i],'textFlag',false)
                        } else {
                            //hour
                            let HH = parseInt( timeDifference / 3600000 )
                            if(HH < 10) {
                                HH = '0' + HH
                            }
                            //minute
                            let mm = parseInt( (timeDifference % 3600000) / 60000 )
                            if(mm < 10) {
                                mm = '0' + mm
                            }
                            //second
                            let ss = parseInt( (timeDifference % 3600000 % 60000) / 1000 )
                            if(ss < 10) {
                                ss = '0' + ss
                            }
                            endDateStr = `${HH}:${mm}:${ss}`
                        }
                        that.$set(that.dataList[i],'endDateStr',endDateStr)
                    }
                }
            },1000)
        }
    },
    onUnload() {
        clearInterval(this.timer)
    },
    onShow () {
        this.getList()
    }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 10px 10px 0;
    box-sizing: border-box;
    .section {
      width: 100%;
      background: #fff;
      padding: 12px 10px;
      box-sizing: border-box;
      border-radius: 16px;
      margin-bottom: 12px;
      .tibs {
        width: 100%;
        overflow: hidden;
        font-size: 13px;
        color: #4A4A4A;
        line-height: 18px;
      }
      .content {
        width: 100%;
        height: 102px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        img {
          width: 60px;
          height: 60px;
          border-radius: 4px;
        }
        .right {
          width: 263px;
          .title {
            width: 100%;
            font-size: 14px;
            color: #171717;
            line-height: 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          .num {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            color: #9B9B9B;
            line-height: 20px;
            .many {
              color: #FF5100
            }
          }
        }
      }
      .btn {
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        button {
          width: 72px;
          height: 28px;
          line-height: 28px;
          padding: 0;
          text-align: center;
          border: 1rpx solid #4A4A4A;
          border-radius: 16px;
          background: #fff;
          font-size: 12px;
          color: #4A4A4A;
        }
      }
    }
  }
}
</style>
