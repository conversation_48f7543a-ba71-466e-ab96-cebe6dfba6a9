<template>
  <div class="container">
    <div class="integral_top">
      <div class="integral">
        <img src="/static/images/icon-integral-back.png" alt class="integral_img" />
        <div class="integral_content">
          <p>可用积分</p>
          <h3>{{integral.nowPoint ? integral.nowPoint : 0}}</h3>
          <div class="integral_msg">
            <!-- <div class="Stand">
              <span>待到账积分</span>
              <p>{{integral.bePoint ? integral.bePoint : 0}}</p>
            </div> -->
            <div class="Stand">
              <span>累计积分</span>
              <p>{{integral.historyPoint ? integral.historyPoint : 0}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ul class="list" v-for="(item, index) in integral.logList" :key="index">
      <li>
        <div class="type-box">
          <p class="type">{{item.changeType == 1 ? '增加' : item.changeType == 2 ? '减少' : ''}}</p>
          <p class="time">{{item.gmtCreate}}</p>
        </div>
        <div class="tibs-box">
          <p class="tibs">{{item.changeReason}}</p>
          <p class="num" :class="item.changeType == 1 ? 'reduce2' : 'reduce1'">{{item.changeType == 1 ? '+' : '-'}} {{item.changeNum}}</p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data () {
    return {

    }
  },
  computed: {
    integral () {
      return  this.$store.state.main.integral
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  onShow () {
    this.$store.dispatch('main/getIntegral', {})
  }
};
</script>

<style lang="less" scoped>
.container {
  .integral_top {
    width: 100%;
    height: 204px;
    background: #fff;
    margin-bottom: 10px;
    .integral {
      width: 100%;
      height: 204px;
      text-align: center;
      position: relative;
      background: #fff;
      padding: 24px 15px 0;
      box-sizing: border-box;
      .integral_img {
        width: 345px;
        height: 150px;
      }
      .integral_content {
        width: 345px;
        position: absolute;
        top: 24px;
        left: 50%;
        transform: translate(-50%, 0);
        padding-top: 29px;
        box-sizing: border-box;
        p {
          font-size: 13px;
          color: #ffffff;
          margin-bottom: 6px;
          line-height: 18px;
        }
        h3 {
          font-size: 34px;
          line-height: 48px;
          color: #fff;
          margin-bottom: 19px;
        }
        .integral_msg {
          display: flex;
          justify-content: center;
          padding: 0 33px 0;
          box-sizing: border-box;
          .Stand {
            display: flex;
            justify-content: flex-start;
            span {
              font-size: 13px;
              color: #fff;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
  .list {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    li {
      padding: 15px 0 14px;
      box-sizing: border-box;
      position: relative;
      &::before{
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #DEDEDE;
      }
      &:last-child::before{
        background-color: transparent;
      }
      .type-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .type {
          font-size: 14px;
          color: #2A2A2A;
          line-height: 20px;
        }
        .time {
          font-size: 12px;
          color: #6C6C6C;
          line-height: 17px;
        }
      }
      .tibs-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .tibs {
          font-size: 12px;
          color: #6C6C6C;
          line-height: 17px;
        }
        .num {
          font-size: 14px;
          color: #ED450C;
          line-height: 20px;
        }
        .reduce1 {
          color: #42D4CC;
        }
        .reduce2 {
          color: #ED450C;
        }
      }
    }
  }
}
</style>
