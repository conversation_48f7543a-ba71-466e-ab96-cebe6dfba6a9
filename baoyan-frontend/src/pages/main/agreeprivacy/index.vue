<template>
    <div class="container">
      <wxParse v-if="info.text" :content="info.text"/> 
      <!-- <div>{{text}}</div> -->
    </div>
</template>
<script>
import { privacy } from "@/api";
import wxParse from 'mpvue-wxparse'
export default {
    components:{
        wxParse
    },
  data () {
    return {
      info: {
        code: null,
        text:''
      }
    }
  },
    onLoad(){
                      privacy().then(res=>{
                        if (res.data) {
                            this.info.text=res.data
                        }
                      })
    }
}
</script>
<style lang="less">
.container {
    padding: 15px;
    box-sizing: border-box;
    .mt-20 {
        margin-top: 15px;
    }
}
</style>
