<template>
    <div class="container">
        <img class="bg" src="/static/images/codebj.png">
        <div class="content" v-for="(item,index) in dataList" :key="index">
            <div class="title">
                <img class="img" src="/static/images/first.png" alt="">
                <p>{{item.goodsName}}</p>
            </div>
            <div class="listBox">
                <div class="list">
                    <div class="imgBox">
                        <img class="kuang" src="/static/images/kuang.png" alt="">
                        <img class="code" :src="item.code" alt="">
                    </div>
                    <p>{{item.writeOffName}}</p>
                    <p>可用次数：{{item.surplusNum}}/{{item.totalNum}}</p>
                    <p>有效期：{{item.expiryDate}} ~ {{item.endDate}}</p>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import { codeList,getUser } from '@/api'
export default {
    data () {
        return {
            dataList: [],
            orderNo: '',
            type: '',
            sotk:'',
            id:[],
            index:0,
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        webSocketStart(e){
            getUser().then(res => {
                if(res) {
                    // let wsbasePath = `${process.env.baseURL}/real/user/${res.data.id}/cancel`;
                    // let wsbasePath = `${process.env.socktURl}/real/user`;
                    // let wsbasePath = `${process.env.socktURl}/wss/${res.data.id}`;
                    let wsbasePath = `ws://localhost:10169/api/webSocket/${res.data.id}`;
					//let wsbasePath = `${process.env.socktURl}/wss/${res.data.id}`;
                    // let wsbasePath = `wss://wx.byloft.net/wss/${res.data.id}`;
                    this.sotk = wx.connectSocket({
                        url: wsbasePath,
                        header: { 'content-type':'application/x-www-form-urlencoded' },
                        method: "POST",
                        success: res => {
                            console.log('小程序连接成功11：', res);
                            console.log(res)
                        },
                        fail: err => {
                            console.log('出现错误啦！！' + err);
                            console.log(err)
                        }
                    })
                    this.sotk.onMessage(onMessage => {
                        var data = JSON.parse(onMessage.data);
                        console.log(data)
                        console.log(this.id.indexOf(data))
                        if(this.id.indexOf(data)>=0){
                            this.playmus()
                        }
                    })
                    this.sotk.onError(onError => {
                        this.index++
                        console.log('监听 WebSocket 错误。错误信息', onError)
                        if(this.index>3){
                            return false
                        }
                        this.webSocketStart()
                    })
                }
            })
        },
        //播放提示音
        playmus(){
            var myaudio = wx.createInnerAudioContext({});
            myaudio.src ='/static/isok.m4a'
            myaudio.play()
        },
        getData() {
            codeList({orderNo:this.orderNo,type:this.type}).then(res => {
                if(res.data) {
                    this.dataList = res.data || []
                    this.dataList.forEach(item => {
                        this.id.push(item.id)
                    });
                }
            })
        }
    },
    onHide(){
        this.sotk.close()
    },
    onLoad() {
        this.orderNo = this.$root.$mp.query.orderNo
        this.type = this.$root.$mp.query.type
        this.id=[]
        this.index=0
        this.getData()
        this.webSocketStart()
    }
}
</script>

<style lang="less" scoped>
.container {
    width: 100%;
    height: 100%;
    padding: 15px;
    .content {
        position: relative;
        z-index: 2;
        text-align: center;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        margin-bottom: 15px;
        .listBox {
            background: #FEF7E9;
            padding-bottom: 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            .list {
                padding: 30px 0;
                border-bottom: 1px solid #ED813A;
                width: 303px;
                margin: 0 auto;
                margin-bottom: 10px;
                p:nth-of-type(2) {
                    margin-top: 10px;
                }
                p:nth-of-type(3) {
                    font-size: 12px;
                    color: #FF5100;
                    margin: 8px 0;
                }
                p:nth-of-type(4) {
                    font-size: 12px;
                    color: #9B9B9B;
                }
            }
            .list:last-child {
                border-bottom: 0;
            }
            .imgBox {
                width: 188px;
                height: 184px;
                margin: 0 auto;
                position: relative;
                .kuang {
                    width: 188px;
                    height: 184px;
                }
                .code {
                    width: 160px;
                    height: 160px;
                    position: absolute;
                    left: 15px;
                    top: 12px;
                }
            }
        }
        .title {
            width: 100%;
            height: 59px;
            .img {
                width: 373px;
                height: 89px;
                position: absolute;
                left: -14px;
                top: -15px;
            }
            p {
                position: relative;
                border-bottom: 1px solid #ED813A;
                padding: 13px 0;
                width: 303px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin: 0 auto;
            }
        }
    }
    .bg {
        width: 100%;
        height: 100%;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 1;
    }
}
</style>
