<template>
    <div class="Myorder">
        <ul class="regimen_title">
        <li
            v-for="(item, index) in groupList"
            :key="index"
            :class="{active : cardBag == index}"
            @click="cardTab(index)"
        >{{item.txt}}</li>
        </ul>
        <ul class="Myorder_content">
        <!-- <scroll-view class="scrollView" :style="{height: scrollViewHeight}" :lower-threshold="100" @scrolltolower="loadMore" scroll-y> -->
            <li v-for="(item, index) in orderList" :key="index">
            <div class="Numbers">
                <span>{{item.orderNo}}</span>
                <!-- 订单状态 '订单状态:1-待付款,2-已付款,3-已完成,4-已关闭'	 -->
                <div class="paid">{{item.orderStatus == 1 ? '待支付'
                :item.orderStatus == 2&&((item.isPresenter===1&&item.isCurrent===1)||item.isPresenter===0) ? '待核销'
                :item.orderStatus == 2&&item.isPresenter==1&&item.isCurrent===0 ? '已赠送'
                :item.orderStatus == 3 ? '已完成' 
                :item.orderStatus == 4 ? '已关闭' : '已取消'}}</div>
            </div>
            <div class="commodity" v-for="(opt, id) in item.orderGoodsList" :key="id" @click.stop="getDetail(item)">
                <img mode="aspectFill" :src="opt.goodsImg" alt="">
                <div class="commodity_content">
                <h3><span v-if="item.isCurrent==0">[赠]</span>{{opt.goodsName}}</h3>
                <div class="price">
                    <!-- <img src="/static/images/price-count.png" alt="">  -->
                    <span>¥{{opt.goodsPrice}}</span>
                    <p>x {{opt.goodsNum}}</p>
                </div>
                </div>
            </div>
            <div class="Total">
                <p>共{{item.allGoodsNum}}件，总计：￥{{item.actualAmount}}　</p>
                <div class="btn">
                <!-- 订单状态 '订单状态:1-待付款,2-已付款,3-已完成,4-已关闭'	 -->
                <div class="cancel" v-if="item.orderStatus == 1" @click.stop="queryCancel(item.id)">取消订单</div>
                <div class="cancel" v-if="item.orderStatus == 2&&((item.isPresenter===1&&item.isCurrent===1)||item.isPresenter===0)" @click.stop="tabPage(item, 1)">查看核销码</div>
                <!-- <div class="cancel" v-if="item.orderStatus == 3 && !item.evalType" @click.stop="tabPage(item, 2)">评价</div> -->
                <div class="cancel" v-if="item.orderStatus == 4" @click.stop="deleteOder(item.id)">删除订单</div>
                <form class="payment" @submit="orderPay(item)" report-submit='true' v-if="item.orderStatus == 1">
                    <button form-type="submit">支付</button>
                </form>
                </div>
            </div>
            </li>
            <div v-if="!orderList.length" class="noneData">没有更多数据啦...</div>
        <!-- </scroll-view> -->
        </ul>
    </div>
</template>

<script>
console.log('我的订单========')
import toast from '@/plugins/toast'
import { cancelOrder, getOrderList, delOrder, shoppingPay} from '@/api'
// 订单状态 '订单状态:1-待付款,2-已付款,3-已完成,4-已关闭'	
export default {
    data() {
        return {
            refresh: true,
            groupList: [
                { txt: "全部"},
                { txt: "待支付"},
                { txt: "待核销"},
                { txt: "已完成"}
            ],
            cardBag: 0,
            page: 1,
            pageSize: 10,
            noService: false,
            orderList: [],
            isLastPage: true
        }
    },
    computed: {
        scrollViewHeight () {
            if (this.$store.state.windowHeight) {
                return `${this.$store.state.windowHeight - 60}px`
            }
            return '100%'
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onReachBottom() {
        if (this.noService || this.isLastPage) {
            return
        }
        this.page ++
        this.getinfo()
    },
    methods: {
        loadMore () {
            if (this.noService || this.isLastPage) {
                return
            }
            this.page ++
            this.getinfo()
        },
        orderPay(info) {
            let that = this
            let param = {
                type: 3,	
                id: info.id
            }
            shoppingPay(param).then(res => {
                if (res.data.code) {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
                } else {
                    wx.requestPayment({
                        timeStamp: res.data.pay.timeStamp,
                        nonceStr: res.data.pay.nonceStr,
                        package: res.data.pay.pkg,
                        signType: res.data.pay.signType,
                        paySign: res.data.pay.paySign,
                        success() {
                            that.cardBag = 1
                            that.orderList = []
                            that.getinfo()
                        },
                        fail() {
                            wx.showToast({ title: '支付失败', icon:'none', duration: 1500})
                        }
                    })             
                }
            })
        },
        // tab切换
        cardTab(index) {
            this.orderList = []
            this.cardBag = index;
            this.page = 1
            this.noService = false
            // this.$store.commit('order/RECORD_ORDERLIST', [])
            this.getinfo()
        },
        // 订单列表
        getinfo () {
            let params = {
                pageIndex: this.page,
                pageSize: this.pageSize
            }
            if (this.cardBag === 0) {
                params.orderStatus = ''
            } else {
                params.orderStatus = this.cardBag
            }
            getOrderList(params).then(res => {
                if (res.data.code) {
                    this.orderList = []
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
                } else {
                    this.orderList = this.orderList.concat(res.data.list)
                    this.isLastPage = res.data.isLastPage
                }
            })
        },
        // 跳转订单详情
        getDetail (item) {
            wx.navigateTo({
                url: `/pages/main/Order/orderDetails/main?id=${item.id}`,
                success: () => {
                    this.refresh = false
                }
            })
        },
        // 取消订单
        queryCancel (id) {
            let that = this
            wx.showModal({
                title: '提示',
                content: '确认取消订单吗？',
                success(res) {
                    if (res.confirm) {
                        cancelOrder({id}).then(res => {
                            if(!res.data) {
                                toast.text('取消成功')
                                setTimeout(() => {
                                    that.orderList = []
                                    that.getinfo()
                                },500)
                            } else {
                                toast.text(res.data.msg)
                            }
                        })
                    }
                }
            })
        },
        // 删除订单
        deleteOder (id) {
            let that = this
            wx.showModal({
                title: '提示',
                content: '确认删除订单吗？',
                success(res) {
                    if (res.confirm) {
                        delOrder({id}).then(res => {
                            if(!res.data) {
                                toast.text('删除成功')
                                setTimeout(() => {
                                    that.orderList = []
                                    that.getinfo()
                                },500)
                            } else {
                                toast.text(res.data.msg)
                            }
                        })
                    }
                }
            })
        },
        // 评价/查看核销码
        tabPage (item, e) {
            let url 
            if (e == 1) { //核销码
                url = `/pages/main/Order/QRcodeList/main?type=0&orderNo=${item.orderNo}`
            } else {
                // return
                url = `/pages/main/Order/orderDetails/evaluate/main?id=${item.id}&delta=1`
            }
            wx.navigateTo({
                url: url,
                success: () => {
                    this.refresh = false
                }
            })
        }
    },
    onShow() {
        if (!this.refresh) return
        this.orderList = []
        this.$store.commit('getWindowHeight')
        this.page = 1
        this.noService = false
        // this.$store.commit('order/RECORD_ORDERLIST', [])
        this.getinfo()
        this.refresh = true
    },
    onLoad() {
        this.refresh = true
        this.orderList = []
        // this.cardBag = 0
        if(this.$root.$mp.query) {
            this.cardBag = this.$root.$mp.query.type == 0 ? '' : this.$root.$mp.query.type
        }
        if(this.$root.$mp.query.tip) {
            toast.text(this.$root.$mp.query.tip)
        }
        // this.getinfo()
    }
}
</script>

<style lang="less" scoped>
.Myorder {
  width: 100%;
  min-height: calc(100vh - 50px);
  background: #fafafa;
  padding-top: 50px;
//   overflow: hidden;
  ::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
    }
  .regimen_title {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;
    height: 49px;
    background: #fff;
    li {
      height: 49px;
      line-height: 49px;
      font-size: 13px;
      font-weight:500;
      color: #9b9b9b;
    }
    .active {
      border-bottom: 2px solid #ED813A;
      color: #ED813A;
    }
  }
  .Myorder_content{
    width: 100%;
    background: #fafafa;
    padding: 10px;
    box-sizing: border-box;
    li{
      width: 100%;
      background: #fff;
      padding: 12px 10px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border-radius: 6px;
      .Numbers{
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 21px;
        span{
          font-size: 13px;
          color: #4A4A4A;
        }
        .paid{
          font-size: 13px;
          color: #FF5100;
        }
      }
      .commodity{
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 11px;
        img{
          width: 60px;
          height: 60px;
          border-radius: 4px;
        }
        .commodity_content{
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          width: 78%;
          h3{
            width: 263px;
            font-size: 14px;
            color: #171717;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;  //设置两行溢出显示省略号
            overflow: hidden;
          }
          .price{
            width: 100%;
            display: flex;
            justify-content: space-between;
            span{
              font-size: 14px;
              color: #FF5100;
            }
            p{
              display: flex;
              font-size: 14px;
              color: #090203;
              align-items: center;
              img {
                  width: 8px;
                  height: 8px;
              }
            }
          }
        }
      }
      .Total{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 21px;
        p{
          font-size: 13px;
          color: #9B9B9B;
        }
        .btn{
          display: flex;
          justify-content: flex-start;
          div {
            min-width: 72px;
            height: 28px;
            line-height: 28px;
            padding: 0 12px;
            box-sizing: border-box;
            margin-left: 10px;
            &:first-child {
              margin: 0
            }
          }
          .cancel{
            box-sizing: border-box;
            text-align: center;
            border-radius:16px;
            font-size: 12px;
            border:1px solid rgba(74,74,74,1);
          }
          .payment{
            height: 28px;
            min-width: 72px;
            box-sizing: border-box;
            text-align: center;
            border-radius:16px;
            border:1px solid rgba(255,81,0,1);
            font-size: 12px;
            margin-left: 10px;
            button {
                color: #FF5100;
                height: 28px;
                line-height: 28px;
                padding: 0;
                margin: 0;
                font-size: 12px;
                background: transparent;
            }
          }
        }
      }
    }
  }
}
</style>
