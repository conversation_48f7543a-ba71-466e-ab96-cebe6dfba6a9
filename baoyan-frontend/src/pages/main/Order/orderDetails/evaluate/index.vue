<template>
    <div class="container">
        <div class="main">
            <div class="section" v-for="(item, index) in info" :key="index">
                <div class="title">
                    <img class="headimg" mode="aspectFill" :src="item.goodsImg" alt="">
                    <p class="text">评价星级：</p>
                    <div class="starlist">
                        <img v-for="(opt, i) in 5" :key="i" @click.stop="rateStar(index, i+1)" mode="aspectFit" :src="'/static/images/'+(item.mark >= i+1 ? 'icon-star-selected' : 'icon-star-select')+'.png'" alt="">
                    </div>
                </div>
                <div class="content">
                    <textarea v-model="item.evaluateText" placeholder="输入评价内容，不超过300字" maxlength="300"></textarea>
                    <ul class="imglist">
                        <li @click="uploadImages(index)" v-if="item.imgArr.length < 4">
                            <img src="/static/images/icon-add-img.png" alt="">
                        </li>
                        <li v-for="(obj, index2) in item.imgArr" :key="index2">
                            <img :src="obj" alt="">
                            <img @click="deleteImg(item.imgArr,index2)" class="delete" src="/static/images/icon-delete-img.png" alt="">
                        </li>
                    </ul>
                </div>
                <div class="type">
                    <div class="tibs" @click="getType(index)">
                        <img mode="aspectFit" :src="'/static/images/'+(item.isAnonymous ? 'icon-selected' : 'icon-select')+'.png'" alt="">
                        <p class="tibs-text">匿名发布</p>
                    </div>
                    <p class="point">你写的评价会以匿名的形式展现</p>
                </div>
            </div>
        </div>
        <div class="footer">
            <button @click="commit">发布</button>
        </div>
    </div>
</template>

<script>
console.log('评价==========')
import { getossToken, evalOrder, getOrderDetail, teamEvalTeam } from '@/api'
import toast from '@/plugins/toast'
import { setTimeout } from 'timers';
export default {
    data () {
        return {
            id: '',
            orderType: '',
            delta: 1,
            info: [],
            postData: {},
            uploadUrl: 'https://baoyan.oss-cn-shanghai.aliyuncs.com/'
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        // 打分
        rateStar (index, num) {
            this.info[index].mark = num
        },
    //  上传图片
        async postDataFun(state) {
            await getossToken().then(res => {
                var postData = {}
                    postData["OSSAccessKeyId"] = res.data.accessid
                    postData["policy"] = res.data.policy
                    postData["Signature"] = res.data.signature
                    postData["stringKey"] = res.data.dir
                    postData["expire"] = res.data.expire
                    postData["fileName"] = "file"
                    this.postData = postData
            })
        },
        randomString (len) {
        　　len = len || 32
        　　var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'  /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
        　　var maxPos = $chars.length
        　　var pwd = ''
        　　for (var i = 0; i < len; i++) {
        　　　　pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
        　　}
        　　return pwd
        },
        uploadImages (e) {
            var _this = this
            wx.chooseImage({
                count: 9, // 默认9
                success: function (res) {
                    wx.showLoading({
                        title: '上传中'
                    })
                    // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                    // const tempFilePaths = res.tempFilePaths[0]
                    const tempFilePaths = res.tempFilePaths
                    let data = _this.postData
                    data.success_action_status = 200
                    _this.wxUploadFile(0, tempFilePaths.length, data, tempFilePaths, e)
                }
            })
        },
        wxUploadFile(i, len, data, tempFilePaths, e) {
            let _this = this
            data.key = data['stringKey'] + this.randomString(36)

            wx.uploadFile({
                url: _this.uploadUrl,
                filePath: tempFilePaths[i],
                name: 'file',
                formData: data,
                success: function (res) {   
                    wx.hideLoading()
                    _this.info[e].imgArr.push(_this.uploadUrl + data.key)
                    _this.info[e].img = _this.info[e].imgArr.join(',')
                    if (++i < len) {
                        _this.wxUploadFile(i, len, data, tempFilePaths, e)
                    } else {
                        wx.hideLoading()
                    }
                }
            })
        },
        //  删除图片
        deleteImg (obj,index) {
            obj.splice(index,1)
        },
        // 是否匿名
        getType (index) {
            this.info[index].isAnonymous = !this.info[index].isAnonymous
        },
        // 发布评价
        commit () {        
            for (let i=0;i<this.info.length;i++) {
                if (this.info[i].evaluateText == '') {
                    toast.text('请填写评价内容')
                    return
                }
            }
            if (this.orderType == 1) { 
                let param = this.info[0]
                teamEvalTeam(param).then(res => {
                    if (res.data.code) {
                        toast.text(res.data.msg)
                    } else {
                        toast.text('发布成功')
                        setTimeout(() => {
                            wx.redirectTo({ 
                                url: `/pages/main/Order/orderDetails/evasuccess/main?commentIntegral=${res.data.commentIntegral}&delta=${this.delta}` 
                            })
                        },500)
                    }
                })
            } else {
                evalOrder({evaluateInfoList: this.info}).then(res => {
                    if (res.data.code) {
                        toast.text(res.data.msg)
                    } else {
                        toast.text('发布成功')
                        setTimeout(() => {
                            wx.redirectTo({ 
                                url: `/pages/main/Order/orderDetails/evasuccess/main?commentIntegral=${res.data.commentIntegral}&delta=${this.delta}` 
                            })
                        },500)
                    }
                })
            }
        },
        getDetail() {
            if (this.orderType == 1) {
                let teamObj = wx.getStorageSync('teamObj');
                this.info.push({
                    goodsImg: teamObj.goodsImg,
                    orderId: teamObj.orderId,
                    resouceType: 4,
                    orderNo: teamObj.orderNo,
                    evaluateText: '',
                    isAnonymous: false,
                    imgArr: [],
                    img: '',
                    mark: 0
                })
            } else {
                this.$store.dispatch('order/getOrderDetail', {id: this.id}).then(res => {
                    this.info = []
                    res.goodsList.map((item, index) => {
                        if (!item.refundNum) {
                            this.info.push({
                                detailId: item.id,
                                goodsImg: item.goodsImg,
                                resouceType: item.productType,
                                orderNo: res.orderInfo.orderNo,
                                goodsId: item.goodsId,
                                evaluateText: '',
                                isAnonymous: false,
                                imgArr: [],
                                img: '',
                                mark: 0
                            })
                        }
                    })
                })
            }
        }
    },
    onLoad () {
        this.info = []
        this.id = this.$root.$mp.query.id
        this.orderType = this.$root.$mp.query.orderType
        this.delta = this.$root.$mp.query.delta
        this.getDetail()
    },
    mounted() {
        this.postDataFun()
    }
}
</script>

<style lang="less">
.container {
    .main {
        padding-bottom: 57px;
        .section {
            width: 100%;
            background: #fff;
            margin-bottom: 8px;
            .title {
                width: 100%;
                height: 59px;
                padding: 0 15px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                position: relative;
                &::before {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    height: 1px;
                    content: "";
                    -webkit-transform: scaleY(0.5);
                    transform: scaleY(0.5);
                    background-color: #D6D6D6;
                }
                .headimg {
                    width: 40px;
                    height: 40px;
                    margin-right: 10px;
                }
                .text {
                    font-size: 13px;
                    color: #343333;
                    line-height: 18px;
                }
                .starlist {
                    display: flex;
                    align-items: center;
                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 15px;
                }
            }
        }
        .content {
            width: 100%;
            padding: 15px;
            box-sizing: border-box;
            textarea {
            width: 100%;
            height: 80px;
            margin-bottom: 7px;
            font-size: 13px;
            color: #9B9B9B;
            line-height: 18px;
            }
            .imglist {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            li {
                width: 78px;
                height: 78px;
                margin-right: 5px;
                margin-top: 5px;
                position: relative;
                &:last-child{
                margin-right: 0;
                }
                img {
                width: 78px;
                height: 78px;
                }
                .delete {
                position: absolute;
                right: 0;
                top: 0;
                width: 16px;
                height: 16px;
                }
            }
            }
        }
        .type {
            width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
            height: 49px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            &::before {
                position: absolute;
                right: 0;
                top: 0;
                left: 0;
                height: 1px;
                content: "";
                -webkit-transform: scaleY(0.5);
                transform: scaleY(0.5);
                background-color: #D6D6D6;
            }
            .tibs {
                display: flex;
                align-items: center;
                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 8px;
                }
                .tibs-text {
                    font-size: 13px;
                    line-height: 18px;
                    color: #4A4A4A;
                }
            }
                .point {
                    font-size: 13px;
                    color: #9B9B9B;
                    line-height: 18px;
                }
            }
        }
    }
    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 57px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        &::before {
        position: absolute;
        right: 0;
        top: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #D6D6D6;
        }
        button {
        width: 345px;
        height: 40px;
        line-height: 40px;
        padding: 0;
        text-align: center;
        background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
        border-radius:22px;
        font-size: 16px;
        color: #fff;
        }
    }
}
</style>
