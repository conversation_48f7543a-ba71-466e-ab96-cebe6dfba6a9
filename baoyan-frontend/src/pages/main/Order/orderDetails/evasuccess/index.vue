<template>
    <div class="container">
        <div class="main">
            <img mode="aspectFit" src="/static/images/icon-success.png" alt="">
            <p class="success">评价成功</p>
            <p class="tibs">恭喜您获得{{commentIntegral}}积分！</p>
        </div>
        <div class="btn">
            <button @click="goback">返回订单</button>
        </div>
    </div>
</template>

<script>
export default {
    data () {
        return {
            logs: [],
            commentIntegral: 0,
            delta: 1
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        goback () {
            let pageData = getCurrentPages()
            if (this.delta == 1) {
                wx.navigateBack({
                    delta: 1
                })
                let prevPage = pageData[pageData.length - 2]
                prevPage.onLoad()
            }
            if (this.delta == 2) {
                wx.navigateBack({
                    delta: 2
                })
                let prevPage = pageData[pageData.length - 3]
                prevPage.onLoad()
            }
        },
    },
    onLoad() {
        this.commentIntegral = this.$root.$mp.query.commentIntegral
        this.delta = this.$root.$mp.query.delta
    }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 50px 0 50px;
    box-sizing: border-box;
    text-align: center;
    font-size: 18px;
    color: #4A4A4A;
    line-height: 25px;
    img {
      width: 72px;
      height: 88px;
      margin-bottom: 13px;
    }
    .tibs {
      font-size: 14px;
      color: #9B9B9B;
      line-height: 20px;
      margin-top: 9px;
    }
  }
  .btn {
    width: 100%;
    // padding: 0 52px;
    // box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    button {
      width: 120px;
      height: 40px;
      line-height: 40px;
      border-radius: 22px;
      border: 1px solid #ED813A;
      background: transparent;
      padding: 0;
      font-size: 16px;
      color: #FE743D;
      box-sizing: border-box;
    }
  }
}
</style>
