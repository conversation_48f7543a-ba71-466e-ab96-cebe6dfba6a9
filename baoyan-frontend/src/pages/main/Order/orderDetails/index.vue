<template>
  <div class="container">
    <div class="Orderdetails_content">
      <div class="Orderdetails_top">
        <h3>商品清单</h3>
        <div class="item">
          <div v-for="(item, index) in goodsList" :key="index">
            <div class="commodity" @click="goCommodityDetail(item.goodsId,item.productType,item.teamType)">
              <img mode="aspectFill" :src="item.goodsImg" alt />
              <div class="commodity_content">
                <h4> <span v-if="orderInfo.isCurrent===0">[赠]</span>{{item.goodsName}}</h4>
                <div class="price">
                  <span>￥{{item.goodsPrice}}</span>
                  <p>x {{item.goodsNum}}</p>
                </div>
              </div>
            </div>
            <div class="writeOffDate" v-if="item.writeOffDate">
              使用时间： {{item.writeOffDate}}
            </div>

            <div class="sale" v-if="!item.writeOffDate">
              <div class="sale_content" v-if="(item.refundType===0 && item.orderStatus!==1 && item.orderStatus!==3 && item.orderStatus!==4 && item.orderStatus!==-1)" @click="afterSaleHandle(item)">申请售后</div>
              <div class="sale_content" v-if="item.isExpire===1 && item.orderStatus===3" @click="afterSaleHandle(item)">申请售后</div>
              <div class="refund" v-if="item.refundType===1 || item.refundType===2">已退款*{{item.refundNum?item.refundNum:item.goodsNum}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="Amount_content">
        <div class="AmountGoods" v-for="item in writeInfo">
          <div class="Amount_top">
            <h5>商品名称</h5>
            <span>{{item.goodsName}}</span>
          </div>
          <div class="Amount_top">
            <h5>商品编号</h5>
            <span>{{item.goodsId}}</span>
          </div>
          <div class="Amount_top">
            <h5>已核销次数</h5>
            <span>{{item.writeOffCount}} / {{item.writeOffTotal}}</span>
          </div>
          <div class="Amount_top">
            <h5>核销有效期</h5>
            <span>{{item.expiryDate}} ~ {{item.endDate}}</span>
          </div>
          <div class="Amount_top" v-if="item.lastWriteDate">
            <h5>最后一次核销时间</h5>
            <span>{{item.lastWriteDate}}</span>
          </div>

          <div class="Amount_top" v-if="item.writeOffStatus">
            <h5>订单状态</h5>
            <span v-if="item.writeOffStatus==1">待付款</span>
            <span v-if="item.writeOffStatus==2 && !(item.writeOffStatus==3 || item.refundType===1 || item.refundType===2)">已付款</span>
            <span v-if="item.writeOffStatus==3 || item.refundType===1 || item.refundType===2">已完成</span>
            <span v-if="item.writeOffStatus==4">已过期</span>
          </div>
        </div>
      </div>
      <div class="Amount_content">
        <div class="AmountGoods">
          <div class="Amount_top">
            <h5>商品总额</h5>
            <span>￥{{orderInfo.orderAmount}}</span>
          </div>
          <div class="Totalprice">
            <h5>订单总价</h5>
            <span>￥{{orderInfo.orderAmount}}</span>
          </div>
        </div>
      </div>
      <div class="Amount_content">
        <div class="AmountGoods">
          <div class="Amount_top">
            <h5>积分抵扣</h5>
            <span>-￥{{orderInfo.integralAmount}}</span>
          </div>
          <div class="Amount_top">
            <h5>优惠券抵扣</h5>
            <span>-￥{{orderInfo.couponAmount}}</span>
          </div>
          <div class="Totalprice">
            <h5>实付款</h5>
            <span>￥{{orderInfo.actualAmount}}</span>
          </div>
        </div>
      </div>
      <!-- 订单状态:-1订单取消 1-待付款, 2-已付款, 3-已完成, 4-已关闭 -->
      <div class="Amount_content">
        <div class="AmountGoods">
          <div class="Amount_top">
            <h5>订单编号</h5>
            <span>{{orderInfo.orderNo}}</span>
          </div>
          <div class="Amount_top">
            <h5>下单时间</h5>
            <span>{{orderInfo.orderTime}}</span>
          </div>
          <div class="Amount_top" v-if="(orderInfo.orderStatus==2 || orderInfo.orderStatus==3 || orderInfo.orderStatus==4) && orderInfo.payTime!=null">
            <h5>支付时间</h5>
            <span>{{orderInfo.payTime}}</span>
          </div>
          <div class="Amount_top" v-if="(orderInfo.orderStatus==2 || orderInfo.orderStatus==3 || orderInfo.orderStatus==4) && orderInfo.payTime!=null">
            <h5>支付方式</h5>
            <span>微信支付</span>
          </div>
          <div class="Amount_top" v-if="orderInfo.payFlowNo">
            <h5>支付流水号</h5>
            <span>{{orderInfo.payFlowNo}}</span>
          </div>
          <div class="Amount_top" v-if="orderInfo.orderStatus==3">
            <h5>使用完成时间</h5>
            <span>{{orderInfo.useTime}}</span>
          </div>
          <div class="Amount_top" v-if="orderInfo.orderStatus==4 || orderInfo.orderStatus==-1">
            <h5>关闭时间</h5>
            <span>{{orderInfo.gmtUpdate}}</span>
          </div>
          <div class="Amount_top" v-if="orderInfo.orderStatus==4 || orderInfo.orderStatus==-1">
            <h5>关闭原因</h5>
            <span>{{orderInfo.orderCloseType == 1 ? '取消订单/订单超时' : '全部退款'}}</span>
          </div>
        </div>
      </div>
    </div>
     <!-- 订单状态:   -1订单取消,  1-待付款,   2-已付款,   3-已完成,   4-已关闭	 -->
    <div class="btn" >
        <form @submit="e=>{this.orderPay(e,orderInfo)}" report-submit='true' v-if="orderInfo.orderStatus == 1">
            <button class="payment" form-type="submit">支付</button>
        </form>
        <div class="cancel" v-if="orderInfo.orderStatus == 1" @click.stop="queryCancel(orderInfo.id)">取消订单</div>
        <div class="cancel" v-if="orderInfo.orderStatus == 2&&((orderInfo.isPresenter===1&&orderInfo.isCurrent===1)||orderInfo.isPresenter===0)" @click.stop="tabPage(1,orderInfo.orderNo)">核销码</div>
        <div class="send" v-if="orderInfo.orderStatus == 2&&orderInfo.isCurrent==1" @click.stop="wish=true">送给好友</div>
        <!-- <div class="cancel" v-if="orderInfo.orderStatus == 3 && !orderInfo.evalType && this.teamType!==1" @click.stop="tabPage(2,orderInfo.orderNo,goodsList[0].orderId,goodsList[0].goodsImg)">评价</div> -->
         <div class="cancel" v-if="(orderInfo.orderStatus == 3 || orderInfo.orderStatus == -1 || orderInfo.orderStatus == 4) && orderInfo.teamType!==1" @click.stop="deleteOder">删除订单</div>
    </div>
        <!-- 广告 -->
        <div class="wishfriend" v-show="wish">
          <icon class="cancel" type="cancel" size="40" color='rgb(255,255,255)' @click="wish=false"/>
          <img class="image" src="/static/images/wish.png" alt="">
          <button class="wish_btn" open-type="share" @click="onShareAppMessage()" > 分享</button>
        </div>
  </div>
</template>

<script>
console.log('订单详情===========')
import toast from '@/plugins/toast'
import { cancelOrder, getAfterDetail, getTeamDetail, shoppingPay } from '@/api'
import { setTimeout } from 'timers';
export default {
    data () {
        return {
            id: '',
            orderType: '',  //核销、售后  拼团传1，其他传0
            orderInfo: {},
            goodsList: [],
            writeInfo: [],
            wish:false,
        }
    },
    computed: {
        orderDetail () {
            return this.$store.state.order.orderDetail
        }
    },
    onShareAppMessageWpp(){
      console.log(1111)
      return{
        title:'书籍详情',
        path:'/pages/index/main'
      }
    },
    onShareAppMessage(res) {
      if (res.from === 'button') {
        let name=''
        this.goodsList.forEach(item => {
          name+=item.goodsName+','
        });
        name=name.slice(0,name.length-1)
        // 来自页面内转发按钮
        console.log(this.orderInfo.random)
        return {
            title: this.goodsList[0].goodsName,
            imageUrl: this.goodsList[0].goodsImg,
            path: `/pages/wish/main?orderId=${this.orderInfo.id}&name=${name}&random=${this.orderInfo.random}`
          }
        }
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
      getStatus(item){
          console.log(item)
          if(item.writeOffStatus==1){
            console.log(1111)
            return "待付款";
          }else if( item.writeOffStatus==3 || item.refundType==1 || item.refundType==2 ){
            console.log(2222)
            return "已完成";
          }else if(item.writeOffStatus==2){
            console.log(3333)
            return "已付款";
          }else if(item.writeOffStatus==4){
            console.log(4444)
            return "已过期";
          }else{
            console.log(5555)
            return "暂无";
          }
      },
        goCommodityDetail(id,type,teamType) {
            if (teamType === 1) {
                wx.navigateTo({
                    url: `/pages/home/<USER>/clusterGoods/main?id=${id}`
                })
            } else {
                wx.navigateTo({
                    url: `/pages/home/<USER>/classDetails/main?id=${id}&type=${type}`
                })
            }
        },
        //申请售后
        afterSaleHandle(item) {
            wx.showLoading({
              mask: true
            })
            let param = {
                orderGoodsId: item.id,
                resouceType: this.orderType,
                orderId: item.orderId
            }
            getAfterDetail(param).then(res => {
                if(res.data.code) {
                    wx.hideLoading()
                    toast.text(res.data.msg)
                } else {
                    wx.hideLoading()
                    wx.navigateTo({
                        url: `/pages/main/refund/main?orderGoodsId=${item.id}&resouceType=${this.orderType}&orderId=${item.orderId}&orderDetailId=${this.id}`
                    })
                }
            })
        },
        // 取消订单
        queryCancel (id) {
            let pageData = getCurrentPages()
            let prevPage = pageData[pageData.length - 2]

            wx.showModal({
                title: '提示',
                content: '确认取消订单吗？',
                success(res) {
                    if (res.confirm) {
                        cancelOrder({id}).then(res => {
                            if(!res.data) {
                                toast.text('已取消')
                                setTimeout(() => {
                                    wx.navigateBack({
                                        delta: 1
                                    })
                                },1000)
                                prevPage.onLoad()
                            } else {
                                toast.text(res.data.msg)
                            }
                        })
                    }
                }
            })
        },
        orderPay(e,info) {
            let formId = e.target.formId
            let param = {
                type: 3,
                id: info.id,
                fromId: formId
            }
            shoppingPay(param).then(res => {
                if(!res.data.code) {
                    let orderId = res.data.orderId;
                    wx.requestPayment({
                        timeStamp: res.data.pay.timeStamp,
                        nonceStr: res.data.pay.nonceStr,
                        package: res.data.pay.pkg,
                        signType: res.data.pay.signType,
                        paySign: res.data.pay.paySign,
                        success() {
                            wx.redirectTo({
                                url: "/pages/ticket/orders/success/main"
                            });
                        },
                        fail() {
                            wx.showToast({ title: '支付失败', icon:'none', duration: 1500})
                        }
                    })
                } else {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
                }
            })
        },
        // 删除订单
        deleteOder () {
            let pageData = getCurrentPages()
            let prevPage = pageData[pageData.length - 2]
            let that = this
            wx.showModal({
                title: '提示',
                content: '确认删除订单吗？',
                success(res) {
                    if (res.confirm) {
                        that.$store.dispatch('order/delOrder', {id: that.id}).then(res => {
                            if (!res.code) {
                                toast.text('已删除')
                                wx.navigateBack({
                                    delta: 1
                                })
                                prevPage.onLoad()
                            } else {
                                toast.text(res.msg)
                            }
                        })
                    }
                }
            })
        },
        // 评价/查看核销码
        tabPage (e,orderNo,orderId,goodsImg) {
            console.log(orderId,goodsImg)
            // return
            let url
            if (e == 1) { //核销码
                url = `/pages/main/Order/QRcodeList/main?type=${this.orderType}&orderNo=${orderNo}`
            } else {
                // this.orderType == 1: 拼团
                let teamObj = {
                    orderNo,
                    orderId,
                    goodsImg
                }
                wx.setStorageSync('teamObj',teamObj)
                url = `/pages/main/Order/orderDetails/evaluate/main?id=${this.orderInfo.id}&delta=2&orderType=${this.orderType}`
            }
            wx.navigateTo({
                url: url
            })
        },
        getTeamDetail() {
            getTeamDetail({id: this.id}).then(res => {
                this.orderInfo = res.data.orderInfo
                this.goodsList = res.data.goodsList
            })
        },
        // info
        getinfo () {
            this.$store.dispatch('order/getOrderDetail', {id: this.id}).then(res => {
                this.orderInfo = this.orderDetail.orderInfo
                this.goodsList = this.orderDetail.goodsList
                this.writeInfo = this.orderDetail.writeInfo
            })
        }
    },
    onLoad () {
        console.log(this.$root.$mp.query)
        this.orderType = this.$root.$mp.query.orderType == 1 ? 1 : 0
        this.id = this.$root.$mp.query.id
        this.wish=false
    },
    onShow() {
        if (this.orderType == 1) {
            this.getTeamDetail()
        } else {
            this.getinfo()
        }

    }
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  padding-bottom: 60px;
  box-sizing: border-box;
  background: #fafafa;
  .Orderdetails_content {
    width: 100%;

    .Orderdetails_top {
      width: 100%;
      background: #fff;
      h3 {
        font-size: 13px;
        color: #4a4a4a;
        height: 51px;
        line-height: 51px;
        padding-left: 16px;
        position: relative;
        // &::before {
        //   position: absolute;
        //   right: 0;
        //   bottom: 0;
        //   left: 0;
        //   height: 1px;
        //   content: "";
        //   -webkit-transform: scaleY(0.5);
        //   transform: scaleY(0.5);
        //   background-color: #e6e3dc;
        // }
      }
      .item {
        width: 100%;
        .commodity {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          padding: 10px 15px;
          box-sizing: border-box;
          img {
            width: 80px;
            height: 80px;
            border-radius: 4px;
          }
          .commodity_content {
            width: 68%;
            h4 {
              width: 240px;
              font-size: 14px;
              color: #2D2D2D;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2; //设置两行溢出显示省略号
              overflow: hidden;
              margin-bottom: 22px;
              font-weight: 400;
            }
            .price {
              width: 100%;
              display: flex;
              justify-content: space-between;
              span {
                font-size: 14px;
                color: #FF1A1A;
              }
              p {
                font-size: 14px;
                color: #FF1A1A;
              }
            }
          }
        }
        .writeOffDate {
          display: flex;
          flex-direction: row-reverse;
          padding-right: 15px;
          font-size: 24rpx;
          color: #6C6C6C;
          box-sizing: border-box;
          padding-bottom: 12px;
          box-sizing: border-box;
        }
        .sale {
          width: 100%;
          display: flex;
          flex-direction: row-reverse;
          padding-right: 15px;
          box-sizing: border-box;
          padding-bottom: 12px;
          box-sizing: border-box;
          .sale_content {
            width: 68px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 11px;
            // border: 1px solid rgba(74, 74, 74, 1);
            font-size: 12px;
            color: #FFFFFF;
            background: linear-gradient(270deg, #F46A22 0%, #F4A424 100%);
            box-shadow: 1px 8px 16px 0px rgba(244, 106, 34, 0.24);
          }
          .refund {
            padding: 6px 14px;
            box-sizing: border-box;
            background: rgba(229, 229, 229, 1);
            border-radius: 16px;
            font-size: 12px;
            color: #7a7a7a;
          }
        }
      }
    }
    .Amount_content {
      width: 100%;
      .AmountGoods {
        width: 100%;
        padding: 12px 15px;
        box-sizing: border-box;
        background: #fff;
        margin-top: 10px;
        .Amount_top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-top: 12px;
          h5 {
            font-size: 12px;
            color: #6C6C6C;
          }
          span {
            color: #6C6C6C;
            font-size: 12px;
          }
        }
        .Totalprice {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-top: 12px;
          h5 {
            font-size: 14px;
            color: #535353;
            font-weight: bold;
          }
          span {
            font-size: 14px;
            color: #FF1A1A;
          }
        }
      }
    }
  }
  .btn {
    width: 100%;
    // padding: 11px 0px 11px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: row-reverse;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0px;
    border-top: 1px solid #EDEDED;
    .cancel {
      min-width: 75px;
      height: 27px;
      line-height: 27px;
      text-align: center;
      // border-radius: 16px;
      border: 1px solid#EDEDED;
      color: #646464;
      font-size: 12px;
      margin-right: 10px;
      border-radius: 3px;
      margin-top: 11px;
      margin-bottom: 11px;
    }
    .send {
      min-width: 75px;
      height: 27px;
      line-height: 27px;
      // border-radius: 16px;
      text-align: center;
      border: 1px solid#F4A023;
      color: #f4a023;
      font-size: 12px;
      line-height: 28px;
      margin-right: 10px;
      border-radius: 3px;
      margin-top: 11px;
      margin-bottom: 11px;
    }
    .payment {
      min-width: 75px;
      height: 27px;
      line-height: 27px!important;
      // border-radius: 16px;
      text-align: center;
      border: 1px solid rgba(255, 81, 0, 1);
      color: #ff5100;
      font-size: 12px;
      line-height: 28px;
      margin-right: 10px;
      border-radius: 3px;
      margin-top: 11px;
      margin-bottom: 11px;
    }
  }
  .wishfriend {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    .cancel {
      position: absolute;
      z-index: 99;
      top: 110px;
      right: 5%;
    }
    .image {
      width: 315px;
      height: 335px;
      position: absolute;
      top: 110px;
      left: 50%;
      transform: translateX(-50%);
    }
    .wish_btn {
      width: 175px;
      height: 36px;
      background: #f7d652;
      border-radius: 18px;
      color: #995c19;
      text-align: center;
      line-height: 36px;
      font-size: 15px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 350px;
      box-shadow: 1px 9px 23px 0px rgba(184, 42, 42, 0.42);
    }
  }
}
</style>
