<template>
    <div class="container">
        <div class="main">
            <img mode="aspectFit" src="/static/images/icon-success.png" alt="">
            <p class="success">售后申请已提交</p>
            <p class="tibs">您的售后申请已提交，请耐心等待处理结果~</p>
        </div>
        <div class="btn">
            <button @click="goOrder">返回</button>
            <button @click="goList" class="group">售后进度</button>
        </div>
    </div>
</template>

<script>
export default {
    data () {
        return {
            logs: []
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        goOrder() {
            wx.redirectTo({   
                url: `/pages/home/<USER>/clusterGoods/collaging/partakeCluster/main?id=${this.id}`
            })
        },
        goList() {
            wx.redirectTo({   
                url: "/pages/main/myAftersale/main"
            })            
        }
    },
    onLoad() {
        this.id = this.$root.$mp.query.id
    }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 50px 0 50px;
    box-sizing: border-box;
    text-align: center;
    font-size: 18px;
    color: #4A4A4A;
    line-height: 25px;
    img {
      width: 72px;
      height: 88px;
      margin-bottom: 13px;
    }
    .tibs {
      font-size: 14px;
      color: #9B9B9B;
      line-height: 20px;
      margin-top: 9px;
    }
  }
  .btn {
    width: 100%;
    padding: 0 52px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    button {
      width: 120px;
      height: 40px;
      line-height: 40px;
      border-radius: 22px;
      border: 1px solid #ED813A;
      background: #fff;
      padding: 0;
      font-size: 16px;
      color: #FE743D;
      box-sizing: border-box;
    }
    .group {
      background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      color: #fff;
      border: none;
    }
  }
}
</style>
