<template>
  <div class="container">
    <div class="details">
      <img mode="aspectFill" :src="dataObj.goodsImg" alt="" />
      <div class="details_title">
        <h3>{{ dataObj.goodsName }}</h3>
        <p>x{{ dataObj.goodsNum }}</p>
      </div>
    </div>
    <ul class="list">
      <!-- <li>
            <p>售后类型</p>
            <span>退款</span>
        </li> -->
      <li>
        <p>退款金额</p>
        <span>￥{{ dataObj.goodsPrice*returnNum }}</span>
      </li>
      <li>
        <p class="many">退款数量</p>
        <div class="computed">
          <!-- <img
            mode="aspectFit"
            @click="returnNum != 1 && returnNum--"
            :src="returnNum == 1 ? '/static/images/icon-reduce.png' : '/static/images/icon-reduce2.png' "
            alt=""
          /> -->
          <p class="num">{{ returnNum }}</p>
          <!-- <img @click="returnNum != dataObj.goodsNum && returnNum++" mode="aspectFit"
            :src=" returnNum != dataObj.goodsNum  ? '/static/images/icon-plus.png' : '/static/images/icon-reduce2.png' "
            alt=""
          /> -->

          <!-- <img src="/static/images/icon-plus.png" alt="" />
          <p class="num">111</p>
          <img mode="aspectFit" src="/static/images/icon-plus.png" alt="" /> -->
        </div>
      </li>
    </ul>
    <!-- <div class="select">
        <p class="text">退款理由</p>
        <div class="right">
            <picker
                @change="bindreasonchange($event)"
                mode="selector"
                :value="index"
                :range="reasonList"
            >
                <div class="picker">
                <p class="picker-name">{{reasonitem}}</p>
                <img mode="aspectFit" class="arrow" src="/static/images/icon-gray-arrow.png" alt />
                </div>
            </picker>
        </div>
        </div> -->
    <div class="content">
      <p class="text">详细原因</p>
      <textarea placeholder="请输入售后原因" v-model="afterReason"></textarea>
    </div>
    <div class="btn">
      <button @click="submitHandle">提交申请</button>
    </div>
  </div>
</template>

<script>
import toast from "@/plugins/toast";
import { getAfterDetail, querApplySale } from "@/api";
export default {
  data() {
    return {
      id: "",
      orderType: "",
      dataObj: {},
      orderId: "",
      reasonList: [
        "重复购买",
        "买多了，买错了",
        "商家关店、装修",
        "预约不上",
        "门店活动更优惠",
        "活动计划有变、无时间消费",
      ],
      afterType: "",
      reasonitem: "请选择",
      afterReason: "",
      returnType: "",
      returnNum: 1,
    };
  },
  onShareAppMessage(res) {
    return {
      title: "宝燕乐园",
      path: `pages/index/main`,
    };
  },
  methods: {
    bindreasonchange(e) {
      let i = e.target.value;
      this.reasonitem = this.reasonList[i];
      this.afterType = e.target.value;
    },
    getDetail() {
      let param = {
        orderGoodsId: this.orderGoodsId,
        resouceType: this.resouceType,
        orderId: this.orderId,
      };
      getAfterDetail(param).then((res) => {
        if (res.data.code) {
          toast.text(res.data.msg);
        } else {
          this.dataObj = res.data;
          this.dataObj.afterAmount = this.dataObj.afterAmount.toFixed(2);
          this.returnNum = this.dataObj.goodsNum;
        }
      });
    },
    submitHandle() {
      // if (this.reasonitem == '请选择') {
      //     toast.text('请选择退款理由')
      //     return
      // }
      if (this.afterReason == "") {
        toast.text("请填写售后原因");
        return;
      }
      if (this.dataObj.afterAmount === 0) {
        toast.text("退款金额为零");
        return;
      }
      let param = {
        orderGoodsId: this.dataObj.orderGoodsId,
        afterType: this.afterType,
        orderNo: this.dataObj.orderNo,
        resouceType: this.dataObj.resouceType,
        afterReason: this.afterReason,
        returnNum: this.returnNum,
      };

      querApplySale(param).then((res) => {
        if (res.data.code) {
          toast.text(res.data.msg);
        } else {
          toast.text("提交成功");
          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/main/refund/refsuccess/main?id=${this.id}&orderType=${this.resouceType}`,
            });
          }, 500);
        }
      });
    },
  },
  onLoad() {
    this.orderGoodsId = this.$root.$mp.query.orderGoodsId;
    this.resouceType = this.$root.$mp.query.resouceType;
    this.orderId = this.$root.$mp.query.orderId;
    this.id = this.$root.$mp.query.orderDetailId;
    //   1 普通商品 2 拼团
    this.returnType = this.$root.$mp.query.returnType;

    this.reasonitem = "请选择";
    this.afterReason = "";
    this.getDetail();
  },
};
</script>

<style lang="less" scoped>
.container {
  .details {
    width: 100%;
    background: #fff;
    display: flex;
    justify-content: space-between;
    padding: 10px 15px 10px;
    box-sizing: border-box;
    margin-bottom: 10px;
    img {
      width: 96px;
      height: 96px;
      border-radius: 4px;
    }
    .details_title {
      width: 240px;
      h3 {
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; //设置两行溢出显示省略号
        overflow: hidden;
        font-size: 16px;
        line-height: 22px;
        color: #171717;
      }
      p {
        font-size: 14px;
        color: #615454;
        margin-top: 30px;
        float: right;
      }
    }
  }
  .list {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    li {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      padding: 0 15px;
      box-sizing: border-box;
      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      &:last-child::before {
        background-color: transparent;
      }
      p {
        font-size: 14px;
        color: #4a4a4a;
        line-height: 20px;
      }
      span {
        font-size: 14px;
        color: #7a7a7a;
        line-height: 20px;
      }
    }
    .tibs {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .price {
        width: 190px;
        overflow: hidden;
        display: flex;
        align-items: center;
        margin-left: -5px;
        .step {
          font-size: 12px;
          color: #baadad;
          line-height: 17px;
          text-decoration: line-through;
        }
      }
    }
    .computed {
      display: flex;
      position: relative;
      left: 10px;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
      }
      .num {
        width: 30px;
        text-align: center;
        line-height: 18px;
        font-size: 13px;
        color: #333333;
      }
    }
  }
  .select {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    &::before {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1px;
      content: "";
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #d6d6d6;
    }
    .text {
      font-size: 14px;
      color: #4a4a4a;
      line-height: 20px;
    }
    .right {
      .picker {
        display: flex;
        align-items: center;
        .picker-name {
          font-size: 14px;
          color: #727272;
          line-height: 20px;
          margin-right: 6px;
        }
        .arrow {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
  .content {
    width: 100%;
    height: 129px;
    padding: 14px 15px 0;
    box-sizing: border-box;
    background: #fff;
    .text {
      font-size: 14px;
      color: #4a4a4a;
    }
    textarea {
      width: 100%;
      height: 80px;
      font-size: 14px;
      color: #9b9b9b;
      margin-top: 10px;
    }
  }
  .btn {
    width: 100%;
    padding: 0 15px 12px;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    bottom: 0;
    button {
      width: 345px;
      height: 40px;
      line-height: 40px;
      padding: 0;
      text-align: center;
      background: linear-gradient(
        90deg,
        rgba(255, 151, 56, 1) 0%,
        rgba(254, 116, 61, 1) 100%
      );
      border-radius: 22px;
      font-size: 16px;
      color: #fff;
    }
  }
}
</style>
