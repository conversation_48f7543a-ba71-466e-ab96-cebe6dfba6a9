<template>
    <div class="container">
        <ul class="list">
            <li
                :class="{'active' : tabindex == index}"
                v-for="(item, index) in list"
                :key="index"
                @click="tabList(index)"
            >{{item}}</li>
        </ul>
        <scroll-view
            class="scrollView"
            :style="{height: scrollViewHeight}"
            :lower-threshold="100"
            @scrolltolower="loadMore"
            scroll-y
            >
            <div class="main">
                <div class="volume-box" v-for="(item, index) in couponUser" :key="index">
                <img v-if="item.couponType==1 && item.full" src="/static/images/icon-Coupon.png" alt />
                <img
                    v-if="item.couponType==1 && !item.full"
                    src="/static/images/icon-Deduction-Volume.png"
                    alt
                />
                <img v-if="item.couponType==2" src="/static/images/icon-Discount-Volume.png" alt />
                <div class="volume">
                    <div class="many" v-if="item.couponType==2">
                        <h5>{{item.discount}}折</h5>
                        <p class="text">折扣券</p>
                    </div>
                    <div class="many" v-else>
                        <h5>￥{{item.discount}}</h5>
                        <p class="text">{{!item.full?'抵扣券':'优惠券'}}</p>
                    </div>

                    <div class="content">
                        <div class="title">
                            <h6>{{item.type==0?'全部商品可用':'限'+ item.limitation +'商品可用'}}</h6>
                            <p class="type">{{tabindex == 0 ? '可使用' : tabindex == 1 ? '已过期' : '已完成'}}</p>
                        </div>
                        <p class="fullReduce">满{{item.full}}可用</p>
                        <p class="time">{{item.startDate}}～{{item.endDate}}</p>
                    </div>
                </div>
                </div>
            </div>
            <div v-if="!couponUser.length" class="noneData">没有更多数据啦...</div>
        </scroll-view>
    </div>
</template>

<script>
export default {
  data() {
    return {
      list: ["可使用", "已过期", "已完成"],
      tabindex: 0,
      page: 1,
      pageSize: 20,
      noService: false
    };
  },
  computed: {
    scrollViewHeight() {
      if (this.$store.state.windowHeight) {
        return `${this.$store.state.windowHeight - 55}px`;
      }
      return "100%";
    },
    couponUser() {
      return this.$store.state.main.couponUser;
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    loadMore() {
      if (this.noService) {
        return;
      }
      this.page++;
      this.getinfo();
    },
    // 获取信息
    getinfo() {
      let type = this.tabindex == 0 ? 0 : this.tabindex == 1 ? 2 : 1;
      let params = {
        isUse: type,
        pageIndex: this.page,
        pageSize: this.pageSize
      };
      this.$store.dispatch("main/getCouponUser", params).then(res => {
        this.noService = res;
      });
    },
    // tab
    tabList(index) {
      this.tabindex = index;
      this.page = 1;
      this.noService = false;
      this.$store.commit("main/RECORD_COUPONUSER", []);
      this.getinfo();
    }
  },
  onLoad() {
    this.$store.commit("getWindowHeight");
    this.tabindex = 0;
    this.page = 1;
    this.noService = false;
    this.$store.commit("main/RECORD_COUPONUSER", []);
    this.getinfo();
  }
};
</script>

<style lang="less">
.container {
  .list {
    width: 100%;
    height: 45px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;
    &::before {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1px;
      content: "";
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #e5e5e5;
    }
    li {
      height: 100%;
      line-height: 45px;
      font-size: 15px;
      color: #999999;
      position: relative;
      &::before {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 0);
        content: "";
        width: 30px;
        height: 4px;
        background: transparent;
      }
    }
    .active {
      color: #333333;
      font-weight: 500;
      &::before {
        background: linear-gradient(
          90deg,
          rgba(255, 151, 56, 1) 0%,
          rgba(254, 116, 61, 1) 100%
        );
      }
    }
  }
  .main {
    width: 100%;
    padding: 10px 15px 0;
    box-sizing: border-box;
    margin-bottom: 52px;
    .volume-box {
      width: 345px;
      height: 93px;
      margin-bottom: 10px;
      position: relative;
      img {
        width: 345px;
        height: 93px;
      }
      .volume {
        position: absolute;
        width: 345px;
        height: 93px;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        .many {
          width: 93px;
          height: 93px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          flex-direction: column;
          h5 {
            font-size: 20px;
            line-height: 28px;
            color: #fff;
            // margin-bottom: 3px;
          }
          .text {
            font-size: 12px;
            color: #fff;
          }
        }
        .content {
          flex: 1;
          padding-left: 15px;
          font-size: 12px;
          color: #746c6c;
          line-height: 17px;
          margin-right: 26px;
          padding-top: 13px;
          box-sizing: border-box;
          .fullReduce {
            //   margin: 16px 0;
          }
          .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            h6 {
              width: 159px;
            //   overflow: hidden;
            //   text-overflow: ellipsis;
            //   white-space: nowrap;
              font-size: 14px;
              color: #290b0b;
            }
            .type {
              font-size: 14px;
              color: #9b9b9b;
              line-height: 20px;
            }
          }

          .time {
            font-size: 10px;
            color: #ada8a8;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}
</style>
