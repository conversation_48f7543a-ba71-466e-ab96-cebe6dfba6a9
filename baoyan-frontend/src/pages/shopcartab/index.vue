<template>
  <div class="container">
    <div class="header">
      <div class="checkAll" @click="selectAll = !selectAll">
        <img class="checkbox" mode="aspectFit" :src="'/static/images/' + (selectAll ? 'icon-selected' : 'icon-select') + '.png'" alt="">
        <p>全选</p>
      </div>
      <div class="edit" @click="editing = !editing">
        <img mode="aspectFit" class="icon" :src="'/static/images/'+(editing ? 'icon-complete' : 'icon-Administration')+'.png'" alt="">
        <p>{{ editing ? '完成' : '管理'}}</p>
      </div>
    </div>

      <scroll-view class="scrollView" :style="{height: scrollViewHeight}" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>

        <block v-for="(item, index) of carList" :key="index">
          <div class="item">
            <div class="img_box" @click="checkItem(index)">
              <img class="checkbox" mode="aspectFit" :src="'/static/images/' + (item.isSelected ? 'icon-selected' : 'icon-select') + '.png'" alt="">
              <!-- <img v-if="item.type == 0" class="checkbox" mode="aspectFit" src="'/static/images/icon-select.png'" alt=""> -->
            </div>
            <div class="tally">
              <img class="tally_img" :src="item.goodsImg" @click="goCommodityDetail(item.goodsId,item.goodsType)" mode="aspectFill" alt="">
              <div class="tally_content">
                <h4 class="title">{{item.goodsName}}</h4>
                <div class="tibs">
                  <div class="price">
                    <p class="many">¥{{item.sellPrice}}</p>
                    <p class="step" v-if="item.marketPrice!=''&&item.marketPrice">¥{{item.marketPrice}}</p>
                  </div>
                  <div class="computed">
                    <img mode="aspectFit" @click="changeNum(item, 1)" :src="item.goodsCount==1 ? '/static/images/icon-reduce.png' : '/static/images/icon-reduce2.png'" alt="">
                     <p class="num">{{item.goodsCount}}</p>
                    <img mode="aspectFit" @click="changeNum(item, 2)" src="/static/images/icon-plus.png" alt="">
                  </div>
                </div>
                <!-- <p class="invalid" v-if="item.type == 0">失效</p> -->
              </div>
            </div>
          </div>
        </block>
        <div class="noneData" v-if="!carList.length">没有更多数据啦...</div>
      </scroll-view>
      <div class="conclution">
        <div class="count">
          <p>合计：<span>¥{{ totalMoney }}</span></p>
        </div>
        <div class="btnGroup">
          <button v-if="!editing" type="button" name="button" @click="buyNow">结算</button>
          <button v-if="editing" @click="removeItem" class="red" type="button" name="button">删除</button>
        </div>
      </div>
  </div>
</template>

<script>
import toast from '@/plugins/toast'
import { changeShopcar,shopCalculation, checkUserDisable,payDetail } from '@/api'

export default {
  data () {
    return {
      editing: false,
      carList: [],
      totalMoney: 0
    }
  },
  computed: {
    scrollViewHeight () {
      if (this.$store.state.windowHeight) {
        return `${this.$store.state.windowHeight - 120}px`
      }
      return '100%'
    },
    selectAll: {
        get () {
            const all = this.carList.length
            const selected = this.carList.filter(item => item.isSelected).length
            if (!this.carList.length) {
                return false
            } else {
                return all === selected
            }
        },
        set (val) {
            this.carList = this.carList.map(item => {
                item.isSelected = val
                return item
            })
        }
    },
  },
  watch:{
    //监听 carList数据变化   计算价格
    carList: {
        deep: true,
        handler() {
            const selected = this.carList.filter(item => item.isSelected)
            if(!selected) {
                this.totalMoney = 0
            } else {
                shopCalculation(selected).then(res => {
                    this.totalMoney = res.data.amount.toFixed(2)
                })
            }
        }
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    goCommodityDetail(id,type) { 
        wx.navigateTo({ 
            url: `/pages/home/<USER>/classDetails/main?id=${id}&type=${type}`
        })
    },
    checkItem (index) {
      let obj = Object.assign({}, this.carList[index])
      obj.isSelected = !obj.isSelected
      this.$set(this.carList, index, obj)
    },
    initData () {
      this.$store.dispatch('shop/getShopcar', {}).then(res => {
        this.carList = res.map(item => {
          item.isSelected = false
          return item
        })
      })
    },
    // 结算
    buyNow () {
        let ids = []
        this.carList.filter(item => {
            if(item.isSelected) {
                ids.push(item.id)
            }
        })
        if (!ids.length) {
            toast.text('请选择商品')
            return
        }    
        ids = ids.join(',')
        for(let i=0;i<this.carList.length;i++) {
            if(this.carList[i].type == 0 && this.carList[i].isSelected) {
                toast.text('商品：' + this.carList[i].goodsName + '库存不足')
                return
            }
        }
        checkUserDisable({type: 5,ids}).then(data => {
            if (data.data.code == 0) {
            let param = {
                type: 2,
                id: ids
            }
            payDetail(param).then(res => {
              if(res.data.code==418){
                wx.showToast({ 
                  title: res.data.msg,
                  icon: 'none',
                  duration: 1000
                })
                return
              }else{
                wx.navigateTo({
                    url: `/pages/ticket/shopCartPay/main?type=2&id=${ids}`
                })     
              }
            })     
            } else {
                toast.text(data.data.msg)
            }
        })
    },
    removeItem () {
      const selectedArr = this.carList.filter(item => item.isSelected)
      if (!selectedArr.length) {
        toast.text('请选择要删除的商品')
        return
      }
      const cartGroupId = selectedArr.map(item => item.id).join(',')
      this.$store.dispatch('shop/deleteShop', {ids: cartGroupId}).then(res => {
        if (res.code == 0) {
          this.initData()
          toast.text('删除成功')
        } else {
          toast.text('删除失败')
        }
      })
    },
    // 修改商品数量
    changeNum (item, e) {
        if (e == 1) {
            if (item.goodsCount > 1) {
                item.goodsCount --
            } else {
                toast.text('至少选择一件商品')
                return
            }
        } else {
          if(item.goodsCount >= item.goodsStock){
                toast.text('库存不足')
                return
          }
          if(item.goodsCount >= item.limited&& item.limited!==0){
                toast.text('商品限购')
                return
          }
          item.goodsCount ++
            // if (item.goodsCount < item.goodsStock) {
            //   if(item.goodsCount <item.limited){
            //     item.goodsCount ++
            //   }else{
            //     toast.text('库存不足')
            //     return
            //   }
            // } else {
            //     toast.text('库存不足')
            //     return
            // }
        }
        changeShopcar({id: item.id,goodsCount:item.goodsCount}).then(res=>{
          console.log(res)
        })
    }
  },
  
  onShow () {
    wx.setStorageSync('appointmentOrderId', '')
    this.authShow = false
    this.page = 0
    this.editing = false
    this.$store.commit('getWindowHeight')
    this.carList=[]
    this.initData()
    }
}
</script>

<style lang="less">
.container {
  padding-top: 50px;
  padding-bottom: 50px;
  .header {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    margin-bottom: 10px;
    .edit {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }
      p {
        line-height: 20px;
        font-size:14px;
        color:#4A4A4A;
      }
    }
    .checkAll {
      display: flex;
      align-items: center;
      .checkbox {
        margin-right: 7px;
        width: 16px;
        height: 16px;
        vertical-align: bottom;
      }
      p {
        font-size:14px;
        font-weight:400;
        line-height: 20px;
        color:#999999;
      }
    }
  }
  
  .scrollView {
    width: 100vw;
    margin-top: 10px;
  }
  .conclution {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    box-sizing: border-box;
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    background: #FFFFFF;
    .count {
      display: flex;
      align-items: center;
      p {
        font-size:15px;
        color:#333333;
        span {
          color: #F34F1E;
        }
      }
    }
    .btnGroup {
      display: flex;
      align-items: center;
      button {
        padding: 0;
        width:109px;
        height: 50px;
        line-height:50px;
        font-size:15px;
        color:#fff;
        border-radius: 0;
        background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      }
      .red{
        background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
      }
    }
  }
  .packageItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1px;
    padding: 0 15px;
    height: 45px;
    background: #FFFFFF;
    .name {
      font-size:12px;
      font-weight:bold;
      color:rgba(51,51,51,1);
      span {
        margin-right: 15px;
        font-size:12px;
        font-weight:400;
        color:rgba(153,153,153,1);
      }
    }
    .amount {
      font-size:12px;
      font-weight:bold;
      color:rgba(215,50,64,1);
    }
  }
  .item {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    background: #FFFFFF;
    .img_box{
      padding: 10px 5px 10px 15px;
      box-sizing: border-box;
      margin-top: 35px;
      .checkbox {
        width: 16px;
        height: 16px;
        vertical-align: bottom;
      }
    }
    .tally{
      display: flex;
      padding: 12px 15px 12px 0;
      width: 90%;
      background: #fff;
      box-sizing: border-box;
      align-items: center;
      .tally_img{
        width: 96px;
        height: 96px;
        margin-right: 10px;
        border-radius:4px;
      }
      .tally_content{
        width: 210px;
        .title{
          width: 208px;
          line-height: 22px;
          font-size: 16px;
          color: #171717;
          margin-bottom: 4px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          margin-bottom: 45px;
        }
        .norms {
          font-size: 14px;
          color: #9B9B9B;
          line-height: 20px;
          margin-bottom: 4px;
        }
        .invalid {
          font-size: 16px;
          color: #C6C6C6;
          line-height: 22px;
        }
        .tibs {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-around;
          .price {
            width: 190px;
            overflow: hidden;
            display: flex;
            align-items: center;
            margin-left: -5px;
            .many {
              font-size: 16px;
              color: #FF5100;
              line-height: 22px;
              margin-right: 6px;
            }
            .step {
              font-size: 12px;
              color: #BAADAD;
              line-height: 17px;
              text-decoration: line-through;
            }
          }
        }
        .computed {
          display: flex;
          position: relative;
          left: 10px;
          align-items: center;
          img {
            width: 24px;
            height: 24px;
          }
          .num {
            width: 30px;
            text-align: center;
            line-height: 18px;
            font-size: 13px;
            color: #333333;
          }
        }
      }
    }
  }
}
</style>
