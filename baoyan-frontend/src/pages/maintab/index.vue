<template>
  <div class="container">
        <div class="Myodey_top">
            <img class="Myodey_img" src="/static/images/icon-userinfo-back.png" alt />
            <div class="box">
                <div class="Head" v-if="token">
                    <img mode="aspectFill" :src="orderNum.headImg" alt class="Head_img" />
                    <p>{{orderNum.nickName}}</p>
                </div>
                <div class="Head" v-else @click="goAuthorize">
                    <img mode="aspectFill" src="/static/images/icon-logo.png" alt class="Head_img" />
                    <p>登录</p>
                </div>
                <div class="personal" @click="goPersonal">
                    <span v-if="registerFlag">个人资料</span>
                    <span class="zc" v-else>注册</span>
                    <img mode="aspectFit" src="/static/images/icon-user-arrow.png" alt />
                </div>
            </div>
        </div>
        <div class="Order">
            <div class="Order_content">
                <div class="Order_top"  @click="tabDetail(0)">
                    <div class="Order_title">
                        <span>我的订单</span>
                        <div class="Order_right">
                            <span>全部订单</span>
                            <img mode="aspectFit" src="/static/images/icon-gray-arrow.png" alt />
                        </div>
                    </div>
                </div>
                <ul class="Order_msg">
                    <li @click="tabDetail(1)">
                        <img mode="aspectFit" src="/static/images/icon-payment.png" alt />
                        <p>待支付</p>
                        <span class="line" v-if="orderNum.waitPayNum">{{orderNum.waitPayNum}}</span>
                    </li>
                    <li @click="tabDetail(2)">
                        <img mode="aspectFit" src="/static/images/icon-delivery.png" alt />
                        <p>待核销</p>
                        <span class="line" v-if="orderNum.payMentNum">{{orderNum.payMentNum}}</span>
                    </li>
                    <li @click="tabDetail(3)">
                        <img mode="aspectFit" src="/static/images/icon-Trip.png" alt />
                        <p>已完成</p>
                        <!-- <span class="line" v-if="orderNum.completeNum">{{orderNum.completeNum}}</span> -->
                    </li>
                </ul>
            </div>
            <ul class="My_msg">
                <!-- <li @click="tabPage(4)">
                    <img mode="aspectFit" src="/static/images/icon-appointment.png" alt="">
                    <p>我的预约</p>
                    <span class="redDot_p" v-if="orderNum.showAppointmentCount*1"></span>
                </li> -->
                <li @click="tabPage(2)" class="myIntegral">
                    <img mode="aspectFit" src="/static/images/icon-integral.png" alt="">
                    <p>我的积分</p>
                </li>
                <li @click="tabPage(3)">
                    <img mode="aspectFit" src="/static/images/icon-Cardroll.png" alt="">
                    <p>我的卡券</p>
                </li>
                <li class="Card" @click="tabPage(5)">
                    <img mode="aspectFit" src="/static/images/icon-Assemble.png" alt="">
                    <p>我的拼团</p>
                    <span class="redDot_p" v-if="orderNum.teamType"></span>
                </li>
            </ul>
            <ul class="My_msg">
                <li @click="tabPage(1)">
                    <img mode="aspectFit" src="/static/images/icon-Service.png" alt="">
                    <p>我的售后</p>
                    <span class="redDot" v-if="orderNum.showRedCount">{{orderNum.showRedCount}}</span>
                </li>
                <li @click="phone">
                    <img mode="aspectFit" src="/static/images/phone.png" alt="">
                    <p>客服电话</p>
                </li>
            </ul>
            <div class="txt" @click="goPage">
                <img class="img1" src="/static/images/order2.png" alt="">
                <p>共享经济合作伙伴协议</p>
                <img class="img2" src="/static/images/icon-gray-arrow.png" alt="">
            </div>
        </div>
    </div>
</template>

<script>
import { showOrderRedDot } from "@/api";
export default {
    data () {
        return {
            token: '',
            registerFlag: ''
        }
    },
    computed: {
        orderNum () {
            return this.$store.state.order.orderNum
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        goPage() {
            wx.navigateTo({
                url: `/pages/main/agreement/main`
            })
        },
        phone() {
            wx.makePhoneCall({
                phoneNumber: '021-54377273'
            })
        },
        goAuthorize() {
            wx.navigateTo({
                url: `/pages/authorize/main`
            })
        },
        goPersonal() {  
            // 没有授权 => 去授权 ，有授权没有注册 => 去注册
            if (this.token) {
                if (!this.registerFlag) {
                    wx.navigateTo({
                        url: `/pages/login/main`
                    })
                } else {
                    wx.navigateTo({
                        url: `/pages/main/personal/main`
                    })
                }
            } else {
                wx.navigateTo({
                    url: `/pages/authorize/main`
                })
            }
        },
        // 跳我的订单
        tabDetail (e) {
            wx.navigateTo({
                url: `/pages/main/Order/main?type=${e}`
            })
        },
      // 跳页面
      tabPage (e) {
            let url
            if (e == 1) {
                showOrderRedDot().then(res => {
                    console.log(res)
                    if (!res.data) {
                        url = '/pages/main/myAftersale/main'
                        wx.navigateTo({
                            url: url
                        })
                        return
                    }
                })
            } else if (e == 2) {
                url = '/pages/main/integral/main'
            } else if (e == 3) {
                url = '/pages/main/coupons/main'
            } else if (e == 4) {
                url = '/pages/main/appointments/main'
            } else if (e == 5) {
                url = '/pages/main/assembles/main'
            }
            wx.navigateTo({
                url: url
            })
        }
    },
    onShow () {
        wx.setStorageSync('appointmentOrderId', '')
        this.token = wx.getStorageSync('token')
        this.registerFlag = wx.getStorageSync('registerFlag')
        if (this.token) {
            this.$store.dispatch('order/getOrderNum', {})
        }
    }
}
</script>

<style lang="less" scoped>
.container {
    padding-bottom: 15px;
    .txt {
        padding: 0 16px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 48px;
        border-radius: 0px 0px 8px 8px;
        background: #fff;
        .img1 {
            width: 13px;
            height: 14px;
        }
        p {
            margin-left: 8px;
            font-size: 13px;
            color: #333333;
        }
        .img2 {
            width: 7px;
            height: 11px;
            margin-left: auto;
        }
    }
    .Myodey_top {
        width: 100%;
        position: relative;
        margin-bottom: 7px;
        .Myodey_img {
            width: 100%;
            height: 192px;
        }
        .zc {
            margin-right: 10px;
        }
        .box {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 192px;
            .Head {
                display: flex;
                align-items: center;
                flex-direction: column;
                margin-top: 30px;
                img {
                    width: 71px;
                    height: 71px;
                    border-radius: 50%;
                    margin-bottom: 9px;
                }
                p {
                    font-size: 18px;
                    color: #ffffff;
                    text-align: center;
                    line-height: 25px;
                }
            }
            .personal {
                width: 80px;
                height: 28px;
                background: rgba(236, 144, 24, 1);
                border-radius: 100px 0px 0px 100px;
                position: absolute;
                right: 0px;
                top: 46px;
                display: flex;
                align-items: center;
                justify-content: center;
                span {
                    font-size: 12px;
                    color: #ffffff;
                }
                img {
                    width: 14px;
                    height: 14px;
                    margin-left: 1px;
                    vertical-align: middle;
                }
            }
        }
        
    }

    .Order {
        width: 345px;
        // padding-top: 55%;
        box-sizing: border-box;
        margin: 0 auto;
        .Order_content {
            width: 100%;
            background: #fff;
            margin-bottom: 10px;
            .Order_top {
                width: 100%;
                position: relative;
                padding: 12px 13px 12px 13px;
                box-sizing: border-box;
                &::before {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    height: 1px;
                    content: "";
                    -webkit-transform: scaleY(0.5);
                    transform: scaleY(0.5);
                    background-color: #e5e5e5;
                }
                .Order_title {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    span {
                        font-size: 13px;
                        color: #333333;
                    }
                    .Order_right {
                        display: flex;
                        align-items: center;
                        span {
                            font-size: 13px;
                            color: #9b9b9b;
                        }
                        img {
                            width: 7px;
                            height: 11px;
                            margin-left: 7px;
                        }
                    }
                }
            }
            .Order_msg {
                width: 100%;
                display: flex;
                justify-content: space-around;
                padding: 16px 0 15px;
                box-sizing: border-box;
                li {
                font-size: 0;
                position: relative;
                img {
                    width: 32px;
                    height: 32px;
                }
                p {
                    font-size: 11px;
                    color: #999999;
                    margin-top: 4px;
                    line-height: 16px;
                }
                .line {
                    position: absolute;
                    width: 15px;
                    height: 15px;
                    text-align: center;
                    top: -2px;
                    right: -8px;
                    font-size: 10px;
                    line-height: 15px;
                    border-radius: 12px;
                    color: #fff;
                    // padding: 2px 4px;
                    box-sizing: border-box;
                    background: #F01520;
                }
                }
            }
        }
        .My_msg{
            width: 100%;
            background: #fff;
            display: flex;
            justify-content: flex-start;
            &:last-child{
                border-bottom: none;
            }
            li{
                width: 114px;
                border-right: 1rpx solid #E5E5E5;
                border-bottom: 1rpx solid #E5E5E5;
                text-align: center;
                padding-top: 24px;
                padding-bottom: 15px;
                box-sizing: border-box;
                font-size: 0;
                position: relative;
                .line {
                    position: absolute;
                    width: 15px;
                    height: 15px;
                    text-align: center;
                    top: 20px;
                    right: 30px;
                    font-size: 10px;
                    line-height: 15px;
                    border-radius: 12px;
                    color: #fff;
                    // padding: 2px 4px;
                    box-sizing: border-box;
                    background: #F01520;
                }
                .redDot, .redDot_p {
                    position: absolute;
                    top: 20px;
                    right: 35px;
                    width: 15px;
                    height: 15px;
                    text-align: center;
                    font-size: 10px;
                    line-height: 15px;
                    border-radius: 12px;
                    color: #fff;
                    // padding: 2px 4px;
                    box-sizing: border-box;
                    background: #F01520;
                }
                .redDot_p {
                    top: 23px;
                    right: 38px;
                    width: 8px;
                    height: 8px;
                }
                img{
                    width: 34px;
                    height: 34px;
                }
                p{
                    font-size: 12px;
                    color: #7A7A7A;
                    margin-top: 13px;
                    line-height: 17px;
                }
            }
            .no_r {
                border-right: none;
            }
            .Card{
                border-right: none;
            }
            .myIntegral img {
                width: 30px;
                height: 34px;
            }
        }
    }
}
</style>
