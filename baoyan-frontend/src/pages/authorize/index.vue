<template lang="html">
  <div class="authorize">
    <img src="/static/images/byly.png"/>
    <button v-if="checked" name="button" class='default-btn' open-type="getUserInfo" @getuserinfo="getUserInfo">授权微信用户信息</button>
    <button v-else name="button" class='default-btn' @click="noagree">授权微信用户信息</button>
    <div class='text'>
      <checkbox class='checkbox' value="cb" color='#fff' :checked="checked" @click='checked=!checked'/>
      <span @click='toread'>我已阅读并同意《用户及隐私协议》</span>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  data () {
    return {
      info: {
        code: null
      },
      checked:false
    }
  },
  onShow () {
    let that = this
    this.info = {
      code: null
    }
    wx.login({
      success (resLogin) {
        if (resLogin.code) {
          that.info['code'] = resLogin.code
          that.$store.commit('RECORD_CODE', resLogin.code)
        }
      }
    })
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    toread(){
        wx.navigateTo({ url: `/pages/main/agreeprivacy/main` })
    },
    ...mapActions([
      'getAccessToken'
    ]),
    noagree(){
      if(!this.checked){
        wx.showToast({ title:'请先阅读并同意用户及隐私协议', icon:'none', mask: true})
        return
      }
    },
    getUserInfo (e) {
      const userInfo = e.target && e.target.userInfo
      if (!userInfo) {
        wx.showToast({ title:'拒绝授权，将无法使用本小程序全部功能，请重新获取授权！', icon:'none', mask: true})
        return
      }
      this.info.encryptedData = e.target.encryptedData
      this.info.iv = e.target.iv
      this.info.nickname = e.target.userInfo.nickName
      this.info.headImg = e.target.userInfo.avatarUrl
      this.$store.dispatch('getAccessToken', this.info).then(res => {
        if (res) {
          // 获取个人中心页面信息
          // this.$store.dispatch('users/getUserInfo', {})
          //  授权成功，获取到用户信息
          let registerFlag = wx.getStorageSync('registerFlag')
          if (!registerFlag) {
              wx.redirectTo({ url: `/pages/login/main` })
              return
          }
          wx.navigateBack({
            delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
          })
        }
      })
    }
  }
}
</script>

<style lang="less">
.authorize {
  width: 100vw;
  height: 100vh;
  background: #fff;
  clear: both;
  overflow: hidden;
  text-align: center;
  img{
    width:248px;
    height: 124px;
    margin:0 auto;
    margin-top:69px;
    margin-bottom:62px;
  }
  button {
    width:275px;
    height:38px;
    padding: 0;
    line-height: 38px;
    color: #FFFFFF;
    font-size: 17px;
    // background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
background: linear-gradient(270deg, #F46A22 0%, #F4A424 100%);
box-shadow: 1px 4px 8px 0px rgba(244, 106, 34, 0.24);
    border-radius:22px;
    margin: 0 auto;
  }
  .checkbox .wx-checkbox-input {
        width: 20rpx; /* 背景的宽 */
        height: 20rpx; /* 背景的高 */
        border-color: #EE8200;
        border-radius: 50%;
  }
      .checkbox  .wx-checkbox-input.wx-checkbox-input-checked::before {
        font-size: 8px;
        background: #EE8200;
        transform: translate(-50%, -50%) scale(1);
        -webkit-transform: translate(-50%, -50%) scale(1);
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        color: transparent;
      }
  .text{
    margin-top: 15px;
    font-size: 10px;
    color: #A6A6A6;
    line-height: 18rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
