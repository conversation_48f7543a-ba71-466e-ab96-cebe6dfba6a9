<template>
  <div class="container">
    <div class="box">
      <div class="header">
        <div class="banner">
          <swiper
            class="swiper"
            @change="swiperChange"
            :autoplay="true"
            :interval="2000"
          >
            <swiper-item v-for="(item, index) in banner" :key="index">
              <image :src="item" mode="aspectFill" class="slide-image" />
            </swiper-item>
          </swiper>
          <div class="point">{{curr}}/{{banner.length}}</div>
        </div>
        <div class="cont">
          <div class="title">
            <h5>{{detail.goodsName}}</h5>
            <button open-type="share" class="share-bt-box">
              <div class="share-box">
                <img class="share" src="/static/images/icon-share.png" alt />
                <p>分享</p>
              </div>
            </button>
          </div>
          <div class="text">
            <p class="num">¥{{detail.sellPrice}}</p>
            <span class="abolish">¥{{detail.marketPrice}}</span>
          </div>
        </div>
      </div>
      <!--1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品 -->
      <div class="detail" v-if="type != 1">
        <p
          class="detail-title"
        >{{type == 1 ? '普通商品' : type==2?'联票详情':type==3?'次卡详情':type==7?'联票详情':type==6?'普通商品':'次卡详情'}}</p>
        <ul class="detail-list">
          <li v-for="(item, index) in goods" :key="index" @click="seeDetail(item.id,1)">
            <img class="d-img" :src="item.goodsImg" alt />
            <div class="right">
              <p class="d-name">{{item.goodsName}}</p>
              <p class="num">x {{ item.subCardGoodsNum }}</p>
            </div>
          </li>
        </ul>
      </div>
      <!-- <div class="evaluate" v-if="evaluate.length > 0">
        <div class="title-box" @click.stop="getMore">
          <p class="eval">商品评价（{{evaluate.length ? evaluate.length : 0}}）</p>
          <div class="more">
            <span class="txt">查看更多</span>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
          </div>
        </div>
        <div
            class="content-box"
            v-if="evaluate.length > 0 && index < 2"
            v-for="(item, index) in evaluate"
            :key="index"
            >
            <div class="info">
                <div class="my-info">
                    <img class="headimg" :src="item.isAnonymous == 1 ? '/static/images/icon-no-headimg.png' : item.headImg" alt="">
                    <p class="name">{{ item.userName }}</p>
                </div>
                <p class="time">{{item.gmtCreate}}</p>
            </div>
            <p class="content">{{item.evaluateText}}</p>
            <div class="imgBox" v-if="item.img.length">
                <img v-for="(items,index2) in item.img" @click="previewImageHandler(item.img)" :key="index2" :src="items " alt="">
            </div>
        </div>
        <div class="tibs" v-if="!evaluate.length">当前暂无评价</div>
      </div> -->
      <!-- <div class="img-box" v-html="detail.richContent"></div> -->
      <div class="img-box">
         <wxParse :content="richContent" noData="" /> 
      </div>  
    </div>
    <div class="footer">
      <ul class="icon-list">
        <li @click.stop="tabPage(1)">
          <img src="/static/images/icon-goback.png" alt class="icon" />
          <p>首页</p>
        </li>
        <li class="service">
          <button open-type="contact" class="customer">
            <img src="/static/images/icon-Customer-service.png" alt class="icon" />
            <p>客服</p>
          </button>
        </li>
        <li @click.stop="tabPage(2)">
          <img src="/static/images/icon-shop-car.png" alt class="icon" />
          <p>购物车</p>
          <i class="icon-num" v-if="shoppingCount">{{shoppingCount}}</i>
        </li>
      </ul>
      <div class="btn">
        <button class="addcar" @click="isShow = true, commitType = 1">加入购物车</button>
        <button @click="isShow = true, commitType = 2">立即购买</button>
      </div>
    </div>
    <div class="mask" catchtouchmove="ture" v-if="isShow">
      <div class="main">
        <div class="info">
          <img :src="detail.goodsImg" mode="aspectFill" alt class="headimg" />
          <div class="right_info">
            <div class="many">
              <h6>¥{{detail.sellPrice}}</h6>
              <p class="abolish">¥{{detail.marketPrice}}</p>
            </div>
            <img
              @click="isShow = false"
              class="close"
              mode="aspectFit"
              src="/static/images/icon-Close.png"
              alt
            />
          </div>
        </div>
        <div class="number">
          <p>数量：</p>
          <div class="box box1">
            <img
              @click.stop="changeNum(1)"
              mode="aspectFit"
              src="/static/images/icon-goods-reduce.png"
              alt
              class="icon"
            />
            <p class="num">{{num}}</p>
            <img
              @click.stop="changeNum(2)"
              mode="aspectFit"
              src="/static/images/icon-goods-plus.png"
              alt
              class="icon"
            />
          </div>
        </div>
        <button class="determine" @click.stop="getcommit">确定</button>
      </div>
    </div>
  </div>
</template>

<script>
import { getGoodsdetail, checkUserDisable } from "@/api";
import toast from "@/plugins/toast";
import wxParse from 'mpvue-wxparse'
export default {
    data() {
        return {
            num: 1,
            curr: 1,
            type: 0,
            goodsId: 0,
            detail: {},
            banner: [],
            evaluate: [],
            goods: [],
            isShow: false,
            commitType: 0,
            shoppingCount: "",
            richContent2: '',
            token: '',
            registerFlag: ''
        }
    },
    components:{
        wxParse
    },
    onShareAppMessage(res) {
        return {
            title: this.detail.goodsName,
            path: `pages/home/<USER>/classDetails/main?id=${this.goodsId}&type=${this.type}`
        }
    },
    methods: {
        previewImageHandler(urls,current) {
            wx.previewImage({
                current: urls[0],
                urls
            })
        },
        seeDetail(id,type) {
            wx.redirectTo({
                url: `/pages/home/<USER>/classDetails/main?id=${id}&type=${type}`
            })
            // this.goodsId = id
            // this.type = type
            // this.queryGoodsDetail()
            // wx.pageScrollTo({
            //     scrollTop: 0,
            //     duration: 500
            // })
        },
        swiperChange(e) {
            this.curr = e.target.current + 1
        },
        // 跳转页面
        tabPage(e) {
            if (e == 1) {
                wx.switchTab({
                    url: '/pages/index/main'
                })
            } else {
                wx.navigateTo({ 
                    url: '/pages/home/<USER>/main' 
                })
            }
        },
        // 评价查看更多
        getMore() {
            if (this.evaluate.length) {
                let _this = this
                let id = this.$root.$mp.query.id
                wx.navigateTo({
                    url: `/pages/home/<USER>/clusterGoods/comment/main?id=${id}&type=${
                        _this.type
                    }`
                })
            }
        },
        // 加减商品数量
        changeNum(e) {
            if (e == 1) {
                if (this.num > 1) {
                    this.num --
                } else {
                    toast.text("至少保留一件商品");
                }
            } else {
                if (this.num < this.detail.goodsStock) {
                    this.num ++
                } else {
                    toast.text("库存不足")
                }
            }
        },
        // 确认
        getcommit() {
            if (!this.token) {
                wx.showModal({
                    content: '您还没有登录', 
                    cancelText: '取消', 
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            } else {
                if (!this.registerFlag) {
                    wx.showModal({
                        content: '您还没有注册', 
                        cancelText: '取消', 
                        confirmText: '去注册',
                        success: res => {
                            if (res.confirm) {
                                wx.navigateTo({ url: `/pages/login/main` })
                            }
                        }
                    })
                    return
                }
            }
            if (this.num <= this.detail.goodsStock) {
                if (this.commitType == 1) {
                // 加入购物车
                let params = {
                    goodsId: this.$root.$mp.query.id,
                    goodsType: this.type*1,
                    goodsCount: this.num
                };
                this.$store.dispatch("shop/queryShop", params).then(res => {
                    if (res.code == 0) {
                    this.isShow = false;
                    /**
                     * 因为没有专门查购物车数量的接口所以只能再调一次查详情 本地加不知道规则
                     */
                    this.queryGoodsDetail();
                    toast.text("添加成功");
                    } else {
                    toast.text(res.msg);
                    }
                });
                } else if (this.commitType == 2) {
                    let id = this.$root.$mp.query.id;
                    // 立即购买
                    let obj = {
                        type: 1,
                        goodsType: this.type*1,
                        id: parseInt(id),
                        goodsCount: this.num,
                        goods: []
                    };
                    checkUserDisable({type: this.type,ids: this.goodsId}).then(data => {
                        if (data.data.code == 0) {
                            this.$store.dispatch("shop/payDetail", obj).then(res => {
                                if (res.code) {
                                    toast.text(res.msg);
                                } else {
                                    wx.navigateTo({
                                        url: `/pages/ticket/orders/main?type=1&goodsType=${
                                        this.type
                                        }&id=${id}&goodsCount=${this.num}`
                                    })
                                }
                            })
                        } else {
                            toast.text(data.data.msg)
                        }
                    })
                }
            } else {
                toast.text("库存不足");
            }
        },
        /**
         * 查询商品详情
         * */

        queryGoodsDetail() {
            let params = {
                id: this.goodsId,
                type: this.type
            }
            getGoodsdetail(params).then(res => {
                if (res.data.code) {
                    wx.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 1500
                    });
                } else {
                    let data = res.data;
                    if (data.byGoodsInfo) {
                        this.detail = data.byGoodsInfo;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.byGoodsInfo.goodsBanner
                        ? data.byGoodsInfo.goodsBanner.split(",")
                        : [];
                    } else if (data.bySubCardGoods) {
                        this.detail = data.bySubCardGoods;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.bySubCardGoods.goodsBanner
                        ? data.bySubCardGoods.goodsBanner.split(",")
                        : [];
                    } else {
                        this.detail = data.byTicketGoods;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.byTicketGoods.goodsBanner
                        ? data.byTicketGoods.goodsBanner.split(",")
                        : [];
                    }
                    this.evaluate = data.evaluate;
                    this.evaluate.forEach((item,index) => {
                        if (item.isAnonymous) {
                            item.userName = item.userName ? item.userName.substr(0, 1)+'****' : ''
                        }
                        if (item.img) {
                            item.img = item.img.split(',')
                        } else {
                            item.img = []
                        }                    
                    })
                    this.goods = data.goods;
                    this.shoppingCount = data.shoppingCount;
                }
            })
        }
    },
    onShow() {
        this.token = wx.getStorageSync('token')
        this.registerFlag = wx.getStorageSync('registerFlag')
    },
    onLoad() {
        this.curr = 1
        this.banner = []
        this.num = 1;
        this.commitType = 0;
        this.isShow = false;
        // 6 普通商品 7联票商品 8次卡商品 /1 普通商品 2 次卡 3 联票
        this.type = this.$root.$mp.query.type;
        this.goodsId = this.$root.$mp.query.id;
        this.queryGoodsDetail();
        //this.type = type
        // if ( this.type == 6 ) {
        //     type = 1
        // } else if ( this.type == 7 ) {
        //     type = 3
        // } else if ( this.type == 8 ) {
        //     type = 2
        // } else {
        //     type = this.type
        // }
    }
}
</script>

<style lang="less">
.container {
  overflow: hidden;
  .box {
    padding-bottom: 50px;
    box-sizing: border-box;
  }
  .imgBox {
      margin-top: 5px;
      img {
          width: 60px;
          height: 60px;
          margin-right: 11px;
      }
  }
  .header {
    margin-bottom: 10px;
    .banner {
      width: 100%;
      height: 375px;
      position: relative;
      .swiper {
        width: 100%;
        height: 375px;
        .slide-image {
          width: 100%;
          height: 375px;
        }
      }
      .point {
        position: absolute;
        right: 10px;
        bottom: 10px;
        padding: 0 10px;
        box-sizing: border-box;
        height: 21px;
        line-height: 21px;
        border-radius: 11px;
        background: rgba(0, 0, 0, 0.2);
        font-size: 14px;
        color: #fff;
      }
    }
    .cont {
      width: 100%;
      padding: 11px 15px 6px;
      box-sizing: border-box;
      background: #fff;
      .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        h5 {
          width: 304px;
          font-size: 18px;
          line-height: 25px;
          font-weight: 500;
          color: #171717;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .share-bt-box {
          background: rgba(0, 0, 0, 0);
          .share-box {
            width: 19px;
            font-size: 9px;
            color: #9b9b9b;
            line-height: 13px;
            text-align: center;
            .share {
              width: 19px;
              height: 19px;
              // margin-bottom: 3px;
            }
          }
        }
      }
      .text {
        // display: flex;
        .num {
          font-size: 26px;
          line-height: 37px;
          color: #ff5100;
          margin-right: 10px;
          display: inline-block;
        }
        .abolish {
          font-size: 16px;
          color: #999999;
          text-decoration: line-through;
        }
      }
    }
  }
  .detail {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .detail-title {
      width: 100%;
      height: 44px;
      line-height: 44px;
      padding: 0 15px;
      font-size: 14px;
      color: #302e27;
    }
    .detail-list {
      width: 100%;
      li {
        width: 100%;
        padding: 10px 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        position: relative;
        &::after {
          position: absolute;
          right: 0;
          top: 0;
          left: 0;
          height: 1px;
          content: "";
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #d6d6d6;
        }
        .d-img {
          width: 60px;
          height: 60px;
          margin-right: 12px;
        }
        .right {
          width: 272px;
          .d-name {
            width: 266px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 14px;
            color: #302e27;
            line-height: 20px;
            margin-bottom: 3px;
          }
          .num {
            width: 100%;
            font-size: 12px;
            color: #302e27;
            line-height: 17px;
            text-align: right;
          }
        }
      }
    }
  }
  .evaluate {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .title-box {
      width: 100%;
      height: 42px;
      display: flex;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      justify-content: space-between;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      .eval {
        font-size: 14px;
        color: #302e27;
        font-weight:500;
        line-height: 20px;
      }
      .more {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9b9b9b;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .content-box {
      width: 100%;
      padding: 14px 15px 7px;
      box-sizing: border-box;
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .my-info {
          display: flex;
          align-items: center;
          margin-bottom: 13px;
          .headimg {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
          }
          .name {
            font-size: 12px;
            color: #090203;
            line-height: 17px;
          }
        }
        .time {
          font-size: 12px;
          color: #9b9b9b;
          line-height: 17px;
        }
      }
      .content {
        font-size: 14px;
        color: #090203;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
    .tibs {
      padding: 10px 15px;
      box-sizing: border-box;
      font-size: 12px;
      color: #9b9b9b;
      line-height: 17px;
    }
  }
  .img-box {
    width: 100%;
    img {
      width: 100%;
      
    }
    image {
        display:flex !important;
    }
  }
  .footer {
    width: 100%;
    height: 49px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 10px 0 0;
    box-sizing: border-box;
    justify-content: space-between;
    .icon-list {
      width: 145px;
      display: flex;
      align-items: center;
      justify-content: center;
      li,
      .customer {
        padding: 0 11px;
        box-sizing: border-box;
        font-size: 0;
        text-align: center;
        position: relative;
        background: #fff;
        .icon {
          width: 23px;
          height: 23px;
          margin-bottom: 1px;
        }
        p {
          font-size: 10px;
          line-height: 14px;
          color: #4a4a4a;
        }
        .icon-num {
          position: absolute;
          top: -2px;
          right: 8px;
          font-size: 10px;
          line-height: 10px;
          border-radius: 12px;
          color: #fff;
          padding: 2px 4px;
          box-sizing: border-box;
          background: #f01520;
        }
      }
      .service {
        padding: 0;
      }
    }
    .btn {
      width: 220px;
      height: 36px;
      line-height: 36px;
      display: flex;
      align-items: center;
      button {
        width: 110px;
        height: 36px;
        padding: 0;
        font-size: 14px;
        color: #fff;
        border-radius: 0 18px 18px 0;
        background: linear-gradient(
          90deg,
          rgba(255, 151, 56, 1) 0%,
          rgba(254, 116, 61, 1) 100%
        );
      }
      .addcar {
        border-radius: 18px 0 0 18px;
        background: #4aa343;
      }
    }
  }
  .mask {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10;
    .main {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      background: #fff;
      padding: 0 15px 12px;
      box-sizing: border-box;
      z-index: 11;
      .info {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 25px;
        .headimg {
          width: 96px;
          height: 96px;
          border-radius: 4px;
          margin-top: -15px;
          background: #fff;
        }
        .right_info {
          width: 240px;
          padding: 10px 0 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          .many {
            h6 {
              font-size: 18px;
              font-weight:500;
              color: #ff5100;
              line-height: 25px;
              margin-bottom: 10px;
            }
            .abolish {
              font-size: 12px;
              color: #615454;
              line-height: 17px;
              text-decoration: line-through;
            }
          }
          .close {
            width: 15px;
            height: 15px;
            position: absolute;
            right: 16px;
            top: 16px;
          }
        }
      }
      .number {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        color: #615454;
        line-height: 20px;
        margin-bottom: 33px;
        .box {
          display: flex;
          align-items: center;
          &.box1 {
            padding: 0;
          }
          .icon {
            width: 33px;
            height: 30px;
          }
          .num {
            width: 38px;
            height: 30px;
            line-height: 30px;
            background: #edebeb;
            text-align: center;
            overflow: hidden;
            font-size: 14px;
            color: #343333;
            margin: 0 1px;
          }
        }
       
      }
      .determine {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 0;
        border-radius: 22px;
        text-align: center;
        background: linear-gradient(
          90deg,
          rgba(255, 151, 56, 1) 0%,
          rgba(254, 116, 61, 1) 100%
        );
        font-size: 16px;
        color: #fff;
      }
    }
  }
}
</style>
