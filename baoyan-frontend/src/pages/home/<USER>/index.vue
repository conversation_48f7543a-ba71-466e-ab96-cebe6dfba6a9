<template>
  <div class="container">
    <scroll-view class="scrollView" style="height: 100vh" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
      <div class="main">
        <div class="volume-box" v-for="(item, index) in dataList" :key="index">
          <img  v-if="item.couponType==1 && item.full" src="/static/images/icon-Coupon.png" alt="">
          <img v-if="item.couponType==1 && !item.full"  src="/static/images/icon-Deduction-Volume.png" alt="">
          <img v-if="item.couponType==2"  src="/static/images/icon-Discount-Volume.png" alt="">
          <div class="volume">
            <div class="many" v-if="item.couponType==2">
              <h5>{{item.discount}}折</h5>
              <p class="text">折扣券</p>
            </div>
            <div class="many" v-else>
                <h5>￥{{item.discount}}</h5>
                <p class="text">{{!item.full?'抵扣券':'优惠券'}}</p>
            </div>
            <div class="content">
              <h6>{{item.type==0?'全部商品可用':'限'+ item.limitation +'商品可用'}}</h6>
              <p v-if="item.full">满{{item.full}}可用</p>
              <p class="time">{{item.startDate}}～{{item.endDate}}</p>
            </div>
            <button v-if="item.registerType!==0" @click="receive(item.id,index)" :disabled="item.registerType===0" :class=" item.couponType==2 ? 'blue' : (!item.full? 'yellow' : 'red') ">立即领取</button>
            <button v-else class="gray">立即领取</button>
          </div>
        </div>
      </div>
      <div v-if="!dataList.length" class="noneData">没有更多数据啦...</div>
    </scroll-view>
  </div>
</template>

<script>
import { couponList , receiveCoupon } from '@/api'
import toast from '@/plugins/toast'
export default {
    data () {
        return {
            dataList: []
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        getList() {
            couponList().then(res => {
                if(res.data.length) {
                    this.dataList = res.data
                }
            })
        },
        // 领取优惠券
        receive (couponId,index) {
            let token = wx.getStorageSync('token')
            let registerFlag = wx.getStorageSync('registerFlag')
            if (!token) {
                wx.showModal({
                    content: '您还没有登录', 
                    cancelText: '取消', 
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            }
            if (!registerFlag) {
                wx.showModal({
                    content: '您还没有注册', 
                    cancelText: '取消', 
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }           
            receiveCoupon({couponId}).then(res => {
                if (res.data.code) {
                    toast.text(res.data.msg)
                } else {
                    this.dataList[index].disFlag = false
                    this.getList()
                    toast.text('领取成功')
                }
            })
        },
    },
    onLoad () {
        this.getList()
    }
}
</script>

<style lang="less">
.container {
  .main {
    width: 100%;
    padding: 10px 15px 0;
    box-sizing: border-box;
    .volume-box {
      width: 345px;
      height: 93px;
      margin-bottom: 10px;
      position: relative;
      img {
        width: 345px;
        height: 93px;
      }
      .volume {
        position: absolute;
        width: 345px;
        height: 93px;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        .many {
          box-sizing: border-box;
          text-align: center;
          width: 95px;
          h5 {
            font-size: 20px;
            line-height: 28px;
            color: #fff;
            // margin-bottom: 3px;
          }
          .text {
            font-size: 12px;
            color: #fff;
          }
        }
        .content {
          font-size: 12px;
          color: #746C6C;
          line-height: 17px;
          margin-right: 26px;
          margin-left: 20px;
          h6 {
            font-size: 14px;
            color: #290B0B;
            margin-bottom: 8px;
            width: 205px;
          }
        }
        .time {
            font-size: 10px;
            color: #ada8a8;
            height: 28px;
            line-height: 28px;
          }
        button {
          width: 74px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          padding: 0;
          border-radius: 13px;
          color: #fff;
          background: #D8D8D8;
          font-size: 13px;
          position: absolute;
          right: 10px;
        }
        .red {
          background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
        }
        .blue {
          background:linear-gradient(270deg,rgba(129,201,255,1) 0%,rgba(56,149,255,1) 100%);
        }
        .yellow {
          background:linear-gradient(270deg,rgba(255,186,22,1) 0%,rgba(254,140,31,1) 100%);
        }
        .gray {
            color: #fff;
            background: #D8D8D8;
        }
      }
    }
  }
}
</style>
