<template>
    <div class="container">
        <div class="header">
            <img class="head-img" :src="imgUrl" alt="">
            <img class="line" src="/static/images/icon-line.png" alt="">
        </div>
        <div class="scrollView">
        <!-- <scroll-view class="scrollView" :style="{height: scrollViewHeight}" :lower-threshold="100" @scrolltolower="loadMore" scroll-y> -->
            <ul class="list">
                <li v-for="(item, index) in ticket" :key="index" @click="tabDetail(item)">
                    <div class="img-box">
                        <img :src="item.goodsImg" alt="">
                        <div class="group-box">
                            <img class="group-img" src="/static/images/icon-group.png" alt="">
                            <p class="group-text">{{item.teamNum}}人团</p>
                        </div>
                    </div>
                    <p class="title">{{item.teamName}}</p>
                    <p class="tibs">拼团价：¥<span>{{item.teamPrice}}</span></p>
                </li>
            </ul>
            <div v-if="!ticket.length" class="noneData">没有更多数据啦...</div>
        </div>
        <!-- </scroll-view> -->
    </div>
</template>

<script>
export default {
    data () {
        return {
            imgUrl: '',
            page: 1,
            pageSize: 20,
            noService: false,
        }
    },
    computed: {
        scrollViewHeight () {
        if (this.$store.state.windowHeight) {
            return `${this.$store.state.windowHeight - 55}px`
        }
        return '100%'
        },
        ticket () {
        return this.$store.state.team.ticket
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onReachBottom() {
        if (this.noService) {
            return
        }
        this.page ++
        this.getInfo()
    },
    methods: {
        loadMore () {
            if (this.noService) {
                return
            }
            this.page++
            this.getinfo()
        },
        getinfo () {
        let params = {
            pageIndex: this.page,
            pageSize: this.pageSize
        }
        this.$store.dispatch('team/getTicket', params).then(res => {
            this.noService = res
        })
        },
        // 跳转详情
        tabDetail (item) {
        wx.navigateTo({
            url: `/pages/home/<USER>/clusterGoods/main?id=${item.id}`
        })
        }
    },
    onLoad () {
        this.$store.commit('getWindowHeight')
        this.imgUrl = wx.getStorageSync('imgUrl')
        this.page = 1
        this.noService = false
        this.$store.commit('team/RECORD_TICKET', [])
        this.getinfo()
    }
}
</script>

<style lang="less">
.container {
    ::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
    }
  .header {
    width: 100%;
    height: 219px;
    position: fixed;
    top: 0;
    z-index: 999;
    left: 0;
    .head-img {
      width: 100%;
      height: 200px
    }
    .line {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 35px;
    }
  }
  .scrollView {
      margin-top: 219px;
  }
  .list {
    width: 100%;
    padding: 3px 12px 0;
    box-sizing: border-box;
    li {
      width: 100%;
      margin-bottom: 10px;
      padding: 0 0 7px;
      box-sizing: border-box;
      background: #fff;
      &:last-child {
        margin-bottom: 0;
      }
      .img-box {
        width: 100%;
        height: 184px;
        position: relative;
        img {
          width: 100%;
          height: 184px;
          border-radius: 10px 10px 0 0;
        }
        .group-box {
          width: 70px;
          height: 26px;
          position: absolute;
          left: 0;
          bottom: 0;
          .group-img {
            width: 70px;
            height: 26px;
          }
          .group-text {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 70px;
            height: 26px;
            line-height: 26px;
            text-align: center;
            font-size: 13px;
            color: #fff;
          }
        }
      }
      
      .title {
        width: 336px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 14px;
        line-height: 20px;
        color: #343333;
        margin: 10px auto 11px;
      }
      .tibs {
        width: 336px;
        font-size: 14px;
        line-height: 20px;
        color: #FF5100;
        margin: 0 auto;
        span {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
