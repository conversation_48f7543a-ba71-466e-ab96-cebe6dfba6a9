<template>
    <div class="container">
            <ul class="types1">
                <li>
                    <p :class="{active:sortType===1}" @click="tabInfo(1)">综合</p>
                </li>
                <li @click="tabInfo(2)">
                    <p :class="{active:sortType===2}">价格</p>
                    <div class="triangle-box">
                        <div :class="{'triangle-top':true,'triangle-top-select':isPriceAscending===false}"></div>
                        <div :class="{'triangle-bottom':true,'triangle-bottom-select':isPriceAscending===true}"></div>
                    </div>
                </li>
                <li @click="tabInfo(3)">
                    <p :class="{active:sortType===3}">距离</p>
                    <div class="triangle-box">
                        <div :class="{'triangle-top':true,'triangle-top-select':isDistanceAscending===true}"></div>
                        <div :class="{'triangle-bottom':true,'triangle-bottom-select':isDistanceAscending===false}"></div>
                    </div>
                </li>
            </ul>
        <scroll-view class="scrollView" :style="{height: scrollViewHeight}" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
        <div class="header" v-if="baseBanner.length==0">
          <img class="head-img" :src="imgUrl" alt="" >
        </div>
          <div class="header"  v-if="baseBanner.length!=0">
            <div class="bannerbg">
              <div class="swiperbox">
                <swiper
                  class="swiper"
                  indicator-color="rgba(255,255,255,0.5)"
                  indicator-active-color="#fff"
                  :autoplay="true"
                  :interval="3000"
                  :duration="1000"
                  :circular="true"
                >
                  <div v-for="(item,index) in baseBanner" :key="index">
                    <swiper-item>
                      <image :src="item" class="slide-image" mode="aspectFill"/>
                    </swiper-item>
                  </div>
                </swiper>
              </div>
            </div>
          </div>
            <ul class="list">
                <li v-for="(item, index) in products" :key="index" @click="goDetail(item)">
                <img :src="item.goodsImg" alt="">
                <div class="conter">
                    <p class="title">{{item.goodsName}}</p>
                    <div class="tibs">
                        <p class="num" v-if="!item.isCombination">¥{{item.sellPrice}}
                            <span class="marketPrice">¥{{item.marketPrice}}</span>
                        </p>
                        <p class="num" v-else>¥{{item.lowPrice}}
                            <span class="marketPrice1">起</span>
                        </p>
                        <div class="but_new">
                            立即抢购
                        </div>
                    </div>
                </div>
                </li>
            </ul>
            <div v-if="!products.length" class="noneData">没有更多数据啦...</div>
        </scroll-view>
    </div>

</template>

<script>
import { ticketGoods, getClassProduct } from '@/api'

export default {
    data () {
        return {
            lat: '',
            lng: '',
            target: '',
            classId:'',
            title: '',
            imgUrl: '',
            baseBanner:[],
            page: 1,
            type: 1,
            jumpType:1,
            pageSize: 20,
            noService: false,
            products: [],
            noService: false,
            price: false, //价格
            distance: false, //距离
            sales: false, //销量
            sortType: 1, //排序类型
            isPriceAscending: '', //是否价格升序
            isDistanceAscending: '', //是否距离升序
            isSalesAscending: '' //是否距离升序
        }
    },
    computed: {
        scrollViewHeight () {
        if (this.$store.state.windowHeight) {
            return `${this.$store.state.windowHeight - 40}px`
        }
        return '100%'
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        // getBaseBanner(val){
        //     if (val){
        //         // console.log(JSON.stringify(val).split(","))
        //         // console.log(val.split(","))
        //         return val.split(",");
        //     }
        //     return [];
        // },
        // 经纬度
        loadInfo () {
            let _this = this
            wx.getLocation({
                type: 'wgs84', // 返回可以用于wx.openLocation的经纬度
                success(res) {
                    let latArr = { lat: res.latitude, lng: res.longitude }
                    _this.lat = res.latitude
                    _this.lng = res.longitude
                    wx.setStorageSync('latArr', latArr)
                    _this.latArr = wx.getStorageSync('latArr')
                    _this.page = 1
                    _this.noService = false
                    _this.$store.commit("besp/RECORD_PRODUCTS", [])
                    _this.getInfo()                },
                fail() {
                    wx.showModal({
                        title: '温馨提示', //提示的标题,
                        content: '距离筛选需要您授权地理位置',
                        confirmText: '去授权',
                        success: res => {
                            if (res.confirm) {
                                wx.openSetting({
                                    success(res) {
                                        _this.loadInfo()
                                    }
                                })
                            }
                        }
                    })
                }
            })
        },
        goDetail(item) {
            wx.navigateTo({
                url: `/pages/home/<USER>/classDetails/main?id=${item.id}&type=${this.type}`
            })
        },
        loadMore () {
            if (this.noService) {
                return
            }
            this.page++
            this.getInfo()
        },

        // 更新数据/1 综合 2 价格 低 3 高 4 距离 近 5 远 6 销量 低 7 高
        tabInfo(e) {
            this.sortType = e
            if (e == 1) {
                this.type = 1
                this.isPriceAscending = '', //是否价格升序
                this.isDistanceAscending = '', //是否距离升序
                this.isSalesAscending = '' //是否距离升序
            } else if (e == 2) {
                this.price = !this.price;
                this.isPriceAscending = !this.isPriceAscending
                this.type = this.price ? 3 : 2
            } else if (e == 3) {
                this.isDistanceAscending = !this.isDistanceAscending
                this.distance = !this.distance
                this.type = this.distance ? 5 : 4
                if (!this.latArr) {
                    this.loadInfo()
                    return
                }
            }
            this.page = 1
            this.noService = false
            this.$store.commit("besp/RECORD_PRODUCTS", [])
            this.products=[]
            this.getInfo()
        },
        getInfo () {
                let param = {
                    latitude: this.lat,
                    longitude: this.lng,
                    type: this.type,
                    jumpType:this.jumpType,
                    classId: this.classId,
                    goodsName: '',
                    pageIndex: this.page,
                    pageSize: this.pageSize
                }
                getClassProduct(param).then(res => {
                    if (res.data.code) {
                        wx.showToast({
                            title: res.data.msg, //提示的内容,
                            icon: 'none', //图标,
                            duration: 1500
                        })
                    } else {
                        this.products = res.data.list
                    }
                })
        }
    },
    onLoad () {
        this.price= false, //价格
        this.distance= false, //距离
        this.sales= false, //销量
        this.sortType= 1, //排序类型
        this.isPriceAscending= '', //是否价格升序
        this.isDistanceAscending= '', //是否距离升序
        this.isSalesAscending= '' //是否距离升序
        this.title = this.$root.$mp.query.title
        wx.setNavigationBarTitle({
            title: this.title
        })
        this.type = this.$root.$mp.query.type
        this.jumpType = this.$root.$mp.query.type
        this.target = this.$root.$mp.query.target
        this.classId = this.$root.$mp.query.target
        this.$store.commit('getWindowHeight')
        let latArr = wx.getStorageSync('latArr')
        this.lat = latArr.lat
        this.lng = latArr.lng
        this.imgUrl = wx.getStorageSync('imgUrl')
        if (wx.getStorageSync('baseBanner')){
            this.baseBanner=wx.getStorageSync('baseBanner').split(",");
        }else {
            this.baseBanner=[]
        }
        this.page = 1
        this.noService = false
        this.$store.commit('besp/RECORD_PRODUCTS', [])
        this.getInfo()
    }
}
</script>

<style lang="less">
.container {
  .header {
    padding: 3px 11px 0;
    box-sizing: border-box;
    width: 100%;
    height: 225px;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
    .head-img {
      margin-top: 10px;
      width: 100%;
    height: 200px;
    border-radius: 6px;
      box-shadow: 1px 1px 10px 0px rgba(152, 152, 152, 0.33);
    }
    .swiper {
      margin-top: 10px;
      width: 100%;
      height: 200px;
      border-radius: 10px;
      overflow: hidden;
      ._swiper-item {
        border-radius: 10px;
        overflow: hidden;
      }
      .slide-image {
        width: 100%;
        height: 200px;
      }
    }
    .line {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 35px;
    }
  }
  .scrollView{
      margin-top: 40px;
  }
  .list {
    width: 100%;
    padding: 3px 15px 0;
    box-sizing: border-box;
    align-items: center;
    li {
      width: 100%;
      margin: 0 9px 12px 0;
      display: flex;
      justify-content: space-between;
      &:nth-child(2n){
        margin-right: 0;
      }
      img {
        width: 130px;
        height: 120px;
        border-radius: 10px;
      }
      .conter{
        width: calc(100% - 140px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .title {
        font-size: 14px;
        height: 40px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 20px;
        text-align: left;
        color: #000000;
        margin-top: 6px;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .tibs {
        align-items: center;
        font-size: 11px;
        line-height: 16px;
        color: #9B9B9B;
        display: flex;
        justify-content: space-between;
        .num {
          font-size: 20px;
          line-height: 26px;
          color: #FF3E45
        }
            .marketPrice{
                color:#666666;
                font-size: 12px;
                text-decoration:line-through;
            }
            .marketPrice1{
                color:#666666;
                font-size: 12px;
            }
            .but_new{
                width: 76px;
                height: 27px;
                background: linear-gradient(0deg, #E87E00, #FD9C28);
                border-radius: 14px;
                text-align: center;
                line-height: 27px;
                color: white;
            }
      }
    }
  }

    .types1 {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 21px 0 25px;
        box-sizing: border-box;
        background: #fff;
        justify-content: space-between;
        margin-bottom: 10px;
        position: fixed;
        top: 0;
        z-index: 10;
        li {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #4a4a4a;
            line-height: 20px;
            img {
                width: 7px;
                height: 10px;
                margin-left: 3px;
            }
            .triangle-box {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: space-around;
                flex-direction: column;
                .triangle-top {
                width: 0;
                height: 0;
                border: 4px solid;
                border-color: transparent transparent rgba(0, 0, 0, 0.5);
                }
                .triangle-top-select {
                border-color: transparent transparent rgb(251,124,60,1);
                }
                .triangle-bottom {
                width: 0;
                height: 0;
                border: 4px solid;
                border-color: rgba(0, 0, 0, 0.5) transparent transparent;
                }
                .triangle-bottom-select {
                border-color: rgba(251,124,60,1) transparent transparent;
                }
            }
        }
        .active {
            color: #fb7c3c;
        }
    }
}
</style>
