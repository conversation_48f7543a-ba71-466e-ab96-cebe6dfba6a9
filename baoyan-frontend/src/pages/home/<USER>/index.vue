

<template>
  <div class="container">
    <!-- <div class="main" v-html="byarticle"></div> -->
    <div class="articleinfo">
      <wxParse :content="byarticle"/> 
    </div>
    <div class="footer" v-if="goods.goodsId">
      <ul class="icon-list">
        <li @click.stop="tabPage(1)">
          <img src="/static/images/icon-goback.png" style="width:41rpx;height:38rpx;margin-top:5rpx;" alt class="icon" />
          <p>首页</p>
        </li>
        <li class="service">
          <button open-type="contact" class="customer">
            <img src="/static/images/icon-Customer-service.png" style="width:42rpx;height:39rpx;margin-top:3rpx;" alt class="icon" />
            <p>客服</p>
          </button>
        </li>
        <li @click.stop="tabPage(2)">
          <img src="/static/images/icon-shop-car.png" style="width:43rpx;height:43rpx" alt class="icon" />
          <p>购物车</p>
          <i class="icon-num" v-if="shoppingCount">{{shoppingCount}}</i>
        </li>
      </ul>
      <div class="btn">
        <button class="addcar" @click="getcommit(1)">加入购物车</button>
        <button @click="getcommit(2)">立即购买</button>
      </div>
    </div>
  </div>
</template>

<script>
import wxParse from 'mpvue-wxparse'
import { getbyarticle,checkUserDisable } from "@/api";
import toast from "@/plugins/toast";

export default {
  data () {
    return {
      token:'',
      registerFlag:'',
      byarticle: ' ',
      shoppingCount: "",
      num:1,
      goods:'',
      jumpType:'',
    }
  },
  computed: {
    byarticle () {
      return this.$store.state.home.byarticle
    }
  },
    components:{
        wxParse
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        getarticle(){
          getbyarticle({id: this.$root.$mp.query.id}).then(res => {

              var reg = /\<br[ ]*\/{0,1}\>/ig;
              res.data.content = res.data.content.replace(reg,'');
              this.byarticle = res.data.content
              this.goods=res.data
              this.shoppingCount=res.data.shoppingCount
              wx.setNavigationBarTitle({
                  title: res.data.articleName
              })
          })
        },
        // 跳转页面
        tabPage(e) {
            if (e == 1) {
                wx.switchTab({
                    url: '/pages/index/main'
                })
            } else {
                wx.navigateTo({ 
                    url: '/pages/home/<USER>/main' 
                })
            }
        },
        // 确认
        getcommit(commitType) {
          
            if (!this.token) {
                wx.showModal({
                    content: '您还没有登录', 
                    cancelText: '取消', 
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            } else {
                if (!this.registerFlag) {
                    wx.showModal({
                        content: '您还没有注册', 
                        cancelText: '取消', 
                        confirmText: '去注册',
                        success: res => {
                            if (res.confirm) {
                                wx.navigateTo({ url: `/pages/login/main` })
                            }
                        }
                    })
                    return
                }
            }
                if (commitType == 1) {
                // 加入购物车
                let params = {
                    goodsId: this.goods.goodsId,
                    goodsType: this.goods.type*1,
                    goodsCount: this.num
                };
                this.$store.dispatch("shop/queryShop", params).then(res => {
                    if (res.code == 0) {
                    this.isShow = false;
                    /**
                     * 因为没有专门查购物车数量的接口所以只能再调一次查详情 本地加不知道规则
                     */
                    this.getarticle();
                    toast.text("添加成功");
                    } else {
                    toast.text(res.msg);
                    }
                });
                } else if (commitType == 2) {
                    let id = this.goods.goodsId;
                    id=id.toString()
                    if(this.goods.type==2){
                      this.goods.type=3
                    }
                    // 立即购买
                    let obj = {
                        type: 1,
                        goodsType: this.goods.type*1,
                        id: this.goods.goodsId,
                        goodsCount: this.num,
                        goods: []
                    };
                    checkUserDisable({type: this.goods.type,ids: id}).then(data => {
                        if (data.data.code == 0) {
                            this.$store.dispatch("shop/payDetail", obj).then(res => {
                                if (res.code) {
                                    toast.text(res.msg);
                                } else {
                                    wx.navigateTo({
                                        url: `/pages/ticket/orders/main?type=1&goodsType=${
                                        this.goods.type
                                        }&id=${id}&goodsCount=${this.num}`
                                    })
                                }
                            })
                        } else {
                            toast.text(data.data.msg)
                        }
                    })
                }
        },
        
    },
  onShow(){
        this.token = wx.getStorageSync('token')
        this.registerFlag = wx.getStorageSync('registerFlag')
  },
  onLoad () {
    this.jumpType=this.$root.$mp.query.jumpType
    this.token = wx.getStorageSync('token')
    this.registerFlag = wx.getStorageSync('registerFlag')
    this.byarticle = ' '
    this.getarticle()
  }
}
</script>

<style lang="less">
.container {
    width: 100%;
    height: auto;
    overflow: hidden;
    .articleinfo{
      view {
          width: 100vw !important;
      }
      image {
          // width: 100vw !important;
          // height: 100vh !important;
      }
    }
  .footer {
    width: 100%;
    height: 49px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    // padding: 0 10px 0 0;
    box-sizing: border-box;
    justify-content: space-between;
    .icon-list {
      width: 145px;
      display: flex;
      align-items: center;
      justify-content: center;
      li,
      .customer {
        padding: 0 11px;
        box-sizing: border-box;
        font-size: 0;
        text-align: center;
        position: relative;
        background: #fff;
        .icon {
          margin-bottom: 4px;
        }
        p {
          font-size: 10px;
          line-height: 14px;
          color: #4a4a4a;
        }
        .icon-num {
          position: absolute;
          top: -2px;
          right: 8px;
          font-size: 10px;
          line-height: 10px;
          border-radius: 12px;
          color: #fff;
          padding: 2px 4px;
          box-sizing: border-box;
          background: #f01520;
        }
      }
      .service {
        padding: 0;
      }
    }
    .btn {
      width: 220px;
      height: 100%;
      line-height: 49px;
      display: flex;
      align-items: center;
      button {
        border-radius: 0!important;
        width: 110px;
        height: 100%;
        line-height: 49px;
        padding: 0;
        font-size: 14px;
        color: #fff;
        background: #F39103;
      }
      .addcar {
        background: #67B32E;
      }
    }
  }
}
</style>
