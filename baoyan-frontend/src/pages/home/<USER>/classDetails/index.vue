<template>
  <div class="container">
    <scroll-view id="scrollview" :style="{height: scrollViewHeight}" class="scrollview" scroll-y :scroll-into-view='toview' @scroll='scrolls' @dragging='scrollend' :enhanced='true'>
            <ul class="types1" v-if="navlist" :style="{opacity: navlist}">
                <li @click="tabInfo('header',1)">
                    <p :class="{active:sortType===1}">商品</p>
                </li>
                <li @click="tabInfo('tuijian',2)" v-if="detail.brights">
                    <p :class="{active:sortType===2}">亮点推荐</p>
                </li>
                <li @click="tabInfo('xuzhi',3)" v-if="detail.buyNotice||detail.useNotice">
                    <p :class="{active:sortType===3}">使用须知</p>
                </li>
                <li @click="tabInfo('detale',4)">
                    <p :class="{active:sortType===4}">详情介绍</p>
                </li>
            </ul>
    <div class="box">
      <div class="header" id="header" ref="header">
        <div class="banner">
          <swiper
            class="swiper"
            @change="swiperChange"
            :autoplay="true"
            :interval="2000"
          >
            <swiper-item v-for="(item, index) in banner" :key="index">
              <image :src="item" mode="aspectFill" class="slide-image" />
            </swiper-item>
          </swiper>
          <!-- <div class="point">{{curr}}/{{banner.length}}</div> -->
          <div class="counttime" v-if="detail.marketingEnd&&counttime&&(nowtime>detail.marketingStart)">
            <div class="text">距离活动结束仅剩</div>
            <div class="times">{{counttime}}</div>
          </div>
        </div>
        <div class="cont">
          <div class="title">
            <h5 >{{detail.goodsName}}</h5>
            <button open-type="share" class="share-bt-box">
              <div class="share-box">
                <img class="share" src="/static/images/icon-share1.png" alt />
                <p>分享</p>
              </div>
            </button>
          </div>
          <div class="text">
            <p class="num" v-if="combination.length">¥{{minprice}}</p>
            <p class="num" v-else>¥{{detail.sellPrice}}</p>
            <span class="aabolish" v-if="combination.length">起</span>
            <span v-else class="abolish">¥{{detail.marketPrice}}</span>
          </div>
        </div>
      </div>
      <!-- <div class="couponBox" @click="isCardShow=true"> -->
      <div class="couponBox" v-if="cardlist.length" @click="isCardShow=true">
          <h1>领券</h1>
          <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
      </div>
      <div v-if="combination.length>1">
        <div class="couponBox">
            <h1>可选规格</h1>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
        </div>
        <div class="couponlist" v-if="combination.length">
          <div class="listbox" :style="{'width':combination.length*230+'rpx'}">
              <div class="block" v-for="(item,index) in combination" :key="index" :class="{'active':item.id===payDetail.goodsId}" @click="selcoupon(item,index)">
                <img class="selt" src="/static/images/selt.png" alt="">
                <div class="price">{{item.price}}元</div>
                <div class="name">{{item.name}}</div>
                <div class="time">{{item.verificationStart}}-{{item.verificationEnd}}</div>
                <!-- <div class="time" v-else>{{item.dayNum}}天</div> -->
              </div>
          </div>
          </div>
      </div>
      <div v-if="type==1||type==6||type==8||type==3" class="margin mendian">
        <div class="couponBoxs">
            <h1>适用门店 <span>（{{storeList.length? storeList.length : 0}}家）</span></h1>
            <img :style="{'transform': rotate}" class="arrow" src="/static/images/icon-gray-arrow.png" alt />
        </div>
        <div class="storeBox" v-for="(item,index) in storeList" :key="index" v-if="index<morelist">
          <div>
            <p class="name">{{item.name}}</p>
            <p class="time" v-if="item.businessTime"><img class="timeicon" src="/static/images/icon-goods11.png" alt="">{{item.businessTime}}</p>
            <p class="address"><img class="addressicon" src="/static/images/icon-goods22.png" alt="">{{item.address}}</p>
          </div>
          <div class="block">
            <img class="image1" src="/static/images/icon-goods33.png" @click="Phone(item.mobile)" alt="">
            <img class="image2" src="/static/images/icon-goods44.png" @click="tabMap(item)" alt="">
          </div>
        </div>
        <div @click="arrowclick(storeList.length)" class="moremendian" v-if="storeList.length!=morelist&&storeList.length>3">
          查看更多门店
          <img class="right" src="/static/images/icon-date-right.png" alt="">
        </div>
      </div>
      <div v-if="detail.brights" id="tuijian" class="margin">
        <div class="couponBox">
            <h1>亮点推荐</h1>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
        </div>
        <div style="box-sizing: border-box;background: #fff;">
        <wxParse :content="detail.brights" noData="" />
        </div>
      </div>
      <div v-if="detail.buyNotice||detail.useNotice" id="xuzhi" class="margin">
        <div class="couponBox">
            <h1>购买及使用须知</h1>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
        </div>
        <div style="box-sizing: border-box;background: #fff;">
         <wxParse :content="detail.buyNotice" noData="" />
         <wxParse :content="detail.useNotice" noData="" />

        </div>
      </div>
      <!--1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品 -->
      <!-- <div class="detail" v-if="type != 1">
        <p
          class="detail-title"
        >{{type == 1 ? '普通商品' : type==2?'联票详情':type==3?'次卡详情':type==7?'联票详情':type==6?'普通商品':'次卡详情'}}</p>
        <ul class="detail-list">
          <li v-for="(item, index) in goods" :key="index" @click="seeDetail(item.id,1)">
            <img class="d-img" :src="item.goodsImg" alt />
            <div class="right">
              <p class="d-name">{{item.goodsName}}</p>
              <p class="num">x {{ item.subCardGoodsNum }}</p>
            </div>
          </li>
        </ul>
      </div> -->
      <!-- <div class="evaluate" v-if="evaluate.length > 0">
        <div class="title-box" @click.stop="getMore">
          <p class="eval">商品评价（{{evaluate.length ? evaluate.length : 0}}）</p>
          <div class="more">
            <span class="txt">查看更多</span>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt />
          </div>
        </div>
        <div
            class="content-box"
            v-if="evaluate.length > 0 && index < 2"
            v-for="(item, index) in evaluate"
            :key="index"
            >
            <div class="info">
                <div class="my-info">
                    <img class="headimg" :src="item.isAnonymous == 1 ? '/static/images/icon-no-headimg.png' : item.headImg" alt="">
                    <p class="name">{{ item.userName }}</p>
                </div>
                <p class="time">{{item.gmtCreate}}</p>
            </div>
            <p class="content">{{item.evaluateText}}</p>
            <div class="imgBox" v-if="item.img.length">
                <img v-for="(items,index2) in item.img" @click="previewImageHandler(item.img)" :key="index2" :src="items " alt="">
            </div>
        </div>
        <div class="tibs" v-if="!evaluate.length">当前暂无评价</div>
      </div> -->
      <!-- <div class="img-box" v-html="detail.richContent"></div> -->
      <div class="img-box" v-if="richContent!=''">
        <p class="detale" id="detale">详情介绍</p>
        <wxParse :content="richContent" noData="" />
      </div>
    </div>
    <div class="footer">
      <ul class="icon-list">
        <li @click.stop="tabPage(1)">
          <img src="/static/images/icon-goback.png" style="width:41rpx;height:38rpx;margin-top:5rpx;" alt class="icon" />
          <p>首页</p>
        </li>
        <li class="service">
          <button open-type="contact" class="customer">
            <img src="/static/images/icon-Customer-service.png" style="width:42rpx;height:39rpx;margin-top:3rpx;" alt class="icon" />
            <p>客服</p>
          </button>
        </li>
        <li @click.stop="tabPage(2)">
          <img src="/static/images/icon-shop-car.png" style="width:43rpx;height:43rpx" alt class="icon" />
          <p>购物车</p>
          <i class="icon-num" v-if="shoppingCount">{{shoppingCount}}</i>
        </li>
      </ul>
      <div class="btn">
        <button class="addcar" @click="isShow = true, commitType = 1">加入购物车</button>
        <button @click="isShow = true, commitType = 2">立即购买</button>
      </div>
    </div>
    <!-- 领取卡券 -->
    <div class="mask" catchtouchmove="ture" v-if="isCardShow">
      <div class="main">
        <div class="cardlist">
          <div class="title">
            <p style="margin: 0 auto;">领取优惠券</p>
            <img
              @click="isCardShow = false"
              class="close"
              mode="aspectFit"
              src="/static/images/icon-Close.png"
              alt
            />
          </div>
          <div class="items" v-for="(item,index) in cardlist" :key="index">
            <img class="cardbg" src="/static/images/card.png" alt="">
            <div class="boxs">
              <div class="price">
                ¥<span>{{item.discount}}</span>
              </div>
              <div class="info">
                <p>{{item.name}}</p>
                <p v-if="item.effectiveType===1">{{item.startDate+'-'+item.endDate}}</p>
                <p v-else>{{item.dayNum}}天</p>
                <p v-if="item.couponType==1">满{{item.full}}元可用</p>
              </div>
            </div>
            <div class="lingqu" @click="receive(item.id)">
              立即领取
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mask" catchtouchmove="ture" v-if="isShow">
      <div class="main">
        <div class="info">
          <img :src="payDetail.goodsImg" mode="aspectFill" alt class="headimg" />
          <div class="right_info">
            <div class="many">
              <h6>¥{{payDetail.sellPrice}}</h6>
              <p class="abolish" v-if="payDetail.marketPrice">¥{{payDetail.marketPrice}}</p>
            </div>
            <img
              @click="isShow = false"
              class="close"
              mode="aspectFit"
              src="/static/images/icon-Close.png"
              alt
            />
          </div>
        </div>
        <div class="number">
          <p>数量：</p>
          <div class="box box1">
            <img
              @click.stop="changeNum(1)"
              mode="aspectFit"
              src="/static/images/icon-goods-reduce.png"
              alt
              class="icon"
            />
            <p class="num">{{num}}</p>
            <img
              @click.stop="changeNum(2)"
              mode="aspectFit"
              src="/static/images/icon-goods-plus.png"
              alt
              class="icon"
            />
          </div>
        </div>
        <button class="determine" @click.stop="getcommit">确定</button>
      </div>
    </div>
        </scroll-view>
  </div>
</template>

<script>
import { getGoodsdetail, checkUserDisable,receiveCoupon,couponListByGoods } from "@/api";
import toast from "@/plugins/toast";
import wxParse from 'mpvue-wxparse'
export default {
    data() {
        return {
          minprice:'',
            num: 1,
            curr: 1,
            type: 0,
            goodsId: 0,
            detail: {},
            storeList: [],
            banner: [],
            evaluate: [],
            goods: [],
            combination:[],//规格
            cardlist:[],
            goodsType:'',//选中规格
            isShow: false,
            isCardShow:false,
            commitType: 0,
            shoppingCount: "",
            richContent2: '',
            token: '',
            registerFlag: '',
            nowtime:'',
            counttime:'',
            settime:'',
            payDetail:{
              goodsId:'',
              goodsImg:'',
              sellPrice:'',
              marketPrice:'',
              goodsStock:'',
              type:'',
              limited:''
            },
            morelist:3,
            arrowright:true,
            rotate:'rotate(0deg)',
            toview:'',
            sortType:1,
            showview:false,
            top:{
              header:0,
              tuijian:0,
              xuzhi:0,
              detale:0,
            },
            navlist:0,
        }
    },
    components:{
        wxParse
    },
    computed: {
        scrollViewHeight () {
        return '100vh'
        }
    },
    onShareAppMessage(res) {
        return {
            title: this.detail.goodsName,
            path: `pages/home/<USER>/classDetails/main?id=${this.goodsId}&type=${this.type}`
        }
    },
    methods: {
      scrollend(){
        this.toview=''
      },
      scrolls(){
        this.scorllheight()
        setTimeout(() => {
          if(this.top.header==0||(this.top.tuijian>0&&this.detail.tuijia)){
            this.sortType=1
          }else if(this.top.tuijian<=0&&this.top.xuzhi>0&&this.detail.brights){
            this.sortType=2
          }else if(this.top.xuzhi<=0&&this.top.detale>0&&(this.detail.buyNotice||this.detail.useNotice)){
            this.sortType=3
          }else if(this.top.detale<=0){
            this.sortType=4
          }
        }, 50);
        this.toview=''
      },
      scorllheight(){
      wx.createSelectorQuery().select('#header').boundingClientRect(rect=>{
            // console.log(rect)
            this.top.header=rect.top
            if(this.top.header>=-50){
              this.navlist=Math.abs(this.top.header)/50
              this.navlist<0.1?this.navlist=0:this.navlist=this.navlist
            }else{
              this.navlist=1
            }
        }).exec()
        if(this.detail.brights){
          wx.createSelectorQuery().select('#tuijian').boundingClientRect(rect=>{
                if(rect!=null){
                  this.top.tuijian=rect.top
                }
            }).exec()
        }
        if(this.detail.buyNotice||this.detail.useNotice){
          wx.createSelectorQuery().select('#xuzhi').boundingClientRect(rect=>{
              //  console.log(rect)
              if(rect!=null){
                this.top.xuzhi=rect.top
              }
            }).exec()
        }
      wx.createSelectorQuery().select('#detale').boundingClientRect(rect=>{
            this.top.detale=rect.top
        }).exec()
      },
      tabInfo(id,index){
        this.showview=true
        this.toview=id
        this.sortType=index
      },
          arrowclick(index){
            if(index<3){
              return
            }
            this.arrowright=!this.arrowright
            if(this.arrowright){
              this.morelist=index
              this.rotate='rotate(90deg)'
            }else{
              this.morelist=3
              this.rotate='rotate(0deg)'
            }
          },
          selcoupon(item,index){
            this.num=1
            this.payDetail.goodsId=item.id
            this.payDetail.sellPrice=item.price
            this.payDetail.marketPrice=''
            this.payDetail.goodsStock=item.goodsStock
            this.payDetail.limited=item.limited
            if(index==0){
              this.payDetail.type=this.$root.$mp.query.type==10?1:this.$root.$mp.query.type
            }else{
              this.payDetail.type=10
            }
          },
          addZero(i) {
              return i < 10 ? "0" + i: i + "";
          },
           countDown(endtime) {
              var nowtime = new Date();
              this.nowtime=nowtime.getTime()
              var lefttime = parseInt((parseInt(endtime/ 1000)- parseInt(nowtime.getTime()/ 1000)));
              var d = parseInt(lefttime / (24*60*60))
              var h = parseInt(lefttime / (60 * 60) % 24);
              var m = parseInt(lefttime / 60 % 60);
              var s = parseInt(lefttime % 60);
              d = this.addZero(d)
              h = this.addZero(h);
              m = this.addZero(m);
              s = this.addZero(s);
              this.counttime = `${d}日${h}时${m}分${s}秒`;
              if (lefttime <= 0) {
                  this.counttime = 0;
                  return;
              }
              clearInterval(this.settime)
              this.settime=null
              this.settime=setInterval(() => {
                this.countDown(endtime)
              }, 1000);
            },
        // 领取优惠券
        receive (couponId) {
            let token = wx.getStorageSync('token')
            let registerFlag = wx.getStorageSync('registerFlag')
            if (!token) {
                wx.showModal({
                    content: '您还没有登录',
                    cancelText: '取消',
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            }
            if (!registerFlag) {
                wx.showModal({
                    content: '您还没有注册',
                    cancelText: '取消',
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }
            receiveCoupon({couponId}).then(res => {
                if (res.data.code) {
                    toast.text(res.data.msg)
                } else {
                    toast.text('领取成功')
                }
            })
        },
        // 跳地图导航
        tabMap (item) {
            let lat = Number(item.latitude)
            let lng = Number(item.longitude)
            let name = item.name
            // let add = this.detail.storeAddr
            wx.getLocation({
                type: 'wgs84',
                success: function (res) {
                wx.openLocation({//​使用微信内置地图查看位置。
                    latitude: lat,//要去的纬度-地址
                    longitude: lng,//要去的经度-地址
                    name: name,
                })
                },
                fail() {
                    wx.showModal({
                        title: '温馨提示', //提示的标题,
                        content: '距离筛选需要您授权地理位置',
                        confirmText: '去授权',
                        success: res => {
                            if (res.confirm) {
                                wx.openSetting({
                                    success(res) {
                                        _this.tabMap()
                                    }
                                })
                            }
                        }
                    })
                }
            })
        },
      timestampToTime(timestamp) {
          var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
          var Y = date.getFullYear() + '.';
          var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '.';
          var D = date.getDate();
          return Y + M + D
      },
        Phone(phoneNumber) {
            wx.makePhoneCall({
                phoneNumber
            })
        },
        previewImageHandler(urls,current) {
            wx.previewImage({
                current: urls[0],
                urls
            })
        },
        seeDetail(id,type) {
            // checkUserDisable({type: 1,ids: id+''}).then(data => {
            //     if (data.data.code == 0) {
                    wx.navigateTo({
                        url: `/pages/home/<USER>/classDetailsCopy/main?id=${id}&type=${type}`
                    })
                // } else {
                //     toast.text(data.data.msg)
                // }
            // })
        },
        swiperChange(e) {
            this.curr = e.target.current + 1
        },
        // 跳转页面
        tabPage(e) {
            if (e == 1) {
                wx.switchTab({
                    url: '/pages/index/main'
                })
            } else {
                wx.navigateTo({
                    url: '/pages/home/<USER>/main'
                })
            }
        },
        // 评价查看更多
        getMore() {
            if (this.evaluate.length) {
                let _this = this
                let id = this.$root.$mp.query.id
                wx.navigateTo({
                    url: `/pages/home/<USER>/clusterGoods/comment/main?id=${id}&type=${
                        _this.type
                    }`
                })
            }
        },
        // 加减商品数量
        changeNum(e) {
            if (e == 1) {
                if (this.num > 1) {
                    this.num --
                } else {
                    toast.text("至少保留一件商品");
                }
            } else {
                if(this.num >=this.payDetail.limited&&this.payDetail.limited!==0){
                    toast.text("商品限购"+this.payDetail.limited)
                    return
                }
                if(this.num >= this.payDetail.goodsStock){
                    toast.text("库存不足")
                    return
                }
                this.num ++
            }
        },
        // 确认
        getcommit() {
            if (!this.token) {
                wx.showModal({
                    content: '您还没有登录',
                    cancelText: '取消',
                    confirmText: '去登录',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/authorize/main` })
                        }
                    }
                })
                return
            } else {
                if (!this.registerFlag) {
                    wx.showModal({
                        content: '您还没有注册',
                        cancelText: '取消',
                        confirmText: '去注册',
                        success: res => {
                            if (res.confirm) {
                                wx.navigateTo({ url: `/pages/login/main` })
                            }
                        }
                    })
                    return
                }
            }
            if (this.num <= this.payDetail.goodsStock) {
                if (this.commitType == 1) {
                // 加入购物车
                let params = {
                    goodsId: this.payDetail.goodsId,
                    goodsType: this.payDetail.type*1,
                    goodsCount: this.num
                };
                this.$store.dispatch("shop/queryShop", params).then(res => {
                    if (res.code == 0) {
                    this.isShow = false;
                    /**
                     * 因为没有专门查购物车数量的接口所以只能再调一次查详情 本地加不知道规则
                     */
                    this.queryGoodsDetail();
                    toast.text("添加成功");
                    } else {
                    toast.text(res.msg);
                    }
                });
                } else if (this.commitType == 2) {
                    let id = this.payDetail.goodsId;
                    // 立即购买
                    let obj = {
                        type: 1,
                        goodsType: this.payDetail.type*1,
                        id: parseInt(id),
                        goodsCount: this.num,
                        goods: []
                    };
                    let ids=this.payDetail.goodsId
                    ids=ids.toString()
                    checkUserDisable({type: this.payDetail.type,ids: ids}).then(data => {
                        if (data.data.code == 0) {
                            this.$store.dispatch("shop/payDetail", obj).then(res => {
                                if (res.code) {
                                    toast.text(res.msg);
                                } else {
                                    wx.navigateTo({
                                        url: `/pages/ticket/orders/main?type=1&goodsType=${
                                        this.payDetail.type
                                        }&id=${id}&goodsCount=${this.num}`
                                    })
                                }
                            })
                        } else {
                            toast.text(data.data.msg)
                        }
                    })
                }
            } else {
                toast.text("库存不足");
            }
        },
        /**
         * 查询商品详情
         * */

        queryGoodsDetail() {
            let params = {
                id: this.goodsId,
                type: this.type
            }
            getGoodsdetail(params).then(res => {
              console.log(res)
                if (res.data.code) {
                    wx.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 1500
                    });
                } else {
                    let data = res.data;
                    if (res.data.store) {
                        this.storeList = res.data.store || []
                    }
                    if (data.byGoodsInfo) {
                        var reg = /\<br[ ]*\/{0,1}\>/ig;
                        data.byGoodsInfo.useNotice?data.byGoodsInfo.useNotice = data.byGoodsInfo.useNotice.replace(reg,''):data.byGoodsInfo.useNotice=''
                        data.byGoodsInfo.buyNotice?data.byGoodsInfo.buyNotice = data.byGoodsInfo.buyNotice.replace(reg,''):data.byGoodsInfo.buyNotice=''
                        data.byGoodsInfo.richContent?data.byGoodsInfo.richContent = data.byGoodsInfo.richContent.replace(reg,''):data.byGoodsInfo.richContent=''
                        data.byGoodsInfo.brights?data.byGoodsInfo.brights = data.byGoodsInfo.brights.replace(reg,''):data.byGoodsInfo.brights=''
                        // data.byGoodsInfo.buyNotice = data.byGoodsInfo.buyNotice.replace(reg,'');
                        // data.byGoodsInfo.richContent = data.byGoodsInfo.richContent.replace(reg,'');
                        // data.byGoodsInfo.brights = data.byGoodsInfo.brights.replace(reg,'')
                        this.detail = data.byGoodsInfo;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.byGoodsInfo.goodsBanner
                        ? data.byGoodsInfo.goodsBanner.split(",")
                        : [];
                        if(data.byGoodsInfo.marketingEnd){
                          this.countDown(data.byGoodsInfo.marketingEnd)
                        }
                    } else if (data.bySubCardGoods) {
                        if(data.bySubCardGoods.marketingEnd){
                          this.countDown(data.bySubCardGoods.marketingEnd)
                        }
                        this.detail = data.bySubCardGoods;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.bySubCardGoods.goodsBanner
                        ? data.bySubCardGoods.goodsBanner.split(",")
                        : [];
                    } else {
                        this.detail = data.byTicketGoods;
                        this.richContent = this.detail.richContent || ''
                        this.banner = data.byTicketGoods.goodsBanner
                        ? data.byTicketGoods.goodsBanner.split(",")
                        : [];
                    }
                    if(data.combination){
                      if(data.combination.length){
                      let maingoods={
                        goodsStock:this.detail.goodsStock,
                        id:'0'+this.detail.id,
                        isCoupon: data.byGoodsInfo.isCoupon,
                        isIntegral: data.byGoodsInfo.isIntegral,
                        limited: data.byGoodsInfo.limited,
                        name: data.byGoodsInfo.specName?data.byGoodsInfo.specName:data.byGoodsInfo.goodsName,
                        price: data.byGoodsInfo.sellPrice,
                        verificationEnd: data.byGoodsInfo.verificationEnd,
                        verificationStart: data.byGoodsInfo.verificationStart,
                      }
                      this.combination=data.combination

                      this.combination.unshift(maingoods)
                      this.minprice=this.combination[0].price
                      let minindex=0
                      this.combination.forEach((item,index)=>{
                        item.verificationStart=this.timestampToTime(item.verificationStart)
                        item.verificationEnd=this.timestampToTime(item.verificationEnd)
                        if(this.minprice>this.combination[index].price){
                          this.minprice=this.combination[index].price
                          minindex=index
                        }
                        // this.payDetail.goodsId=this.combination[minindex].id
                        // this.payDetail.sellPrice=this.combination[minindex].price
                        // this.payDetail.marketPrice=''
                        // this.payDetail.goodsStock=this.combination[minindex].goodsStock
                        // this.payDetail.type=10
                      })
                      }
                    }
                      this.payDetail.goodsId='0'+this.$root.$mp.query.id
                      this.payDetail.goodsImg=this.detail.goodsImg
                      this.payDetail.sellPrice=this.detail.sellPrice
                      this.payDetail.marketPrice=this.detail.marketPrice
                      this.payDetail.goodsStock=this.detail.goodsStock
                      this.payDetail.type = this.$root.$mp.query.type;
                      if(this.detail.limited){
                        this.payDetail.limited = this.detail.limited;
                      }else{
                        this.payDetail.limited =0
                      }
                    if(data.evaluate){
                      this.evaluate = data.evaluate;
                      this.evaluate.forEach((item,index) => {
                          if (item.isAnonymous) {
                              item.userName = item.userName ? item.userName.substr(0, 1)+'****' : ''
                          }
                          if (item.img) {
                              item.img = item.img.split(',')
                          } else {
                              item.img = []
                          }
                      })
                    }
                    this.goods = data.goods;
                    this.shoppingCount = data.shoppingCount;
                    //优惠券列表
                    let goodsparams = {
                          goodsId: this.goodsId,
                          type: this.type
                      }
                      couponListByGoods(goodsparams).then(res=>{
                        if (res.data) {
                          this.cardlist=res.data
                          this.cardlist.forEach((item,index)=>{
                            item.startDate=this.timestampToTime(item.startDate)
                            item.endDate=this.timestampToTime(item.endDate)
                          })
                        }
                      })
                      setTimeout(() => {
                    this.scorllheight()
                      }, 500);
                    // if(this.detai.isCoupon==1||this.detai.isIntegral==1){
                    //   let goodsparams = {
                    //       goodsId: this.goodsId,
                    //       type: this.type
                    //   }
                    //   couponListByGoods(goodsparams).then(res=>{
                    //     if (res.data) {
                    //       this.cardlist=res.data
                    //     }
                    //   })
                    // }
                }
            })
        }
    },
    onUnload(){
      clearInterval(this.settime)
      this.settime=null
    },
    onHide(){
      clearInterval(this.settime)
      this.settime=null
    },
    onShow() {
        this.token = wx.getStorageSync('token')
        this.registerFlag = wx.getStorageSync('registerFlag')
        // this.queryGoodsDetail();
    },
    onLoad() {
        this.token = wx.getStorageSync('token')
        this.registerFlag = wx.getStorageSync('registerFlag')
        this.curr = 1
        this.banner = []
        this.num = 1;
        this.commitType = 0;
        this.isShow = false;
        // 6 普通商品 7联票商品 8次卡商品 /1 普通商品 2 次卡 3 联票
        this.type = this.$root.$mp.query.type;
        this.goodsId = this.$root.$mp.query.id;

        this.detail={},
        this.storeList=[],
        this.banner=[],
        this.evaluate=[],
        this.goods=[],
        this.combination=[],//规格
        this.cardlist=[],
        this.morelist=3
        this.nowtime='',
        this.detail.marketingStart='',
        this.detail.marketingEnd='',
        this.counttime='',
        this.sortTyp=1

        this.queryGoodsDetail();
        //this.type = type
        // if ( this.type == 6 ) {
        //     type = 1
        // } else if ( this.type == 7 ) {
        //     type = 3
        // } else if ( this.type == 8 ) {
        //     type = 2
        // } else {
        //     type = this.type
        // }
    }
}
</script>

<style lang="less">
.container {
  overflow: hidden;
  .box {
    padding-bottom: 50px;
    box-sizing: border-box;
  }
  .couponBox {
    background: #fff;
    // margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h1 {
      font-size: 14px;
      font-weight: 500;
      color: #302e27;
      padding: 13px 0 13px 15px;
      span {
        font-size: 9px;
        color: #949494;
      }
    }
    .arrow {
      width: 7px;
      height: 13px;
      margin-right: 13px;
      transition: all 1s;
    }
  }
  .couponBoxs {
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h1 {
      font-size: 14px;
      font-weight: 500;
      color: #302e27;
      padding: 13px 0 13px 15px;
      span {
        font-size: 9px;
        color: #949494;
      }
    }
    .arrow {
      width: 7px;
      height: 13px;
      margin-right: 13px;
    }
  }
  .couponlist {
    box-sizing: border-box;
    background: white;
    padding: 15px 12px;
    overflow-x: auto;
    .listbox {
      overflow: hidden;
    }
    .active {
      border-color: #e87e00 !important;
      position: relative;
      overflow: hidden;
      background: #fff6eb !important;
      .selt {
        display: block !important;
        position: absolute;
        top: -5rpx;
        right: -1px;
        width: 25px;
        height: 25px;
      }
    }
    .block {
      float: left;
      margin-right: 10px;
      .selt {
        display: none;
        position: absolute;
        bottom: -3px;
        right: -3px;
      }
      width: 101px;
      height: 100%;
      border: 1px solid #999999;
      border-radius: 8px;
      text-align: center;
      box-sizing: border-box;
      padding: 6px 0;
      .price {
        color: #e87e00;
        font-size: 17px;
        margin-bottom: 3px;
      }
      .name {
        font-size: 9px;
        color: #999999;
        margin-bottom: 3px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }
      .time {
        font-size: 7px;
        color: #999999;
      }
    }
  }
  .margin {
    margin-bottom: 10px;
  }
  .storeBox {
    background: white;
    display: flex;
    justify-content: space-between;
    // align-items: center;
    box-sizing: border-box;
    padding: 10px 15px;
    .name {
      font-size: 12px;
      color: #3e3e3e;
      margin-bottom: 5px;
    }
    .time {
      color: #7b7b7b;
      font-size: 9px;
      margin-bottom: 5px;
      .timeicon {
        width: 8px;
        height: 8px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }
    .address {
      color: #7b7b7b;
      font-size: 10px;
      .addressicon {
        width: 9px;
        height: 10px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }
    .block {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .image1 {
      width: 15px;
      height: 12px;
      display: block;
      margin-right: 10px;
    }
    .image2 {
      width: 11px;
      height: 12px;
      display: block;
      margin-right: 5px;
    }
  }
  .imgBox {
    margin-top: 5px;
    img {
      width: 60px;
      height: 60px;
      margin-right: 11px;
    }
  }
  .header {
    margin-bottom: 10px;
    .banner {
      width: 100%;
      height: 250px;
      position: relative;
      .swiper {
        width: 100%;
        height: 250px;
        .slide-image {
          width: 100%;
          height: 250px;
        }
      }
      .point {
        position: absolute;
        right: 10px;
        bottom: 10px;
        padding: 0 10px;
        box-sizing: border-box;
        height: 21px;
        line-height: 21px;
        border-radius: 11px;
        background: rgba(0, 0, 0, 0.2);
        font-size: 14px;
        color: #fff;
      }
      .counttime {
        display: flex;
        justify-content: space-between;
        position: absolute;
        right: 0;
        bottom: 0px;
        padding: 0 10px;
        box-sizing: border-box;
        height: 30px;
        width: 100%;
        line-height: 30px;
        background: linear-gradient(
          68deg,
          rgba(224, 173, 107, 0.8) 0%,
          rgba(250, 210, 161, 0.8) 100%
        );
        font-size: 14px;
        color: #fff;
        .text {
          font-size: 14px;
          color: #ffffff;
        }
        .times {
          font-size: 10px;
          color: #ffffff;
          letter-spacing: 2px;
        }
      }
    }
    .cont {
      width: 100%;
      padding: 11px 0px 6px 15px;
      box-sizing: border-box;
      background: #fff;
      .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        h5 {
          width: 304px;
          font-size: 14px;
          line-height: 25px;
          // font-weight: 500;
          color: #343434;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .share-bt-box {
          padding: 0px 5px !important;
          width: 68px;
          height: 30px;
          background: #ffbb29;
          border-radius: 0;
          border-top-left-radius: 15px;
          border-bottom-left-radius: 15px;
          .share-box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            .share {
              display: block;
              width: 15px;
              height: 15px;
              margin-left: 8px;
              // margin-bottom: 3px;
            }
            p {
              font-size: 12px;
              color: #fff;
            }
          }
        }
      }
      .text {
        // display: flex;
        .num {
          font-size: 30px;
          line-height: 37px;
          color: #e01212;
          margin-right: 10px;
          display: inline-block;
        }
        .aabolish {
          font-size: 12px;
          color: #929292;
        }
        .abolish {
          font-size: 12px;
          color: #929292;
          text-decoration: line-through;
        }
      }
    }
  }
  .detail {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .detail-title {
      width: 100%;
      height: 44px;
      line-height: 44px;
      padding: 0 15px;
      font-size: 14px;
      color: #302e27;
    }
    .detail-list {
      width: 100%;
      li {
        width: 100%;
        padding: 10px 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        position: relative;
        &::after {
          position: absolute;
          right: 0;
          top: 0;
          left: 0;
          height: 1px;
          content: "";
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #d6d6d6;
        }
        .d-img {
          width: 60px;
          height: 60px;
          margin-right: 12px;
        }
        .right {
          width: 272px;
          .d-name {
            width: 266px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 14px;
            color: #302e27;
            line-height: 20px;
            margin-bottom: 3px;
          }
          .num {
            width: 100%;
            font-size: 12px;
            color: #302e27;
            line-height: 17px;
            text-align: right;
          }
        }
      }
    }
  }
  .evaluate {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .title-box {
      width: 100%;
      height: 42px;
      display: flex;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      justify-content: space-between;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: "";
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #d6d6d6;
      }
      .eval {
        font-size: 14px;
        color: #302e27;
        font-weight: 500;
        line-height: 20px;
      }
      .more {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9b9b9b;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .content-box {
      width: 100%;
      padding: 14px 15px 7px;
      box-sizing: border-box;
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .my-info {
          display: flex;
          align-items: center;
          margin-bottom: 13px;
          .headimg {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
          }
          .name {
            font-size: 12px;
            color: #090203;
            line-height: 17px;
          }
        }
        .time {
          font-size: 12px;
          color: #9b9b9b;
          line-height: 17px;
        }
      }
      .content {
        font-size: 14px;
        color: #090203;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
    .tibs {
      padding: 10px 15px;
      box-sizing: border-box;
      font-size: 12px;
      color: #9b9b9b;
      line-height: 17px;
    }
  }
  .img-box {
    width: 100%;
    .detale {
      box-sizing: border-box;
      padding: 10px;
      background: white;
      font-size: 14px;
    }
    img {
      width: 100%;
    }
    image {
      display: flex !important;
    }
  }
  .footer {
    width: 100%;
    height: 49px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    // padding: 0 10px 0 0;
    box-sizing: border-box;
    justify-content: space-between;
    .icon-list {
      width: 145px;
      display: flex;
      align-items: center;
      justify-content: center;
      li,
      .customer {
        padding: 0 11px;
        box-sizing: border-box;
        font-size: 0;
        text-align: center;
        position: relative;
        background: #fff;
        .icon {
          margin-bottom: 4px;
        }
        p {
          font-size: 10px;
          line-height: 14px;
          color: #4a4a4a;
        }
        .icon-num {
          position: absolute;
          top: -2px;
          right: 8px;
          font-size: 10px;
          line-height: 10px;
          border-radius: 12px;
          color: #fff;
          padding: 2px 4px;
          box-sizing: border-box;
          background: #f01520;
        }
      }
      .service {
        padding: 0;
      }
    }
    .btn {
      width: 220px;
      height: 100%;
      line-height: 49px;
      display: flex;
      align-items: center;
      button {
        border-radius: 0 !important;
        width: 110px;
        height: 100%;
        line-height: 49px;
        padding: 0;
        font-size: 14px;
        color: #fff;
        background: #f39103;
      }
      .addcar {
        background: #67b32e;
      }
    }
  }
  .mask {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 10;
    .main {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      background: #fff;
      padding: 0 15px 12px;
      box-sizing: border-box;
      z-index: 11;
      .info {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 25px;
        .headimg {
          width: 96px;
          height: 96px;
          border-radius: 4px;
          margin-top: -15px;
          background: #fff;
        }
        .right_info {
          width: 240px;
          padding: 10px 0 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          .many {
            h6 {
              font-size: 18px;
              font-weight: 500;
              color: #ff5100;
              line-height: 25px;
              margin-bottom: 10px;
            }
            .abolish {
              font-size: 12px;
              color: #615454;
              line-height: 17px;
              text-decoration: line-through;
            }
          }
          .close {
            width: 15px;
            height: 15px;
            position: absolute;
            right: 16px;
            top: 16px;
          }
        }
      }
      .number {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        color: #615454;
        line-height: 20px;
        margin-bottom: 33px;
        .box {
          display: flex;
          align-items: center;
          &.box1 {
            padding: 0;
          }
          .icon {
            width: 33px;
            height: 30px;
          }
          .num {
            width: 38px;
            height: 30px;
            line-height: 30px;
            background: #edebeb;
            text-align: center;
            overflow: hidden;
            font-size: 14px;
            color: #343333;
            margin: 0 1px;
          }
        }
      }
      .determine {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 0;
        border-radius: 22px;
        text-align: center;
        background: linear-gradient(
          90deg,
          rgba(255, 151, 56, 1) 0%,
          rgba(254, 116, 61, 1) 100%
        );
        font-size: 16px;
        color: #fff;
      }
      .cardlist {
        background: white;
        .title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 40px;
          padding: 10px 0;
          p {
            color: #343434;
            font-size: 17px;
          }
          .close {
            width: 15px;
            height: 15px;
          }
        }
        .items {
          width: 345px;
          height: 87px;
          position: relative;
          .cardbg {
            width: 345px;
            height: 87px;
            display: block;
            position: absolute;
            z-index: 1;
            top: 0;
            left: 0;
          }
          .boxs {
            position: absolute;
            top: 7px;
            left: 20px;
            z-index: 2;
            display: flex;
          }
          .price {
            color: #ea8000;
            font-size: 31px;
            span {
              font-size: 57px;
            }
          }
          .info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin: 10px;
            p {
              font-size: 12px;
              color: #ea8000;
            }
          }
          .lingqu {
            color: #ea8000;
            font-size: 16px;
            position: absolute;
            z-index: 2;
            top: 0;
            right: 20px;
            height: 87px;
            line-height: 87px;
            width: 87px;
            text-align: center;
          }
        }
      }
    }
  }
  .wxParse {
    background: white;
    // padding-bottom: 10px;
    // margin-bottom: 10px;
    // ._text {
    //   display: none;
    // }
  }
  .types1 {
    // transition: all 0.5s;
    border-bottom: 1px solid #ccc;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 21px 0 25px;
    box-sizing: border-box;
    background: #fff;
    justify-content: space-around;
    margin-bottom: 10px;
    position: fixed;
    top: 0;
    z-index: 10;
    li {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #4a4a4a;
      line-height: 20px;
      img {
        width: 7px;
        height: 10px;
        margin-left: 3px;
      }
      .triangle-box {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        flex-direction: column;
        .triangle-top {
          width: 0;
          height: 0;
          border: 4px solid;
          border-color: transparent transparent rgba(0, 0, 0, 0.5);
        }
        .triangle-top-select {
          border-color: transparent transparent rgb(251, 124, 60, 1);
        }
        .triangle-bottom {
          width: 0;
          height: 0;
          border: 4px solid;
          border-color: rgba(0, 0, 0, 0.5) transparent transparent;
        }
        .triangle-bottom-select {
          border-color: rgba(251, 124, 60, 1) transparent transparent;
        }
      }
    }
    .active {
      color: #fb7c3c;
    }
  }
  .mendian {
    // height: 100vh;
    position: relative;
    .moremendian {
      font-size: 14px;
      position: absolute;
      height: 60px;
      line-height: 60px;
      text-align: center;
      color: rgb(52, 52, 52);
      width: 100%;
      bottom: 0;
      background-image: linear-gradient(
        -180deg,
        rgba(255, 255, 255, 0.3),
        #fff
      );
      .right {
        transform: rotate(90deg);
        width: 7px;
        height: 12px;
        vertical-align: middle;
        margin-left: 5px;
      }
    }
  }
}
</style>
