<template>
    <div class="container bgw">
        <div class="main">
            <p class="inventory">购物清单</p>
            <div class="content">
                <img :src="byTeamGoods.goodsImg" alt="">
                <div class="right">
                <h5>{{byTeamGoods.teamName}}</h5>
                <div class="num">
                    <p>¥{{byTeamGoods.teamPrice}}</p>
                </div>
                </div>
            </div>
            <div class="deduction first" v-if="type&&fraction">
                <p class="text">可使用{{fraction}}积分抵扣 ¥{{count}}</p>
                <img @click="getSelect" class="select" :src="'/static/images/'+(isSelect ? 'icon-selected' : 'icon-select')+'.png'" alt="">
            </div>
            <div class="deduction" v-if="coupon.length && coupon" @click="getCoupon">
                <p class="text">优惠券</p>
                <div class="right">
                    <p class="tibs" v-if="discountAmount">-￥{{discountAmount}}</p>
                    <p class="tibs" v-else>{{coupon.length}}个优惠券可用</p>
                    <img src="/static/images/icon-gray-arrow.png" alt="" class="arrow">
                </div>
            </div>
            <p class="goods">共1件商品，合计：￥{{byTeamGoods.teamPrice}}</p>
        </div>
        <!-- <div class="section">
            <p>买家留言</p>
            <input type="text" v-model="text" placeholder="输入留言">
        </div> -->
        <div class="section">
            <p>支付方式</p>
            <p>微信支付</p>
        </div>
        <div class="agree">
            <p class="text"><checkbox class='checkbox' value="cb" color='#EE8200' :checked="checked" @click="checked=!checked"/><span @click='toread'>我已阅读并同意《商品购买协议》</span></p>
            <p><img class="arrow" src="/static/images/icon-gray-arrow.png" alt /></p>
        </div>
        <form @submit="orderPay" report-submit='true'>
            <cover-view class="footer">
                <cover-view class="total">总计：<cover-view class="span">¥{{num}}　</cover-view></cover-view>
                <cover-image class="btnBg" src="/static/images/btnBg.png"></cover-image>
                <button form-type="submit">提交订单</button>
            </cover-view>
        </form>
    </div>
</template>

<script>
import { payDetail,calculation,getTeamGoods,collectPay } from '@/api'
export default {
    data () {
        return {
      checked:false,
            id: '',
            text: '',
            isSelect: false,
            state: '',
            num: 0,
            byTeamGoods: {},
            count: 0,
            type: false,
            false: 0,
            couponType: false,
            coupon: [],
            discount: 0,
            payFlag: true,
            discountAmount: '',
            fraction: '',
            param: {
                orderAmount: '',
                couponId: '',
                scoreAmount: 0
            }
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
    toread(){
        wx.navigateTo({ url: `/pages/main/agreepay/main` })
    },
        integralHandle() {

        },
        orderPay(e) {
      if(!this.checked){
                wx.showToast({
                  title: "请先阅读并同意商品购买协议",
                  icon: "none",
                  duration: 1000
                });
                return
      }
            wx.showLoading({
                mask: true
            })
            let formId = e.target.formId
            let that = this
            let param = {
                id: this.id,
                type: this.state,	
                remark: this.text,
                payAmount: this.num,
                couponId: this.param.couponId,
                couponAmount: this.discountAmount,
                scoreAmount: this.param.scoreAmount,
                fromId: formId,
                orderAmount: this.byTeamGoods.teamPrice
            }
            collectPay(param).then(res => {
                if(!res.data.code) {
                    let orderId = res.data.orderId;
                    if (res.data.type == 0) {
                        that.payFlag = false
                        wx.redirectTo({
                            url: "/pages/home/<USER>/clusterGoods/paySuccess/main"
                        })
                        return
                    }
                    wx.hideLoading()
                    wx.requestPayment({
                        timeStamp: res.data.pay.timeStamp,
                        nonceStr: res.data.pay.nonceStr,
                        package: res.data.pay.pkg,
                        signType: res.data.pay.signType,
                        paySign: res.data.pay.paySign,
                        success() {
                            that.payFlag = false
                            wx.redirectTo({
                                url: "/pages/home/<USER>/clusterGoods/paySuccess/main"
                            })
                        },
                        fail() {
                            that.payFlag = true
                            wx.showToast({ title: '支付失败', icon:'none', duration: 1500})
                        }
                    })
                } else {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
                }
            })
        },
        // 选择优惠券
        getCoupon () {
            wx.setStorageSync("calculatePriceObj",this.param)
            wx.navigateTo({
                url: `/pages/home/<USER>/clusterGoods/orders/coupon/main?id=${this.id}&type=${this.state}`
            })
        },
        // 积分抵扣 
        getSelect () {
            this.isSelect = !this.isSelect
            if(this.isSelect) {
                this.param.scoreAmount = this.count
            } else {
                this.param.scoreAmount = 0
            }
            this.param.orderAmount = this.byTeamGoods.teamPrice
            this.calculatePrice()
        },
        calculatePrice() {
            calculation(this.param).then(res => {
                if(res.data.code) {
                    wx.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 1000
                    })
                    this.param.couponId = ''
                } else {
                    this.num = res.data.orderAmount
                    this.discountAmount = res.data.discountAmount
                }
            })
        }
    },
    onLoad(couponId) {
        if (!this.$mp.query.isBack) {
            this.param.scoreAmount = 0
            this.isSelect = false
        }        
        this.id = this.$root.$mp.query.id
        this.state = this.$root.$mp.query.type
        this.param.couponId = this.$root.$mp.query.couponId || ''
        this.text = ''
        let that = this
        getTeamGoods({id: this.id,type: this.state}).then(res => {
            if (res.data.code) {
                wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
            } else {
                this.coupon = res.data.coupon
                this.type = res.data.type  //是否可使用积分
                this.couponType = res.data.couponType //是否可使用优惠券
                this.fraction = res.data.fraction
                this.count = res.data.count
                this.byTeamGoods = res.data.byTeamGoods
                let coupon = JSON.stringify(res.data.coupon)
                this.param.orderAmount = this.byTeamGoods.teamPrice
                wx.setStorageSync('wx_coupon',coupon)
                that.calculatePrice()
            }
        })
    },
    // onShow() {
    //     let that = this
    //     if (this.payFlag) {
    //         getTeamGoods({id: this.id,type: this.state}).then(res => {
    //             if (res.data.code) {
    //                 wx.showToast({ title: res.data.msg, icon:'none', duration: 1500})
    //             } else {
    //                 this.coupon = res.data.coupon
    //                 this.type = res.data.type  //是否可使用积分
    //                 this.couponType = res.data.couponType //是否可使用优惠券
    //                 this.fraction = res.data.fraction
    //                 this.count = res.data.count
    //                 this.byTeamGoods = res.data.byTeamGoods
    //                 let coupon = JSON.stringify(res.data.coupon)
    //                 this.param.orderAmount = this.byTeamGoods.teamPrice
    //                 wx.setStorageSync('wx_coupon',coupon)
    //                 that.calculatePrice()
    //             }
    //         })
    //     }
    // }
}
</script>

<style lang="less">
.bgw{
    background: white!important;
}
.container {
  .main {
    width: 100%;
    background: #fff;
    padding: 0 12px 12px;
    box-sizing: border-box;
    // margin-bottom: 10px;
    border-bottom: 10px solid #F6F6F6;
    .inventory {
    //   padding: 0 11px;
      box-sizing: border-box;
      font-size: 13px;
      color: #090203;
      line-height: 18px;
    }
    .content {
      padding: 11px 0 10px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      img {
        width: 96px;
        height: 96px;
        border-radius: 4px;
        margin-right: 10px;
      }
      .right {
        width: 240px;
        h5 {
          width: 100%;
          font-size: 16px;
          color: #171717;
          line-height: 22px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        //   margin-bottom: 28px;
    border-bottom: 28px solid #fff;
        }
        .num {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #FF3333;
          line-height: 20px;
          p {
            font-size: 16px;
            color: #FF3434;
            line-height: 22px;
          }
        }
      }
    }
    .deduction {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid #E5E5E5;
      .text {
        font-size: 14px;
        color: #4A4A4A;
        line-height: 20px;
      }
      .select {
        width: 16px;
        height: 16px;
      }
      .right {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9B9B9B;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .first {
      border-top: 1rpx solid #E5E5E5;
    }
    .goods {
      font-size: 14px;
      color: #4A4A4A;
      line-height: 20px;
      text-align: right;
      margin-top: 5px;
    }
  }
  .section {
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    font-size: 14px;
    color: #4A4A4A;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 10px;
    border-bottom: 10px solid #F6F6F6;
    input {
      width: 270px;
      height: 100%;
      line-height: 50%;
      font-size: 14px;
    }
  }
  .agree{
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
    box-sizing: border-box;
    background: #fff;
    font-size: 14px;
    color: #686868;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text{
      .checkbox .wx-checkbox-input {
        width: 20rpx; /* 背景的宽 */
        height: 20rpx; /* 背景的高 */
        border-color: #EE8200;
        border-radius: 50%;
      }
      .checkbox  .wx-checkbox-input.wx-checkbox-input-checked::before {
        font-size: 8px;
        background: #EE8200;
        transform: translate(-50%, -50%) scale(1);
        -webkit-transform: translate(-50%, -50%) scale(1);
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        color: transparent;
      }
      span{
        font-size: 9px;
        color: #A6A6A6;
        line-height: 8px;
      }
    }
    .arrow {
          width: 7px;
          height: 13px;
          margin-right: 13px;
    }
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 49px;
    line-height: 49px;
    padding: 0 0 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    box-shadow: -1px -1px 10px 0px rgba(152, 152, 152, 0.33);
    .total {
      font-size: 14px;
      color: #7F7F7F;
      min-width: 150px;
      .span {
        display: inline;
        color: #FF3434;
        font-weight: 500;
      }
    }
    .btnBg {
        width: 110px;
        height: 49px;      
        position: absolute;
        right: 0;
        bottom: 0;
    }
    button {
      width: 110px;
      height: 49px;
      line-height: 49px;
      padding: 0;
      border-radius: 0;
      text-align: center;
      font-size: 14px;
      color: #fff;
      background: transparent;
    }
  }
}
</style>
