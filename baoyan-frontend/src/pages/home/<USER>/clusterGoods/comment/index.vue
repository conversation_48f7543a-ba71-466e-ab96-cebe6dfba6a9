<template>
  <div class="container">
    <scroll-view class="scrollView" style="height: 100vh;" :lower-threshold="100" @scrolltolower="loadMore" scroll-y>
      <div class="main" v-for="(item, id) in evaluate" :key="id">
        <div class="title">
          <div class="info">
            <img :src="item.isAnonymous == 1 ? '/static/images/icon-no-headimg.png' : item.headImg" alt="">
            <p class="name">{{item.userName ? item.userName : ''}}</p>
          </div>
          <p class="time">{{item.gmtCreate}}</p>
        </div>
        <div class="content">
          <p class="text">{{item.evaluateText}}</p>
          <ul class="list" v-if="item.img.length">
            <li v-for="(items, index) in item.img" :key="index">
              <img mode="aspectFit" :src="items" @click="previewImageHandler(item.img)" alt="">
            </li>
          </ul>
        </div>
        <div class="reply" v-if="item.returnDesc">
          <div class="business">
            <img src="/static/images/icon-business.png" alt="" class="icon">
            <p class="bus-name">商家回复</p>
          </div>
          <p class="bus-text">{{item.returnDesc}}</p>
        </div>
      </div>
    </scroll-view>
  </div>
</template>

<script>
export default {
  data () {
    return {
      page: 1,
      pageSize: 20,
      noService: false,
    }
  },
  computed: {
    evaluate () {
      return this.$store.state.home.evaluate
    }
  },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
  methods: {
    previewImageHandler(urls) {
        console.log(urls)
        wx.previewImage({
            current: urls[0],
            urls
        })
    },
    loadMore () {
      if (this.noService) {
        return
      }
      this.page++
      this.getinfo()
    },
    // 获取信息
    getinfo () {
      let params = {
        id: this.$root.$mp.query.id,
        type: this.$root.$mp.query.type,
        pageIndex: this.page,
        pageSize: this.pageSize,
      }
      this.$store.dispatch('home/getEvaluate', params).then(res => {
        this.noService = res
      })
    },
  },
  onLoad () {
    this.page = 1
    this.noService = false
    this.$store.commit('home/RECORD_EVALUATE', [])
    this.getinfo()
  }
}
</script>

<style lang="less">
.container {
//   .imgBox {
//       margin-top: 5px;
//       img {
//           width: 78px;
//           height: 78px;
//           border-radius: 8px;
//           margin-right: 5px;
//       }
//   }
  .main {
    width: 100%;
    margin-bottom: 10px;
    background: #ffF;
    padding: 15px;
    box-sizing: border-box;
    .title {
      width: 100%;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .info {
        display: flex;
        align-items: center;
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
        }
        .name {
          font-size: 12px;
          color: #090203;
          line-height: 17px;
        }
      }
      .time {
        font-size: 12px;
        color: #9B9B9B;
        line-height: 17px;
      }
    }
    .content {
      width: 100%;
      .text {
        width: 100%;
        font-size: 14px;
        color: #090203;
        line-height: 20px;
        margin-bottom: 10px;
      }
      .list {
        width: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        li {
          width: 60px;
          height: 60px;
          margin-right: 11px;
          margin-bottom: 10px;
          &:nth-child(5n){
            margin-right: 0;
          }
          img {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
    .reply {
      width: 345px;
      margin: 0 auto;
      background: #FAFAFA;
      padding: 10px;
      box-sizing: border-box;
      .business {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        .icon {
          width: 13px;
          height: 14px;
          margin-right: 5px;
        }
        .bus-name {
          font-size: 13px;
          line-height: 18px;
          color: #4A4A4A;
        }
      }
      .bus-text {
        font-size: 13px;
        color: #4A4A4A;
        line-height: 18px;
      }
    }
  }
}
</style>
