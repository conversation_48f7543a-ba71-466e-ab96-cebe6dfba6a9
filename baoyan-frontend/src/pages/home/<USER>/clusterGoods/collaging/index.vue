<template>
    <div class="container">
        <ul class="detail-list">
            <li v-for="(item, index) in teams" :key="index" v-if="item.textFlag">
                <div class="info">
                    <img class="head-img" :src="item.headImg" alt="">
                    <p class="name">{{item.nickName}}</p>
                </div>
                <div class="right">
                    <div class="assemble">
                        <p class="tibs-text">还差<span>{{item.lessNum}}人</span>拼成</p>
                        <p class="time">剩余{{item.endDateStr}}</p>
                    </div>
                    <div class="btn" @click="gettickCount(item)">去拼单</div>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import { queryTeam } from '@/api'
export default {
    data () {
        return {
            id: '',
            teams: []
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        // 去拼单
        gettickCount ({id}) {
            wx.navigateTo({
                url: `/pages/home/<USER>/clusterGoods/collaging/partakeCluster/main?id=${id}`
            })
        },
        timer() {
            let that = this
            let endDateStr = ''
            let timer = setInterval(function(){
                let timestamp = Date.parse(new  Date())
                if(that.teams.length && that.teams) {
                    for(let i=0;i<that.teams.length;i++) {
                        var time = that.teams[i].endDate2  //取出时间
                        //时间差
                        let timeDifference = time - timestamp
                        if(timeDifference <= 0) {
                            endDateStr = '00:00:00'
                            that.$set(that.teams[i],'textFlag',false)
                        } else {
                            that.$set(that.teams[i],'textFlag',true)
                            //hour
                            let HH = parseInt( timeDifference / 3600000 )
                            if(HH < 10) {
                                HH = '0' + HH
                            }
                            //minute
                            let mm = parseInt( (timeDifference % 3600000) / 60000 )
                            if(mm < 10) {
                                mm = '0' + mm
                            }
                            //second
                            let ss = parseInt( (timeDifference % 3600000 % 60000) / 1000 )
                            if(ss < 10) {
                                ss = '0' + ss
                            }
                            endDateStr = `${HH}:${mm}:${ss}`
                        }
                        that.$set(that.teams[i],'endDateStr',endDateStr)
                    }
                }
            },1000)
        }
    },
    onShow () {
        this.id = this.$root.$mp.query.id
        queryTeam({id: this.id}).then(res => {
            if(res) {
                this.teams = res.data
                if(this.teams.length && this.teams) {
                    this.teams.filter((item,index) => {
                        this.$set(item,'textFlag',false)
                        this.$set(item,'endDateStr','00:00:00')
                    })
                    this.timer()
                }
            }
        })
    }
}
</script>

<style lang="less">
.container {
   .detail-list {
    width: 100%;
    li {
      width: 100%;
      padding: 14px 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      margin-bottom: 10px;
      .info {
        display: flex;
        align-items: center;
        .head-img {
          width: 40px;
          height: 40px;
          margin-right: 12px;
        }
        .name {
          font-size: 14px;
          color: #4A4A4A;
          line-height: 20px;
        }
      }
      .right {
        display: flex;
        align-items: center;
        .assemble {
          text-align: center;
          margin-right: 20px;
          .tibs-text {
            font-size: 12px;
            line-height: 17px;
            color: #4A4A4A;
            margin-bottom: 2px;
            span {
              color: #F01520;
            }
          }
          .time {
            font-size: 11px;
            color: #9B9B9B;
            line-height: 16px;
            text-align: center;
          }
        }
        .btn {
          width: 70px;
          height: 24px;
          line-height: 24px;
          padding: 0;
          border-radius: 13px;
          background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
          text-align: center;
          font-size: 13px;
          color: #fff;
        }
      }
    }
  }
}
</style>
