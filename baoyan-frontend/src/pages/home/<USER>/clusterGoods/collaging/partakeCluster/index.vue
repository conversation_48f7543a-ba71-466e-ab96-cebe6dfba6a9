<!-- 拼团详情  -->
<template>
    <div class="container">
        <div class="section">
            <img :src="goods.goodsImg" alt="">
            <div class="right">
                <h6>{{goods.goodsName}}</h6>
                <p class="num">{{goods.teamNum}}人团 <span>￥{{goods.teamPrice}}</span></p>
            </div>
            <img v-if="type==1" class="sign" src="/static/images/p_success.png" alt="">
            <img v-if="type==2" class="sign" src="/static/images/p_fail.png" alt="">
        </div>
        <div class="main">
            <ul class="head-list">
                <li v-for="(item, index) in goods.team" :key="index">
                    <img mode="aspectFill" :src="item.userImg" alt="">
                    <span v-if="item.isTeam" class="captain">团长</span>
                </li>  
                <li v-if="false">
                    <img mode="aspectFill" src="/static/images/icon-Unassembled-Group.png" alt="">
                </li> 
            </ul>
            <p class="success" v-if="type==1">人员已满，拼团成功</p>
            <p class="success" v-if="type==2">拼团失败</p>
            <p class="tibs" v-if="type==0">还差{{goods.lessNum}}人团购成功，赶快召唤小伙伴来参团吧</p>
            <p class="fail" v-if="type==2">系统将会在1~2工作日内，将货款退还至您的账户</p>
            <div class="time-box" v-if="type==0 && btnFlag">
                <p class="time">剩余</p>
                <span>{{goods.endDateStr.HH}}</span>
                <p>:</p>
                <span>{{goods.endDateStr.mm}}</span>
                <p>:</p>
                <span>{{goods.endDateStr.ss}}</span>
            </div> 
        </div>
        <ul class="list">
            <li v-for="(item, index) in goods.team" :key="index">
                <div class="img-box">
                    <img mode="aspectFill" :src="item.userImg" alt="">
                    <span v-if="item.isTeam" class="captain">团长</span>
                </div>
                <p class="text">{{item.payTime}}{{item.isTeam ? '开团' : '参团'}}</p>
            </li>
        </ul>
        <div class="btn">
            <button open-type="share" v-if="btnFlag===true&&type===0&&participateType===1">邀请好友参团</button>
            <button v-if="btnFlag===false && type==2" @click="goAssemble">去开团</button>
            <button v-if="type==1&&participateType===1" @click="goDetail">订单详情</button>
            <button @click="goOrder" v-if="type==0&&participateType===0">参与拼团</button>
        </div>
    </div>
</template>

<script>
import { teamDetail, getTeamGoods } from '@/api'
import { formatTime } from '@/utils/index'
import toast from "@/plugins/toast";
export default {
    data () {
        return {
            timer: null,
            id: '',
            type: '',   //1成功  0进行中   2失败
            participateType: '',  //0：参与   1：邀请
            btnFlag: '',
            goods: {
                endDateStr: {
                    HH: '00',
                    mm: '00',
                    ss: '00'
                }
            }
        }
    },
    computed: {
        teamDetail () {
            return this.$store.state.team.teamDetail
        }
    },
    onShareAppMessage(res) {
        return {
            title: '拼团详情',
            path: `pages/home/<USER>/clusterGoods/collaging/partakeCluster/main?id=${this.id}`
            // path: 'pages/main/assembles/clusterDetail/main?id=' + this.id
        }
    },
    methods: {
        goOrder() {
            getTeamGoods({id: this.id,type: 2}).then(res => {
                if (res.data.code) {
                    wx.showToast({ title: res.data.msg, icon:'none', duration: 1000})
                } else {
                    wx.navigateTo({
                        url: `/pages/home/<USER>/clusterGoods/orders/main?id=${this.id}&type=2`
                    })
                }
            })
        },
        goAssemble() {
            wx.navigateTo({ url: `/pages/home/<USER>/clusterGoods/main?id=${this.goods.goodsId}` })
        },
        goDetail() {
            wx.navigateTo({ url: `/pages/main/Order/orderDetails/main?id=${this.id}&orderType=1` })
        },
        timerHandler() {
            let that = this
            let endDateStr = {}
            this.timer = setInterval(function(){
                let timestamp = Date.parse(new  Date())
                if(that.goods.endDateStr) {
                    var time = that.goods.endDate2  //取出时间
                    //时间差
                    let timeDifference = time - timestamp
                    if(timeDifference <= 0) {
                        endDateStr.HH = '00'
                        endDateStr.mm = '00'
                        endDateStr.ss = '00'
                        that.$set(that.goods,'textFlag',false)
                        that.btnFlag = false
                    } else {
                        //hour
                        let HH = parseInt( timeDifference / 3600000 )
                        if(HH < 10) {
                            HH = '0' + HH
                        }
                        //minute
                        let mm = parseInt( (timeDifference % 3600000) / 60000 )
                        if(mm < 10) {
                            mm = '0' + mm
                        }
                        //second
                        let ss = parseInt( (timeDifference % 3600000 % 60000) / 1000 )
                        if(ss < 10) {
                            ss = '0' + ss
                        }
                        endDateStr.HH = HH
                        endDateStr.mm = mm
                        endDateStr.ss = ss
                        that.btnFlag = true
                    }
                    if (endDateStr.HH == '00' && endDateStr.mm == '00' & endDateStr.ss == '00') {
                        that.btnFlag = false
                    }
                    that.$set(that.goods,'endDateStr',endDateStr)
                }
            },1000)
        }
    },
    onUnload() {
        clearInterval(this.timer)
    },
    onShow () {
        let id = this.$root.$mp.query.id
        this.id = this.$root.$mp.query.id
        teamDetail({id}).then(res => {
            if (res.data.code) {
                wx.showToast({ title: res.data.msg, icon:'none', duration: 1000})
            } else {
                res.data.team.forEach(item => {
                    item.payTime = formatTime(item.payTime)
                })
                this.goods = res.data
                this.type = res.data.type
				this.participateType = res.data.participateType
                this.$set(this.goods,'textFlag',true)
                let obj = {
                    HH: '00',
                    mm: '00',
                    ss: '00'
                }
                this.$set(this.goods,'endDateStr', obj)
                this.timerHandler()
            }
        })
    }
}
</script>

<style lang="less">
.container {
  .section {
    width: 100%;
    padding: 10px 15px 12px;
    box-sizing: border-box;
    margin-bottom: 10px;
    background: #fff;
    display: flex;
    align-items: center;
    position: relative;
    img {
      width: 96px;
      height: 96px;
      border-radius: 4px;
      margin-right: 10px;
    }
      .sign {
        width: 70px;
        height: 70px;
        position: absolute;
        right: 5px;
        bottom: 10px;
    }
    .right {
      h6 {
        width: 240px;
        font-size: 16px;
        color: #171717;
        line-height: 22px;
        margin-bottom: 30px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .num {
        font-size: 14px;
        line-height: 20px;
        color: #343333;
        span {
          color: #FF5100;
        }
      }
    }
  }
  .main {
    width: 100%;
    margin-bottom: 10px;
    padding: 20px 0;
    box-sizing: border-box;
    background: #fff;
    .success {
        margin-bottom: 10px;
        font-size: 14px;
        color: #343333;
        text-align: center;
    }
    .fail {
        font-size: 14px;
        color: #9B9B9B;
        text-align: center;
    }
    .head-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 19px;
      li {
        width: 50px;
        height: 50px;
        position: relative;
        margin-right: 20px;
        &:last-child{
          margin-right: 0;
        }
        img {
          width: 50px;
          height: 50px;
          border-radius: 50%;
        }
        .captain{
          position: absolute;
          bottom: 0;
          right: -13px;
          width: 32px;
          height: 14px;
          line-height: 14px;
          background:rgba(254,124,60,1);
          border-radius:7px;
          font-size: 10px;
          color: #fff;
          text-align: center;
        }
      }
    }
    .tibs {
      font-size: 13px;
      line-height: 18px;
      color: #343333;
      text-align: center;
      margin-bottom: 20px;
    }
    .time-box {
      text-align: center;
      font-size: 12px;
      line-height: 17px;
      color: #343333;
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        margin: 0 5px;
      }
      .time {
        margin: 0 10px 0 0;
      }
      span {
        padding: 0 8px;
        box-sizing: border-box;
        height: 17px;
        color: #fff;
        background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
        border-radius:4px;
      }
    }
  }
  .list {
    margin-bottom: 52px;
    li {
      width: 100%;
      height: 50px;
      padding: 0 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      background: #fff;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #EDEBEB;
      }
      &:last-child::after{
        background-color: transparent;
      }
      .img-box {
        position: relative;
        img {
          width: 36px;
          height: 36px;
          border-radius: 50%;
        }
        .captain{
          position: absolute;
          bottom: 0;
          right: -13px;
          width: 32px;
          height: 14px;
          line-height: 14px;
          background:rgba(254,124,60,1);
          border-radius:7px;
          font-size: 10px;
          color: #fff;
          text-align: center;
        }
      }
      .text {
        font-size: 13px;
        color: #4A4A4A;
        line-height: 18px;
      }
    }
  }
  .btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 15px 12px;
    box-sizing: border-box;
    button {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      color: #fff;
      background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      border-radius:22px;
    }
  }
}
</style>
        