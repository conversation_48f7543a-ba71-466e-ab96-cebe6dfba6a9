<template>
  <div class="container">
    <div class="box">
      <div class="header">
        <div class="banner">
          <swiper class="swiper"
            @change="swiperChange"
            :indicator-dots= false
            :autoplay="true"
            :interval="2000"
            :circular = true
            :current="curr">
            <swiper-item v-for="(item, index) in banner" :key="index">
              <image :src="item + '?x-oss-process=image/resize,m_fixed,h_500,w_500'" mode="aspectFill" class="slide-image" />
            </swiper-item>
          </swiper>
          <div class="point">{{curr+1}}/{{banner.length}}</div>
        </div>
        <div class="line-box">
          <div class="left">
            <div class="group-price">拼团价<span>¥</span><span class="price">{{ticketDetail.teamPrice}}</span></div>
            <!-- <span class="original">原价¥ <span>{{ticketDetail.orgPrice}}</span></p> -->
          </div>
          <div class="people">
            <img class="ren" src="/static/images/icon-people.png" alt="">
            <p class="people-num">{{ticketDetail.teamPerson}}人团</p>
          </div>
        </div>
        <div class="cont">
          <div class="title">
            <h5>{{ticketDetail.teamName}}</h5>
            <button  open-type="share" class="share-bt-box">
              <div class="share-box">
                <img class="share" src="/static/images/icon-share.png" alt="">
                <p>分享</p>
              </div>
            </button>
          </div>
        </div>
      </div>
      <div class="detail">
        <div class="detail-title" @click="queryCount">
          <div class="grounp">
            <img mode="aspectFit" src="/static/images/icon-group-tibs.png" alt="" class="tib">
            <p class="narrate" v-if="ticketDetail.tickCount != 0"><span>{{ticketDetail.tickCount}}人</span>正在拼团，可直接参与</p>
            <p class="narrate" v-if="ticketDetail.tickCount == 0">当前暂无拼团</p>
          </div>
          <div class="more" v-if="ticketDetail.tickCount != 0">
            <span class="txt" @click="seeAll">查看更多</span>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt="">
          </div>
        </div>
        <ul class="detail-list">
          <li v-for="(item, index) in ticketDetail.byTeamOrders" :key="index" v-if="item.textFlag">
            <div class="info">
              <img class="head-img" :src="item.headImg" alt="">
              <p class="name">{{item.nickName}}</p>
            </div>
            <div class="right">
              <div class="assemble">
                <p class="tibs-text">还差<span>{{item.lessNum}}人</span>拼成</p>
                <p class="time">{{item.endDateStr}}</p>
              </div>
              <div class="btn" @click="gettickCount(item)">去拼单</div>
            </div>
          </li>
        </ul>
      </div>
      <!-- <div class="evaluate" v-if="ticketDetail.byEvaluates.length"> 
        <div class="title-box" @click="getMore">
          <p class="eval">商品评价（{{ticketDetail.byEvaluatesCount}}）</p>
          <div class="more">
            <span class="txt">查看更多</span>
            <img class="arrow" src="/static/images/icon-gray-arrow.png" alt="">
          </div>
        </div>
        <div class="content-box" v-for="(item, index) in ticketDetail.byEvaluates" :key="index" v-if="ticketDetail.byEvaluatesCount != 0">
          <div class="info">
            <div class="my-info">
              <img class="headimg" :src="item.headImg" alt="">
              <p class="name">{{item.userName}}</p>
            </div>
            <p class="time">{{item.gmtCreate}}</p>
          </div>
          <p class="content">{{item.evaluateText}}</p>
        </div>
        <div class="tibs" v-if="ticketDetail.byEvaluatesCount == 0">
          当前暂无评价
        </div>
      </div> -->
      <!-- <div class="img-box" v-html="ticketDetail.richContent">
      </div> -->
       <div class="img-box">
         <wxParse :content="ticketDetail.richContent" /> 
      </div>  
    </div>
    <div class="footer">
      <ul class="icon-list">
        <li @click="toIndex">
          <img src="/static/images/icon-goback.png" alt="" class="icon">
          <p>首页</p>
        </li>
        <li class="service">
          <button open-type="contact" class="customer">
            <img src="/static/images/icon-Customer-service.png" alt="" class="icon">
            <p>客服</p>
          </button>
        </li>
      </ul>
      <div class="btn">
        <button v-if="ticketDetail.stockNum != 0" @click="queryOrder">发起拼团</button>
        <button class="prohibit" v-if="ticketDetail.stockNum == 0">发起拼团（库存不足）</button>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { getTicketDetail, checkUserDisable } from '@/api'
import wxParse from 'mpvue-wxparse'
import toast from "@/plugins/toast";
export default {
  data () {
    return {
      token: '',
      registerFlag: '',
      timer: null,
      id: '',
      curr: 0,
      banner: [],
      ticketDetail: {},
      textFlag: true,
      activity: false  //拼团是否结束
    }
  },
   components:{
    wxParse
  },
//   computed: {
//     ticketDetail () {
//       return this.$store.state.team.ticketDetail
//     }
//   },
    methods: {
        seeAll() {
            wx.navigateTo({ 
                url: `/pages/home/<USER>/clusterGoods/collaging/main?id=${this.id}`
            })
        },
    swiperChange (e) {
      this.curr = e.target.current
    },
    // 回到首页
    toIndex () {
      wx.switchTab({
        url: '/pages/index/main'
      })
    },
    // 评价查看更多
    getMore () {
      if(this.activity) {
        toast.text("拼团活动已结束");
        return;
      }
      if (this.ticketDetail.byEvaluatesCount != 0) {
        let id = this.$root.$mp.query.id
        wx.navigateTo({
          url: `/pages/home/<USER>/clusterGoods/comment/main?id=${id}&type=4`
        })
      }
    },
    // 拼团查看更多
    queryCount () {
      if(this.activity) {
        toast.text("拼团活动已结束");
        return;
      }
      if (this.ticketDetail.tickCount != 0) {
        let id = this.$root.$mp.query.id
        wx.navigateTo({
          url: `/pages/home/<USER>/clusterGoods/collaging/main?id=${id}`
        })
      }
    },
    // 去拼单
    gettickCount ({id}) {
        if (!this.token) {
            wx.showModal({
                content: '您还没有登录', 
                cancelText: '取消', 
                confirmText: '去登录',
                success: res => {
                    if (res.confirm) {
                        wx.navigateTo({ url: `/pages/authorize/main` })
                    }
                }
            })
            return
        } else {
            if (!this.registerFlag) {
                wx.showModal({
                    content: '您还没有注册', 
                    cancelText: '取消', 
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }
        }
        checkUserDisable({type: 4,ids: this.$mp.query.id}).then(data => {
            if (data.data.code == 0) {
                if(this.activity) {
                    toast.text("拼团活动已结束");
                    return;
                }
                wx.navigateTo({
                    url: `/pages/home/<USER>/clusterGoods/collaging/partakeCluster/main?id=${id}`
                })           
            } else {
                toast.text(data.data.msg)
            }
        })
    },
    // 发起拼团
    queryOrder () {
        if (!this.token) {
            wx.showModal({
                content: '您还没有登录', 
                cancelText: '取消', 
                confirmText: '去登录',
                success: res => {
                    if (res.confirm) {
                        wx.navigateTo({ url: `/pages/authorize/main` })
                    }
                }
            })
            return
        } else {
            if (!this.registerFlag) {
                wx.showModal({
                    content: '您还没有注册', 
                    cancelText: '取消', 
                    confirmText: '去注册',
                    success: res => {
                        if (res.confirm) {
                            wx.navigateTo({ url: `/pages/login/main` })
                        }
                    }
                })
                return
            }
        }
        checkUserDisable({type: 4,ids: this.$mp.query.id}).then(data => {
            if (data.data.code == 0) {
                if(this.activity) {
                    toast.text("拼团活动已结束")
                    return;
                }
                let id = this.$root.$mp.query.id
                wx.navigateTo({
                    url: `/pages/home/<USER>/clusterGoods/orders/main?id=${id}&type=1`
                })         
            } else {
                toast.text(data.data.msg)
            }
        })
    },
    timerHandler() {
        let that = this
        let endDateStr = ''
        this.timer = setInterval(function(){
            let timestamp = Date.parse(new  Date())
            if(that.ticketDetail.byTeamOrders.length && that.ticketDetail.byTeamOrders) {
                for(let i=0;i<that.ticketDetail.byTeamOrders.length;i++) {
                    var time = that.ticketDetail.byTeamOrders[i].endDate2  //取出时间
                    //时间差
                    let timeDifference = time - timestamp
                    if(timeDifference <= 0) {
                        endDateStr = '00:00:00'
                        that.$set(that.ticketDetail.byTeamOrders[i],'textFlag',false)
                    } else {
                        that.$set(that.ticketDetail.byTeamOrders[i],'textFlag',true)
                        //hour
                        let HH = parseInt( timeDifference / 3600000 )
                        if(HH < 10) {
                            HH = '0' + HH
                        }
                        //minute
                        let mm = parseInt( (timeDifference % 3600000) / 60000 )
                        if(mm < 10) {
                            mm = '0' + mm
                        }
                        //second
                        let ss = parseInt( (timeDifference % 3600000 % 60000) / 1000 )
                        if(ss < 10) {
                            ss = '0' + ss
                        }
                        endDateStr = `${HH}:${mm}:${ss}`
                    }
                    that.$set(that.ticketDetail.byTeamOrders[i],'endDateStr',endDateStr)
                }
            }
        },1000)
    },
  
  },
  onUnload() {
      clearInterval(this.timer)
  },
  onShow () {
    this.curr = 0
    this.token = wx.getStorageSync('token')
    this.registerFlag = wx.getStorageSync('registerFlag')
    let id = this.$mp.query.id
    this.id = id
    // this.$store.dispatch('team/getTicketDetail', {id: id}).then(res => {
    //   this.banner = this.ticketDetail.goodsBanner ? this.ticketDetail.goodsBanner.split(',') : []
    // })
    getTicketDetail({id}).then(res => {
        if(!res.data.code) {
            this.banner = res.data.goodsBanner ? res.data.goodsBanner.split(',') : []
            this.ticketDetail = res.data
            if(this.ticketDetail.byTeamOrders.length && this.ticketDetail.byTeamOrders) {
                this.ticketDetail.byTeamOrders.filter((item) => {
                    this.$set(item,'textFlag',false)
                    this.$set(item,'endDateStr','00:00:00')
                })
                this.timerHandler()
            }
        } else {
           toast.text(res.data.msg);
           this.activity = true;
        }
    })
  },
    onShareAppMessage: function(options) {
        let that = this;
        return {
            title: `${that.ticketDetail.teamName}`,
            path: "pages/home/<USER>/clusterGoods/main?id=" + that.id
        }
	}
}
</script>

<style lang="less">
.container {
  image {
      display: block !important;
  }
  .box {
    padding-bottom: 50px;
    box-sizing: border-box;
  }
  .header {
    margin-bottom: 10px;
    .banner {
      width: 100%;
      height: 375px;
      position: relative;
      .swiper {
        width: 100%;
        height: 375px;
        .slide-image {
          width: 100%;
          height: 375px;
        }
      }
      .point {
        position: absolute;
        right: 10px;
        bottom: 10px;
        padding: 0 10px;
        box-sizing: border-box;
        height: 21px;
        line-height: 21px;
        border-radius: 11px;
        background:rgba(0,0,0,0.2);
        font-size: 14px;
        color: #fff;
      }
    }
    .line-box {
      width: 100%;
      height: 50px;
      background:linear-gradient(90deg,rgba(254,116,61,1) 0%,rgba(255,151,56,1) 100%);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
      box-sizing: border-box;
      color: #fff;
      .left {
        display: flex;
        align-items: center;
        position: relative;
        .group-price {
          font-size: 16px;
          line-height: 22px;
          display: inline-block;
          margin-right: 10px;
          span {
            font-size: 18px;
          }
          .price {
            font-size: 24px;
          }
        }
        .original {
          font-size: 11px;
          line-height: 22px;
          span {
            text-decoration: line-through;
          }
        }
      }
      .people {
        width: 64px;
        height: 18px;
        line-height: 18px;
        position: relative;
        .ren {
          width: 64px;
          height: 18px;
        }
        .people-num {
          position: absolute;
          top: 0;
          left: 0;
          width: 64px;
          height: 18px;
          padding: 0 0 0 23px;
          box-sizing: border-box;
          font-size: 14px;
        }
      }
    }
    .cont {
      width: 100%;
      padding: 11px 15px 6px;
      box-sizing: border-box;
      background: #fff;
      .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        h5 {
          width: 304px;
          font-size: 18px;
          line-height: 25px;
          font-weight: 500;
          color: #171717;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .share-bt-box{
          background:rgba(0, 0, 0, 0);
        .share-box {
          width: 19px;
          font-size: 9px;
          color: #9B9B9B;
          line-height: 13px;
          text-align: center;
          .share {
            width: 19px;
            height: 19px;
            // margin-bottom: 3px;
          }
        }
        }
      }
      .text {
        // display: flex;
        .num {
          font-size: 26px;
          line-height: 37px;
          color: #FF5100;
          margin-right: 10px;
          display: inline-block;
        }
        .abolish {
          font-size: 16px;
          color: #999999;
          text-decoration: line-through;
        }
      }
    }
  }
  .detail {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .detail-title {
      width: 100%;
      height: 44px;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #EDEBEB;
      }
      .grounp {
        display: flex;
        align-items: center;
        .tib {
          width: 19px;
          height: 19px;
          margin-right: 8px;
        }
        .narrate {
          font-size: 14px;
          line-height: 20px;
          color: #4A4A4A;
          span {
            color: #FF5100;
          }
        }
      }
      .more {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9B9B9B;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .detail-list {
      width: 100%;
      padding: 0 15px;
      box-sizing: border-box;
      li {
        width: 100%;
        padding: 10px 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        &::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(.5);
          transform: scaleY(.5);
          background-color: #EDEBEB;
        }
        &:last-child::after{
          background-color: transparent;
        }
        .info {
          display: flex;
          align-items: center;
          .head-img {
            width: 40px;
            height: 40px;
            margin-right: 12px;
          }
          .name {
            font-size: 14px;
            color: #4A4A4A;
            word-wrap:break-word;
            width: 140px;
            line-height: 20px;
          }
        }
        
        .right {
          display: flex;
          align-items: center;
          .assemble {
            width: 75px;
            text-align: center;
            margin-right: 12px;
            .tibs-text {
              font-size: 12px;
              line-height: 17px;
              color: #4A4A4A;
              margin-bottom: 2px;
              span {
                color: #F01520;
              }
            }
            .time {
              font-size: 11px;
              color: #9B9B9B;
              line-height: 16px;
            }
          }
          .btn {
            width: 70px;
            height: 24px;
            line-height: 24px;
            padding: 0;
            border-radius: 13px;
            background:linear-gradient(270deg,rgba(240,21,32,1) 0%,rgba(255,84,93,1) 100%);
            text-align: center;
            font-size: 13px;
            color: #fff;
          }
        }
      }
    }
  }
  .evaluate {
    width: 100%;
    background: #fff;
    margin-bottom: 10px;
    .title-box {
      width: 100%;
      height: 42px;
      display: flex;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      justify-content: space-between;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #D6D6D6;
      }
      .eval {
        font-size: 14px;
        color: #302E27;
        line-height: 20px;
      }
      .more {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #9B9B9B;
        line-height: 20px;
        .arrow {
          width: 7px;
          height: 13px;
          margin-left: 10px;
        }
      }
    }
    .content-box {
      width: 100%;
      padding: 14px 15px 7px;
      box-sizing: border-box;
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .my-info {
          display: flex;
          align-items: center;
          margin-bottom: 13px;
          .headimg {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
          }
          .name {
            font-size: 12px;
            color: #090203;
            line-height: 17px;
          }
        }
        .time {
          font-size: 12px;
          color: #9B9B9B;
          line-height: 17px;
        }
      }
      .content {
        font-size: 14px;
        color: #090203;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
    .tibs {
      padding: 10px 15px;
      box-sizing: border-box;
      font-size: 12px;
      color: #9B9B9B;
      line-height: 17px;
    }
  }
  .img-box {
    width: 100%;
    img {
      width: 100%;
    }
  }
  .footer {
    width: 100%;
    height: 49px;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 10px 0 0;
    box-sizing: border-box;
    justify-content: space-between;
    .icon-list {
      width: 105px;
      display: flex;
      align-items: center;
      justify-content: center;
      li, .customer{
        padding: 0 11px;
        box-sizing: border-box;
        font-size: 0;
        text-align: center;
        background: #fff;
        .icon {
          width: 23px;
          height: 23px;
          margin-bottom: 1px;
        }
        p {
          font-size: 10px;
          line-height: 14px;
          color: #4A4A4A;
        }
      }
      .service {
        padding: 0;
      }
    }
    .btn {
      width: 260px;
      height: 36px;
      line-height: 36px;
      display: flex;
      align-items: center;
      button {
        width: 280px;
        height: 36px;
        padding: 0;
        font-size: 14px;
        color: #fff;
        border-radius: 18px;
        background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
      }
      .prohibit {
        background: #C6C6C6;
      }
    }
  }
}
</style>
