<template>
    <div class="container">
        <div class="main">
            <img mode="aspectFit" src="/static/images/icon-success.png" alt="">
            <p class="success">支付成功</p>
        </div>
        <div class="btn btn2" v-if="type == 'ticket' ">
            <button @click="goOrder">查看订单</button>
            <!-- <button @click="goAppoint" class="group">立即预约</button> -->
        </div>
        <div class="btn" v-else>
            <button @click="goHome">返回首页</button>
            <button @click="goList" class="group">查看拼团</button>
        </div>
        <!-- <div class="tip">温馨提示：下单成功后，务必在“宝燕乐园”公众号菜单点击“预约”进行线上预约哦</div> -->
        <!-- 温馨提示 -->
        <div class="guanggao" v-show="guanggaoshow">
          <icon class="cancel" type="cancel" size="40" color='rgb(255,255,255)' @click="guanggaoshow=false"/>
          <div class="box">
            <img class="image" mode="aspectFit" src="/static/images/ts.png" alt="">
            <!-- <p class="ggtext">温馨提示：下单成功后，务必在“宝燕乐园”公众号菜单点击“预约”进行线上预约哦</p> -->
          </div>
        </div>
    </div>
</template>

<script>
export default {
    data () {
        return {
            logs: [],
            type:'',
            guanggaoshow:true,
        }
    },
    onLoad() {
    this.guanggaoshow=true
      this.type = this.$mp.query.type;

    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        goHome() {
            wx.switchTab({
                url: '/pages/index/main'
            })
        },
        goList() {
            wx.navigateTo({ 
                url: '/pages/main/assembles/main' 
            })
        },
        goOrder() {
          wx.navigateTo({ 
                url: '/pages/main/Order/main?type=' + 0 
            })
        },

        goAppoint() {
           wx.switchTab({ 
                url: '/pages/bespeaktab/main'
            })
        }
    }
}
</script>

<style lang="less">
.container {
    .tip {
        color: #FE743D;
        font-size: 12px;
        margin-top: 40px;
        padding: 20px 26px 20px 28px;
    }
    .wcolor{
        color: #fff;
    }
    .main {
        width: 100%;
        padding: 115px 0 59px;
        box-sizing: border-box;
        text-align: center;
        font-size: 18px;
        color: #4A4A4A;
        line-height: 25px;
        img {
            width: 72px;
            height: 88px;
            margin-bottom: 13px;
        }
    }
    .btn {
        margin-top: 60px;
        width: 100%;
        padding: 0 52px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        button {
            width: 120px;
            height: 40px;
            line-height: 40px;
            border-radius: 22px;
            border: 1px solid #ED813A;
            background: transparent;
            padding: 0;
            font-size: 16px;
            color: #FE743D;
            box-sizing: border-box;
        }
        .group {
        background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
        color: #fff;
        border: none;
        }
    }
    .btn2 {
        justify-content: center;
    }
  .guanggao {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    .cancel {
      position: absolute;
      top: 10vh;
      right: 5%;
    }
    .box{
      width: 100%;
      position: absolute;
      top: 18vh;
      left: 50%;
      transform: translateX(-50%);
    }
    .image {
        width: 100%;
        height: 424px;
    }
    .ggtext{
        color: #fff;
        font-size: 12px;
        padding: 20px 0;
    }
  }
}
</style>
