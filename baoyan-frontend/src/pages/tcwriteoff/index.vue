<template>
  <div class="container">
    <div class="header">
      <img :src="dataObj.goodsImg" alt="">
      <h5>{{dataObj.goodsName}}</h5>
    </div>
    <ul class="list">
      <li>
        <p>购买人</p>
        <span>{{dataObj.userName}}</span>
      </li>
      <li>
        <p>手机号</p>
        <span>{{dataObj.mobile}}</span>
      </li>
      <li>
        <p>总可用次数</p>
        <span>{{dataObj.total}}</span>
      </li>
      <li>
        <p>剩余可用次数</p>
        <span>{{dataObj.surplus}}</span>
      </li>
      <li>
        <p>有效期至</p>
        <span>{{dataObj.endDate}}</span>
      </li>
      <li>
        <p>状态</p>
        <span>{{dataObj.isUser ? '可使用' : '不可使用'}}</span>
      </li>
    </ul>
    <div class="type">
      <p>退款状态</p>
      <span>{{dataObj.refuse ? '已退款' : '未退款'}}</span>
    </div>
    <button @click="showModelHandle">确认核销</button>

    <div class="mask" v-if="isShow">
      <div class="main">
        <h6>确认核销</h6>
        <div class="second">
          <p class="name">核销次数：</p>
          <div class="num-box">
            <div class="img-box">
              <img @click="reduceHandle" src="/static/images/icon-reduce.png" alt="">
            </div>
            <p>{{num}}</p>
            <div class="img-box">
              <img @click="addHandle" src="/static/images/icon-plus.png" alt="">
            </div>
          </div>
        </div>
        <div class="btn-box">
          <div class="btn" @click="isShow = false">取消</div>
          <div class="btn" @click="confirmHandle">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { writeCheck, writeGoods } from '@/api'

export default {
    data () {
        return {
            num: 1,
            isShow: false,
            dataObj: {},
            confirmbtn:false,
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    methods: {
        showModelHandle() {
            if (this.dataObj.surplus == 0) {
                wx.showToast({
                    title: '剩余次数不足', //提示的内容,
                    icon: 'none', //图标,
                    duration: 1000
                })
                return
            }
            this.isShow = true
        },
        confirmHandle() {
          if(this.confirmbtn){
            return false
          }
          this.confirmbtn=true
            writeGoods({id: this.id, count: this.num}).then(res => {
                if (res.data.code) {
                    this.confirmbtn=false
                    wx.showToast({
                        title: res.data.msg, //提示的内容,
                        icon: 'none', //图标,
                        duration: 500
                    })
                    return
                } else {
                    wx.showToast({
                        title: '核销完成', //提示的内容,
                        icon: 'none', //图标,
                        duration: 500
                    })
                    wx.setStorageSync('WriteOffInfo', res.data)
                    setTimeout(() => {
                        this.confirmbtn=false
                        wx.reLaunch({ url: '/pages/writeoff/success/main' })
                    },500)
                }
            })
        },
        reduceHandle() {
            if (this.num == 1) return
            this.num --
        },
        addHandle() {
            if (this.num == this.dataObj.surplus) {
                wx.showToast({
                    title: '剩余次数不足', //提示的内容,
                    icon: 'none', //图标,
                    duration: 1000
                })
                return
            }
            this.num ++
        },
        getData() {
            writeCheck({id: this.id}).then(res => {
                if (res.data.code) {
                    wx.showToast({
                        title: res.data.msg, //提示的内容,
                        icon: 'none', //图标,
                        duration: 1000
                    })
                } else {
                    this.dataObj = res.data
                }
            })
        }
    },
    onLoad (query) {
        this.num = 1
        this.isShow = false
        this.id = decodeURIComponent(query.scene)
        // this.id=41172
        this.getData()
    }
}
</script>

<style lang="less">
.container {
  .header {
    width: 100%;
    background: #fff;
    padding: 10px 15px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 8px;
    img {
      width: 96px;
      height: 96px;
      border-radius: 4px;
      margin-right: 10px;
    }
    h5 {
      width: 240px;
      font-size: 15px;
      color: #343333;
      line-height: 21px;
    }
  }
  .list {
    background: #fff;
    margin-bottom: 10px;
    li {
      width: 100%;
      height: 50px;
      line-height: 50px;
      padding: 0 15px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      font-size: 14px;
      color: #9B9B9B;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #D6D6D6;
      }
      &:last-child::after {
        background-color: transparent;
      }
      p {
        color: #4A4A4A;
      }
    }
  }
  .type {
    width: 100%;
    background: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #9B9B9B;
    margin-bottom: 62px;
    p {
      color: #4A4A4A;
    }
  }
  button {
    width: 345px;
    height: 40px;
    line-height: 40px;
    padding: 0;
    text-align: center;
    margin: 0 auto;
    background:linear-gradient(90deg,rgba(255,151,56,1) 0%,rgba(254,116,61,1) 100%);
    border-radius:22px;
    font-size: 16px;
    color: #fff;
  }
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background:rgba(0,0,0,.3);
    z-index: 10;
    .main {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 244px;
      background: #fff;
      padding: 20px 0 0;
      box-sizing: border-box;
      text-align: center;
      z-index: 11;
      border-radius: 2px;
      h6 {
        font-size: 15px;
        color: #333333;
        margin-bottom: 26px;
      }
      .second {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        color: #333333;
        line-height: 18px;
        margin-bottom: 31px;
        .name {
          margin-right: 3px;
          color: #7A7A7A;
        }
        .num-box {
          display: flex;
          align-items: center;
          .img-box {
            padding: 0 12px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            img {
              width: 18px;
              height: 18px;
            }
          }
        }
      }
      .btn-box {
        width: 100%;
        height: 45px;
        display: flex;
        align-items: center;
        border-top: 1rpx solid #E5E5E5;
        .btn {
          width: 50%;
          height: 100%;
          line-height: 45px;
          font-size: 15px;
          color: #9B9B9B;
          &:last-child {
            border-left: 1rpx solid #E5E5E5;
            box-sizing: border-box;
            color: #ED813A;
          }
        }
      }
    }
  }
}
</style>
