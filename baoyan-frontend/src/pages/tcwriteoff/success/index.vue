<template>
  <div class="container">
    <div class="header">
      <img mode="aspectFit" src="/static/images/icon-success.png" alt="">
      <h5>核销完成</h5>
    </div>
    <ul class="list">
      <li>
        <p>核销门店</p>
        <span>{{dataObj.storeName}}</span>
      </li>
      <li>
        <p>核销员工</p>
        <span>{{dataObj.userName}}</span>
      </li>
      <li>
        <p>核销时间</p>
        <span>{{dataObj.expiryDate}}</span>
      </li>
      <li>
        <p>核销券码</p>
        <span>{{dataObj.goodsName}}</span>
      </li>
      <li>
        <p>核销次数</p>
        <span>{{dataObj.surplusNum}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
    data () {
        return {
            dataObj: {}
        }
    },
    onShareAppMessage(res) {
        return {
            title: '宝燕乐园',
            path: `pages/index/main`
        }
    },
    onLoad() {
        this.dataObj = wx.getStorageSync('WriteOffInfo')
    }
}
</script>

<style lang="less">
.container {
  .header {
    width: 100%;
    padding: 49px 0 32px;
    box-sizing: border-box;
    text-align: center;
    img {
      width: 72px;
      height: 88px;
      margin-bottom: 15px;
    }
    h5 {
      font-size: 18px;
      line-height: 25px;
      color: #4A4A4A;
    }
  }
  .list {
    width: 100%;
    background: #fff;
    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      color: #9B9B9B;
      min-height: 50px;
      padding: 0 15px;
      box-sizing: border-box;
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: #D6D6D6;
      }
      &:last-child::after {
        background-color: transparent;
      }
      p {
        color: #4A4A4A;
      }
      span {
          flex: 1;
          margin-left: 20px;
      }
    }
  }
}
</style>
