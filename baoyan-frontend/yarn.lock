# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@7.0.0-beta.44":
  "integrity" "sha1-KgJkM2jegJFhYr5whlyXd08629k="
  "resolved" "http://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/highlight" "7.0.0-beta.44"

"@babel/generator@7.0.0-beta.44":
  "integrity" "sha1-x+Z7m1KEr89pswm1DX038+UDPUI="
  "resolved" "http://registry.npm.taobao.org/@babel/generator/download/@babel/generator-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"
    "jsesc" "^2.5.1"
    "lodash" "^4.2.0"
    "source-map" "^0.5.0"
    "trim-right" "^1.0.1"

"@babel/helper-function-name@7.0.0-beta.44":
  "integrity" "sha1-4YVSqq4iMRAKbkheA4VLw1MtRN0="
  "resolved" "http://registry.npm.taobao.org/@babel/helper-function-name/download/@babel/helper-function-name-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/helper-get-function-arity" "7.0.0-beta.44"
    "@babel/template" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-get-function-arity@7.0.0-beta.44":
  "integrity" "sha1-0Dym3SufewseazLFbHKDYUDbOhU="
  "resolved" "http://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-split-export-declaration@7.0.0-beta.44":
  "integrity" "sha1-wLNRc14PvLOCLIrY205YOwXr2dw="
  "resolved" "http://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/highlight@7.0.0-beta.44":
  "integrity" "sha1-GMlM5UORaoBVPtzc9oGJCyAHR9U="
  "resolved" "http://registry.npm.taobao.org/@babel/highlight/download/@babel/highlight-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "chalk" "^2.0.0"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.0"

"@babel/template@7.0.0-beta.44":
  "integrity" "sha1-+IMvT9zuXVm/UV5ZX8UQbFKbOU8="
  "resolved" "http://registry.npm.taobao.org/@babel/template/download/@babel/template-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "lodash" "^4.2.0"

"@babel/traverse@7.0.0-beta.44":
  "integrity" "sha1-qXCixFR3rRgBfi5GWgYG/u4NKWY="
  "resolved" "http://registry.npm.taobao.org/@babel/traverse/download/@babel/traverse-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/generator" "7.0.0-beta.44"
    "@babel/helper-function-name" "7.0.0-beta.44"
    "@babel/helper-split-export-declaration" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "debug" "^3.1.0"
    "globals" "^11.1.0"
    "invariant" "^2.2.0"
    "lodash" "^4.2.0"

"@babel/types@7.0.0-beta.44":
  "integrity" "sha1-axsWRZH3fewKA0KsqZXy0Eazp1c="
  "resolved" "http://registry.npm.taobao.org/@babel/types/download/@babel/types-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"
  dependencies:
    "esutils" "^2.0.2"
    "lodash" "^4.2.0"
    "to-fast-properties" "^2.0.0"

"@types/node@^10.11.7":
  "integrity" "sha1-13+fqgJ8ra2ckSzUf0+LB7D7CGQ="
  "resolved" "http://registry.npm.taobao.org/@types/node/download/@types/node-10.12.2.tgz"
  "version" "10.12.2"

"@types/semver@^5.5.0":
  "integrity" "sha1-FGwqKe59O65L8vyydGNuJkyBPEU="
  "resolved" "http://registry.npm.taobao.org/@types/semver/download/@types/semver-5.5.0.tgz"
  "version" "5.5.0"

"abbrev@1":
  "integrity" "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg="
  "resolved" "http://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz"
  "version" "1.1.1"

"accepts@~1.3.5":
  "integrity" "sha1-63d99gEXI6OxTopywIBcjoZ0a9I="
  "resolved" "http://registry.npm.taobao.org/accepts/download/accepts-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "mime-types" "~2.1.18"
    "negotiator" "0.6.1"

"acorn-dynamic-import@^2.0.0":
  "integrity" "sha1-x1K9IQvvZ5UBtsbLf8hPj0cVjMQ="
  "resolved" "http://registry.npm.taobao.org/acorn-dynamic-import/download/acorn-dynamic-import-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "acorn" "^4.0.3"

"acorn-jsx@^3.0.0":
  "integrity" "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s="
  "resolved" "http://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "acorn" "^3.0.4"

"acorn@^3.0.4":
  "integrity" "sha1-ReN/s56No/JbruP/U2niu18iAXo="
  "resolved" "http://registry.npm.taobao.org/acorn/download/acorn-3.3.0.tgz"
  "version" "3.3.0"

"acorn@^4.0.3":
  "integrity" "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="
  "resolved" "http://registry.npm.taobao.org/acorn/download/acorn-4.0.13.tgz"
  "version" "4.0.13"

"acorn@^5.0.0", "acorn@^5.3.0", "acorn@^5.5.0":
  "integrity" "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk="
  "resolved" "http://registry.npm.taobao.org/acorn/download/acorn-5.7.3.tgz"
  "version" "5.7.3"

"ajv-errors@^1.0.0":
  "integrity" "sha1-7PAh+hCP0X37Xms4Py3SM+Mf/Fk="
  "resolved" "http://registry.npm.taobao.org/ajv-errors/download/ajv-errors-1.0.0.tgz"
  "version" "1.0.0"

"ajv-keywords@^2.1.0":
  "integrity" "sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I="
  "resolved" "http://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-2.1.1.tgz"
  "version" "2.1.1"

"ajv-keywords@^3.1.0":
  "integrity" "sha1-6GuBnGAs+IIa1jdBNpjx3sAhhHo="
  "resolved" "http://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.2.0.tgz"
  "version" "3.2.0"

"ajv@^5.0.0", "ajv@^5.2.3", "ajv@^5.3.0", "ajv@>=5.0.0":
  "integrity" "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU="
  "resolved" "http://registry.npm.taobao.org/ajv/download/ajv-5.5.2.tgz"
  "version" "5.5.2"
  dependencies:
    "co" "^4.6.0"
    "fast-deep-equal" "^1.0.0"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.3.0"

"ajv@^6.0.0", "ajv@^6.1.0":
  "integrity" "sha1-z5fNreccY5mpLG1sQXc4EpG3gaE="
  "resolved" "http://registry.npm.taobao.org/ajv/download/ajv-6.5.5.tgz"
  "version" "6.5.5"
  dependencies:
    "fast-deep-equal" "^2.0.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^6.5.5":
  "integrity" "sha512-7q7gtRQDJSyuEHjuVgHoUa2VuemFiCMrfQc9Tc08XTAc4Zj/5U1buQJ0HU6i7fKjXU09SVgSmxa4sLvuvS8Iyg=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.5.5.tgz"
  "version" "6.5.5"
  dependencies:
    "fast-deep-equal" "^2.0.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"align-text@^0.1.1", "align-text@^0.1.3":
  "integrity" "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc="
  "resolved" "http://registry.npm.taobao.org/align-text/download/align-text-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"
    "longest" "^1.0.1"
    "repeat-string" "^1.5.2"

"alphanum-sort@^1.0.1", "alphanum-sort@^1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "http://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-escapes@^3.0.0":
  "integrity" "sha1-9zIHu4EgfXX9bIPxJa8m7qN4yjA="
  "resolved" "http://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-3.1.0.tgz"
  "version" "3.1.0"

"ansi-regex@^0.2.0", "ansi-regex@^0.2.1":
  "integrity" "sha1-DY6UaWej2BQ/k+JOKYUl/BsiNfk="
  "resolved" "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-0.2.1.tgz"
  "version" "0.2.1"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-styles@^1.1.0":
  "integrity" "sha1-6uy/Zs1waIJ2Cy9GkVgrj1XXp94="
  "resolved" "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-1.1.0.tgz"
  "version" "1.1.0"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "http://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"aproba@^1.0.3":
  "version" "1.2.0"

"aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "http://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz"
  "version" "1.2.0"

"are-we-there-yet@~1.1.2":
  "version" "1.1.4"
  dependencies:
    "delegates" "^1.0.0"
    "readable-stream" "^2.0.6"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "http://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "http://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "http://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "http://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "http://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-union@^1.0.1":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "http://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "http://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "http://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"arrify@^1.0.0", "arrify@^1.0.1":
  "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0="
  "resolved" "http://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz"
  "version" "1.0.1"

"asap@~2.0.3":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@^4.0.0":
  "integrity" "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA="
  "resolved" "http://registry.npm.taobao.org/asn1.js/download/asn1.js-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"asn1@~0.2.3":
  "integrity" "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg=="
  "resolved" "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE="
  "resolved" "http://registry.npm.taobao.org/assert/download/assert-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "http://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.0":
  "integrity" "sha1-GdOGodntxufByF04iu28xW0zYC0="
  "resolved" "http://registry.npm.taobao.org/async-each/download/async-each-1.0.1.tgz"
  "version" "1.0.1"

"async-limiter@~1.0.0":
  "integrity" "sha1-ePrtjD0HSrgfIrTphdeehzj3IPg="
  "resolved" "http://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.0.tgz"
  "version" "1.0.0"

"async-validator@~1.8.1":
  "integrity" "sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A="
  "resolved" "https://registry.nlark.com/async-validator/download/async-validator-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "babel-runtime" "6.x"

"async@^1.5.2":
  "integrity" "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="
  "resolved" "http://registry.npm.taobao.org/async/download/async-1.5.2.tgz"
  "version" "1.5.2"

"async@^2.1.2", "async@^2.4.1":
  "integrity" "sha1-skWiPKcZMAROxT+kaqAKPofGphA="
  "resolved" "http://registry.npm.taobao.org/async/download/async-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "lodash" "^4.17.10"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.1":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "http://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^6.3.1":
  "integrity" "sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ="
  "resolved" "http://registry.npm.taobao.org/autoprefixer/download/autoprefixer-6.7.7.tgz"
  "version" "6.7.7"
  dependencies:
    "browserslist" "^1.7.6"
    "caniuse-db" "^1.0.30000634"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "postcss" "^5.2.16"
    "postcss-value-parser" "^3.2.3"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha512-ReZxvNHIOv88FlT7rxcXIIC0fPt4KZqZbOlivyWtXLt8ESx84zd3kMC6iK5jVeS2qt+g7ftS7ye4fi06X5rtRQ=="
  "resolved" "https://registry.npmjs.org/aws4/-/aws4-1.8.0.tgz"
  "version" "1.8.0"

"babel-code-frame@^6.22.0", "babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "http://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-core@^6.22.1", "babel-core@^6.26.0", "babel-core@^6.7.2", "babel-core@6":
  "integrity" "sha1-suLwnjQtDwyI4vAuBneUEl51wgc="
  "resolved" "http://registry.npm.taobao.org/babel-core/download/babel-core-6.26.3.tgz"
  "version" "6.26.3"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-generator" "^6.26.0"
    "babel-helpers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-register" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "convert-source-map" "^1.5.1"
    "debug" "^2.6.9"
    "json5" "^0.5.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.4"
    "path-is-absolute" "^1.0.1"
    "private" "^0.1.8"
    "slash" "^1.0.0"
    "source-map" "^0.5.7"

"babel-eslint@^8.2.3":
  "integrity" "sha1-YnDQxzIFYoBnwPeuFpOp55es79k="
  "resolved" "http://registry.npm.taobao.org/babel-eslint/download/babel-eslint-8.2.6.tgz"
  "version" "8.2.6"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/traverse" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    "babylon" "7.0.0-beta.44"
    "eslint-scope" "3.7.1"
    "eslint-visitor-keys" "^1.0.0"

"babel-generator@^6.26.0":
  "integrity" "sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA="
  "resolved" "http://registry.npm.taobao.org/babel-generator/download/babel-generator-6.26.1.tgz"
  "version" "6.26.1"
  dependencies:
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "detect-indent" "^4.0.0"
    "jsesc" "^1.3.0"
    "lodash" "^4.17.4"
    "source-map" "^0.5.7"
    "trim-right" "^1.0.1"

"babel-helper-bindify-decorators@^6.24.1":
  "integrity" "sha1-FMGeXxQte0fxmlJDHlKxzLxAozA="
  "resolved" "http://registry.npm.taobao.org/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-builder-binary-assignment-operator-visitor@^6.24.1":
  "integrity" "sha1-zORReto1b0IgvK6KAsKzRvmlZmQ="
  "resolved" "http://registry.npm.taobao.org/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-assignable-expression" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-call-delegate@^6.24.1":
  "integrity" "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340="
  "resolved" "http://registry.npm.taobao.org/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-define-map@^6.24.1":
  "integrity" "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8="
  "resolved" "http://registry.npm.taobao.org/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-explode-assignable-expression@^6.24.1":
  "integrity" "sha1-8luCz33BBDPFX3BZLVdGQArCLKo="
  "resolved" "http://registry.npm.taobao.org/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-explode-class@^6.24.1":
  "integrity" "sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes="
  "resolved" "http://registry.npm.taobao.org/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-bindify-decorators" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-function-name@^6.24.1":
  "integrity" "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk="
  "resolved" "http://registry.npm.taobao.org/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-get-function-arity@^6.24.1":
  "integrity" "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0="
  "resolved" "http://registry.npm.taobao.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-hoist-variables@^6.24.1":
  "integrity" "sha1-HssnaJydJVE+rbyZFKc/VAi+enY="
  "resolved" "http://registry.npm.taobao.org/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-optimise-call-expression@^6.24.1":
  "integrity" "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc="
  "resolved" "http://registry.npm.taobao.org/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-regex@^6.24.1":
  "integrity" "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI="
  "resolved" "http://registry.npm.taobao.org/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-remap-async-to-generator@^6.24.1":
  "integrity" "sha1-XsWBgnrXI/7N04HxySg5BnbkVRs="
  "resolved" "http://registry.npm.taobao.org/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-replace-supers@^6.24.1":
  "integrity" "sha1-v22/5Dk40XNpohPKiov3S2qQqxo="
  "resolved" "http://registry.npm.taobao.org/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-vue-jsx-merge-props@^2.0.0":
  "integrity" "sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY="
  "resolved" "https://registry.npm.taobao.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-helpers@^6.24.1":
  "integrity" "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI="
  "resolved" "http://registry.npm.taobao.org/babel-helpers/download/babel-helpers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-loader@^7.1.1":
  "integrity" "sha1-4+4M1zlKpVfgE7AtPkkr/QeqbWg="
  "resolved" "http://registry.npm.taobao.org/babel-loader/download/babel-loader-7.1.5.tgz"
  "version" "7.1.5"
  dependencies:
    "find-cache-dir" "^1.0.0"
    "loader-utils" "^1.0.2"
    "mkdirp" "^0.5.1"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "http://registry.npm.taobao.org/babel-messages/download/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-check-es2015-constants@^6.22.0":
  "integrity" "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-syntax-async-functions@^6.8.0":
  "integrity" "sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-async-generators@^6.5.0":
  "integrity" "sha1-a8lj67FuzLrmuStZbrfzXDQqi5o="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-class-properties@^6.8.0":
  "integrity" "sha1-1+sjt5oxf4VDlixQW4J8fWysJ94="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-decorators@^6.13.0":
  "integrity" "sha1-MSVjtNvePMgGzuPkFszurd0RrAs="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-dynamic-import@^6.18.0":
  "integrity" "sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-syntax-exponentiation-operator@^6.8.0":
  "integrity" "sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-object-rest-spread@^6.5.0", "babel-plugin-syntax-object-rest-spread@^6.8.0":
  "integrity" "sha1-/WU28rzhODb/o6VFjEkDpZe7O/U="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"
  "version" "6.13.0"

"babel-plugin-syntax-trailing-function-commas@^6.22.0":
  "integrity" "sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"
  "version" "6.22.0"

"babel-plugin-transform-async-generator-functions@^6.24.1":
  "integrity" "sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-generators" "^6.5.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-async-to-generator@^6.22.0", "babel-plugin-transform-async-to-generator@^6.24.1":
  "integrity" "sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-remap-async-to-generator" "^6.24.1"
    "babel-plugin-syntax-async-functions" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-class-properties@^6.24.1":
  "integrity" "sha1-anl2PqYdM9NvN7YRqp3vgagbRqw="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-plugin-syntax-class-properties" "^6.8.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-decorators@^6.24.1":
  "integrity" "sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-explode-class" "^6.24.1"
    "babel-plugin-syntax-decorators" "^6.13.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-arrow-functions@^6.22.0", "babel-plugin-transform-es2015-arrow-functions@^6.5.2":
  "integrity" "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoped-functions@^6.22.0":
  "integrity" "sha1-u8UbSflk1wy42OC5ToICRs46YUE="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoping@^6.23.0":
  "integrity" "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-plugin-transform-es2015-classes@^6.23.0":
  "integrity" "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-define-map" "^6.24.1"
    "babel-helper-function-name" "^6.24.1"
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-helper-replace-supers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-computed-properties@^6.22.0":
  "integrity" "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-destructuring@^6.23.0":
  "integrity" "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-duplicate-keys@^6.22.0":
  "integrity" "sha1-c+s9MQypaePvnskcU3QabxV2Qj4="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-for-of@^6.23.0":
  "integrity" "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-function-name@^6.22.0":
  "integrity" "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-literals@^6.22.0":
  "integrity" "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-modules-amd@^6.22.0", "babel-plugin-transform-es2015-modules-amd@^6.24.1":
  "integrity" "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-commonjs@^6.23.0", "babel-plugin-transform-es2015-modules-commonjs@^6.24.1":
  "integrity" "sha1-WKeThjqefKhwvcWogRF/+sJ9tvM="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-es2015-modules-systemjs@^6.23.0":
  "integrity" "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-umd@^6.23.0":
  "integrity" "sha1-rJl+YoXNGO1hdq22B9YCNErThGg="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-object-super@^6.22.0":
  "integrity" "sha1-JM72muIcuDp/hgPa0CH1cusnj40="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-replace-supers" "^6.24.1"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-parameters@^6.23.0":
  "integrity" "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-call-delegate" "^6.24.1"
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-shorthand-properties@^6.22.0", "babel-plugin-transform-es2015-shorthand-properties@^6.5.0":
  "integrity" "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-spread@^6.22.0", "babel-plugin-transform-es2015-spread@^6.6.5":
  "integrity" "sha1-1taKmfia7cRTbIGlQujdnxdG+NE="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-sticky-regex@^6.22.0":
  "integrity" "sha1-AMHNsaynERLN8M9hJsLta0V8zbw="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-template-literals@^6.22.0", "babel-plugin-transform-es2015-template-literals@^6.6.5":
  "integrity" "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-typeof-symbol@^6.23.0":
  "integrity" "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-unicode-regex@^6.22.0":
  "integrity" "sha1-04sS9C6nMj9yk4fxinxa4frrNek="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "regexpu-core" "^2.0.0"

"babel-plugin-transform-exponentiation-operator@^6.22.0", "babel-plugin-transform-exponentiation-operator@^6.24.1":
  "integrity" "sha1-KrDJx/MJj6SJB3cruBP+QejeOg4="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-builder-binary-assignment-operator-visitor" "^6.24.1"
    "babel-plugin-syntax-exponentiation-operator" "^6.8.0"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-object-rest-spread@^6.22.0", "babel-plugin-transform-object-rest-spread@^6.6.5":
  "integrity" "sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-plugin-syntax-object-rest-spread" "^6.8.0"
    "babel-runtime" "^6.26.0"

"babel-plugin-transform-regenerator@^6.22.0":
  "integrity" "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "regenerator-transform" "^0.10.0"

"babel-plugin-transform-runtime@^6.22.0":
  "integrity" "sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-runtime/download/babel-plugin-transform-runtime-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-strict-mode@^6.24.1", "babel-plugin-transform-strict-mode@^6.6.5":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "http://registry.npm.taobao.org/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-preset-env@^1.3.2":
  "integrity" "sha1-3qefpOvriDzTXasH4mDBycBN93o="
  "resolved" "http://registry.npm.taobao.org/babel-preset-env/download/babel-preset-env-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "babel-plugin-check-es2015-constants" "^6.22.0"
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-to-generator" "^6.22.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoped-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoping" "^6.23.0"
    "babel-plugin-transform-es2015-classes" "^6.23.0"
    "babel-plugin-transform-es2015-computed-properties" "^6.22.0"
    "babel-plugin-transform-es2015-destructuring" "^6.23.0"
    "babel-plugin-transform-es2015-duplicate-keys" "^6.22.0"
    "babel-plugin-transform-es2015-for-of" "^6.23.0"
    "babel-plugin-transform-es2015-function-name" "^6.22.0"
    "babel-plugin-transform-es2015-literals" "^6.22.0"
    "babel-plugin-transform-es2015-modules-amd" "^6.22.0"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-systemjs" "^6.23.0"
    "babel-plugin-transform-es2015-modules-umd" "^6.23.0"
    "babel-plugin-transform-es2015-object-super" "^6.22.0"
    "babel-plugin-transform-es2015-parameters" "^6.23.0"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.22.0"
    "babel-plugin-transform-es2015-spread" "^6.22.0"
    "babel-plugin-transform-es2015-sticky-regex" "^6.22.0"
    "babel-plugin-transform-es2015-template-literals" "^6.22.0"
    "babel-plugin-transform-es2015-typeof-symbol" "^6.23.0"
    "babel-plugin-transform-es2015-unicode-regex" "^6.22.0"
    "babel-plugin-transform-exponentiation-operator" "^6.22.0"
    "babel-plugin-transform-regenerator" "^6.22.0"
    "browserslist" "^3.2.6"
    "invariant" "^2.2.2"
    "semver" "^5.3.0"

"babel-preset-stage-2@^6.22.0":
  "integrity" "sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE="
  "resolved" "http://registry.npm.taobao.org/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-dynamic-import" "^6.18.0"
    "babel-plugin-transform-class-properties" "^6.24.1"
    "babel-plugin-transform-decorators" "^6.24.1"
    "babel-preset-stage-3" "^6.24.1"

"babel-preset-stage-3@^6.24.1":
  "integrity" "sha1-g2raCp56f6N8sTj7kyb4eTSkg5U="
  "resolved" "http://registry.npm.taobao.org/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-syntax-trailing-function-commas" "^6.22.0"
    "babel-plugin-transform-async-generator-functions" "^6.24.1"
    "babel-plugin-transform-async-to-generator" "^6.24.1"
    "babel-plugin-transform-exponentiation-operator" "^6.24.1"
    "babel-plugin-transform-object-rest-spread" "^6.22.0"

"babel-register@^6.22.0", "babel-register@^6.26.0":
  "integrity" "sha1-btAhFz4vy0htestFxgCahW9kcHE="
  "resolved" "http://registry.npm.taobao.org/babel-register/download/babel-register-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-core" "^6.26.0"
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "home-or-tmp" "^2.0.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "source-map-support" "^0.4.15"

"babel-runtime@^6.18.0", "babel-runtime@^6.22.0", "babel-runtime@^6.26.0", "babel-runtime@6.x":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "http://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "http://registry.npm.taobao.org/babel-template/download/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.24.1", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "http://registry.npm.taobao.org/babel-traverse/download/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.19.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "http://registry.npm.taobao.org/babel-types/download/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babelon@^1.0.5":
  "integrity" "sha1-jIiAWFrhwpDr7GNsVv72sAkVHXY="
  "resolved" "http://registry.npm.taobao.org/babelon/download/babelon-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "babel-core" "^6.7.2"
    "babel-plugin-syntax-object-rest-spread" "^6.5.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.5.2"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.5.0"
    "babel-plugin-transform-es2015-spread" "^6.6.5"
    "babel-plugin-transform-es2015-template-literals" "^6.6.5"
    "babel-plugin-transform-object-rest-spread" "^6.6.5"
    "babel-plugin-transform-strict-mode" "^6.6.5"

"babylon@^6.18.0":
  "integrity" "sha1-ry87iPpvXB5MY00aD46sT1WzleM="
  "resolved" "http://registry.npm.taobao.org/babylon/download/babylon-6.18.0.tgz"
  "version" "6.18.0"

"babylon@7.0.0-beta.44":
  "integrity" "sha1-iRWeFebjDFCW4i1zjYwK+KDoyh0="
  "resolved" "http://registry.npm.taobao.org/babylon/download/babylon-7.0.0-beta.44.tgz"
  "version" "7.0.0-beta.44"

"balanced-match@^0.4.2":
  "integrity" "sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg="
  "resolved" "http://registry.npm.taobao.org/balanced-match/download/balanced-match-0.4.2.tgz"
  "version" "0.4.2"

"balanced-match@^1.0.0":
  "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
  "resolved" "http://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz"
  "version" "1.0.0"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "http://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.0.2":
  "integrity" "sha1-yrHmEY8FEJXli1KBrqjBzSK/wOM="
  "resolved" "http://registry.npm.taobao.org/base64-js/download/base64-js-1.3.0.tgz"
  "version" "1.3.0"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bfj-node4@^5.2.0":
  "integrity" "sha1-4j2LJwV/HQIU/FYRQq2duZjyaDA="
  "resolved" "http://registry.npm.taobao.org/bfj-node4/download/bfj-node4-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "bluebird" "^3.5.1"
    "check-types" "^7.3.0"
    "tryer" "^1.0.0"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "http://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz"
  "version" "3.2.0"

"binary-extensions@^1.0.0":
  "integrity" "sha1-wteA9T1Fu6gxeokC1M7q86Y4WxQ="
  "resolved" "http://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.12.0.tgz"
  "version" "1.12.0"

"bluebird@^3.1.1", "bluebird@^3.5.1":
  "integrity" "sha1-G+CQjgVKdRdUVJwnBInBUF1KsVo="
  "resolved" "http://registry.npm.taobao.org/bluebird/download/bluebird-3.5.2.tgz"
  "version" "3.5.2"

"bn.js@^4.0.0", "bn.js@^4.1.0", "bn.js@^4.1.1", "bn.js@^4.4.0":
  "integrity" "sha1-LN4J617jQfSEdGuwMJsyU7GxRC8="
  "resolved" "http://registry.npm.taobao.org/bn.js/download/bn.js-4.11.8.tgz"
  "version" "4.11.8"

"body-parser@1.18.3":
  "integrity" "sha1-WykhmP/dVTs6DyDe0FkrlWlVyLQ="
  "resolved" "http://registry.npm.taobao.org/body-parser/download/body-parser-1.18.3.tgz"
  "version" "1.18.3"
  dependencies:
    "bytes" "3.0.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "~1.6.3"
    "iconv-lite" "0.4.23"
    "on-finished" "~2.3.0"
    "qs" "6.5.2"
    "raw-body" "2.3.3"
    "type-is" "~1.6.16"

"boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "http://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "http://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.0", "braces@^2.3.1":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "http://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"brorand@^1.0.1":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "http://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "http://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "http://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "http://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0":
  "integrity" "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ="
  "resolved" "http://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.1.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg="
  "resolved" "http://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.1"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.2"
    "elliptic" "^6.0.0"
    "inherits" "^2.0.1"
    "parse-asn1" "^5.0.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "http://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^1.3.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "http://registry.npm.taobao.org/browserslist/download/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.5.2":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "http://registry.npm.taobao.org/browserslist/download/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^1.7.6":
  "integrity" "sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk="
  "resolved" "http://registry.npm.taobao.org/browserslist/download/browserslist-1.7.7.tgz"
  "version" "1.7.7"
  dependencies:
    "caniuse-db" "^1.0.30000639"
    "electron-to-chromium" "^1.2.7"

"browserslist@^3.2.6":
  "integrity" "sha1-sABTYdZHHw9ZUnl6dvyYXx+Xj8Y="
  "resolved" "http://registry.npm.taobao.org/browserslist/download/browserslist-3.2.8.tgz"
  "version" "3.2.8"
  dependencies:
    "caniuse-lite" "^1.0.30000844"
    "electron-to-chromium" "^1.3.47"

"buffer-from@^1.0.0":
  "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
  "resolved" "http://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz"
  "version" "1.1.1"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "http://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg="
  "resolved" "http://registry.npm.taobao.org/buffer/download/buffer-4.9.1.tgz"
  "version" "4.9.1"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-modules@^1.0.0":
  "integrity" "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="
  "resolved" "http://registry.npm.taobao.org/builtin-modules/download/builtin-modules-1.1.1.tgz"
  "version" "1.1.1"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "http://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "http://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz"
  "version" "3.0.0"

"cacache@^10.0.4":
  "integrity" "sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA="
  "resolved" "http://registry.npm.taobao.org/cacache/download/cacache-10.0.4.tgz"
  "version" "10.0.4"
  dependencies:
    "bluebird" "^3.5.1"
    "chownr" "^1.0.1"
    "glob" "^7.1.2"
    "graceful-fs" "^4.1.11"
    "lru-cache" "^4.1.1"
    "mississippi" "^2.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.2"
    "ssri" "^5.2.4"
    "unique-filename" "^1.1.0"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "http://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"caller-path@^0.1.0":
  "integrity" "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8="
  "resolved" "http://registry.npm.taobao.org/caller-path/download/caller-path-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "callsites" "^0.2.0"

"callsites@^0.2.0":
  "integrity" "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo="
  "resolved" "http://registry.npm.taobao.org/callsites/download/callsites-0.2.0.tgz"
  "version" "0.2.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "http://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^1.0.2":
  "integrity" "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk="
  "resolved" "http://registry.npm.taobao.org/camelcase/download/camelcase-1.2.1.tgz"
  "version" "1.2.1"

"camelcase@^4.1.0":
  "integrity" "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0="
  "resolved" "http://registry.npm.taobao.org/camelcase/download/camelcase-4.1.0.tgz"
  "version" "4.1.0"

"caniuse-api@^1.5.2":
  "integrity" "sha1-tTTnxzTE+B7F++isoq0kNUuWLGw="
  "resolved" "http://registry.npm.taobao.org/caniuse-api/download/caniuse-api-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "browserslist" "^1.3.6"
    "caniuse-db" "^1.0.30000529"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-db@^1.0.30000529", "caniuse-db@^1.0.30000634", "caniuse-db@^1.0.30000639":
  "integrity" "sha1-BL7WfjRDOfZqxPtQLSBHNGlZhJs="
  "resolved" "http://registry.npm.taobao.org/caniuse-db/download/caniuse-db-1.0.30000904.tgz"
  "version" "1.0.30000904"

"caniuse-lite@^1.0.30000844":
  "integrity" "sha1-REXQDahZoOCubbsodsVF8zJPbHQ="
  "resolved" "http://registry.npm.taobao.org/caniuse-lite/download/caniuse-lite-1.0.30000904.tgz"
  "version" "1.0.30000904"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"center-align@^0.1.1":
  "integrity" "sha1-qg0yYptu6XIgBBHL1EYckHvCt60="
  "resolved" "http://registry.npm.taobao.org/center-align/download/center-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.3"
    "lazy-cache" "^1.0.3"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "http://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.1.0", "chalk@^2.3.0", "chalk@^2.3.1", "chalk@^2.4.0", "chalk@^2.4.1":
  "integrity" "sha1-GMSasWoDe26wFSzIPjRxM4IVtm4="
  "resolved" "http://registry.npm.taobao.org/chalk/download/chalk-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@~0.5.1":
  "integrity" "sha1-Zjs6ZItotV0EaQ1JFnqoN4WPIXQ="
  "resolved" "http://registry.npm.taobao.org/chalk/download/chalk-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "ansi-styles" "^1.1.0"
    "escape-string-regexp" "^1.0.0"
    "has-ansi" "^0.1.0"
    "strip-ansi" "^0.3.0"
    "supports-color" "^0.2.0"

"chardet@^0.4.0":
  "integrity" "sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I="
  "resolved" "http://registry.npm.taobao.org/chardet/download/chardet-0.4.2.tgz"
  "version" "0.4.2"

"check-types@^7.3.0":
  "integrity" "sha1-A3jsG5YW7HH3dJMaPGUW+tjBUvQ="
  "resolved" "http://registry.npm.taobao.org/check-types/download/check-types-7.4.0.tgz"
  "version" "7.4.0"

"chokidar@^2.0.2":
  "integrity" "sha1-NW/04rDo5D4yLRijckYLvPOszSY="
  "resolved" "http://registry.npm.taobao.org/chokidar/download/chokidar-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.0"
    "braces" "^2.3.0"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.1"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "lodash.debounce" "^4.0.8"
    "normalize-path" "^2.1.1"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.0.0"
    "upath" "^1.0.5"
  optionalDependencies:
    "fsevents" "^1.2.2"

"chownr@^1.0.1":
  "integrity" "sha1-VHJri4//TfBTxCGH6AH7RBLfFJQ="
  "resolved" "http://registry.npm.taobao.org/chownr/download/chownr-1.1.1.tgz"
  "version" "1.1.1"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "http://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"circular-json@^0.3.1":
  "integrity" "sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY="
  "resolved" "http://registry.npm.taobao.org/circular-json/download/circular-json-0.3.3.tgz"
  "version" "0.3.3"

"clap@^1.0.9":
  "integrity" "sha1-TzZ0WzIAhJJVf0ZBLWbVDLmbzlE="
  "resolved" "http://registry.npm.taobao.org/clap/download/clap-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "chalk" "^1.1.3"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "http://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha1-LUEe92uFabbQyEBo2r6FsKpeXBc="
  "resolved" "http://registry.npm.taobao.org/clean-css/download/clean-css-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "source-map" "~0.6.0"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "http://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-spinners@^1.1.0":
  "integrity" "sha1-ACwZkJEtDVlYDJO9NsBW3pnkJZo="
  "resolved" "http://registry.npm.taobao.org/cli-spinners/download/cli-spinners-1.3.1.tgz"
  "version" "1.3.1"

"cli-width@^2.0.0":
  "integrity" "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk="
  "resolved" "http://registry.npm.taobao.org/cli-width/download/cli-width-2.2.0.tgz"
  "version" "2.2.0"

"cliui@^2.1.0":
  "integrity" "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE="
  "resolved" "http://registry.npm.taobao.org/cliui/download/cliui-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "center-align" "^0.1.1"
    "right-align" "^0.1.1"
    "wordwrap" "0.0.2"

"cliui@^3.2.0":
  "integrity" "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0="
  "resolved" "http://registry.npm.taobao.org/cliui/download/cliui-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wrap-ansi" "^2.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "http://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"clone@^2.1.2":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "http://registry.npm.taobao.org/co/download/co-4.6.0.tgz"
  "version" "4.6.0"

"coa@~1.0.1":
  "integrity" "sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0="
  "resolved" "http://registry.npm.taobao.org/coa/download/coa-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "q" "^1.1.2"

"coalescy@1.0.0":
  "integrity" "sha1-SwZYRrg2NhrabEtKSr9LwcrDG/E="
  "resolved" "http://registry.npm.taobao.org/coalescy/download/coalescy-1.0.0.tgz"
  "version" "1.0.0"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "http://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "http://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.3.0", "color-convert@^1.9.0":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "http://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "http://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^0.3.0":
  "integrity" "sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE="
  "resolved" "http://registry.npm.taobao.org/color-string/download/color-string-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "color-name" "^1.0.0"

"color@^0.11.0":
  "integrity" "sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q="
  "resolved" "http://registry.npm.taobao.org/color/download/color-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "clone" "^1.0.2"
    "color-convert" "^1.3.0"
    "color-string" "^0.3.0"

"colormin@^1.0.5":
  "integrity" "sha1-6i90IKcrlogaOKrlnsEkpvcpgTM="
  "resolved" "http://registry.npm.taobao.org/colormin/download/colormin-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "color" "^0.11.0"
    "css-color-names" "0.0.4"
    "has" "^1.0.1"

"colors@~1.1.2":
  "integrity" "sha1-FopHAXVran9RoSzgyXv6KMCE7WM="
  "resolved" "http://registry.npm.taobao.org/colors/download/colors-1.1.2.tgz"
  "version" "1.1.2"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.13.0", "commander@~2.17.1", "commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "http://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@^2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "http://registry.npm.taobao.org/commander/download/commander-2.19.0.tgz"
  "version" "2.19.0"

"commander@~2.13.0":
  "integrity" "sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w="
  "resolved" "http://registry.npm.taobao.org/commander/download/commander-2.13.0.tgz"
  "version" "2.13.0"

"commander@~2.6.0":
  "integrity" "sha1-nfflL7Kgyw+4kFjugMMQQiXzfh0="
  "resolved" "http://registry.npm.taobao.org/commander/download/commander-2.6.0.tgz"
  "version" "2.6.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "http://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY="
  "resolved" "http://registry.npm.taobao.org/component-emitter/download/component-emitter-1.2.1.tgz"
  "version" "1.2.1"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "http://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0", "concat-stream@^1.6.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "http://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"config-chain@~1.1.5":
  "integrity" "sha1-D96NCRIA616AjK8l/mGMAvSOTvo="
  "resolved" "http://registry.npm.taobao.org/config-chain/download/config-chain-1.1.12.tgz"
  "version" "1.1.12"
  dependencies:
    "ini" "^1.3.4"
    "proto-list" "~1.2.1"

"connect-history-api-fallback@^1.3.0":
  "integrity" "sha1-sGhzk0vF40T+9hGhlqb6rgruAVo="
  "resolved" "http://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.5.0.tgz"
  "version" "1.5.0"

"console-browserify@^1.1.0":
  "integrity" "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA="
  "resolved" "http://registry.npm.taobao.org/console-browserify/download/console-browserify-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "date-now" "^0.1.4"

"console-control-strings@^1.0.0", "console-control-strings@~1.1.0":
  "version" "1.1.0"

"consolidate@^0.14.0":
  "integrity" "sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM="
  "resolved" "http://registry.npm.taobao.org/consolidate/download/consolidate-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "http://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"contains-path@^0.1.0":
  "integrity" "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo="
  "resolved" "http://registry.npm.taobao.org/contains-path/download/contains-path-0.1.0.tgz"
  "version" "0.1.0"

"content-disposition@0.5.2":
  "integrity" "sha1-DPaLud318r55YcOoUXjLhdunjLQ="
  "resolved" "http://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.2.tgz"
  "version" "0.5.2"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "http://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.5.1":
  "integrity" "sha1-UbU3qMQ+DwTewZk7/83VBOdYrCA="
  "resolved" "http://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "http://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.3.1":
  "integrity" "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="
  "resolved" "http://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz"
  "version" "0.3.1"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "http://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "http://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-webpack-plugin@^4.5.1":
  "integrity" "sha1-5/QN2KaEd9QF3Rt6hUquMksVi64="
  "resolved" "http://registry.npm.taobao.org/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.0"
    "loader-utils" "^1.1.0"
    "minimatch" "^3.0.4"
    "p-limit" "^1.0.0"
    "serialize-javascript" "^1.4.0"

"core-js@^2.4.0", "core-js@^2.5.0":
  "integrity" "sha1-+XJgj/DOrWi4QaFqky0LGDeRgU4="
  "resolved" "http://registry.npm.taobao.org/core-js/download/core-js-2.5.7.tgz"
  "version" "2.5.7"

"core-util-is@~1.0.0", "core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "http://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^2.1.0", "cosmiconfig@^2.1.1":
  "integrity" "sha1-YXPOvVb6wELB9DkO33r2wHx8uJI="
  "resolved" "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "is-directory" "^0.3.1"
    "js-yaml" "^3.4.3"
    "minimist" "^1.2.0"
    "object-assign" "^4.1.0"
    "os-homedir" "^1.0.1"
    "parse-json" "^2.2.0"
    "require-from-string" "^1.1.0"

"cosmiconfig@^4.0.0":
  "integrity" "sha1-dgORVJWAu9LfHlYrwXexPCkJctw="
  "resolved" "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-directory" "^0.3.1"
    "js-yaml" "^3.9.0"
    "parse-json" "^4.0.0"
    "require-from-string" "^2.0.1"

"create-ecdh@^4.0.0":
  "integrity" "sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8="
  "resolved" "http://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.0.0"

"create-hash@^1.1.0", "create-hash@^1.1.2":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "http://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.2", "create-hmac@^1.1.4":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "http://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-spawn@^5.0.1", "cross-spawn@^5.1.0":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "http://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "http://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-loader@*", "css-loader@^0.28.11":
  "integrity" "sha1-w/mGSnAL4nEbtaJGKyOJsaOS2rc="
  "resolved" "http://registry.npm.taobao.org/css-loader/download/css-loader-0.28.11.tgz"
  "version" "0.28.11"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "css-selector-tokenizer" "^0.7.0"
    "cssnano" "^3.10.0"
    "icss-utils" "^2.1.0"
    "loader-utils" "^1.0.2"
    "lodash.camelcase" "^4.3.0"
    "object-assign" "^4.1.1"
    "postcss" "^5.0.6"
    "postcss-modules-extract-imports" "^1.2.0"
    "postcss-modules-local-by-default" "^1.2.0"
    "postcss-modules-scope" "^1.1.0"
    "postcss-modules-values" "^1.3.0"
    "postcss-value-parser" "^3.3.0"
    "source-list-map" "^2.0.0"

"css-select@^1.1.0":
  "integrity" "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg="
  "resolved" "http://registry.npm.taobao.org/css-select/download/css-select-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "boolbase" "~1.0.0"
    "css-what" "2.1"
    "domutils" "1.5.1"
    "nth-check" "~1.0.1"

"css-selector-tokenizer@^0.7.0":
  "integrity" "sha1-oXcnGovKUBkXL0+JH8bu2cv2jV0="
  "resolved" "http://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "cssesc" "^0.1.0"
    "fastparse" "^1.1.1"
    "regexpu-core" "^1.0.0"

"css-what@2.1":
  "integrity" "sha1-wIdtnQSAkn19SSDc1yrzWVZJVU0="
  "resolved" "http://registry.npm.taobao.org/css-what/download/css-what-2.1.2.tgz"
  "version" "2.1.2"

"css@~2.2.0":
  "integrity" "sha1-xkZ1XHOXHyu6amAeLPL9cbEpiSk="
  "resolved" "http://registry.npm.taobao.org/css/download/css-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "inherits" "^2.0.3"
    "source-map" "^0.6.1"
    "source-map-resolve" "^0.5.2"
    "urix" "^0.1.0"

"cssesc@^0.1.0":
  "integrity" "sha1-yBSQPkViM3GgR3tAEJqq++6t27Q="
  "resolved" "http://registry.npm.taobao.org/cssesc/download/cssesc-0.1.0.tgz"
  "version" "0.1.0"

"cssnano@^3.10.0", "cssnano@^3.4.0":
  "integrity" "sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg="
  "resolved" "http://registry.npm.taobao.org/cssnano/download/cssnano-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "autoprefixer" "^6.3.1"
    "decamelize" "^1.1.2"
    "defined" "^1.0.0"
    "has" "^1.0.1"
    "object-assign" "^4.0.1"
    "postcss" "^5.0.14"
    "postcss-calc" "^5.2.0"
    "postcss-colormin" "^2.1.8"
    "postcss-convert-values" "^2.3.4"
    "postcss-discard-comments" "^2.0.4"
    "postcss-discard-duplicates" "^2.0.1"
    "postcss-discard-empty" "^2.0.1"
    "postcss-discard-overridden" "^0.1.1"
    "postcss-discard-unused" "^2.2.1"
    "postcss-filter-plugins" "^2.0.0"
    "postcss-merge-idents" "^2.1.5"
    "postcss-merge-longhand" "^2.0.1"
    "postcss-merge-rules" "^2.0.3"
    "postcss-minify-font-values" "^1.0.2"
    "postcss-minify-gradients" "^1.0.1"
    "postcss-minify-params" "^1.0.4"
    "postcss-minify-selectors" "^2.0.4"
    "postcss-normalize-charset" "^1.1.0"
    "postcss-normalize-url" "^3.0.7"
    "postcss-ordered-values" "^2.1.0"
    "postcss-reduce-idents" "^2.2.2"
    "postcss-reduce-initial" "^1.0.0"
    "postcss-reduce-transforms" "^1.0.3"
    "postcss-svgo" "^2.1.1"
    "postcss-unique-selectors" "^2.0.2"
    "postcss-value-parser" "^3.2.3"
    "postcss-zindex" "^2.0.1"

"csso@~2.3.1":
  "integrity" "sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U="
  "resolved" "http://registry.npm.taobao.org/csso/download/csso-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "clap" "^1.0.9"
    "source-map" "^0.5.3"

"cyclist@~0.2.2":
  "integrity" "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA="
  "resolved" "http://registry.npm.taobao.org/cyclist/download/cyclist-0.2.2.tgz"
  "version" "0.2.2"

"d@1":
  "integrity" "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8="
  "resolved" "http://registry.npm.taobao.org/d/download/d-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es5-ext" "^0.10.9"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"date-now@^0.1.4":
  "integrity" "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs="
  "resolved" "http://registry.npm.taobao.org/date-now/download/date-now-0.1.4.tgz"
  "version" "0.1.4"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "http://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.1.2":
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.2.0", "debug@^2.3.3", "debug@^2.6.8", "debug@^2.6.9", "debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.0":
  "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
  "resolved" "http://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz"
  "version" "3.2.6"
  dependencies:
    "ms" "^2.1.1"

"debug@=3.1.0":
  "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
  "resolved" "http://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.0.0", "decamelize@^1.1.1", "decamelize@^1.1.2":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "http://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "http://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="
  "resolved" "http://registry.npm.taobao.org/deep-equal/download/deep-equal-1.0.1.tgz"
  "version" "1.0.1"

"deep-extend@^0.5.1":
  "version" "0.5.1"

"deep-is@~0.1.3":
  "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
  "resolved" "http://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz"
  "version" "0.1.3"

"deepmerge@^1.2.0":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "http://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "http://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "http://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "http://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"defined@^1.0.0":
  "integrity" "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM="
  "resolved" "http://registry.npm.taobao.org/defined/download/defined-1.0.0.tgz"
  "version" "1.0.0"

"del@^2.0.2":
  "integrity" "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag="
  "resolved" "http://registry.npm.taobao.org/del/download/del-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "globby" "^5.0.0"
    "is-path-cwd" "^1.0.0"
    "is-path-in-cwd" "^1.0.0"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"
    "rimraf" "^2.2.8"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegates@^1.0.0":
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "http://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"des.js@^1.0.0":
  "integrity" "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw="
  "resolved" "http://registry.npm.taobao.org/des.js/download/des.js-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "http://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-indent@^4.0.0":
  "integrity" "sha1-920GQ1LN9Docts5hnE7jqUdd4gg="
  "resolved" "http://registry.npm.taobao.org/detect-indent/download/detect-indent-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "repeating" "^2.0.0"

"detect-libc@^1.0.2":
  "version" "1.0.3"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "http://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0":
  "integrity" "sha1-CyBdK2rvmCOMooZZioIE0p0KADQ="
  "resolved" "http://registry.npm.taobao.org/dir-glob/download/dir-glob-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "arrify" "^1.0.1"
    "path-type" "^3.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "http://registry.npm.taobao.org/doctrine/download/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@1.5.0":
  "integrity" "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo="
  "resolved" "http://registry.npm.taobao.org/doctrine/download/doctrine-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "esutils" "^2.0.2"
    "isarray" "^1.0.0"

"dom-converter@~0.2":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "http://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@0":
  "integrity" "sha1-BzxpdUbOB4DOI75KKOKT5AvDDII="
  "resolved" "http://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "domelementtype" "~1.1.1"
    "entities" "~1.1.1"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "http://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^1.3.0", "domelementtype@1":
  "integrity" "sha1-sXrtguirWeUt2cGbF1bg/BhyBMI="
  "resolved" "http://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.0.tgz"
  "version" "1.3.0"

"domelementtype@~1.1.1":
  "integrity" "sha1-vSh3PiZCiBrsUVRJJCmcXNgiGFs="
  "resolved" "http://registry.npm.taobao.org/domelementtype/download/domelementtype-1.1.3.tgz"
  "version" "1.1.3"

"domhandler@^2.3.0":
  "integrity" "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="
  "resolved" "http://registry.npm.taobao.org/domhandler/download/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domhandler@2.1":
  "integrity" "sha1-0mRvXlf2w7qxHPbLBdPArPdBJZQ="
  "resolved" "http://registry.npm.taobao.org/domhandler/download/domhandler-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "domelementtype" "1"

"domutils@^1.5.1":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "http://registry.npm.taobao.org/domutils/download/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@1.1":
  "integrity" "sha1-vdw94Jm5ou+sxRxiPyj0FuzFdIU="
  "resolved" "http://registry.npm.taobao.org/domutils/download/domutils-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "domelementtype" "1"

"domutils@1.5.1":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "http://registry.npm.taobao.org/domutils/download/domutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"duplexer@^0.1.1":
  "integrity" "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E="
  "resolved" "http://registry.npm.taobao.org/duplexer/download/duplexer-0.1.1.tgz"
  "version" "0.1.1"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha1-saeinEq/1jlYXvrszoDWZrHjQSU="
  "resolved" "http://registry.npm.taobao.org/duplexify/download/duplexify-3.6.1.tgz"
  "version" "3.6.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"editorconfig@^0.15.0":
  "integrity" "sha1-BHvpg6u5qzwu7+UZnLK3xWifBwI="
  "resolved" "http://registry.npm.taobao.org/editorconfig/download/editorconfig-0.15.2.tgz"
  "version" "0.15.2"
  dependencies:
    "@types/node" "^10.11.7"
    "@types/semver" "^5.5.0"
    "commander" "^2.19.0"
    "lru-cache" "^4.1.3"
    "semver" "^5.6.0"
    "sigmund" "^1.0.1"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "http://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.5.7":
  "integrity" "sha1-SY7A1JVlWrxvI81hho2SZGQHGqA="
  "resolved" "http://registry.npm.taobao.org/ejs/download/ejs-2.6.1.tgz"
  "version" "2.6.1"

"electron-to-chromium@^1.2.7", "electron-to-chromium@^1.3.47":
  "integrity" "sha1-dFhOsJcrtnd4EcXWjZiMci9eZmY="
  "resolved" "http://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.83.tgz"
  "version" "1.3.83"

"element-ui@^2.15.3":
  "integrity" "sha1-VRCKuCo7zGRuewVwhxxIupYwBlI="
  "resolved" "https://registry.nlark.com/element-ui/download/element-ui-2.15.3.tgz?cache=0&sync_timestamp=1624954513817&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felement-ui%2Fdownload%2Felement-ui-2.15.3.tgz"
  "version" "2.15.3"
  dependencies:
    "async-validator" "~1.8.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.0"
    "deepmerge" "^1.2.0"
    "normalize-wheel" "^1.0.1"
    "resize-observer-polyfill" "^1.5.0"
    "throttle-debounce" "^1.0.1"

"elliptic@^6.0.0":
  "integrity" "sha1-wtC3d2kRuGcixjLDwGxg8vgZk5o="
  "resolved" "http://registry.npm.taobao.org/elliptic/download/elliptic-6.4.1.tgz"
  "version" "6.4.1"
  dependencies:
    "bn.js" "^4.4.0"
    "brorand" "^1.0.1"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "http://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "http://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha1-7SljTRm6ukY7bOa4CjchPqtx7EM="
  "resolved" "http://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^3.4.0":
  "integrity" "sha1-BCHjOf1xQZs9oT0Smzl5BAIwR24="
  "resolved" "http://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.4.0"
    "object-assign" "^4.0.1"
    "tapable" "^0.2.7"

"entities@^1.1.1", "entities@~1.1.1":
  "integrity" "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="
  "resolved" "http://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz"
  "version" "1.1.2"

"errno@^0.1.1", "errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg="
  "resolved" "http://registry.npm.taobao.org/errno/download/errno-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0", "error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "http://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.0":
  "integrity" "sha1-Sujbqiv5CotFBwe5FJ3KvKE1Ug0="
  "resolved" "http://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "stackframe" "^1.0.4"

"es-abstract@^1.5.1":
  "integrity" "sha1-nbvdJ8aFbwABQhyhh4LXhr+KYWU="
  "resolved" "http://registry.npm.taobao.org/es-abstract/download/es-abstract-1.12.0.tgz"
  "version" "1.12.0"
  dependencies:
    "es-to-primitive" "^1.1.1"
    "function-bind" "^1.1.1"
    "has" "^1.0.1"
    "is-callable" "^1.1.3"
    "is-regex" "^1.0.4"

"es-to-primitive@^1.1.1":
  "integrity" "sha1-7fckeAM0VujdqO8J4ArZZQcH83c="
  "resolved" "http://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"es5-ext@^0.10.14", "es5-ext@^0.10.35", "es5-ext@^0.10.9", "es5-ext@~0.10.14":
  "integrity" "sha1-79mfZ8Wn7Hibqj2qf3mHA4j39XI="
  "resolved" "http://registry.npm.taobao.org/es5-ext/download/es5-ext-0.10.46.tgz"
  "version" "0.10.46"
  dependencies:
    "es6-iterator" "~2.0.3"
    "es6-symbol" "~3.1.1"
    "next-tick" "1"

"es6-iterator@^2.0.1", "es6-iterator@~2.0.1", "es6-iterator@~2.0.3":
  "integrity" "sha1-p96IkUGgWpSwhUQDstCg+/qY87c="
  "resolved" "http://registry.npm.taobao.org/es6-iterator/download/es6-iterator-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.35"
    "es6-symbol" "^3.1.1"

"es6-map@^0.1.3":
  "integrity" "sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA="
  "resolved" "http://registry.npm.taobao.org/es6-map/download/es6-map-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-set" "~0.1.5"
    "es6-symbol" "~3.1.1"
    "event-emitter" "~0.3.5"

"es6-set@~0.1.5":
  "integrity" "sha1-0rPsXU2ADO2BjbU40ol02wpzzLE="
  "resolved" "http://registry.npm.taobao.org/es6-set/download/es6-set-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"
    "es6-iterator" "~2.0.1"
    "es6-symbol" "3.1.1"
    "event-emitter" "~0.3.5"

"es6-symbol@^3.1.1", "es6-symbol@~3.1.1", "es6-symbol@3.1.1":
  "integrity" "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc="
  "resolved" "http://registry.npm.taobao.org/es6-symbol/download/es6-symbol-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"es6-weak-map@^2.0.1":
  "integrity" "sha1-XjqzIlH/0VOKH45f+hNXdy+S2W8="
  "resolved" "http://registry.npm.taobao.org/es6-weak-map/download/es6-weak-map-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.14"
    "es6-iterator" "^2.0.1"
    "es6-symbol" "^3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "http://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.0", "escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "http://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escope@^3.6.0":
  "integrity" "sha1-4Bl16BJ4GhY6ba392AOY3GTIicM="
  "resolved" "http://registry.npm.taobao.org/escope/download/escope-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "es6-map" "^0.1.3"
    "es6-weak-map" "^2.0.1"
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-config-standard@^11.0.0":
  "integrity" "sha1-h+4NPJ2VOC3HYZWMuyPanuox4Lo="
  "resolved" "http://registry.npm.taobao.org/eslint-config-standard/download/eslint-config-standard-11.0.0.tgz"
  "version" "11.0.0"

"eslint-friendly-formatter@^4.0.1":
  "integrity" "sha1-J9UE3IN/fK3b8gGy6EpO5zC6Pvo="
  "resolved" "http://registry.npm.taobao.org/eslint-friendly-formatter/download/eslint-friendly-formatter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "chalk" "^2.0.1"
    "coalescy" "1.0.0"
    "extend" "^3.0.0"
    "minimist" "^1.2.0"
    "strip-ansi" "^4.0.0"
    "text-table" "^0.2.0"

"eslint-import-resolver-node@^0.3.1":
  "integrity" "sha1-WPFfuDm40FdsqYBBNHaqskcttmo="
  "resolved" "http://registry.npm.taobao.org/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "debug" "^2.6.9"
    "resolve" "^1.5.0"

"eslint-loader@^2.0.0":
  "integrity" "sha1-KpJRUjZSQwv91kPv2wr8GiqJVGo="
  "resolved" "http://registry.npm.taobao.org/eslint-loader/download/eslint-loader-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-module-utils@^2.2.0":
  "integrity" "sha1-snA2LNiLGkitMIl2zn+lTphBF0Y="
  "resolved" "http://registry.npm.taobao.org/eslint-module-utils/download/eslint-module-utils-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "debug" "^2.6.8"
    "pkg-dir" "^1.0.0"

"eslint-plugin-html@^4.0.3":
  "integrity" "sha1-cku5Jy77TfAH3+6N+yae2DV35bQ="
  "resolved" "http://registry.npm.taobao.org/eslint-plugin-html/download/eslint-plugin-html-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "htmlparser2" "^3.8.2"

"eslint-plugin-import@^2.11.0", "eslint-plugin-import@>=2.8.0":
  "integrity" "sha1-axdibS4+atUs/OiAeoRdFeIhEag="
  "resolved" "http://registry.npm.taobao.org/eslint-plugin-import/download/eslint-plugin-import-2.14.0.tgz"
  "version" "2.14.0"
  dependencies:
    "contains-path" "^0.1.0"
    "debug" "^2.6.8"
    "doctrine" "1.5.0"
    "eslint-import-resolver-node" "^0.3.1"
    "eslint-module-utils" "^2.2.0"
    "has" "^1.0.1"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.3"
    "read-pkg-up" "^2.0.0"
    "resolve" "^1.6.0"

"eslint-plugin-node@^6.0.1", "eslint-plugin-node@>=5.2.1":
  "integrity" "sha1-vxlkIpgGQ3kxXXpLKnWTc3b6BeQ="
  "resolved" "http://registry.npm.taobao.org/eslint-plugin-node/download/eslint-plugin-node-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ignore" "^3.3.6"
    "minimatch" "^3.0.4"
    "resolve" "^1.3.3"
    "semver" "^5.4.1"

"eslint-plugin-promise@^3.4.0", "eslint-plugin-promise@>=3.6.0":
  "integrity" "sha1-ZevyeoRePB6db2pWIt3TgBaUtiE="
  "resolved" "http://registry.npm.taobao.org/eslint-plugin-promise/download/eslint-plugin-promise-3.8.0.tgz"
  "version" "3.8.0"

"eslint-plugin-standard@^3.0.1", "eslint-plugin-standard@>=3.0.1":
  "integrity" "sha1-Kp4hJZukxHwC1TstDJE11LECLUc="
  "resolved" "http://registry.npm.taobao.org/eslint-plugin-standard/download/eslint-plugin-standard-3.1.0.tgz"
  "version" "3.1.0"

"eslint-scope@^3.7.1", "eslint-scope@3.7.1":
  "integrity" "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug="
  "resolved" "http://registry.npm.taobao.org/eslint-scope/download/eslint-scope-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-visitor-keys@^1.0.0":
  "integrity" "sha1-PzGA+y4pEBdxastMnW1bXDSmqB0="
  "resolved" "http://registry.npm.taobao.org/eslint-visitor-keys/download/eslint-visitor-keys-1.0.0.tgz"
  "version" "1.0.0"

"eslint@^4.19.1", "eslint@>=1.6.0 <6.0.0", "eslint@>=3.1.0", "eslint@>=3.19.0", "eslint@>=4.18.0", "eslint@2.x - 5.x":
  "integrity" "sha1-MtHWU+HZBAiFS/spbwdux+GGowA="
  "resolved" "http://registry.npm.taobao.org/eslint/download/eslint-4.19.1.tgz"
  "version" "4.19.1"
  dependencies:
    "ajv" "^5.3.0"
    "babel-code-frame" "^6.22.0"
    "chalk" "^2.1.0"
    "concat-stream" "^1.6.0"
    "cross-spawn" "^5.1.0"
    "debug" "^3.1.0"
    "doctrine" "^2.1.0"
    "eslint-scope" "^3.7.1"
    "eslint-visitor-keys" "^1.0.0"
    "espree" "^3.5.4"
    "esquery" "^1.0.0"
    "esutils" "^2.0.2"
    "file-entry-cache" "^2.0.0"
    "functional-red-black-tree" "^1.0.1"
    "glob" "^7.1.2"
    "globals" "^11.0.1"
    "ignore" "^3.3.3"
    "imurmurhash" "^0.1.4"
    "inquirer" "^3.0.6"
    "is-resolvable" "^1.0.0"
    "js-yaml" "^3.9.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.4"
    "minimatch" "^3.0.2"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.2"
    "path-is-inside" "^1.0.2"
    "pluralize" "^7.0.0"
    "progress" "^2.0.0"
    "regexpp" "^1.0.1"
    "require-uncached" "^1.0.3"
    "semver" "^5.3.0"
    "strip-ansi" "^4.0.0"
    "strip-json-comments" "~2.0.1"
    "table" "4.0.2"
    "text-table" "~0.2.0"

"espree@^3.5.4":
  "integrity" "sha1-sPRHGHyKi+2US4FaZgvd9d610ac="
  "resolved" "http://registry.npm.taobao.org/espree/download/espree-3.5.4.tgz"
  "version" "3.5.4"
  dependencies:
    "acorn" "^5.5.0"
    "acorn-jsx" "^3.0.0"

"esprima@^2.6.0":
  "integrity" "sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE="
  "resolved" "http://registry.npm.taobao.org/esprima/download/esprima-2.7.3.tgz"
  "version" "2.7.3"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "http://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.0.0":
  "integrity" "sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg="
  "resolved" "http://registry.npm.taobao.org/esquery/download/esquery-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "estraverse" "^4.0.0"

"esrecurse@^4.1.0":
  "integrity" "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8="
  "resolved" "http://registry.npm.taobao.org/esrecurse/download/esrecurse-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estraverse" "^4.1.0"

"estraverse@^4.0.0", "estraverse@^4.1.0", "estraverse@^4.1.1":
  "integrity" "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM="
  "resolved" "http://registry.npm.taobao.org/estraverse/download/estraverse-4.2.0.tgz"
  "version" "4.2.0"

"esutils@^2.0.2":
  "integrity" "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs="
  "resolved" "http://registry.npm.taobao.org/esutils/download/esutils-2.0.2.tgz"
  "version" "2.0.2"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "http://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-emitter@~0.3.5":
  "integrity" "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk="
  "resolved" "http://registry.npm.taobao.org/event-emitter/download/event-emitter-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"eventemitter3@^3.0.0":
  "integrity" "sha1-CQtNbNvWRe0Qv3UNS1QHlC17oWM="
  "resolved" "http://registry.npm.taobao.org/eventemitter3/download/eventemitter3-3.1.0.tgz"
  "version" "3.1.0"

"events@^1.0.0":
  "integrity" "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ="
  "resolved" "http://registry.npm.taobao.org/events/download/events-1.1.1.tgz"
  "version" "1.1.1"

"eventsource-polyfill@^0.9.6":
  "integrity" "sha1-EODRh/ERsWfyj9q5GIQ859gY8Tw="
  "resolved" "http://registry.npm.taobao.org/eventsource-polyfill/download/eventsource-polyfill-0.9.6.tgz"
  "version" "0.9.6"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "http://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^0.7.0":
  "integrity" "sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c="
  "resolved" "http://registry.npm.taobao.org/execa/download/execa-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "http://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.16.2", "express@^4.16.3":
  "integrity" "sha1-/d72GSYQniTFFeqX/S8b2/Yt8S4="
  "resolved" "http://registry.npm.taobao.org/express/download/express-4.16.4.tgz"
  "version" "4.16.4"
  dependencies:
    "accepts" "~1.3.5"
    "array-flatten" "1.1.1"
    "body-parser" "1.18.3"
    "content-disposition" "0.5.2"
    "content-type" "~1.0.4"
    "cookie" "0.3.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.1.1"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.4"
    "qs" "6.5.2"
    "range-parser" "~1.2.0"
    "safe-buffer" "5.1.2"
    "send" "0.16.2"
    "serve-static" "1.13.2"
    "setprototypeof" "1.1.0"
    "statuses" "~1.4.0"
    "type-is" "~1.6.16"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@^3.0.0", "extend@~3.0.1", "extend@~3.0.2":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "http://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^2.0.4":
  "integrity" "sha1-BFURz9jRM/OEZnPRBHwVTiFK09U="
  "resolved" "http://registry.npm.taobao.org/external-editor/download/external-editor-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chardet" "^0.4.0"
    "iconv-lite" "^0.4.17"
    "tmp" "^0.0.33"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "http://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extract-text-webpack-plugin@^3.0.2":
  "integrity" "sha1-XwQ+qgL5dQqSWLeMCm4NwUCPsvc="
  "resolved" "http://registry.npm.taobao.org/extract-text-webpack-plugin/download/extract-text-webpack-plugin-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "async" "^2.4.1"
    "loader-utils" "^1.1.0"
    "schema-utils" "^0.3.0"
    "webpack-sources" "^1.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^1.0.0":
  "integrity" "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="
  "resolved" "http://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz"
  "version" "1.1.0"

"fast-deep-equal@^2.0.1":
  "integrity" "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk="
  "resolved" "http://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz"
  "version" "2.0.1"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="
  "resolved" "http://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz"
  "version" "2.0.0"

"fast-levenshtein@~2.0.4":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "http://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastparse@^1.1.1":
  "integrity" "sha1-kXKMWllC7O2FMSg8eUQe5BIsNak="
  "resolved" "http://registry.npm.taobao.org/fastparse/download/fastparse-1.1.2.tgz"
  "version" "1.1.2"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "http://registry.npm.taobao.org/figures/download/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^2.0.0":
  "integrity" "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E="
  "resolved" "http://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "flat-cache" "^1.2.1"
    "object-assign" "^4.0.1"

"file-loader@^1.1.11":
  "integrity" "sha1-b+iGRJsPKpNuQ8q6rAzb+zaVBvg="
  "resolved" "http://registry.npm.taobao.org/file-loader/download/file-loader-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "loader-utils" "^1.0.2"
    "schema-utils" "^0.4.5"

"filesize@^3.5.11":
  "integrity" "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc="
  "resolved" "http://registry.npm.taobao.org/filesize/download/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "http://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"finalhandler@1.1.1":
  "integrity" "sha1-7r9O2EAHnIP0JJA4ydcDAIMBsQU="
  "resolved" "http://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.2"
    "statuses" "~1.4.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^1.0.0":
  "integrity" "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8="
  "resolved" "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^1.0.0"
    "pkg-dir" "^2.0.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "http://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^2.0.0", "find-up@^2.1.0":
  "integrity" "sha1-RdG35QbHF93UgndaK3eSCjwMV6c="
  "resolved" "http://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "locate-path" "^2.0.0"

"flat-cache@^1.2.1":
  "integrity" "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE="
  "resolved" "http://registry.npm.taobao.org/flat-cache/download/flat-cache-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "circular-json" "^0.3.1"
    "del" "^2.0.2"
    "graceful-fs" "^4.1.2"
    "write" "^0.2.1"

"flatten@^1.0.2":
  "integrity" "sha1-2uRqnXj74lKSJYzB54CkHZXAN4I="
  "resolved" "http://registry.npm.taobao.org/flatten/download/flatten-1.0.2.tgz"
  "version" "1.0.2"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-xdWG7zivYJdlC0m8QbVfq7GfNb0="
  "resolved" "http://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.4"

"flyio@^0.6.2":
  "integrity" "sha512-ImUMVRwyuGbVtz5M12cAZHsaiwSoyZ/4QH7lUXGppt6WoQerYJbTNlXFlPca87K4hfkqUZDsvAxu8OX5wBscmw=="
  "resolved" "https://registry.npmjs.org/flyio/-/flyio-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "request" "^2.85.0"

"follow-redirects@^1.0.0":
  "integrity" "sha1-ye2ddIuBSjlTVxblMbkZaoRdicY="
  "resolved" "http://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.5.9.tgz"
  "version" "1.5.9"
  dependencies:
    "debug" "=3.1.0"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "http://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@~0.1.2":
  "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
  "resolved" "http://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz"
  "version" "0.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "http://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "http://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"friendly-errors-webpack-plugin@^1.7.0":
  "integrity" "sha1-78hsu4FiJFZYYaG+ep2E0Kr+oTY="
  "resolved" "http://registry.npm.taobao.org/friendly-errors-webpack-plugin/download/friendly-errors-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "chalk" "^1.1.3"
    "error-stack-parser" "^2.0.0"
    "string-width" "^2.0.0"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "http://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@~0.16.3":
  "integrity" "sha1-GtZh+myGyWCM0bSe/G/Og0k5p1A="
  "resolved" "http://registry.npm.taobao.org/fs-extra/download/fs-extra-0.16.5.tgz"
  "version" "0.16.5"
  dependencies:
    "graceful-fs" "^3.0.5"
    "jsonfile" "^2.0.0"
    "rimraf" "^2.2.8"

"fs-minipass@^1.2.5":
  "version" "1.2.5"
  dependencies:
    "minipass" "^2.2.1"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "http://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "http://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.2.2":
  "integrity" "sha1-9B3LGvJYKvNpLaNvxVy9jhBBxCY="
  "resolved" "http://registry.npm.taobao.org/fsevents/download/fsevents-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "nan" "^2.9.2"
    "node-pre-gyp" "^0.10.0"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "http://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "http://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gauge@~2.7.3":
  "version" "2.7.4"
  dependencies:
    "aproba" "^1.0.3"
    "console-control-strings" "^1.0.0"
    "has-unicode" "^2.0.0"
    "object-assign" "^4.1.0"
    "signal-exit" "^3.0.0"
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wide-align" "^1.1.0"

"get-caller-file@^1.0.1":
  "integrity" "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o="
  "resolved" "http://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz"
  "version" "1.0.3"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "http://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "http://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "http://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob@^7.0.0", "glob@^7.0.3", "glob@^7.0.5", "glob@^7.1.2":
  "integrity" "sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE="
  "resolved" "http://registry.npm.taobao.org/glob/download/glob-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.0.1":
  "integrity" "sha1-we9F7pvta63wZjxcuQ6NGt7BMh0="
  "resolved" "http://registry.npm.taobao.org/globals/download/globals-11.8.0.tgz"
  "version" "11.8.0"

"globals@^11.1.0":
  "integrity" "sha1-we9F7pvta63wZjxcuQ6NGt7BMh0="
  "resolved" "http://registry.npm.taobao.org/globals/download/globals-11.8.0.tgz"
  "version" "11.8.0"

"globals@^9.18.0":
  "integrity" "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="
  "resolved" "http://registry.npm.taobao.org/globals/download/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^5.0.0":
  "integrity" "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0="
  "resolved" "http://registry.npm.taobao.org/globby/download/globby-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "array-union" "^1.0.1"
    "arrify" "^1.0.0"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "http://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"graceful-fs@^3.0.5":
  "integrity" "sha1-dhPHeKGv6mLyXGMKCG1/Osu92Bg="
  "resolved" "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-3.0.11.tgz"
  "version" "3.0.11"
  dependencies:
    "natives" "^1.1.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6":
  "integrity" "sha1-/7cD4QZuig7qpMi4C6klPu77+wA="
  "resolved" "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.1.15.tgz"
  "version" "4.1.15"

"gzip-size@^4.1.0":
  "integrity" "sha1-iuCWJX6r59acRb4rZ8RIEk/7UXw="
  "resolved" "http://registry.npm.taobao.org/gzip-size/download/gzip-size-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^3.0.0"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.0":
  "integrity" "sha512-sNvOCzEQNr/qrvJgc3UG/kD4QtlHycrzwS+6mfTrrSq97BvaYcPZZI1ZSqGSPR73Cxn4LKTD4PttRwfU7jWq5g=="
  "resolved" "https://registry.npmjs.org/har-validator/-/har-validator-5.1.3.tgz"
  "version" "5.1.3"
  dependencies:
    "ajv" "^6.5.5"
    "har-schema" "^2.0.0"

"has-ansi@^0.1.0":
  "integrity" "sha1-hPJlqujA5qiKEtcCKJS3VoiUxi4="
  "resolved" "http://registry.npm.taobao.org/has-ansi/download/has-ansi-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "ansi-regex" "^0.2.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "http://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "http://registry.npm.taobao.org/has-flag/download/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^2.0.0":
  "integrity" "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE="
  "resolved" "http://registry.npm.taobao.org/has-flag/download/has-flag-2.0.0.tgz"
  "version" "2.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "http://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-symbols@^1.0.0":
  "integrity" "sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q="
  "resolved" "http://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.0.tgz"
  "version" "1.0.0"

"has-unicode@^2.0.0":
  "version" "2.0.1"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "http://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "http://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "http://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "http://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.1":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "http://registry.npm.taobao.org/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg="
  "resolved" "http://registry.npm.taobao.org/hash-base/download/hash-base-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "http://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-44q0uF37HgxA/pJlwOm1SFTCOBI="
  "resolved" "http://registry.npm.taobao.org/hash.js/download/hash.js-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "http://registry.npm.taobao.org/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"hmac-drbg@^1.0.0":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "http://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"home-or-tmp@^2.0.0":
  "integrity" "sha1-42w/LSyufXRqhX440Y1fMqeILbg="
  "resolved" "http://registry.npm.taobao.org/home-or-tmp/download/home-or-tmp-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.1"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-l/I2l3vW4SVAiTD/bePuxigewEc="
  "resolved" "http://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.7.1.tgz"
  "version" "2.7.1"

"html-comment-regex@^1.1.0":
  "integrity" "sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c="
  "resolved" "http://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.2.tgz"
  "version" "1.1.2"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "http://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-webpack-plugin@^3.2.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "http://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"htmlparser2@^3.8.2":
  "integrity" "sha1-X15CLc9hGcDZg+02Jgzp3tC+5GQ="
  "resolved" "http://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "domelementtype" "^1.3.0"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.0.6"

"htmlparser2@~3.3.0":
  "integrity" "sha1-zHDQWln2VC5D8OaFyYLhTJJKnv4="
  "resolved" "http://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "domelementtype" "1"
    "domhandler" "2.1"
    "domutils" "1.1"
    "readable-stream" "1.0"

"http-errors@~1.6.2", "http-errors@~1.6.3", "http-errors@1.6.3":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "http://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-proxy-middleware@^0.18.0":
  "integrity" "sha1-CYfmu1pWBuWmkWjY+WeofxXdiqs="
  "resolved" "http://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "http-proxy" "^1.16.2"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.5"
    "micromatch" "^3.1.9"

"http-proxy@^1.16.2":
  "integrity" "sha1-etOElGWPhGBeL220Q230EPTlvpo="
  "resolved" "http://registry.npm.taobao.org/http-proxy/download/http-proxy-1.17.0.tgz"
  "version" "1.17.0"
  dependencies:
    "eventemitter3" "^3.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "http://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"iconv-lite@^0.4.17":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"iconv-lite@^0.4.4":
  "version" "0.4.21"
  dependencies:
    "safer-buffer" "^2.1.0"

"iconv-lite@0.4.23":
  "integrity" "sha1-KXhx9jvlB63Pv8pxXQzQ7thOmmM="
  "resolved" "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.23.tgz"
  "version" "0.4.23"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-replace-symbols@^1.1.0":
  "integrity" "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="
  "resolved" "http://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz"
  "version" "1.1.0"

"icss-utils@^2.1.0":
  "integrity" "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI="
  "resolved" "http://registry.npm.taobao.org/icss-utils/download/icss-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^6.0.1"

"ieee754@^1.1.4":
  "integrity" "sha1-UL8k5bnIu5ivSWTJQc2wkY2ntgs="
  "resolved" "http://registry.npm.taobao.org/ieee754/download/ieee754-1.1.12.tgz"
  "version" "1.1.12"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "http://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore-walk@^3.0.1":
  "version" "3.0.1"
  dependencies:
    "minimatch" "^3.0.4"

"ignore@^3.3.3", "ignore@^3.3.5", "ignore@^3.3.6":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "http://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz"
  "version" "3.3.10"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "http://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "http://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "http://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "http://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"indexof@0.0.1":
  "integrity" "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="
  "resolved" "http://registry.npm.taobao.org/indexof/download/indexof-0.0.1.tgz"
  "version" "0.0.1"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "http://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "http://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz"
  "version" "2.0.1"

"ini@^1.3.4":
  "integrity" "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="
  "resolved" "http://registry.npm.taobao.org/ini/download/ini-1.3.5.tgz"
  "version" "1.3.5"

"ini@~1.3.0":
  "version" "1.3.5"

"inquirer@^3.0.6":
  "integrity" "sha1-ndLyrXZdyrH/BEO0kUQqILoifck="
  "resolved" "http://registry.npm.taobao.org/inquirer/download/inquirer-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.0"
    "cli-cursor" "^2.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^2.0.4"
    "figures" "^2.0.0"
    "lodash" "^4.3.0"
    "mute-stream" "0.0.7"
    "run-async" "^2.2.0"
    "rx-lite" "^4.0.8"
    "rx-lite-aggregates" "^4.0.8"
    "string-width" "^2.1.0"
    "strip-ansi" "^4.0.0"
    "through" "^2.3.6"

"interpret@^1.0.0":
  "integrity" "sha1-ftGxQQxqDg94z5XTuEQMY/eLhhQ="
  "resolved" "http://registry.npm.taobao.org/interpret/download/interpret-1.1.0.tgz"
  "version" "1.1.0"

"invariant@^2.2.0", "invariant@^2.2.2":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "http://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"invert-kv@^1.0.0":
  "integrity" "sha1-EEqOSqym09jNFXqO+L+rLXo//bY="
  "resolved" "http://registry.npm.taobao.org/invert-kv/download/invert-kv-1.0.0.tgz"
  "version" "1.0.0"

"ipaddr.js@1.8.0":
  "integrity" "sha1-6qM9bd16zo9/b+DJygRA5wZzix4="
  "resolved" "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.8.0.tgz"
  "version" "1.8.0"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "http://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "http://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "http://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "http://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-builtin-module@^1.0.0":
  "integrity" "sha1-VAVy0096wxGfj3bDDLwbHgN6/74="
  "resolved" "http://registry.npm.taobao.org/is-builtin-module/download/is-builtin-module-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "builtin-modules" "^1.0.0"

"is-callable@^1.1.3", "is-callable@^1.1.4":
  "integrity" "sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU="
  "resolved" "http://registry.npm.taobao.org/is-callable/download/is-callable-1.1.4.tgz"
  "version" "1.1.4"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY="
  "resolved" "http://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz"
  "version" "1.0.1"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "http://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "http://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "http://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "http://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finite@^1.0.0":
  "integrity" "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko="
  "resolved" "http://registry.npm.taobao.org/is-finite/download/is-finite-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "http://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0":
  "integrity" "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A="
  "resolved" "http://registry.npm.taobao.org/is-glob/download/is-glob-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "http://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-path-cwd@^1.0.0":
  "integrity" "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0="
  "resolved" "http://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-1.0.0.tgz"
  "version" "1.0.0"

"is-path-in-cwd@^1.0.0":
  "integrity" "sha1-WsSLNF72dTOb1sekipEhELJBz1I="
  "resolved" "http://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-path-inside" "^1.0.0"

"is-path-inside@^1.0.0":
  "integrity" "sha1-jvW33lBDej/cprToZe96pVy0gDY="
  "resolved" "http://registry.npm.taobao.org/is-path-inside/download/is-path-inside-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "path-is-inside" "^1.0.1"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "http://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^2.0.1", "is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "http://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-promise@^2.1.0":
  "integrity" "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o="
  "resolved" "http://registry.npm.taobao.org/is-promise/download/is-promise-2.1.0.tgz"
  "version" "2.1.0"

"is-regex@^1.0.4":
  "integrity" "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE="
  "resolved" "http://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has" "^1.0.1"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "http://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "http://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-svg@^2.0.0":
  "integrity" "sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk="
  "resolved" "http://registry.npm.taobao.org/is-svg/download/is-svg-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "html-comment-regex" "^1.1.0"

"is-symbol@^1.0.2":
  "integrity" "sha1-oFX2rlcZLK7jKeeoYBGLSXqVDzg="
  "resolved" "http://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.0"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "http://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "http://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "http://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "http://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "http://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "http://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"js-base64@^2.1.9":
  "integrity" "sha1-dIkR+wT0imDEdxs3XKxFqA3xHAM="
  "resolved" "http://registry.npm.taobao.org/js-base64/download/js-base64-2.4.9.tgz"
  "version" "2.4.9"

"js-beautify@^1.6.14":
  "integrity" "sha1-HrF1tzo1caXx7Y2Y588rBb+phHE="
  "resolved" "http://registry.npm.taobao.org/js-beautify/download/js-beautify-1.8.8.tgz"
  "version" "1.8.8"
  dependencies:
    "config-chain" "~1.1.5"
    "editorconfig" "^0.15.0"
    "mkdirp" "~0.5.0"
    "nopt" "~4.0.1"

"js-tokens@^3.0.0", "js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "http://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.4.3", "js-yaml@~3.7.0":
  "integrity" "sha1-XJZ93YN6m/3KXy3oQlOr6KHAO4A="
  "resolved" "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^2.6.0"

"js-yaml@^3.9.0":
  "integrity" "sha1-6u1lbsg0TxD1J8a/obbiJE3hZ9E="
  "resolved" "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@^3.9.1":
  "integrity" "sha1-6u1lbsg0TxD1J8a/obbiJE3hZ9E="
  "resolved" "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^1.3.0":
  "integrity" "sha1-RsP+yMGJKxKwgz25vHYiF226s0s="
  "resolved" "http://registry.npm.taobao.org/jsesc/download/jsesc-1.3.0.tgz"
  "version" "1.3.0"

"jsesc@^2.5.1":
  "integrity" "sha1-5CGiqOINawgZ3yiQj3glJrlt0f4="
  "resolved" "http://registry.npm.taobao.org/jsesc/download/jsesc-2.5.1.tgz"
  "version" "2.5.1"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "http://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-loader@^0.5.4":
  "integrity" "sha1-3KFKcCNf+C8KyaOr62DTN6NlGF0="
  "resolved" "http://registry.npm.taobao.org/json-loader/download/json-loader-0.5.7.tgz"
  "version" "0.5.7"

"json-parse-better-errors@^1.0.1":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "http://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.3.0":
  "integrity" "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="
  "resolved" "http://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz"
  "version" "0.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "http://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.2.3":
  "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
  "resolved" "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
  "version" "0.2.3"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "http://registry.npm.taobao.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json5@^0.5.0", "json5@^0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "http://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz"
  "version" "0.5.1"

"jsonfile@^2.0.0":
  "integrity" "sha1-NzaitCi4e72gzIO1P6PWM6NcKug="
  "resolved" "http://registry.npm.taobao.org/jsonfile/download/jsonfile-2.4.0.tgz"
  "version" "2.4.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsprim@^1.2.2":
  "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
  "resolved" "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.2.3"
    "verror" "1.10.0"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-ARRrNqYhjmTljzqNZt5df8b20FE="
  "resolved" "http://registry.npm.taobao.org/kind-of/download/kind-of-6.0.2.tgz"
  "version" "6.0.2"

"last-call-webpack-plugin@^2.1.2":
  "integrity" "sha1-rYDG4xCZgpTS7SGApo6VieR2jEQ="
  "resolved" "http://registry.npm.taobao.org/last-call-webpack-plugin/download/last-call-webpack-plugin-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.4"
    "webpack-sources" "^1.0.1"

"lazy-cache@^1.0.3":
  "integrity" "sha1-odePw6UEdMuAhF07O24dpJpEbo4="
  "resolved" "http://registry.npm.taobao.org/lazy-cache/download/lazy-cache-1.0.4.tgz"
  "version" "1.0.4"

"lcid@^1.0.0":
  "integrity" "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU="
  "resolved" "http://registry.npm.taobao.org/lcid/download/lcid-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "invert-kv" "^1.0.0"

"less-loader@^4.1.0":
  "integrity" "sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg=="
  "resolved" "https://registry.npmjs.org/less-loader/-/less-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^3.0.0"

"less@^2.3.1 || ^3.0.0", "less@^3.8.1":
  "integrity" "sha512-8HFGuWmL3FhQR0aH89escFNBQH/nEiYPP2ltDFdQw2chE28Yx2E3lhAIq9Y2saYwLSwa699s4dBVEfCY8Drf7Q=="
  "resolved" "https://registry.npmjs.org/less/-/less-3.8.1.tgz"
  "version" "3.8.1"
  dependencies:
    "clone" "^2.1.2"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "mime" "^1.4.1"
    "mkdirp" "^0.5.0"
    "promise" "^7.1.1"
    "request" "^2.83.0"
    "source-map" "~0.6.0"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "http://registry.npm.taobao.org/levn/download/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"load-json-file@^2.0.0":
  "integrity" "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg="
  "resolved" "http://registry.npm.taobao.org/load-json-file/download/load-json-file-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "parse-json" "^2.2.0"
    "pify" "^2.0.0"
    "strip-bom" "^3.0.0"

"loader-fs-cache@^1.0.0":
  "integrity" "sha1-VuC/CL2XCLJqdltoUJhAyN7J/bw="
  "resolved" "http://registry.npm.taobao.org/loader-fs-cache/download/loader-fs-cache-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "0.5.1"

"loader-runner@^2.3.0":
  "integrity" "sha1-Am8S/nwxFZkolqwCugIrqSlxuXk="
  "resolved" "http://registry.npm.taobao.org/loader-runner/download/loader-runner-2.3.1.tgz"
  "version" "2.3.1"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "http://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.2", "loader-utils@^1.1.0":
  "integrity" "sha1-yYrvSIvM7aL/teLeZG1qdUQp9c0="
  "resolved" "http://registry.npm.taobao.org/loader-utils/download/loader-utils-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"

"locate-path@^2.0.0":
  "integrity" "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4="
  "resolved" "http://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-locate" "^2.0.0"
    "path-exists" "^3.0.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha1-soqmKIorn8ZRA1x3EfZathkDMaY="
  "resolved" "http://registry.npm.taobao.org/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "http://registry.npm.taobao.org/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "http://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "http://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.10", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.17.5", "lodash@^4.2.0", "lodash@^4.3.0":
  "integrity" "sha1-s56mIp72B+zYniyN8SU2iRysm40="
  "resolved" "http://registry.npm.taobao.org/lodash/download/lodash-4.17.11.tgz"
  "version" "4.17.11"

"log-symbols@^2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "http://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"longest@^1.0.1":
  "integrity" "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc="
  "resolved" "http://registry.npm.taobao.org/longest/download/longest-1.0.1.tgz"
  "version" "1.0.1"

"loose-envify@^1.0.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "http://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "http://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1", "lru-cache@^4.1.1", "lru-cache@^4.1.3":
  "integrity" "sha1-oRdc80lt/IQ2wVbDNLSVWZK85pw="
  "resolved" "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"make-dir@^1.0.0":
  "integrity" "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww="
  "resolved" "http://registry.npm.taobao.org/make-dir/download/make-dir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "pify" "^3.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "http://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "http://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"math-expression-evaluator@^1.2.14":
  "integrity" "sha1-3oGf282E3M2PrlnGrreWFbnSZqw="
  "resolved" "http://registry.npm.taobao.org/math-expression-evaluator/download/math-expression-evaluator-1.2.17.tgz"
  "version" "1.2.17"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "http://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "http://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"mem@^1.1.0":
  "integrity" "sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y="
  "resolved" "http://registry.npm.taobao.org/mem/download/mem-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "mimic-fn" "^1.0.0"

"memory-fs@^0.4.0", "memory-fs@~0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "http://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "http://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4", "micromatch@^3.1.9":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "http://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "http://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@~1.37.0":
  "integrity" "sha1-C2oM5v2+lXbiXx8tL96IMNwK0Ng="
  "resolved" "http://registry.npm.taobao.org/mime-db/download/mime-db-1.37.0.tgz"
  "version" "1.37.0"

"mime-types@^2.1.12", "mime-types@~2.1.18", "mime-types@~2.1.19":
  "integrity" "sha1-KJlaoey3cHQv5q5+WPkYHHRLP5Y="
  "resolved" "http://registry.npm.taobao.org/mime-types/download/mime-types-2.1.21.tgz"
  "version" "2.1.21"
  dependencies:
    "mime-db" "~1.37.0"

"mime@^1.3.4", "mime@^1.4.1", "mime@1.4.1":
  "integrity" "sha1-Eh+evEnjdm8xGnbh+hyAA8SwOqY="
  "resolved" "http://registry.npm.taobao.org/mime/download/mime-1.4.1.tgz"
  "version" "1.4.1"

"mime@^2.0.3":
  "integrity" "sha1-sWIcVNY7l8R9PP5/chX31kUXw2k="
  "resolved" "http://registry.npm.taobao.org/mime/download/mime-2.3.1.tgz"
  "version" "2.3.1"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "http://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "http://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.0", "minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "http://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.2", "minimatch@^3.0.3", "minimatch@^3.0.4":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "http://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "http://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minimist@0.0.8":
  "integrity" "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="
  "resolved" "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz"
  "version" "0.0.8"

"minipass@^2.2.1", "minipass@^2.2.4":
  "version" "2.2.4"
  dependencies:
    "safe-buffer" "^5.1.1"
    "yallist" "^3.0.0"

"minizlib@^1.1.0":
  "version" "1.1.0"
  dependencies:
    "minipass" "^2.2.1"

"mississippi@^2.0.0":
  "integrity" "sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8="
  "resolved" "http://registry.npm.taobao.org/mississippi/download/mississippi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^2.0.1"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha1-pJ5yaNzhoNlpjkUybFYm3zVD0P4="
  "resolved" "http://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.0", "mkdirp@^0.5.1", "mkdirp@~0.5.0", "mkdirp@~0.5.1", "mkdirp@0.5.1", "mkdirp@0.5.x":
  "integrity" "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM="
  "resolved" "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "minimist" "0.0.8"

"moment@^2.22.2":
  "integrity" "sha1-PCV/mDn8DpP/UxSWMiOeuQeD/2Y="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.22.2.tgz"
  "version" "2.22.2"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "http://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"mpvue-loader@^1.1.2":
  "integrity" "sha1-Pu5LhHNnPBXA6l3KbDy/qsA2meQ="
  "resolved" "http://registry.npm.taobao.org/mpvue-loader/download/mpvue-loader-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "babelon" "^1.0.5"
    "consolidate" "^0.14.0"
    "deep-equal" "^1.0.1"
    "hash-sum" "^1.0.2"
    "js-beautify" "^1.6.14"
    "loader-utils" "^1.1.0"
    "lru-cache" "^4.1.1"
    "mkdirp" "^0.5.1"
    "postcss" "^6.0.6"
    "postcss-load-config" "^1.1.0"
    "postcss-selector-parser" "^2.0.0"
    "relative" "^3.0.2"
    "resolve" "^1.3.3"
    "source-map" "^0.5.6"
    "upath" "^1.1.0"
    "vue-hot-reload-api" "^2.1.0"
    "vue-loader" "^13.0.4"
    "vue-style-loader" "^3.0.0"
    "vue-template-es2015-compiler" "^1.5.3"

"mpvue-template-compiler@^1.0.11", "mpvue-template-compiler@^1.0.12":
  "integrity" "sha1-Hj6AmnHc8NGOd7w6NwpEI1EM/SQ="
  "resolved" "http://registry.npm.taobao.org/mpvue-template-compiler/download/mpvue-template-compiler-1.0.13.tgz"
  "version" "1.0.13"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"mpvue-webpack-target@^1.0.0":
  "integrity" "sha1-IDmVt6tj8cgPtyrPhwlixUn8Q8c="
  "resolved" "http://registry.npm.taobao.org/mpvue-webpack-target/download/mpvue-webpack-target-1.0.1.tgz"
  "version" "1.0.1"

"mpvue-wxparse@^0.6.5":
  "integrity" "sha1-P7HhIzjQ6F2sC5ejqjPKhlGkiqg="
  "resolved" "https://registry.npm.taobao.org/mpvue-wxparse/download/mpvue-wxparse-0.6.5.tgz"
  "version" "0.6.5"

"mpvue-wxparse2@0.0.2":
  "integrity" "sha1-dzQWuPz4ZzCtnWpYF4Naeg/XtUU="
  "resolved" "https://registry.npm.taobao.org/mpvue-wxparse2/download/mpvue-wxparse2-0.0.2.tgz"
  "version" "0.0.2"

"mpvue@^1.0.11":
  "integrity" "sha1-nRKNkfKom911B0k/vu9An+MC+9c="
  "resolved" "http://registry.npm.taobao.org/mpvue/download/mpvue-1.0.13.tgz"
  "version" "1.0.13"

"ms@^2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "http://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz"
  "version" "2.1.1"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz"
  "version" "2.0.0"

"mute-stream@0.0.7":
  "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
  "resolved" "http://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.7.tgz"
  "version" "0.0.7"

"nan@^2.9.2":
  "integrity" "sha1-kOIrzLjKV+pM03zIPTgZtS7qZ2Y="
  "resolved" "http://registry.npm.taobao.org/nan/download/nan-2.11.1.tgz"
  "version" "2.11.1"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "http://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natives@^1.1.0":
  "integrity" "sha1-pgO0pJirdxc2ErnqGs3sTZgPALs="
  "resolved" "http://registry.npm.taobao.org/natives/download/natives-1.1.6.tgz"
  "version" "1.1.6"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "http://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"needle@^2.2.0":
  "version" "2.2.0"
  dependencies:
    "debug" "^2.1.2"
    "iconv-lite" "^0.4.4"
    "sax" "^1.2.4"

"negotiator@0.6.1":
  "integrity" "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk="
  "resolved" "http://registry.npm.taobao.org/negotiator/download/negotiator-0.6.1.tgz"
  "version" "0.6.1"

"neo-async@^2.5.0":
  "integrity" "sha1-udFeTXHGdikIZUtRg+04t1M0CDU="
  "resolved" "http://registry.npm.taobao.org/neo-async/download/neo-async-2.6.0.tgz"
  "version" "2.6.0"

"next-tick@1":
  "integrity" "sha1-yobR/ogoFpsBICCOPchCS524NCw="
  "resolved" "http://registry.npm.taobao.org/next-tick/download/next-tick-1.0.0.tgz"
  "version" "1.0.0"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "http://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-libs-browser@^2.0.0":
  "integrity" "sha1-X5QmPUBPbkR2fXJpAf/wVHjWAN8="
  "resolved" "http://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^1.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.0"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.10.3"
    "vm-browserify" "0.0.4"

"node-pre-gyp@^0.10.0":
  "version" "0.10.0"
  dependencies:
    "detect-libc" "^1.0.2"
    "mkdirp" "^0.5.1"
    "needle" "^2.2.0"
    "nopt" "^4.0.1"
    "npm-packlist" "^1.1.6"
    "npmlog" "^4.0.2"
    "rc" "^1.1.7"
    "rimraf" "^2.6.1"
    "semver" "^5.3.0"
    "tar" "^4"

"nopt@^4.0.1":
  "version" "4.0.1"
  dependencies:
    "abbrev" "1"
    "osenv" "^0.1.4"

"nopt@~4.0.1":
  "integrity" "sha1-0NRoWv1UFRk8jHUFYC0NF81kR00="
  "resolved" "http://registry.npm.taobao.org/nopt/download/nopt-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "abbrev" "1"
    "osenv" "^0.1.4"

"normalize-package-data@^2.3.2":
  "integrity" "sha1-EvlaMH1YNSB1oEkHuErIvpisAS8="
  "resolved" "http://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "is-builtin-module" "^1.0.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "http://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "http://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^1.4.0":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "http://registry.npm.taobao.org/normalize-url/download/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-wheel@^1.0.1":
  "integrity" "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU="
  "resolved" "https://registry.npm.taobao.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz"
  "version" "1.0.1"

"npm-bundled@^1.0.1":
  "version" "1.0.3"

"npm-packlist@^1.1.6":
  "version" "1.1.10"
  dependencies:
    "ignore-walk" "^3.0.1"
    "npm-bundled" "^1.0.1"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "http://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npmlog@^4.0.2":
  "version" "4.1.2"
  dependencies:
    "are-we-there-yet" "~1.1.2"
    "console-control-strings" "~1.1.0"
    "gauge" "~2.7.3"
    "set-blocking" "~2.0.0"

"nth-check@~1.0.1":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "http://registry.npm.taobao.org/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "http://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "http://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"oauth-sign@~0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "http://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "http://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha1-dtm6b/ETz478DZlhAoUf5nI5Y+I="
  "resolved" "http://registry.npm.taobao.org/object-hash/download/object-hash-1.3.0.tgz"
  "version" "1.3.0"

"object-keys@^1.0.12":
  "integrity" "sha1-CcU4VTd1dTEMymL1W7M0q/97PtI="
  "resolved" "http://registry.npm.taobao.org/object-keys/download/object-keys-1.0.12.tgz"
  "version" "1.0.12"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "http://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.getownpropertydescriptors@^2.0.3":
  "integrity" "sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY="
  "resolved" "http://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "define-properties" "^1.1.2"
    "es-abstract" "^1.5.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "http://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "http://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "http://registry.npm.taobao.org/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "http://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"opener@^1.4.3":
  "integrity" "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0="
  "resolved" "http://registry.npm.taobao.org/opener/download/opener-1.5.1.tgz"
  "version" "1.5.1"

"optimize-css-assets-webpack-plugin@^3.2.0":
  "integrity" "sha1-CaQMTO/eHdAUJESoc8Vqop6xjm8="
  "resolved" "http://registry.npm.taobao.org/optimize-css-assets-webpack-plugin/download/optimize-css-assets-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "cssnano" "^3.4.0"
    "last-call-webpack-plugin" "^2.1.2"

"optionator@^0.8.2":
  "integrity" "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q="
  "resolved" "http://registry.npm.taobao.org/optionator/download/optionator-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.4"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "wordwrap" "~1.0.0"

"ora@^2.0.0":
  "integrity" "sha1-bK8oMOuSSUGGHsU6FzeZ4Ai1Hls="
  "resolved" "http://registry.npm.taobao.org/ora/download/ora-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "chalk" "^2.3.1"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^1.1.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^4.0.0"
    "wcwidth" "^1.0.1"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "http://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-homedir@^1.0.0", "os-homedir@^1.0.1":
  "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
  "resolved" "http://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz"
  "version" "1.0.2"

"os-locale@^2.0.0":
  "integrity" "sha1-QrwpAKa1uL0XN2yOiCtlr8zyS/I="
  "resolved" "http://registry.npm.taobao.org/os-locale/download/os-locale-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "execa" "^0.7.0"
    "lcid" "^1.0.0"
    "mem" "^1.1.0"

"os-tmpdir@^1.0.0", "os-tmpdir@^1.0.1", "os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "http://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"osenv@^0.1.4":
  "integrity" "sha1-hc36+uso6Gd/QW4odZK18/SepBA="
  "resolved" "http://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "os-homedir" "^1.0.0"
    "os-tmpdir" "^1.0.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "http://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^1.0.0", "p-limit@^1.1.0":
  "integrity" "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg="
  "resolved" "http://registry.npm.taobao.org/p-limit/download/p-limit-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "p-try" "^1.0.0"

"p-locate@^2.0.0":
  "integrity" "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM="
  "resolved" "http://registry.npm.taobao.org/p-locate/download/p-locate-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "p-limit" "^1.1.0"

"p-try@^1.0.0":
  "integrity" "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M="
  "resolved" "http://registry.npm.taobao.org/p-try/download/p-try-1.0.0.tgz"
  "version" "1.0.0"

"pako@~1.0.5":
  "integrity" "sha1-AQEhG6pwxLykoPY/Igbpe3368lg="
  "resolved" "http://registry.npm.taobao.org/pako/download/pako-1.0.6.tgz"
  "version" "1.0.6"

"parallel-transform@^1.1.0":
  "integrity" "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY="
  "resolved" "http://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "cyclist" "~0.2.2"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "http://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parse-asn1@^5.0.0":
  "integrity" "sha1-9r8pOBgzK9DatU77Fgh3JHRebKg="
  "resolved" "http://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "asn1.js" "^4.0.0"
    "browserify-aes" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"

"parse-json@^2.2.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "http://registry.npm.taobao.org/parse-json/download/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "http://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parseurl@~1.3.2":
  "integrity" "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M="
  "resolved" "http://registry.npm.taobao.org/parseurl/download/parseurl-1.3.2.tgz"
  "version" "1.3.2"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "http://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.0":
  "integrity" "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo="
  "resolved" "http://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.0.tgz"
  "version" "0.0.0"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "http://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "http://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "http://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-is-absolute@^1.0.0", "path-is-absolute@^1.0.1":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "http://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.1", "path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "http://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "http://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-parse@^1.0.5":
  "integrity" "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="
  "resolved" "http://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz"
  "version" "1.0.6"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "http://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^2.0.0":
  "integrity" "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM="
  "resolved" "http://registry.npm.taobao.org/path-type/download/path-type-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pify" "^2.0.0"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "http://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha1-l2wgZTBhexTrsyEUI597CTNuk6Y="
  "resolved" "http://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.0.17.tgz"
  "version" "3.0.17"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "http://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz"
  "version" "3.0.0"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "http://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "http://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^2.0.0":
  "integrity" "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s="
  "resolved" "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.1.0"

"pluralize@^7.0.0":
  "integrity" "sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c="
  "resolved" "http://registry.npm.taobao.org/pluralize/download/pluralize-7.0.0.tgz"
  "version" "7.0.0"

"portfinder@^1.0.13":
  "integrity" "sha1-B+h5FKVSQtzaW4M9QvAY1odbWV8="
  "resolved" "http://registry.npm.taobao.org/portfinder/download/portfinder-1.0.19.tgz"
  "version" "1.0.19"
  dependencies:
    "async" "^1.5.2"
    "debug" "^2.2.0"
    "mkdirp" "0.5.x"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "http://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^5.2.0":
  "integrity" "sha1-d7rnypKK2FcW4v2kLyYb98HWW14="
  "resolved" "http://registry.npm.taobao.org/postcss-calc/download/postcss-calc-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "postcss" "^5.0.2"
    "postcss-message-helpers" "^2.0.0"
    "reduce-css-calc" "^1.2.6"

"postcss-colormin@^2.1.8":
  "integrity" "sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks="
  "resolved" "http://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "colormin" "^1.0.5"
    "postcss" "^5.0.13"
    "postcss-value-parser" "^3.2.3"

"postcss-convert-values@^2.3.4":
  "integrity" "sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0="
  "resolved" "http://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-2.6.1.tgz"
  "version" "2.6.1"
  dependencies:
    "postcss" "^5.0.11"
    "postcss-value-parser" "^3.1.2"

"postcss-discard-comments@^2.0.4":
  "integrity" "sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0="
  "resolved" "http://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-duplicates@^2.0.1":
  "integrity" "sha1-uavye4isGIFYpesSq8riAmO5GTI="
  "resolved" "http://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.4"

"postcss-discard-empty@^2.0.1":
  "integrity" "sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU="
  "resolved" "http://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "postcss" "^5.0.14"

"postcss-discard-overridden@^0.1.1":
  "integrity" "sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg="
  "resolved" "http://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "postcss" "^5.0.16"

"postcss-discard-unused@^2.2.1":
  "integrity" "sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM="
  "resolved" "http://registry.npm.taobao.org/postcss-discard-unused/download/postcss-discard-unused-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.14"
    "uniqs" "^2.0.0"

"postcss-filter-plugins@^2.0.0":
  "integrity" "sha1-giRf34IzcEFkXkdxFNjlk6oYuOw="
  "resolved" "http://registry.npm.taobao.org/postcss-filter-plugins/download/postcss-filter-plugins-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "postcss" "^5.0.4"

"postcss-load-config@^1.1.0":
  "integrity" "sha1-U56a/J3chiASHr+djDZz4M5Q0oo="
  "resolved" "http://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"
    "postcss-load-options" "^1.2.0"
    "postcss-load-plugins" "^2.3.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha1-8TEt2/WRLNdHF3CDxe96GdYu5IQ="
  "resolved" "http://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "cosmiconfig" "^4.0.0"
    "import-cwd" "^2.0.0"

"postcss-load-options@^1.2.0":
  "integrity" "sha1-sJixVZ3awt8EvAuzdfmaXP4rbYw="
  "resolved" "http://registry.npm.taobao.org/postcss-load-options/download/postcss-load-options-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cosmiconfig" "^2.1.0"
    "object-assign" "^4.1.0"

"postcss-load-plugins@^2.3.0":
  "integrity" "sha1-dFdoEWWZrKLwCfrUJrABdQSdjZI="
  "resolved" "http://registry.npm.taobao.org/postcss-load-plugins/download/postcss-load-plugins-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cosmiconfig" "^2.1.1"
    "object-assign" "^4.1.0"

"postcss-loader@^2.1.4":
  "integrity" "sha1-HX3XsXxrojS5vtWvE+C+pApC10A="
  "resolved" "http://registry.npm.taobao.org/postcss-loader/download/postcss-loader-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^6.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^0.4.0"

"postcss-merge-idents@^2.1.5":
  "integrity" "sha1-TFUwMTwI4dWzu/PSu8dH4njuonA="
  "resolved" "http://registry.npm.taobao.org/postcss-merge-idents/download/postcss-merge-idents-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.10"
    "postcss-value-parser" "^3.1.1"

"postcss-merge-longhand@^2.0.1":
  "integrity" "sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg="
  "resolved" "http://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "postcss" "^5.0.4"

"postcss-merge-rules@^2.0.3":
  "integrity" "sha1-0d9d+qexrMO+VT8OnhDofGG19yE="
  "resolved" "http://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "browserslist" "^1.5.2"
    "caniuse-api" "^1.5.2"
    "postcss" "^5.0.4"
    "postcss-selector-parser" "^2.2.2"
    "vendors" "^1.0.0"

"postcss-message-helpers@^2.0.0":
  "integrity" "sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4="
  "resolved" "http://registry.npm.taobao.org/postcss-message-helpers/download/postcss-message-helpers-2.0.0.tgz"
  "version" "2.0.0"

"postcss-minify-font-values@^1.0.2":
  "integrity" "sha1-S1jttWZB66fIR0qzUmyv17vey2k="
  "resolved" "http://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "object-assign" "^4.0.1"
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-minify-gradients@^1.0.1":
  "integrity" "sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE="
  "resolved" "http://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "postcss" "^5.0.12"
    "postcss-value-parser" "^3.3.0"

"postcss-minify-params@^1.0.4":
  "integrity" "sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM="
  "resolved" "http://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.2"
    "postcss-value-parser" "^3.0.2"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^2.0.4":
  "integrity" "sha1-ssapjAByz5G5MtGkllCBFDEXNb8="
  "resolved" "http://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "has" "^1.0.1"
    "postcss" "^5.0.14"
    "postcss-selector-parser" "^2.0.0"

"postcss-modules-extract-imports@^1.2.0":
  "integrity" "sha1-3IfjQUjsfqtfeR981YSYMzdbdBo="
  "resolved" "http://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "postcss" "^6.0.1"

"postcss-modules-local-by-default@^1.2.0":
  "integrity" "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk="
  "resolved" "http://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-scope@^1.1.0":
  "integrity" "sha1-1upkmUx5+XtipytCb75gVqGUu5A="
  "resolved" "http://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-selector-tokenizer" "^0.7.0"
    "postcss" "^6.0.1"

"postcss-modules-values@^1.3.0":
  "integrity" "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA="
  "resolved" "http://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "icss-replace-symbols" "^1.1.0"
    "postcss" "^6.0.1"

"postcss-mpvue-wxss@^1.0.0":
  "integrity" "sha1-XWSqCDAtp34gtEDotjZVleG08lA="
  "resolved" "http://registry.npm.taobao.org/postcss-mpvue-wxss/download/postcss-mpvue-wxss-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "postcss" "^6.0.8"
    "postcss-selector-parser" "^2.2.3"

"postcss-normalize-charset@^1.1.0":
  "integrity" "sha1-757nEhLX/nWceO0WL2HtYrXLk/E="
  "resolved" "http://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "postcss" "^5.0.5"

"postcss-normalize-url@^3.0.7":
  "integrity" "sha1-EI90s/L82viRov+j6kWSJ5/HgiI="
  "resolved" "http://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^1.4.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"

"postcss-ordered-values@^2.1.0":
  "integrity" "sha1-7sbCpntsQSqNsgQud/6NpD+VwR0="
  "resolved" "http://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.1"

"postcss-reduce-idents@^2.2.2":
  "integrity" "sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM="
  "resolved" "http://registry.npm.taobao.org/postcss-reduce-idents/download/postcss-reduce-idents-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "postcss" "^5.0.4"
    "postcss-value-parser" "^3.0.2"

"postcss-reduce-initial@^1.0.0":
  "integrity" "sha1-aPgGlfBF0IJjqHmtJA343WT2ROo="
  "resolved" "http://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "postcss" "^5.0.4"

"postcss-reduce-transforms@^1.0.3":
  "integrity" "sha1-/3b02CEkN7McKYpC0uFEQCV3GuE="
  "resolved" "http://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.8"
    "postcss-value-parser" "^3.0.1"

"postcss-selector-parser@^2.0.0", "postcss-selector-parser@^2.2.2", "postcss-selector-parser@^2.2.3":
  "integrity" "sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A="
  "resolved" "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "flatten" "^1.0.2"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-svgo@^2.1.1":
  "integrity" "sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0="
  "resolved" "http://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "is-svg" "^2.0.0"
    "postcss" "^5.0.14"
    "postcss-value-parser" "^3.2.3"
    "svgo" "^0.7.0"

"postcss-unique-selectors@^2.0.2":
  "integrity" "sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0="
  "resolved" "http://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "alphanum-sort" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.1", "postcss-value-parser@^3.0.2", "postcss-value-parser@^3.1.1", "postcss-value-parser@^3.1.2", "postcss-value-parser@^3.2.3", "postcss-value-parser@^3.3.0":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "http://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-zindex@^2.0.1":
  "integrity" "sha1-0hCd3AVbka9n/EyzsCWUZjnSryI="
  "resolved" "http://registry.npm.taobao.org/postcss-zindex/download/postcss-zindex-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "has" "^1.0.1"
    "postcss" "^5.0.4"
    "uniqs" "^2.0.0"

"postcss@^5.0.10", "postcss@^5.0.11", "postcss@^5.0.12", "postcss@^5.0.13", "postcss@^5.0.14", "postcss@^5.0.16", "postcss@^5.0.2", "postcss@^5.0.4", "postcss@^5.0.5", "postcss@^5.0.6", "postcss@^5.0.8", "postcss@^5.2.16":
  "integrity" "sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U="
  "resolved" "http://registry.npm.taobao.org/postcss/download/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^6.0.0":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^6.0.1":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^6.0.6":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"postcss@^6.0.8":
  "integrity" "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ="
  "resolved" "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz"
  "version" "6.0.23"
  dependencies:
    "chalk" "^2.4.1"
    "source-map" "^0.6.1"
    "supports-color" "^5.4.0"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "http://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "http://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^1.7.0", "prettier@~1.12.1":
  "integrity" "sha1-wa0g6APndJ+vkFpAnSNn4Gu+cyU="
  "resolved" "http://registry.npm.taobao.org/prettier/download/prettier-1.12.1.tgz"
  "version" "1.12.1"

"pretty-error@^2.0.2":
  "integrity" "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM="
  "resolved" "http://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "renderkid" "^2.0.1"
    "utila" "~0.4"

"private@^0.1.6", "private@^0.1.8":
  "integrity" "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="
  "resolved" "http://registry.npm.taobao.org/private/download/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o="
  "resolved" "http://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.0.tgz"
  "version" "2.0.0"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "http://registry.npm.taobao.org/process/download/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.0":
  "integrity" "sha1-ySQhaTQrHCnSdYiclXNGIbGVLjE="
  "resolved" "http://registry.npm.taobao.org/progress/download/progress-2.0.1.tgz"
  "version" "2.0.1"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "http://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promise@^7.1.1":
  "integrity" "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"proto-list@~1.2.1":
  "integrity" "sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk="
  "resolved" "http://registry.npm.taobao.org/proto-list/download/proto-list-1.2.4.tgz"
  "version" "1.2.4"

"proxy-addr@~2.0.4":
  "integrity" "sha1-7PxzO/Iv+Mb0B/onUye5q2fki5M="
  "resolved" "http://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "forwarded" "~0.1.2"
    "ipaddr.js" "1.8.0"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "http://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "http://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.24":
  "integrity" "sha512-AeUmQ0oLN02flVHXWh9sSJF7mcdFq0ppid/JkErufc3hGIV/AMa8Fo9VgDo/cT2jFdOWoFvHp90qqBH54W+gjQ=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.1.29.tgz"
  "version" "1.1.29"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "http://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0", "pump@^2.0.1":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "http://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "http://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "http://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^1.4.1":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "http://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "http://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"px2rpx-loader@^0.1.10":
  "integrity" "sha1-FbcNjOggo0wlCEX9RU214YduHCU="
  "resolved" "http://registry.npm.taobao.org/px2rpx-loader/download/px2rpx-loader-0.1.10.tgz"
  "version" "0.1.10"
  dependencies:
    "loader-utils" "^1.1.0"
    "px2rpx" "^0.5.3"

"px2rpx@^0.5.3":
  "integrity" "sha1-EgyG4cZLCprLHOmhP62/9p6YUjk="
  "resolved" "http://registry.npm.taobao.org/px2rpx/download/px2rpx-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "chalk" "~0.5.1"
    "commander" "~2.6.0"
    "css" "~2.2.0"
    "extend" "~3.0.1"
    "fs-extra" "~0.16.3"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "http://registry.npm.taobao.org/q/download/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@~6.5.2", "qs@6.5.2":
  "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
  "resolved" "http://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz"
  "version" "6.5.2"

"query-string@^4.1.0":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "http://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "http://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "http://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5":
  "integrity" "sha1-0wLFIpSFiISKjTAMkytEwkIx2oA="
  "resolved" "http://registry.npm.taobao.org/randombytes/download/randombytes-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "http://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.0.3", "range-parser@~1.2.0":
  "integrity" "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4="
  "resolved" "http://registry.npm.taobao.org/range-parser/download/range-parser-1.2.0.tgz"
  "version" "1.2.0"

"raw-body@2.3.3":
  "integrity" "sha1-GzJOzmtXBuFThVvBFIxlu39uoMM="
  "resolved" "http://registry.npm.taobao.org/raw-body/download/raw-body-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "bytes" "3.0.0"
    "http-errors" "1.6.3"
    "iconv-lite" "0.4.23"
    "unpipe" "1.0.0"

"rc@^1.1.7":
  "version" "1.2.7"
  dependencies:
    "deep-extend" "^0.5.1"
    "ini" "~1.3.0"
    "minimist" "^1.2.0"
    "strip-json-comments" "~2.0.1"

"read-pkg-up@^2.0.0":
  "integrity" "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4="
  "resolved" "http://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "find-up" "^2.0.0"
    "read-pkg" "^2.0.0"

"read-pkg@^2.0.0":
  "integrity" "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg="
  "resolved" "http://registry.npm.taobao.org/read-pkg/download/read-pkg-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "load-json-file" "^2.0.0"
    "normalize-package-data" "^2.3.2"
    "path-type" "^2.0.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.0.4", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@1 || 2":
  "integrity" "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8="
  "resolved" "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^2.0.6":
  "version" "2.3.6"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha1-NRMC5MaLWr1qLtVTdqf5olvjBXo="
  "resolved" "http://registry.npm.taobao.org/readable-stream/download/readable-stream-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@1.0":
  "integrity" "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw="
  "resolved" "http://registry.npm.taobao.org/readable-stream/download/readable-stream-1.0.34.tgz"
  "version" "1.0.34"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readdirp@^2.0.0":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "http://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"rechoir@^0.6.2":
  "integrity" "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q="
  "resolved" "http://registry.npm.taobao.org/rechoir/download/rechoir-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "resolve" "^1.1.6"

"reduce-css-calc@^1.2.6":
  "integrity" "sha1-dHyRTgSWFKTJz7umKYca0dKSdxY="
  "resolved" "http://registry.npm.taobao.org/reduce-css-calc/download/reduce-css-calc-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "balanced-match" "^0.4.2"
    "math-expression-evaluator" "^1.2.14"
    "reduce-function-call" "^1.0.1"

"reduce-function-call@^1.0.1":
  "integrity" "sha1-WiAL+S4ON3UXUv5FsKszD9S2vpk="
  "resolved" "http://registry.npm.taobao.org/reduce-function-call/download/reduce-function-call-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "balanced-match" "^0.4.2"

"regenerate@^1.2.1":
  "integrity" "sha1-SoVuxLVuQHfFV1icroXnpMiGmhE="
  "resolved" "http://registry.npm.taobao.org/regenerate/download/regenerate-1.4.0.tgz"
  "version" "1.4.0"

"regenerator-runtime@^0.11.0":
  "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
  "resolved" "http://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-transform@^0.10.0":
  "integrity" "sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0="
  "resolved" "http://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "babel-runtime" "^6.18.0"
    "babel-types" "^6.19.0"
    "private" "^0.1.6"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "http://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexpp@^1.0.1":
  "integrity" "sha1-DjUW3Qt5BPQT0tQZPc5GGMOmias="
  "resolved" "http://registry.npm.taobao.org/regexpp/download/regexpp-1.1.0.tgz"
  "version" "1.1.0"

"regexpu-core@^1.0.0":
  "integrity" "sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs="
  "resolved" "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regexpu-core@^2.0.0":
  "integrity" "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA="
  "resolved" "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "http://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "http://registry.npm.taobao.org/regjsparser/download/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "http://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"relative@^3.0.2":
  "integrity" "sha1-Dc2OxUpdNaPBXhBFA9ZTdbWlNn8="
  "resolved" "http://registry.npm.taobao.org/relative/download/relative-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "isobject" "^2.0.0"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "http://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.1":
  "integrity" "sha1-EtMQ8lU2DAetj94lP2yeneNy0qo="
  "resolved" "http://registry.npm.taobao.org/renderkid/download/renderkid-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "css-select" "^1.1.0"
    "dom-converter" "~0.2"
    "htmlparser2" "~3.3.0"
    "strip-ansi" "^3.0.0"
    "utila" "^0.4.0"

"repeat-element@^1.1.2":
  "integrity" "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4="
  "resolved" "http://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz"
  "version" "1.1.3"

"repeat-string@^1.5.2", "repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "http://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"repeating@^2.0.0":
  "integrity" "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo="
  "resolved" "http://registry.npm.taobao.org/repeating/download/repeating-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-finite" "^1.0.0"

"request@^2.83.0", "request@^2.85.0":
  "integrity" "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg=="
  "resolved" "https://registry.npmjs.org/request/-/request-2.88.0.tgz"
  "version" "2.88.0"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.0"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.4.3"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "http://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^1.1.0":
  "integrity" "sha1-UpyczvJzgK3+yaL5ZbZJu+5jZBg="
  "resolved" "http://registry.npm.taobao.org/require-from-string/download/require-from-string-1.2.1.tgz"
  "version" "1.2.1"

"require-from-string@^2.0.1":
  "integrity" "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="
  "resolved" "http://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^1.0.1":
  "integrity" "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="
  "resolved" "http://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz"
  "version" "1.0.1"

"require-uncached@^1.0.3":
  "integrity" "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM="
  "resolved" "http://registry.npm.taobao.org/require-uncached/download/require-uncached-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "caller-path" "^0.1.0"
    "resolve-from" "^1.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "http://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.0":
  "integrity" "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="
  "resolved" "https://registry.npm.taobao.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^1.0.0":
  "integrity" "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY="
  "resolved" "http://registry.npm.taobao.org/resolve-from/download/resolve-from-1.0.1.tgz"
  "version" "1.0.1"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "http://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "http://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.1.6", "resolve@^1.3.3", "resolve@^1.4.0", "resolve@^1.5.0", "resolve@^1.6.0":
  "integrity" "sha1-gvHsGaQjrB+9CAsLqwa6NuhKeiY="
  "resolved" "http://registry.npm.taobao.org/resolve/download/resolve-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "path-parse" "^1.0.5"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "http://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "http://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"right-align@^0.1.1":
  "integrity" "sha1-YTObci/mo1FWiSENJOFMlhSGE+8="
  "resolved" "http://registry.npm.taobao.org/right-align/download/right-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.1"

"rimraf@^2.2.8", "rimraf@^2.5.4", "rimraf@^2.6.0", "rimraf@^2.6.1", "rimraf@^2.6.2":
  "integrity" "sha1-LtgVDSShbqhlHm1u8PR8QVjOejY="
  "resolved" "http://registry.npm.taobao.org/rimraf/download/rimraf-2.6.2.tgz"
  "version" "2.6.2"
  dependencies:
    "glob" "^7.0.5"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "http://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"run-async@^2.2.0":
  "integrity" "sha1-A3GrSuC91yDUFm19/aZP96RFpsA="
  "resolved" "http://registry.npm.taobao.org/run-async/download/run-async-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "is-promise" "^2.1.0"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "http://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rx-lite-aggregates@^4.0.8":
  "integrity" "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74="
  "resolved" "http://registry.npm.taobao.org/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "rx-lite" "*"

"rx-lite@*", "rx-lite@^4.0.8":
  "integrity" "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ="
  "resolved" "http://registry.npm.taobao.org/rx-lite/download/rx-lite-4.0.8.tgz"
  "version" "4.0.8"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "http://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "http://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@^1.2.4":
  "version" "1.2.4"

"sax@~1.2.1":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "http://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^0.3.0":
  "integrity" "sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8="
  "resolved" "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "ajv" "^5.0.0"

"schema-utils@^0.4.0":
  "integrity" "sha1-unT1l9K+LqiAExdG7hfQoJPGgYc="
  "resolved" "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^0.4.5":
  "integrity" "sha1-unT1l9K+LqiAExdG7hfQoJPGgYc="
  "resolved" "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"semver@^5.3.0", "semver@^5.4.1", "semver@^5.6.0", "semver@2 || 3 || 4 || 5":
  "integrity" "sha1-fnQlb7qknHWqfHogXMInmcrIAAQ="
  "resolved" "http://registry.npm.taobao.org/semver/download/semver-5.6.0.tgz"
  "version" "5.6.0"

"send@0.16.2":
  "integrity" "sha1-bsyh4PjBVtFBWXVZhI32RzCmu8E="
  "resolved" "http://registry.npm.taobao.org/send/download/send-0.16.2.tgz"
  "version" "0.16.2"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.6.2"
    "mime" "1.4.1"
    "ms" "2.0.0"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.0"
    "statuses" "~1.4.0"

"serialize-javascript@^1.4.0":
  "integrity" "sha1-GqM2FiyIqJDdrVOEuuvJOmVRYf4="
  "resolved" "http://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-1.5.0.tgz"
  "version" "1.5.0"

"serve-static@1.13.2":
  "integrity" "sha1-CV6Ecv1bRiN9tQzkhqQ/S4bGzsE="
  "resolved" "http://registry.npm.taobao.org/serve-static/download/serve-static-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.2"
    "send" "0.16.2"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "http://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-blocking@~2.0.0":
  "version" "2.0.0"

"set-value@^0.4.3":
  "integrity" "sha1-fbCPnT0i3H945Trzw79GZuzfzPE="
  "resolved" "http://registry.npm.taobao.org/set-value/download/set-value-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.1"
    "to-object-path" "^0.3.0"

"set-value@^2.0.0":
  "integrity" "sha1-ca5KiPD+77v1LR6mBPP7MV67YnQ="
  "resolved" "http://registry.npm.taobao.org/set-value/download/set-value-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "http://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "http://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "http://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "http://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shelljs@^0.8.1":
  "integrity" "sha1-NFt993Y/TCNA1YSrtTLF91LKnjU="
  "resolved" "http://registry.npm.taobao.org/shelljs/download/shelljs-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "glob" "^7.0.0"
    "interpret" "^1.0.0"
    "rechoir" "^0.6.2"

"sigmund@^1.0.1":
  "integrity" "sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA="
  "resolved" "http://registry.npm.taobao.org/sigmund/download/sigmund-1.0.1.tgz"
  "version" "1.0.1"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="
  "resolved" "http://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz"
  "version" "3.0.2"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "http://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz"
  "version" "1.0.0"

"slice-ansi@1.0.0":
  "integrity" "sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00="
  "resolved" "http://registry.npm.taobao.org/slice-ansi/download/slice-ansi-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "http://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "http://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "http://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "http://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "http://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-resolve@^0.5.0", "source-map-resolve@^0.5.2":
  "integrity" "sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk="
  "resolved" "http://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "atob" "^2.1.1"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.4.15":
  "integrity" "sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8="
  "resolved" "http://registry.npm.taobao.org/source-map-support/download/source-map-support-0.4.18.tgz"
  "version" "0.4.18"
  dependencies:
    "source-map" "^0.5.6"

"source-map-url@^0.4.0":
  "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
  "resolved" "http://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz"
  "version" "0.4.0"

"source-map@^0.5.0", "source-map@^0.5.3", "source-map@^0.5.6", "source-map@^0.5.7", "source-map@~0.5.1":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "http://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.1", "source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spdx-correct@^3.0.0":
  "integrity" "sha1-GbtAnpG0exrVQVkkP3MSqFjbPC4="
  "resolved" "http://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc="
  "resolved" "http://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz"
  "version" "2.2.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-meEZt6XaAOBUkcn6M4t5BII7QdA="
  "resolved" "http://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-pZ78CXhMKlutoTz+r1x13SFARNI="
  "resolved" "http://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.2.tgz"
  "version" "3.0.2"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "http://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "http://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha512-Ra/OXQtuh0/enyl4ETZAfTaeksa6BXks5ZcjpSUNrjBr0DvrJKX+1fsKDPpT9TBXgHAFsa4510aNVgI8g/+SzA=="
  "resolved" "https://registry.npmjs.org/sshpk/-/sshpk-1.15.2.tgz"
  "version" "1.15.2"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssri@^5.2.4":
  "integrity" "sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY="
  "resolved" "http://registry.npm.taobao.org/ssri/download/ssri-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "safe-buffer" "^5.1.1"

"stackframe@^1.0.4":
  "integrity" "sha1-NXskqZL5Qny6a1RdlqFO0svKGHs="
  "resolved" "http://registry.npm.taobao.org/stackframe/download/stackframe-1.0.4.tgz"
  "version" "1.0.4"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "http://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@~1.4.0":
  "integrity" "sha1-u3PURtonlhBu/MG2AaJT1sRr0Ic="
  "resolved" "http://registry.npm.taobao.org/statuses/download/statuses-1.4.0.tgz"
  "version" "1.4.0"

"stream-browserify@^2.0.1":
  "integrity" "sha1-ZiZu5fm9uZQKTkUUyvtDu3Hlyds="
  "resolved" "http://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "http://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "http://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI="
  "resolved" "http://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.0.tgz"
  "version" "1.0.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "http://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "http://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string_decoder@~0.10.x":
  "integrity" "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="
  "resolved" "http://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz"
  "version" "0.10.31"

"string-width@^1.0.1", "string-width@^1.0.2":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "http://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.0.0", "string-width@^2.1.0", "string-width@^2.1.1":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "http://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"strip-ansi@^0.3.0":
  "integrity" "sha1-JfSOoiynkYfzF0pNuHWTR7sSYiA="
  "resolved" "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "ansi-regex" "^0.2.1"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "http://registry.npm.taobao.org/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "http://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-json-comments@~2.0.1":
  "integrity" "sha1-PFMZQukIwml8DsNEhYwobHygpgo="
  "resolved" "http://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz"
  "version" "2.0.1"

"supports-color@^0.2.0":
  "integrity" "sha1-2S3iaU6z9nMjlz1649i1W0wiGQo="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-0.2.0.tgz"
  "version" "0.2.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^4.2.1":
  "integrity" "sha1-vnoN5ITexcXN34s9WRJQRJEvY1s="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "has-flag" "^2.0.0"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^5.4.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "http://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"svgo@^0.7.0":
  "integrity" "sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U="
  "resolved" "http://registry.npm.taobao.org/svgo/download/svgo-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "coa" "~1.0.1"
    "colors" "~1.1.2"
    "csso" "~2.3.1"
    "js-yaml" "~3.7.0"
    "mkdirp" "~0.5.1"
    "sax" "~1.2.1"
    "whet.extend" "~0.9.9"

"table@4.0.2":
  "integrity" "sha1-ozRHN1OR52atNNNIbm4q7chNLjY="
  "resolved" "http://registry.npm.taobao.org/table/download/table-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "ajv" "^5.2.3"
    "ajv-keywords" "^2.1.0"
    "chalk" "^2.1.0"
    "lodash" "^4.17.4"
    "slice-ansi" "1.0.0"
    "string-width" "^2.1.1"

"tapable@^0.2.7":
  "integrity" "sha1-mTcqXJmb8t8WCvwNdL7U9HlIzSI="
  "resolved" "http://registry.npm.taobao.org/tapable/download/tapable-0.2.8.tgz"
  "version" "0.2.8"

"tapable@^1.0.0":
  "integrity" "sha1-DQdqFy49m6CI/SJysmaPuNGUt4w="
  "resolved" "http://registry.npm.taobao.org/tapable/download/tapable-1.1.0.tgz"
  "version" "1.1.0"

"tar@^4":
  "version" "4.4.1"
  dependencies:
    "chownr" "^1.0.1"
    "fs-minipass" "^1.2.5"
    "minipass" "^2.2.4"
    "minizlib" "^1.1.0"
    "mkdirp" "^0.5.0"
    "safe-buffer" "^5.1.1"
    "yallist" "^3.0.2"

"text-table@^0.2.0", "text-table@~0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "http://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throttle-debounce@^1.0.1":
  "integrity" "sha1-UYU9o3vmihVctugns1FKPEIuic0="
  "resolved" "https://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz"
  "version" "1.1.0"

"through@^2.3.6":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "http://registry.npm.taobao.org/through/download/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha1-AARWmzfHx0ujnEPzzteNGtlBQL4="
  "resolved" "http://registry.npm.taobao.org/through2/download/through2-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "readable-stream" "^2.1.5"
    "xtend" "~4.0.1"

"time-stamp@^2.0.0":
  "integrity" "sha1-kX4KZpBWiHkOx7u94EBGJZr4P1c="
  "resolved" "http://registry.npm.taobao.org/time-stamp/download/time-stamp-2.2.0.tgz"
  "version" "2.2.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-HSjj0qrfHVpZlsTp+VYBzQU0gK4="
  "resolved" "http://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.10.tgz"
  "version" "2.0.10"
  dependencies:
    "setimmediate" "^1.0.4"

"tmp@^0.0.33":
  "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
  "resolved" "http://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "http://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "http://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "http://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "http://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "http://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "http://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "http://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.4.3":
  "integrity" "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "psl" "^1.1.24"
    "punycode" "^1.4.1"

"trim-right@^1.0.1":
  "integrity" "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM="
  "resolved" "http://registry.npm.taobao.org/trim-right/download/trim-right-1.0.1.tgz"
  "version" "1.0.1"

"tryer@^1.0.0":
  "integrity" "sha1-8shUBoALmw90yfdGW4HqrSQSUvg="
  "resolved" "http://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz"
  "version" "1.0.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "http://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "http://registry.npm.taobao.org/type-check/download/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-is@~1.6.16":
  "integrity" "sha1-+JzjQVQcZysl7nrjxz3uOyvlAZQ="
  "resolved" "http://registry.npm.taobao.org/type-is/download/type-is-1.6.16.tgz"
  "version" "1.6.16"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.18"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "http://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-es@^3.3.4":
  "integrity" "sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc="
  "resolved" "http://registry.npm.taobao.org/uglify-es/download/uglify-es-3.3.9.tgz"
  "version" "3.3.9"
  dependencies:
    "commander" "~2.13.0"
    "source-map" "~0.6.1"

"uglify-js@^2.8.29":
  "integrity" "sha1-KcVzMUgFe7Th913zW3qcty5qWd0="
  "resolved" "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.8.29.tgz"
  "version" "2.8.29"
  dependencies:
    "source-map" "~0.5.1"
    "yargs" "~3.10.0"
  optionalDependencies:
    "uglify-to-browserify" "~1.0.0"

"uglify-js@3.4.x":
  "integrity" "sha1-rwLxgMEgfXZDLkc+0koo9KeCuuM="
  "resolved" "http://registry.npm.taobao.org/uglify-js/download/uglify-js-3.4.9.tgz"
  "version" "3.4.9"
  dependencies:
    "commander" "~2.17.1"
    "source-map" "~0.6.1"

"uglify-to-browserify@~1.0.0":
  "integrity" "sha1-bgkk1r2mta/jSeOabWMoUKD4grc="
  "resolved" "http://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz"
  "version" "1.0.2"

"uglifyjs-webpack-plugin@^0.4.6":
  "integrity" "sha1-uVH0q7a9YX5m9j64kUmOORdj4wk="
  "resolved" "http://registry.npm.taobao.org/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-0.4.6.tgz"
  "version" "0.4.6"
  dependencies:
    "source-map" "^0.5.6"
    "uglify-js" "^2.8.29"
    "webpack-sources" "^1.0.1"

"uglifyjs-webpack-plugin@^1.2.5":
  "integrity" "sha1-dfVIFghYFjoIZD4IbV/v4YpdZ94="
  "resolved" "http://registry.npm.taobao.org/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "cacache" "^10.0.4"
    "find-cache-dir" "^1.0.0"
    "schema-utils" "^0.4.5"
    "serialize-javascript" "^1.4.0"
    "source-map" "^0.6.1"
    "uglify-es" "^3.3.4"
    "webpack-sources" "^1.1.0"
    "worker-farm" "^1.5.2"

"union-value@^1.0.0":
  "integrity" "sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ="
  "resolved" "http://registry.npm.taobao.org/union-value/download/union-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^0.4.3"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "http://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "http://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.0":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "http://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-Xp7cbRzo+yZNsYpQfvm9hURFHKY="
  "resolved" "http://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "imurmurhash" "^0.1.4"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "http://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "http://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.0.5", "upath@^1.1.0":
  "integrity" "sha1-NSVll+RqWB20eT0M5H+prr/J+r0="
  "resolved" "http://registry.npm.taobao.org/upath/download/upath-1.1.0.tgz"
  "version" "1.1.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "http://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA="
  "resolved" "http://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "http://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^1.0.1":
  "integrity" "sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng="
  "resolved" "http://registry.npm.taobao.org/url-loader/download/url-loader-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "loader-utils" "^1.1.0"
    "mime" "^2.0.3"
    "schema-utils" "^1.0.0"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "http://registry.npm.taobao.org/url/download/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "http://registry.npm.taobao.org/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "http://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "http://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.10.3":
  "integrity" "sha1-OqASW/5mikZy3liFfTrOJ+y3aQE="
  "resolved" "http://registry.npm.taobao.org/util/download/util-0.10.4.tgz"
  "version" "0.10.4"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "http://registry.npm.taobao.org/util/download/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@^0.4.0", "utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "http://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "http://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2":
  "integrity" "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz"
  "version" "3.3.2"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "http://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "http://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha1-f8te759WI7FWvOqJ7DfWNnbyGAE="
  "resolved" "http://registry.npm.taobao.org/vendors/download/vendors-1.0.2.tgz"
  "version" "1.0.2"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@0.0.4":
  "integrity" "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM="
  "resolved" "http://registry.npm.taobao.org/vm-browserify/download/vm-browserify-0.0.4.tgz"
  "version" "0.0.4"
  dependencies:
    "indexof" "0.0.1"

"vue-hot-reload-api@^2.1.0", "vue-hot-reload-api@^2.2.0":
  "integrity" "sha1-stPZVAKoEWAjgHg+pPVm64dVaaI="
  "resolved" "http://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.1.tgz"
  "version" "2.3.1"

"vue-loader@^13.0.4":
  "integrity" "sha1-4HRA94IwpjnQCtpNp7ltDp1iA38="
  "resolved" "http://registry.npm.taobao.org/vue-loader/download/vue-loader-13.7.3.tgz"
  "version" "13.7.3"
  dependencies:
    "consolidate" "^0.14.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "lru-cache" "^4.1.1"
    "postcss" "^6.0.8"
    "postcss-load-config" "^1.1.0"
    "postcss-selector-parser" "^2.0.0"
    "prettier" "^1.7.0"
    "resolve" "^1.4.0"
    "source-map" "^0.6.1"
    "vue-hot-reload-api" "^2.2.0"
    "vue-style-loader" "^3.0.0"
    "vue-template-es2015-compiler" "^1.6.0"

"vue-style-loader@^3.0.0":
  "integrity" "sha1-a2atNJmPyVIMLx5NX6QJFkHBWXo="
  "resolved" "http://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-style-loader@^4.1.0":
  "integrity" "sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg="
  "resolved" "http://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-es2015-compiler@^1.5.3", "vue-template-es2015-compiler@^1.6.0":
  "integrity" "sha1-3EJpcTMwLOMBdSQ1amxht7abShg="
  "resolved" "http://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.6.0.tgz"
  "version" "1.6.0"

"vuex@^3.0.1":
  "integrity" "sha1-52E1Lr4K9TfUu3Vam53Evj3379I="
  "resolved" "http://registry.npm.taobao.org/vuex/download/vuex-3.0.1.tgz"
  "version" "3.0.1"

"watchpack@^1.4.0":
  "integrity" "sha1-S8EsLr6KonenHx0/FNaFx7RGzQA="
  "resolved" "http://registry.npm.taobao.org/watchpack/download/watchpack-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "chokidar" "^2.0.2"
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "http://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webpack-bundle-analyzer@^2.2.1":
  "integrity" "sha1-B9IXbG6Gw83OTCPlb64qe2tK1SY="
  "resolved" "http://registry.npm.taobao.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "acorn" "^5.3.0"
    "bfj-node4" "^5.2.0"
    "chalk" "^2.3.0"
    "commander" "^2.13.0"
    "ejs" "^2.5.7"
    "express" "^4.16.2"
    "filesize" "^3.5.11"
    "gzip-size" "^4.1.0"
    "lodash" "^4.17.4"
    "mkdirp" "^0.5.1"
    "opener" "^1.4.3"
    "ws" "^4.0.0"

"webpack-dev-middleware-hard-disk@^1.12.0":
  "integrity" "sha1-IKwaPXAIqIr3bsP6OmsQfiiws6Y="
  "resolved" "http://registry.npm.taobao.org/webpack-dev-middleware-hard-disk/download/webpack-dev-middleware-hard-disk-1.12.1.tgz"
  "version" "1.12.1"
  dependencies:
    "mime" "^1.3.4"
    "path-is-absolute" "^1.0.0"
    "range-parser" "^1.0.3"
    "time-stamp" "^2.0.0"

"webpack-merge@^4.1.0":
  "integrity" "sha1-D9446r8tX9hSUcJKWoxI+KP063s="
  "resolved" "http://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "lodash" "^4.17.5"

"webpack-mpvue-asset-plugin@^0.1.1":
  "integrity" "sha1-mvRE7qmwmuz6MZgaDPKlel0b9xQ="
  "resolved" "http://registry.npm.taobao.org/webpack-mpvue-asset-plugin/download/webpack-mpvue-asset-plugin-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "relative" "^3.0.2"
    "upath" "^1.1.0"

"webpack-sources@^1.0.1", "webpack-sources@^1.1.0":
  "integrity" "sha1-KijcufH0X+lg2PFJMlK17mUw+oU="
  "resolved" "http://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0", "webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^1.9 || ^2 || ^2.1.0-beta || ^2.2.0-rc || ^3.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.0.0", "webpack@^3.1.0", "webpack@^3.11.0", "webpack@>=2.0.0 <5.0.0", "webpack@2 || 3 || 4":
  "integrity" "sha1-P540NgNwYC/PY56Xk520hvTsDXQ="
  "resolved" "http://registry.npm.taobao.org/webpack/download/webpack-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "acorn" "^5.0.0"
    "acorn-dynamic-import" "^2.0.0"
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"
    "async" "^2.1.2"
    "enhanced-resolve" "^3.4.0"
    "escope" "^3.6.0"
    "interpret" "^1.0.0"
    "json-loader" "^0.5.4"
    "json5" "^0.5.1"
    "loader-runner" "^2.3.0"
    "loader-utils" "^1.1.0"
    "memory-fs" "~0.4.1"
    "mkdirp" "~0.5.0"
    "node-libs-browser" "^2.0.0"
    "source-map" "^0.5.3"
    "supports-color" "^4.2.1"
    "tapable" "^0.2.7"
    "uglifyjs-webpack-plugin" "^0.4.6"
    "watchpack" "^1.4.0"
    "webpack-sources" "^1.0.1"
    "yargs" "^8.0.2"

"whet.extend@~0.9.9":
  "integrity" "sha1-+HfVv2SMl+WqVC+twW1qJZucEaE="
  "resolved" "http://registry.npm.taobao.org/whet.extend/download/whet.extend-0.9.9.tgz"
  "version" "0.9.9"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "http://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "http://registry.npm.taobao.org/which/download/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"wide-align@^1.1.0":
  "version" "1.1.2"
  dependencies:
    "string-width" "^1.0.2"

"window-size@0.1.0":
  "integrity" "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0="
  "resolved" "http://registry.npm.taobao.org/window-size/download/window-size-0.1.0.tgz"
  "version" "0.1.0"

"wordwrap@~1.0.0":
  "integrity" "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="
  "resolved" "http://registry.npm.taobao.org/wordwrap/download/wordwrap-1.0.0.tgz"
  "version" "1.0.0"

"wordwrap@0.0.2":
  "integrity" "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8="
  "resolved" "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.2.tgz"
  "version" "0.0.2"

"worker-farm@^1.5.2":
  "integrity" "sha1-rsxAWXb6talVJhgIRvDboojzpKA="
  "resolved" "http://registry.npm.taobao.org/worker-farm/download/worker-farm-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^2.0.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "http://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "http://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write@^0.2.1":
  "integrity" "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c="
  "resolved" "http://registry.npm.taobao.org/write/download/write-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^4.0.0":
  "integrity" "sha1-qXm119TaaL9U7+BAiWfDJIaacok="
  "resolved" "http://registry.npm.taobao.org/ws/download/ws-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "async-limiter" "~1.0.0"
    "safe-buffer" "~5.1.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-pcbVMr5lbiPbgg77lDofBJmNY68="
  "resolved" "http://registry.npm.taobao.org/xtend/download/xtend-4.0.1.tgz"
  "version" "4.0.1"

"y18n@^3.2.1":
  "integrity" "sha1-bRX7qITAhnnA136I53WegR4H+kE="
  "resolved" "http://registry.npm.taobao.org/y18n/download/y18n-3.2.1.tgz"
  "version" "3.2.1"

"y18n@^4.0.0":
  "integrity" "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="
  "resolved" "http://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz"
  "version" "4.0.0"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.0", "yallist@^3.0.2":
  "version" "3.0.2"

"yargs-parser@^7.0.0":
  "integrity" "sha1-jQrELxbqVd69MyyvTEA4s+P139k="
  "resolved" "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "camelcase" "^4.1.0"

"yargs@^8.0.2":
  "integrity" "sha1-YpmpBVsc78lp/355wdkY3Osiw2A="
  "resolved" "http://registry.npm.taobao.org/yargs/download/yargs-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "camelcase" "^4.1.0"
    "cliui" "^3.2.0"
    "decamelize" "^1.1.1"
    "get-caller-file" "^1.0.1"
    "os-locale" "^2.0.0"
    "read-pkg-up" "^2.0.0"
    "require-directory" "^2.1.1"
    "require-main-filename" "^1.0.1"
    "set-blocking" "^2.0.0"
    "string-width" "^2.0.0"
    "which-module" "^2.0.0"
    "y18n" "^3.2.1"
    "yargs-parser" "^7.0.0"

"yargs@~3.10.0":
  "integrity" "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E="
  "resolved" "http://registry.npm.taobao.org/yargs/download/yargs-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "camelcase" "^1.0.2"
    "cliui" "^2.1.0"
    "decamelize" "^1.0.0"
    "window-size" "0.1.0"
