/*
 * @Author: your name
 * @Date: 2020-10-30 10:09:01
 * @LastEditTime: 2020-11-23 14:23:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \baoyan-frontend\config\dev.env.js
 */
var merge = require('webpack-merge')
var prodEnv = require('./prod.env')

module.exports = merge(prodEnv, {
    NODE_ENV: '"development"',
    // baseURL: "'https://api.baoyan.llons.com/api/'",
       baseURL: "'http://wx.byloft.net/api'",
  // baseURL: "'https://wx.byloft.net/v2/api'",
  // baseURL: "'http://127.0.0.1:10159/api'",
    // baseURL: "'https://liguangd-wechat-baoyan.f.wmeimob.com/api'"
    // socktURl:"'wss://api.baoyan.llons.com'"
})
