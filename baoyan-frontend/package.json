{"name": "masa-store", "version": "1.0.0", "description": "A Mpvue project", "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev:wx": "node build/dev-server.js wx", "start:wx": "npm run dev:wx", "build:wx": "node build/build.js wx", "dev:swan": "node build/dev-server.js swan", "start:swan": "npm run dev:swan", "build:swan": "node build/build.js swan", "dev": "node build/dev-server.js wx", "start": "npm run dev", "build": "node build/build.js wx", "lint": "eslint --ext .js,.vue src"}, "dependencies": {"element-ui": "^2.15.3", "flyio": "^0.6.2", "moment": "^2.22.2", "mpvue": "^1.0.11", "mpvue-wxparse": "^0.6.5", "mpvue-wxparse2": "0.0.2", "vuex": "^3.0.1"}, "devDependencies": {"babel-core": "^6.22.1", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.4.0", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.5.1", "css-loader": "^0.28.11", "cssnano": "^3.10.0", "eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.0.0", "eslint-plugin-html": "^4.0.3", "eslint-plugin-import": "^2.11.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.16.3", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.7.0", "glob": "^7.1.2", "html-webpack-plugin": "^3.2.0", "http-proxy-middleware": "^0.18.0", "less": "^3.8.1", "less-loader": "^4.1.0", "mpvue-loader": "^1.1.2", "mpvue-template-compiler": "^1.0.11", "mpvue-webpack-target": "^1.0.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^2.0.0", "portfinder": "^1.0.13", "postcss-loader": "^2.1.4", "postcss-mpvue-wxss": "^1.0.0", "prettier": "~1.12.1", "px2rpx-loader": "^0.1.10", "relative": "^3.0.2", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.8.1", "uglifyjs-webpack-plugin": "^1.2.5", "url-loader": "^1.0.1", "vue-style-loader": "^4.1.0", "webpack": "^3.11.0", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware-hard-disk": "^1.12.0", "webpack-merge": "^4.1.0", "webpack-mpvue-asset-plugin": "^0.1.1"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}