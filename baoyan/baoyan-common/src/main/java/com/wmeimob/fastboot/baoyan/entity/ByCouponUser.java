/*
 * ByCouponUser.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 11 17:28:52 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_coupon_user")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ByCouponUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * UserId
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 优惠券ID
     */
    @Column(name = "coupon_id")
    private Integer couponId;

    /**
     *0是全部商品，大于0 是分类ID或者商品ID
     */
    @Column(name = "target_id")
    private Integer targetId;
    /**
     * 0 全部门票可用 1 指定分类门票 2 指定门票
     * 3.淘潮玩指定品牌 4.淘潮玩指定品类
     * 5.淘潮玩指定商品 6.全部淘潮玩可用 7.全部门票和淘潮玩可用
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 优惠券名
     */
    @Column(name = "name")
    private String name;
    /**
     * 折扣金额
     */
    @Column(name = "discount")
    private BigDecimal discount;
    /**
     * 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）
     */
    @Column(name = "full")
    private BigDecimal full;
    /**
     * 有效期
     */
    @Column(name = "start_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;
    /**
     * 有效期
     */
    @Column(name = "end_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;
    /**
     * 0未使用，1使用，2过期
     */
    @Column(name = "is_use")
    private Integer isUse;
    /**
     * 是否赠送优惠券 0 否 1是
     */
    @Column(name = "is_give")
    private Integer isGive;
    /**
     * 导入记录
     */
    @Column(name = "in_batch")
    private String inBatch;
    /**
     * 领取方式，1后台发放，2前端领取
     */
    @Column(name = "get_type")
    private Integer getType;
    /**
     * 使用时间
     */
    @Column(name = "use_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date useDate;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 审核状态  0 待审核 1已通过 2已拒绝
     */
    @Column(name = "audit_status")
    private Integer auditStatus;
    /**
     * 审核人
     */
    @Column(name = "audit_user_id")
    private Integer auditUserId;
    /**
     * 发放人
     */
    @Column(name = "issuer_user_id")
    private Integer issuerUserId;
    /**
     * 发放人
     */
    @Column(name = "audit_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;
    /**
     * 拒绝理由
     */
    @Column(name = "refuse_reason")
    private String refuseReason;
    /**
     * 单个商品类型 1 普通商品 2 次卡 3 联票
     */
    @Column(name = "single_goods_type")
    private Integer singleGoodsType;
    @Transient
    private String mobile;
    @Transient
    private String startTime;
    @Transient
    private String endTime;
    @Transient
    private Integer canUse;
    @Transient
    private String ids;
    @Transient
    private String nickName;
    /**
     * 发放人名称
     */
    @Transient
    private String issuerUserName;
    /**
     * 审核人名称
     */
    @Transient
    private String auditName;
    /**
     * 优惠券适用范围
     */
    @Transient
    private String limitation;

    @Transient
    private String goodsName;
    @Column(name = "coupon_type")
    private Integer couponType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ByCouponUser that = (ByCouponUser) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}