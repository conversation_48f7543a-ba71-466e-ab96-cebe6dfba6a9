package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByCustAppointmentService
 * @Description 客户预约表
 * <AUTHOR>
 * @Date Tue Jul 09 14:34:33 CST 2019
 * @version1.0
 **/
public interface ByCustAppointmentService extends CommonService<ByCustAppointment>{

    /**
     * 客户预约表查询
     * @param id
     * @return
     */
    default ByCustAppointment queryByCustAppointmentById(Object id){throw new NotImplementedException("queryByCustAppointmentById");};

    /**
     * 客户预约表添加
     * @param  byCustAppointment
     * @return
     */
    default  void addByCustAppointment(ByCustAppointment byCustAppointment){throw new NotImplementedException("addByCustAppointment");};


    /**
     * 客户预约表删除
     * @param id
     * @return
     */
    default void removeByCustAppointment(Object id){throw new NotImplementedException("removeByCustAppointment");};


    /**
     * 客户预约表修改
     * @param byCustAppointment
     * @return
     */
    default void modifyByCustAppointment(ByCustAppointment byCustAppointment){throw new NotImplementedException("modifyByCustAppointment");};
    /**
     * 列表查询
     * @param byCustAppointment
     * @return
     */
    default List<ByCustAppointmentVO> queryAppoinmentList(ByCustAppointment byCustAppointment){throw new NotImplementedException("queryAppoinmentList");};

    /**
     * 取消预约
     * @param id
     */
    default RestResult cancelAppointment(Integer id){throw new NotImplementedException("cancelAppointment");};
}
