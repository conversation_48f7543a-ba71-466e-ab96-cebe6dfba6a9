package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 淘潮玩品牌表(TcBrand)实体类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TcBrand implements Serializable {
    private static final long serialVersionUID = -29389397877905082L;
    /**
    * id自增，无意义
    */
    private Integer id;
    /**
    * 品牌名
    */
    private String name;
    /**
    * 备注
    */
    private String remark;

    /**
     * 逻辑删除 1：已经删除  0 ：未删除
     */
    private Boolean isDel;

    /**
     * 品牌图片
     */
    private String img;
}