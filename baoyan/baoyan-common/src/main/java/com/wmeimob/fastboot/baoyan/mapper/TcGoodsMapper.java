package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * (TcGoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcGoodsMapper extends BaseMapper<TcGoods> {


    int reduceStock(@Param("id") Integer id, @Param("change") Integer change);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcGoods queryById(Integer id);

    TcGoods wxQueryById(@Param("id")Integer id);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcGoods 实例对象
     * @return 对象列表
     */
    List<TcGoods> queryAll(TcGoods tcGoods);

    /**
     * 小程序 前端
     */
    List<TcGoods> wxQueryAll(TcGoods tcGoods);

    /**
     * 统计 数量
     * @param tcGoods
     * @return
     */
    Long queryAllCount(TcGoods tcGoods);
    /**
     * 小程序 统计 数量
     * @param tcGoods
     * @return
     */
    Long wxQueryAllCount(TcGoods tcGoods);

    /**
     * 新增数据
     *
     * @param tcGoods 实例对象
     * @return 影响行数
     */
    int insert(TcGoods tcGoods);

    /**
     * 修改数据
     *
     * @param tcGoods 实例对象
     * @return 影响行数
     */
    int update(TcGoods tcGoods);


    /**
     * 根据id做in 查询
     * @param list
     * @return
     */
    List<TcGoods> findByIds(@Param("list") List<Integer> list);

    /**
     * 增加商品的销量
     * @param id
     * @param sellCount
     * @return
     */
    boolean addSellCount(@Param("id")int id, @Param("sellCount")int sellCount);

}