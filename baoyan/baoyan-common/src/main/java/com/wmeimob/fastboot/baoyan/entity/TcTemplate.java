package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


import java.io.Serializable;

/**
 * (TcTemplate)表实体类
 *
 * <AUTHOR>
 * @since 2021-07-20 18:49:49
 */
@Table(name = "tc_template")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TcTemplate implements Serializable {
    //推荐模块id

    private Integer id;
    /*
    * 模块名称
     */

    private String name;
    /**
     * 0横向 1纵向默认横向
     */
    private Boolean way;
    /**
     * 排序值
     */
    private Integer sort;
    /**
     * 跳转类型(
     * 1.商品分类2.联票列表3.次卡列表
     * 4.拼团列表5.文章6.普通商品7.联票商品
     * 8.次卡商品9.拼团商品10.优惠券11.淘潮玩品类
     * 12.淘潮玩品牌 13.淘潮玩标签 14.淘潮玩首页头部
     * )
     */
    private Integer jumpType;
    /**
     * 跳转内容
     */
    private String target;
    //创建时间
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtUpdate;
    //1上架 0 下架  默认上架
    private Boolean isShelves;
    //1删除 0未删除
    private Boolean isDel;
    //1 商城首页 0 淘潮玩主页 默认 商城首页
    private Boolean isHome;

    @Transient
    private String baseBanner;
}
