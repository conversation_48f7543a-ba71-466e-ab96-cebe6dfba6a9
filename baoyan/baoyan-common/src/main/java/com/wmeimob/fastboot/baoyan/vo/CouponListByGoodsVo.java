package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品查看优惠卷
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponListByGoodsVo implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * 优惠金额
     */
    private BigDecimal discount;
    /**
     * 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）
     */
    private BigDecimal full;
    /**
     * 1优惠券（抵扣卷） 2折扣卷
     */
    private Integer couponType;
    /**
     * 有效类型 1 日期范围 2天数
     */
    private Integer effectiveType;
    /**
     * 天数
     */
    private Integer dayNum;
    /**
     * 有效期
     */
    private Date startDate;
    /**
     * 有效期
     */
    private Date endDate;
}
