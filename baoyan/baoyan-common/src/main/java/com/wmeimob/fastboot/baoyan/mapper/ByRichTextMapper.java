
/*
* ByRichTextMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 30 09:01:33 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByRichText;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByRichTextMapper extends Mapper<ByRichText> {
	/**
	 * 修改富文本  根据 dataId type
	 * @param richText
	 * @return
	 */
	int updateByDataIdAndType(ByRichText richText);
}