<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByGoodsShopingMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByGoodsShoping" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="goodsType" column="goods_type" jdbcType="INTEGER"/>
		<result property="goodsCount" column="goods_count" jdbcType="INTEGER"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="marketPrice" column="market_price" jdbcType="VARCHAR"/>
		<result property="sellPrice" column="sell_price" jdbcType="VARCHAR"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="type" column="type" jdbcType="VARCHAR"/>
		<result property="goodsStock" column="goodsStock" jdbcType="VARCHAR"/>
		<result property="isDel" column="is_del" jdbcType="INTEGER"/>
		<result property="limited" column="limited" jdbcType="INTEGER"/>
    </resultMap>

	<select id="show" resultMap="BaseResultMap">
SELECT
	b.id,
	b.gmt_create,
	b.goods_type,
	b.goods_id,
	b.goods_id,
	b.goods_count,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT market_price FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT market_price FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 3 THEN
	( SELECT market_price FROM by_ticket_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT '' FROM by_combination_goods o WHERE o.id = b.goods_id )
	END AS market_price,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT limited FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT limited FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT limited FROM by_combination_goods o WHERE o.id = b.goods_id )
	END AS limited,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT goods_stock FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT goods_stock FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 3 THEN
	( SELECT goods_stock FROM by_ticket_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT goods_stock FROM by_combination_goods o WHERE o.id = b.goods_id )
	END AS goodsStock,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT sell_price FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT sell_price FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 3 THEN
	( SELECT sell_price FROM by_ticket_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT price FROM by_combination_goods o WHERE o.id = b.goods_id )
	END AS sell_price,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT goods_name FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT sub_card_goods_name FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 3 THEN
	( SELECT ticket_goods_name FROM by_ticket_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT name FROM by_combination_goods o WHERE o.id = b.goods_id )
	END AS goods_name,
CASE
	b.goods_type
	WHEN 1 THEN
	( SELECT goods_img FROM by_goods_info o WHERE o.id = b.goods_id )
	WHEN 2 THEN
	( SELECT goods_img FROM by_sub_card_goods o WHERE o.id = b.goods_id )
	WHEN 3 THEN
	( SELECT goods_img FROM by_ticket_goods o WHERE o.id = b.goods_id )
	WHEN 5 THEN
	( SELECT o.goods_img FROM by_goods_info o WHERE o.id = (SELECT goods_id FROM by_combination_goods WHERE id = b.goods_id))
	END AS goods_img,
	ifnull (
CASE
	b.goods_type
	WHEN 1 THEN
	(
SELECT
	ifnull( goods_stock,0 )
FROM
	by_goods_info o WHERE o.id = b.goods_id
	)
	WHEN 2 THEN
	(
SELECT
	ifnull( goods_stock,0 )
FROM
	by_sub_card_goods o WHERE o.id = b.goods_id
	)
	WHEN 3 THEN
	(
SELECT
	ifnull( goods_stock,0 )
FROM
	by_ticket_goods o WHERE o.id = b.goods_id
	)
	WHEN 5 THEN
	(
SELECT
	ifnull( goods_stock,0 )
FROM
	by_combination_goods o WHERE o.id = b.goods_id
	)
	END,
	0
	) > 0 AS type
FROM
	by_goods_shoping b where user_id = #{id} and is_del = 0 order by id desc
	</select>
	<update id="update" >
		update by_goods_shoping set is_del = 1 where id = #{id}
	</update>
	<select id="selectShopCount" resultType="java.lang.Integer">
		select sum(ifnull(goods_count,0)) from by_goods_shoping where user_id = #{id} and is_del = 0
	</select>

	<select id="selectGoodsCount" resultType="java.lang.Integer">
		SELECT goods_count FROM by_goods_shoping WHERE user_id = #{userId} AND goods_type = #{type} AND goods_id = #{goodsId} AND is_del = 0
	</select>
</mapper>

