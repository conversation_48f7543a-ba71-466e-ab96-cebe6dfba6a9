/*
 * ByTeam.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 16 15:59:56 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_team")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByTeam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 拼团商品id
     */
    @Column(name = "team_goods_id")
    private Integer teamGoodsId;
    /**
     * 团长用户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;
    /**
     * 商品编号
     */
    @Column(name = "goods_no")
    private String goodsNo;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 拼团价
     */
    @Column(name = "team_price")
    private BigDecimal teamPrice;
    /**
     * 成团人数
     */
    @Column(name = "team_num")
    private Integer teamNum;
    /**
     * 拼团状态;-1:未支付(作废),0:拼团中,1:拼团成功,2:拼团失败.
     */
    @Column(name = "order_status")
    private Integer orderStatus;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 拼团结束时间
     */
    @Column(name = "end_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endDate;

    @Transient
    private Date endDate2;

    public Date getEndDate2() {
        return endDate;
    }

    public void setEndDate2(Date endDate2) {
        this.endDate2 = endDate2;
    }

    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 是否删除;1:是,0:否.
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 拼团名称
     */
    @Column(name = "team_name")
    private String teamName;

    /**
     * 开始查询时间
     */
    @Transient
    private Date startTime;
    /**
     * 结束时间
     */
    @Transient
    private Date endTime;
    /**
     * 当前拼团人数
     */
    @Transient
    private Integer nowPerson;
    /**
     * 差多少人
     */
    @Transient
    private Integer lessNum;
    /*昵称*/
    @Transient
    private String nickName;

    @Transient
    private String headImg;

    @Transient
    private String Remaining;
}