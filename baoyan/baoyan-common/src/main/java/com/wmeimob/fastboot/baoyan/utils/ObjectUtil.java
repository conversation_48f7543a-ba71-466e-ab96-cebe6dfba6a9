package com.wmeimob.fastboot.baoyan.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.lang.Nullable;
import java.io.IOException;

/**
 * 对象工具类
 */
public class ObjectUtil extends org.springframework.util.ObjectUtils {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T clone(T t){
        try {
            String json = objectMapper.writeValueAsString(t);
            return (T) objectMapper.readValue(json,t.getClass());
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 判断元素不为空
     * @param obj object
     * @return boolean
     */
    public static boolean isNotEmpty(@Nullable Object obj) {
        return !ObjectUtil.isEmpty(obj);
    }

}
