
/*
* ByTeamGoodsMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 15:59:56 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.ByTeamGoods;
import com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByTeamGoodsMapper extends Mapper<ByTeamGoods> {
	/**
	 * 拼团统计
	 * @param byTeamGoods
	 * @return
	 */
    List<TeamGoodsVo> forms(ByTeamGoods byTeamGoods);
	/**
	 * 拼团商品 list
	 * @param byTeamGoods
	 * @return
	 */
    List<ByTeamGoods> findByCondition(ByTeamGoods byTeamGoods);

	/**
	 * 根据id 查询
	 * @param id
	 * @return
	 */
    ByTeamGoods queryByTeamGoodsById(@Param("id") Integer id);

    List<ByTeamGoods> selectList();

    void updateId(@Param("id") Integer teamGoodsId);

	List<ByGoodsInfo> selectLists(@Param("type") Integer type);
}