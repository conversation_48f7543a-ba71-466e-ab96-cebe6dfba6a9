package com.wmeimob.fastboot.baoyan.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/7/5
 */
public class DecimalUtil {

    /**
     * 减法 。 第一个减去后面的全部。第一个不能为null
     * @param decimal1
     * @param decimals
     * @return
     */
    public static BigDecimal sub(BigDecimal decimal1, BigDecimal... decimals){
        for (BigDecimal decimal : decimals) {
            decimal1 = sub(decimal1, decimal);
        }

        return decimal1;
    }

    /**
     * 减法 。如果第二个decimal2是null那么就等于0
     * @param decimal1
     * @param decimal2
     * @return
     */
    public static BigDecimal sub(BigDecimal decimal1, BigDecimal decimal2){
        if ( decimal2==null ){
            return decimal1;
        }

        return decimal1.subtract( decimal2 );
    }

    /**
     * bigdecimal 转double，如果是null转出来是0
     * @param decimal
     * @return
     */
    public static double doubleValue(BigDecimal decimal){
        return decimal==null ? 0 : decimal.doubleValue() ;
    }

    /**
     * bigdecimal 转double，如果是null转出来是0
     * @param decimal
     * @return
     */
    public static int intValue(BigDecimal decimal){
        return decimal==null ? 0 : decimal.intValue() ;
    }

    /**
     * 如果参数BigDecimal不为空并且不为0，返回true
     * @return
     */
    public static boolean notZeroNull(BigDecimal decimal){
        return decimal!=null && decimal.compareTo( BigDecimal.valueOf(0) ) != 0;
    }

    /**
     * 如果参数BigDecimal为空或者为0，返回true
     * @return
     */
    public static boolean zeroNull(BigDecimal decimal){
        return decimal==null || decimal.compareTo( BigDecimal.valueOf(0) ) == 0;
    }



}
