<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcTemplateMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcTemplate" id="TcTemplateMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="way" column="way" jdbcType="BOOLEAN"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="isShelves" column="is_shelves" jdbcType="BOOLEAN"/>
        <result property="isDel" column="is_del" jdbcType="BOOLEAN"/>
        <result property="isHome" column="is_home" jdbcType="BOOLEAN"/>
    </resultMap>
    <resultMap id="wxQuery" type="com.wmeimob.fastboot.baoyan.entity.TcTemplate" extends="TcTemplateMap">

    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TcTemplateMap">

                select
                  tc_template.*
                from tc_template
                where  tc_template.is_del = 0 and id = #{id}

    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="TcTemplateMap">
        select
        tc_template.*
        from tc_template where tc_template.is_del = 0
        <if test="name != null and name != ''">
            and tc_template.name like concat('%',#{name},'%')
        </if>
        <if test="way != null">
            and tc_template.way = #{way}
        </if>
        <if test="sort != null">
            and tc_template.sort = #{sort}
        </if>
        <if test="target != null and target != ''">
            and tc_template.target = #{target}
        </if>
<!--        <if test="gmtCreate != null">-->
<!--            and gmt_create = #{gmtCreate}-->
<!--        </if>-->
        <if test="jumpType != null">
           and tc_template.jump_type = #{jumpType}
        </if>
        <if test="isShelves != null">
            and tc_template.is_shelves = #{isShelves}
        </if>
        <if test="isDel != null">
            and tc_template.is_del = #{isDel}
        </if>
        <if test="isHome!= null">
            and tc_template.is_home = #{isHome}
        </if>
        order by tc_template.id desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
       insert into tc_template(name, way, sort,jump_type, target, gmt_create,is_home)
       values (#{name}, #{way}, #{sort},#{jumpType}, #{target}, #{gmtCreate},#{isHome})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tc_template(name, way, sort, target, gmt_create, gmt_update, is_shelves, is_del)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.way}, #{entity.sort}, #{entity.target}, #{entity.gmtCreate}, #{entity.gmtUpdate},
            #{entity.isShelves}, #{entity.isDel})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tc_template(name, way, sort, target, gmt_create, gmt_update, is_shelves, is_del)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.way}, #{entity.sort}, #{entity.target}, #{entity.gmtCreate}, #{entity.gmtUpdate},
            #{entity.isShelves}, #{entity.isDel})
        </foreach>
        on duplicate key update
        name = values(name) , way = values(way) , sort = values(sort) , target = values(target) , gmt_create =
        values(gmt_create) , gmt_update = values(gmt_update) , is_shelves = values(is_shelves) , is_del = values(is_del)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tc_template
        <set>
            <if test="name != null and name != ''">
                tc_template.name = #{name},
            </if>
            <if test="way != null">
                tc_template.way = #{way},
            </if>
            <if test="sort != null">
                tc_template.sort = #{sort},
            </if>
            <if test="target != null">
                tc_template.target = #{target},
            </if>
            <if test="jumpType != null">
                tc_template.jump_type = #{jumpType},
            </if>
            <if test="gmtUpdate != null">
                tc_template.gmt_update = #{gmtUpdate},
            </if>
            <if test="isShelves != null">
                tc_template.is_shelves = #{isShelves},
            </if>
            <if test="isHome != null">
                tc_template.is_home =  #{isHome},
            </if>
        </set>
        where tc_template.is_del = 0 and tc_template.id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
      update tc_template  set tc_template.is_del = 1  where  id = #{id}
    </update>


<!--     小程序接口 -->
    <select id="wxQueryAll" resultMap="wxQuery">
        select
        tc_template.*
        from tc_template where
        tc_template.is_del = 0
        and tc_template.is_shelves=1
        <if test="jumpType == null">
            and tc_template.jump_type != 14
        </if>
        <if test="jumpType != null">
            and tc_template.jump_type = #{jumpType}
        </if>
        <if test="isHome!= null">
            and tc_template.is_home = #{isHome}
        </if>
        order by tc_template.sort asc
    </select>

</mapper>

