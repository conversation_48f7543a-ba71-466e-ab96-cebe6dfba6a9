/*
 * ByCustUser.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Fri Jul 05 13:51:52 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;

@Table(name = "by_cust_user")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByCustUser implements Serializable,UserDetails,Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 昵称
     */
    @Column(name = "nick_name")
    private String nickName;
    /**
     * 头像
     */
    @Column(name = "head_img")
    private String headImg;
    /**
     * 手机号
     */
    @Column(name = "mobile")
    private String mobile;

    /**
     * 门店 ID
     */
    @Column(name = "store_id")
    private Integer storeId;

    /**
     * 会员余额
     */
    @Column(name = "member_balance")
    private BigDecimal memberBalance;

    /**
     * 会员卡ID
     */
    @Column(name = "member_card_id")
    private String memberCardId;

    /**
     * 会员卡号
     */ 
    @Column(name = "member_card_code")
    private String memberCardCode;

    /**
     * 会员业务ID
     */
    @Column(name = "member_business_id")
    private String memberBusinessId;

    /**
     * 会员等级
     */
    @Column(name = "member_level")
    private String memberLevel; 

    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_time")
    private Date lastSyncTime;

    /**
     * 登录密码
     */
    @Column(name = "password")
    private String password;
    /**
     * 家长姓名
     */
    @Column(name = "parent_name")
    private String parentName;
    /**
     * 家长联系方式
     */
    @Column(name = "parent_phone")
    private String parentPhone;
    /**
     * 小朋友姓名
     */
    @Column(name = "child_name")
    private String childName;
    /**
     * 小朋友年龄
     */
    @Column(name = "child_age")
    private Integer childAge;
    /**
     * 小朋友性别 0男 1女
     */
    @Column(name = "child_sex")
    private Integer childSex;
    /**
     * 生日
     */
    @Column(name = "birthday")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date birthday;
    /**
     * 当前积分
     */
    @Column(name = "now_point")
    private Integer nowPoint;
    /**
     * 即将获得积分
     */
    @Column(name = "be_point")
    private Integer bePoint;
    /**
     * 注册时间
     */
    @Column(name = "register_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date registerTime;
    /**
     * 禁用状态 默认正常0
     */
    @Column(name = "is_disable")
    private Boolean isDisable;
    /**
     * 禁用状态 默认正常0
     */
    @Column(name = "year_coupon_flag")
    private Boolean yearCouponFlag;
    /**
     * 是否删除;0:否,1:是
     */
    @Column(name = "delete_status")
    private Boolean deleteStatus;
    /**
     * 微信授权登录唯一标识
     */
    @Column(name = "wx_open_id")
    private String wxOpenId;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 历史总获得积分
     */
    @Column(name = "history_point")
    private Integer historyPoint;

    /**
     * 是否显示小红点
     */
    @Column(name = "show_red_count")
    private Integer showRedCount;



    /**
     * 孩子数量
     */
//    @Column(name ="child_num")
//    private Integer childNum;

    @Transient
    private String nickname;
    @Transient
    private String inviter;
    @Transient
    private String reason;
    @Transient
    private String code;
    @Transient
    private Integer changeNum;
    @Transient
    private Integer type;
    @Transient
    private String searchName;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getBePoint() {
        return bePoint;
    }

    public void setBePoint(Integer bePoint) {
        this.bePoint = bePoint;
    }

    public Boolean getYearCouponFlag() {
        return yearCouponFlag;
    }

    public void setYearCouponFlag(Boolean yearCouponFlag) {
        this.yearCouponFlag = yearCouponFlag;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Integer getHistoryPoint() {
        return historyPoint;
    }

    public void setHistoryPoint(Integer historyPoint) {
        this.historyPoint = historyPoint;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return false;
    }

    @Override
    public boolean isAccountNonLocked() {
        return false;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return false;
    }

    @Override
    public boolean isEnabled() {
        return false;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getParentPhone() {
        return parentPhone;
    }

    public void setParentPhone(String parentPhone) {
        this.parentPhone = parentPhone;
    }

    public String getChildName() {
        return childName;
    }

    public void setChildName(String childName) {
        this.childName = childName;
    }

    public Integer getChildAge() {
        return childAge;
    }

    public void setChildAge(Integer childAge) {
        this.childAge = childAge;
    }

    public Integer getChildSex() {
        return childSex;
    }

    public void setChildSex(Integer childSex) {
        this.childSex = childSex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getNowPoint() {
        return nowPoint;
    }

    public void setNowPoint(Integer nowPoint) {
        this.nowPoint = nowPoint;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Boolean getDisable() {
        return isDisable;
    }

    public void setDisable(Boolean disable) {
        isDisable = disable;
    }

    public Boolean getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Boolean deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public String getWxOpenId() {
        return wxOpenId;
    }

    public void setWxOpenId(String wxOpenId) {
        this.wxOpenId = wxOpenId;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getInviter() {
        return inviter;
    }

    public void setInviter(String inviter) {
        this.inviter = inviter;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(Integer changeNum) {
        this.changeNum = changeNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public ByCustUser clone() throws CloneNotSupportedException {
        return (ByCustUser) super.clone();
    }
}