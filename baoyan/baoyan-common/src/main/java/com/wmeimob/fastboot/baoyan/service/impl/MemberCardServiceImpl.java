package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByOrdersMapper;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wmeimob.fastboot.baoyan.entity.ByBalanceLog;
import com.wmeimob.fastboot.baoyan.entity.YchSyncTask;
import com.wmeimob.fastboot.baoyan.mapper.ByBalanceLogMapper;
import com.wmeimob.fastboot.baoyan.mapper.YchSyncTaskMapper;
import com.wmeimob.fastboot.baoyan.service.YchApiService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;

import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
@Service
@Slf4j
public class MemberCardServiceImpl implements MemberCardService {

    @Resource
    private ByBalanceLogMapper balanceLogMapper;
    
    @Resource
    private YchSyncTaskMapper ychSyncTaskMapper;

    @Resource
    private YchApiService ychApiService;

    @Resource
    private ByCustUserMapper byCustUserMapper;

    @Resource
    private ByOrdersMapper byOrdersMapper;

    /**
     * 创建余额变更记录并生成同步任务
     * @param memberId 会员ID
     * @param changeBalance 变更金额
     * @param changeType 变更类型 1:充值 2:消费 3:退款
     * @param orderNo 订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void createBalanceChange(Integer memberId, BigDecimal changeBalance, Integer changeType, String orderNo, String summary) {

        if (changeBalance.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        Example example = new Example(ByCustUser.class);
        example.createCriteria().andEqualTo("id", memberId);
        ByCustUser custUser = byCustUserMapper.selectOneByExample(example);

        Example example2 = new Example(ByOrders.class);
        example2.createCriteria().andEqualTo("orderNo", orderNo);
        ByOrders byOrders = byOrdersMapper.selectOneByExample(example2);

        if (byOrders == null) {
            throw new CustomException("订单不存在");
        }

        // 1. 创建余额变更记录
        ByBalanceLog balanceLog = ByBalanceLog.builder()
                .memberId(memberId)
                .changeBalance(changeBalance)
                .balance(custUser.getMemberBalance())
                .changeType(changeType)
                .orderId(byOrders.getId())
                .orderNo(orderNo)
                .summary(summary)
                .status(0) // 待同步
                .createTime(new Date())
                .build();
        balanceLogMapper.insertSelective(balanceLog);

        // 2. 创建同步任务
        YchSyncTask syncTask = YchSyncTask.builder()
                .memberId(memberId)
                .taskType(3) // 余额变更
                .taskStatus(0) // 待执行
                .refId(balanceLog.getId()) // 关联余额变更记录ID
                .createTime(new Date())
                .build();
        ychSyncTaskMapper.insertSelective(syncTask);
    }

    @Override
    public ByCustUser getMemberCardInfo(String phone) {
        return null;
    }

    @Override
    public ByCustUser getMemberBalance() {
        // 1. 获取会员卡信息
        ByCustUser custUser = SecurityContext.getUser();
        if (custUser == null) {
            throw new CustomException("会员卡信息不存在");
        }

        // 2. 创建同步任务
        YchSyncTask syncTask = YchSyncTask.builder()
                .memberId(custUser.getId())
                .taskType(2) // 余额查询
                .taskStatus(0) // 待执行
                .createTime(new Date())
                .build();
        ychSyncTaskMapper.insertSelective(syncTask);
        return byCustUserMapper.selectByPrimaryKey(custUser.getId());
    }

    @Override
    public void registerMemberCard(String phone) {
        // 1. 获取会员卡信息
        ByCustUser custUser = SecurityContext.getUser();
        if (custUser == null) {
            throw new CustomException("会员卡信息不存在");
        }
        YchSyncTask syncTask = YchSyncTask.builder()
            .memberId(custUser.getId())
            .taskType(1) // 会员开卡
            .taskStatus(0) // 待执行
            .createTime(new Date())
            .build();
        ychSyncTaskMapper.insertSelective(syncTask);
    }

    /**
     * 变更会员余额
     * @param memberId 会员ID
     * @param amount 变更金额
     * @param orderNo 订单编号
     * @param summary 摘要
     */
    @Override
    public void addMemberBalance(Integer memberId, BigDecimal amount, String orderNo, String summary) {
        // 1. 获取会员卡信息
        ByCustUser custUser = byCustUserMapper.selectByPrimaryKey(memberId);
        if (custUser == null) {
            throw new CustomException("会员卡信息不存在");
        }

        // 2. 变更余额
        custUser.setMemberBalance(custUser.getMemberBalance().add(amount));
        byCustUserMapper.updateByPrimaryKeySelective(custUser);

        // 3. 创建余额变更记录
        createBalanceChange(custUser.getId(), amount, 1, orderNo, summary);
    }

    /**
     * 扣除会员余额
     * @param memberId 会员ID
     * @param amount 扣除金额
     * @param orderNo 订单编号
     * @param summary 摘要
     */
    @Override
    public void reduceMemberBalance(Integer memberId, BigDecimal amount, String orderNo, String summary) {
        // 1. 获取会员卡信息
        ByCustUser custUser = byCustUserMapper.selectByPrimaryKey(memberId);
        if (custUser == null) {
            throw new CustomException("会员卡信息不存在");
        }

        if (custUser.getMemberBalance().compareTo(amount) < 0) {
            throw new CustomException("会员余额不足");
        }

        // 2. 扣除余额
        custUser.setMemberBalance(custUser.getMemberBalance().subtract(amount));
        byCustUserMapper.updateByPrimaryKeySelective(custUser);

        // 3. 创建余额变更记录
        createBalanceChange(custUser.getId(), amount, 2, orderNo, summary);
    }

    @Override
    public void syncMemberInfo(Integer userId) {

    }

    @Override
    public void retryFailedSyncTasks() {

    }
}