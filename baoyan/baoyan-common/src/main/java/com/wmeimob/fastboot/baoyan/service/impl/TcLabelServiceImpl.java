package com.wmeimob.fastboot.baoyan.service.impl;


import com.wmeimob.fastboot.baoyan.entity.TcLabel;
import com.wmeimob.fastboot.baoyan.mapper.TcLabelMapper;
import com.wmeimob.fastboot.baoyan.service.TcLabelService;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * (TcLabel)表服务实现类
 * <AUTHOR>
 * @since 2021-07-20 14:08:58
 */
@Slf4j
@Service("tcLabelService")
public class TcLabelServiceImpl implements TcLabelService {
    @Resource
    private TcLabelMapper tcLabelMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcLabel queryById(Integer id) {
        if (null == id) throw new CustomException("参数不对");
        return this.tcLabelMapper.queryById(id);
    }

    /**
     * 新增数据
     *
     * @param tcLabel 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcLabel tcLabel) {
        if (null == tcLabel || tcLabel.getLabelName() == null) throw new CustomException("参数不对");
        return this.tcLabelMapper.insert(tcLabel) > 0;
    }

    @Override
    public List<TcLabel> queryAll(TcLabel tcLabel) {
        return tcLabelMapper.queryAll(tcLabel);
    }
    @Override
    public TcLabel queryByName(String name){
        if (null == name) return null;
        return tcLabelMapper.queryByName(name);
    }

}