package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/4
 */
@Data
@Builder
@Table(name = "by_order_log")
@NoArgsConstructor
@AllArgsConstructor
public class ByOrderLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单日志表主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Integer orderGoodsId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 操作的商品id
     */
    private Integer goodsId;
    /**
     * 商品名
     */
    private String goodsName;
    /**
     * 操作类型  参考枚举
     */
    private OrderLogType logType;
    /**
     * 操作人类型 1.用户 2.管理员
     */
    private Integer operatorType;
    /**
     * 操作的数量
     */
    private Integer operatorCount;
    /**
     * 日志记录
     */
    private String logs;
    /**
     *操作时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

}
