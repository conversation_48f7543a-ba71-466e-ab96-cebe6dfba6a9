
/*
* ByTicketGoodsMappingMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 25 13:46:23 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByTicketGoodsMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByTicketGoodsMappingMapper extends Mapper<ByTicketGoodsMapping> {
       /**List<ByTicketGoodsMapping> select(ByTicketGoodsMapping byTicketGoodsMapping);

	ByTicketGoodsMapping selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByTicketGoodsMapping byTicketGoodsMapping);

	int updateByPrimaryKeySelective(ByTicketGoodsMapping byTicketGoodsMapping);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByTicketGoodsMapping byTicketGoodsMapping);*/
	
}