<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByIntegralLogMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByIntegralLog" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="changeType" column="change_type" jdbcType="INTEGER"/>
		<result property="changeNum" column="change_num" jdbcType="INTEGER"/>
		<result property="changeReason" column="change_reason" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="beforeNum" column="before_num" jdbcType="TIMESTAMP"/>
		<result property="integralType" column="integral_type" jdbcType="INTEGER"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByIntegralLog" id="ListResultMap" extends="BaseResultMap">
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
		<result property="userName" column="userName" jdbcType="VARCHAR"/>
		<result property="userId" column="userId" jdbcType="INTEGER"/>
	</resultMap>
    <select id="queryIntegraLogByByUserId" resultType="com.wmeimob.fastboot.baoyan.vo.IntegraLogVO">


	</select>
    <select id="findByCondition" resultMap="ListResultMap">
		SELECT
			il.change_type,
			il.change_num,
			il.change_reason,
			il.gmt_create,
			cu.id as userId,
			cu.mobile as mobile,
			cu.nick_name as userName
		FROM
			by_integral_log il
		LEFT JOIN by_cust_user cu on cu.id=il.user_id
		<where>
			<if test="startTime != null and startTime != ''">
				AND DATE_FORMAT(il.gmt_create,'%Y-%m-%d')>= #{startTime}
			</if>
			<if test="endTime != null and endTime != ''">
				AND DATE_FORMAT(il.gmt_create,'%Y-%m-%d') &lt;= #{endTime}
			</if>
			<if test="searchName !=null and searchName !='' ">
				and ((cu.mobile like concat('%',#{searchName},'%')) or  (cu.id like concat('%',#{searchName},'%')))
			</if>
		</where>
        order by il.gmt_create desc
	</select>


</mapper>

