<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcLabelMapper">

    <resultMap id="BaseResultMap" type="com.wmeimob.fastboot.baoyan.entity.TcLabel">
        <!--@Table tc_label-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, label_name
        from tc_label
        where id = #{id}
    </select>
    <select id="queryByName" parameterType="string" resultMap="BaseResultMap">
        select
          id, label_name
        from tc_label
        where tc_label.label_name = #{name,jdbcType=VARCHAR}
    </select>
    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, label_name
        from tc_label
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="labelName != null and labelName != ''">
                and label_name = #{labelName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tc_label(label_name)
        values (#{labelName})
    </insert>


</mapper>