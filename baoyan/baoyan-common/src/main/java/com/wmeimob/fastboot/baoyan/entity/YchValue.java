package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "ych_value")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchValue implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private Integer id;

    private String leaguerId;

    private String mobile;

    private String businessId;

    private String valueId;

    private String businessName;

    private Integer valueCode;

    private String name;

    /**
     * 总余额
     */
    private String value;

    /**
     * 可用余额
     */
    private String remainAmount;

    private String statusDesc;

    private Integer status;

    private String gmtCreate;

    private String gmtModified;
}
