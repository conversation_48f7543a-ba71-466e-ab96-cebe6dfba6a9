package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import java.util.List;

/**
 * (TcGoodsStock)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcGoodsStockService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcGoodsStock queryById(Integer id);

    TcGoodsStock queryByGoodsId(Integer id);

    /**
     * 新增数据
     *
     * @param tcGoodsStock 实例对象
     * @return 实例对象
     */
    Boolean insert(TcGoodsStock tcGoodsStock);

    /**
     * 修改数据
     *
     * @param tcGoodsStock 实例对象
     * @return 实例对象
     */
    Boolean update(TcGoodsStock tcGoodsStock);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     *
     */

    List<TcGoodsStock> queryPageList(TcGoodsStock queryObject);



}