package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseClassify;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseClassifyService
 * @Description 分类表
 * <AUTHOR>
 * @Date Fri Jul 12 10:13:14 CST 2019
 * @version1.0
 **/
public interface BaseClassifyService extends CommonService<BaseClassify>{

    /**
     * 分类表查询
     * @param id
     * @return
     */
    default BaseClassify queryBaseClassifyById(Object id){throw new NotImplementedException("queryBaseClassifyById");};

    /**
     * 分类表添加
     * @param  baseClassify
     * @return
     */
    default  void addBaseClassify(BaseClassify baseClassify){throw new NotImplementedException("addBaseClassify");};


    /**
     * 分类表删除
     * @param id
     * @return
     */
    default void removeBaseClassify(Integer id){throw new NotImplementedException("removeBaseClassify");};


    /**
     * 分类表修改
     * @param baseClassify
     * @return
     */
    default void modifyBaseClassify(BaseClassify baseClassify){throw new NotImplementedException("modifyBaseClassify");};

    void updateState(BaseClassify baseClassify);
}
