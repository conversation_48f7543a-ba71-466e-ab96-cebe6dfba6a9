package com.wmeimob.fastboot.baoyan.utils;


import com.wmeimob.fastboot.baoyan.api.UrlCommon;
import net.sf.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import static com.wmeimob.fastboot.baoyan.utils.MD5Crypt.getMD5;


/**
 * @author: 美萌:Yao.ck
 * @date: 2019/7/18 18:15
 * @Description: 与尚乎那边对接接口
 */
public class HttpUtils {

    /**
     * @author: 美萌:Yao.ck
     * @date: 2019/7/18 18:15
     * @Description: 宝燕发送验证码
     */
    public static void sendCode(String mobile) {
        String url = UrlCommon.SEND_SMS_CODE;
        String param = ("token=" + getToken() + "&accountName=" + "sh_mpw" + "&mobile=" + mobile); //参数形式跟在地址栏的一样
        String result = "";
        try {
            String urlName = url + "?" + param;//
            URL U = new URL(urlName);
            URLConnection connection = U.openConnection();
            connection.connect();
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @author: 美萌:Yao.ck
     * @date: 2019/7/18 18:15
     * @Description: 宝燕发送验证码
     */
    public static Boolean getSendCode(String mobile, String verifyCode) {
        String url = UrlCommon.GET_SMS_CODE;
        String param = ("token=" + getToken() + "&accountName=" + "sh_mpw" + "&mobile=" + mobile); //参数形式跟在地址栏的一样
        String result = "";
        try {
            String urlName = url + "?" + param;//
            URL U = new URL(urlName);
            URLConnection connection = U.openConnection();
            connection.connect();
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.fromObject(result);
        //获取code
        String code = jsonObject.get("result").toString();
        if (code != null && code.equals(verifyCode)) {
            return true;
        }
        return false;
    }

    /**
     * 获取token
     *
     * @return String
     */
    private static String getToken() {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmm");//设置日期格式
        Calendar nowTime = Calendar.getInstance();
        String before = df.format(nowTime.getTime());
        String s = getMD5("123456".getBytes());
        s = s + before;
        s = getMD5(s.getBytes());
        return s;
    }



}
