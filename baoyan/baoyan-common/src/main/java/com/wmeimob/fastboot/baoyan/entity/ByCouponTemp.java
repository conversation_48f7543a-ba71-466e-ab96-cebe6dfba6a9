/*
 * ByCouponTemp.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 11 17:55:47 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.annotation.Resource;
import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_coupon_temp")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByCouponTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 优惠券名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 0是全部商品，大于0 是分类ID或者商品ID
     */
    @Column(name = "target_id")
    private Integer targetId;
    /**
     * Discount
     */
    @Column(name = "discount")
    private BigDecimal discount;
    /**
     * 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）
     */
    @Column(name = "full")
    private BigDecimal full;
    /**
     * 有效期
     */
    @Column(name = "start_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;
    /**
     * 有效期
     */
    @Column(name = "end_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;
    /**
     * 0否1是
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * GmtCreate
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * GmtUpdate
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * type
     * 0 全部门票可用 1 指定分类门票 2指定门票
     * 3.淘潮玩指定品牌 4.淘潮玩指定品类 5.淘潮玩指定商品
     * 6.全部淘潮玩可用 7.全部门票和淘潮玩可用
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 1优惠券（抵扣卷） 2折扣卷
     */
    @Column(name = "coupon_type")
    private Integer couponType;
    /**
     * 单个商品类型 1 普通商品 2 次卡 3 联票
     */
    @Column(name = "single_goods_type")
    private Integer singleGoodsType;

    @Transient
    private String goodsName;
    /**
     * 有效期类型 1日期范围 2天数
     */
    @Column(name = "effective_type")
    private Integer effectiveType;
    @Column(name = "day_num")
    private Integer dayNum;
    @Transient
    private Integer classifyId;
    @Transient
    private Integer goodsId;
    @Transient
    private Integer tcGoodsId;
    @Transient
    private Integer tcGoodsCate;
}