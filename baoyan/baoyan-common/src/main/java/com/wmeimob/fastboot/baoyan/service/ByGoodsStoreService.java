package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByGoodsStore;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByGoodsStoreService
 * @Description 商品门店关系表
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/
public interface ByGoodsStoreService extends CommonService<ByGoodsStore>{

    /**
     * 商品门店关系表查询
     * @param id
     * @return
     */
    default ByGoodsStore queryByGoodsStoreById(Object id){throw new NotImplementedException("queryByGoodsStoreById");};

    /**
     * 商品门店关系表添加
     * @param  byGoodsStore
     * @return
     */
    default  void addByGoodsStore(ByGoodsStore byGoodsStore){throw new NotImplementedException("addByGoodsStore");};


    /**
     * 商品门店关系表删除
     * @param id
     * @return
     */
    default void removeByGoodsStore(Object id){throw new NotImplementedException("removeByGoodsStore");};


    /**
     * 商品门店关系表修改
     * @param byGoodsStore
     * @return
     */
    default void modifyByGoodsStore(ByGoodsStore byGoodsStore){throw new NotImplementedException("modifyByGoodsStore");};
}
