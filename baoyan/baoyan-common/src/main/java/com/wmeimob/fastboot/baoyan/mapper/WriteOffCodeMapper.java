
/*
* WriteOffCodeMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 23 13:40:35 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByWriteOffInfo;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.qo.GiftsQo;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WriteOffCodeMapper extends Mapper<WriteOffCode> {

	/**
	 * 根据订单id查询订单核销详情
	 * @param detailId
	 * @return
	 */
	ByWriteOffInfo queryWriteOffInfo(Integer detailId);

	/**
	 * 根据订单商品编号删除核销码
	 * @param detailId
	 * @param count
	 * @return
	 */
	int deleteByDetailId(@Param("detailId") Integer detailId, @Param("count") Integer count);

	/**
	 * 修改订单商品id
	 * @param oldDetailId 旧的
	 * @param newDetailId 新的
	 * @return
	 */
	int updateDetailId(@Param("oldDetailId") Integer oldDetailId,
					   @Param("newDetailId") Integer newDetailId,
					   @Param("count") Integer count);

	/**
	 * 核销码列表
 	 * @param writeOffCode
	 * @return
	 */
	List<WriteOffCode> findByCondition(WriteOffCode writeOffCode);


    Integer selectList(@Param("orderGoodsId") Integer orderGoodsId);

	Integer selectCountList(@Param("orderGoodsId") Integer orderGoodsId);


	/**
	 * 过期核销码
	 * @return
	 */
	List<WriteOffCode> checkWriteOffCodeExpire();

	Integer selectById(@Param("id")Integer id);

	int updateWiff(@Param("id") Integer id,@Param("orderType") Integer orderType);


	/**
	 * 更新指定id的核销码状态为 已退款
	 * @param detailId
	 * @param orderType
	 * @param ids
	 * @return
	 */
	int updateWiffByIds(@Param("detailId") Integer detailId, @Param("orderType")Integer orderType, @Param("ids") List<Integer> ids);


	/**
	 * 更新 count个核销码的状态为 已退款
	 * @param detailId
	 * @param orderType
	 * @param count
	 * @return
	 */
	int updateWiffLimit(@Param("detailId") Integer detailId, @Param("orderType")Integer orderType, @Param("count") Integer count);

	/**
	 * 获取支付名称
	 * @param orderNo
	 * @return
	 */
    String getPayPrdName(@Param("orderNo")String orderNo);

	/**
	 * 查询 条件
	 * param  orderNo:订单号
	 * param  surplus_num != 0:数量不等于0
	 * param  status:状态是0
	 * param  afterStatus:未售后通过
	 * @return
	 */
    List<WriteOffCode> queryNotWriteCodeList(@Param("orderNo") String orderNo);

	/**
	 * 修改待处理核销码用户
	 * @param gifts
	 */
    void updateUserByOrdersId(@Param("qo")GiftsQo gifts,@Param("orderNo")String orderNo);

    Integer selectWriteStstus(@Param("id")Integer id, @Param("orderNo")String orderNo,@Param("orderGoodsId")Integer orderGoodsId);


	/**
	 * 查看当前订单商品已过期的核销码数量
	 * @param detailId
	 * @return
	 */
	Integer countExpire(Integer detailId);
}