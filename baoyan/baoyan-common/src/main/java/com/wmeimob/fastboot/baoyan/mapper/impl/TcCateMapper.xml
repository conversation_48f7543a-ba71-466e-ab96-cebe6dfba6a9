<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcCateMapper">

    <resultMap id="BaseResultMap" type="com.wmeimob.fastboot.baoyan.entity.TcCate">
        <!--@Table tc_cate-->
        <result property="id" column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="name" column="name" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="remark" column="remark" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="isDel" column="is_del" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="img" column="img" jdbcType="VARCHAR" javaType="java.lang.String"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, name, remark,is_del,img
        from tc_cate
        where tc_cate.is_del= 0 and tc_cate. id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
            id, name, remark,is_del,img
        from tc_cate where tc_cate.is_del = 0
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
        id, name, remark,is_del,img
        from tc_cate where tc_cate.is_del = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
        order  by id  desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tc_cate(name, remark,img)
        values (#{name}, #{remark},#{img})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tc_cate
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="img != null and img != ''">
                img = #{img},
            </if>
        </set>
        where is_del = 0 and  id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
       update  tc_cate
       set is_del = 1
        where is_del = 0 and  id = #{id}
    </update>

</mapper>