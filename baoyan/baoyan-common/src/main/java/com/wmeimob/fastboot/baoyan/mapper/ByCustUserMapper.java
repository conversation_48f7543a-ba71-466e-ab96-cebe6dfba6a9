
/*
* ByCustUserMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Fri Jul 05 13:51:52 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

public interface ByCustUserMapper extends Mapper<ByCustUser> {
	/**
	 * 更新用户积分
	 * @return
	 */
	int updateUserIntegralAdd(@Param("userId")Integer userId, @Param("amount")Integer amount);

    int updateAddUserPoint(@Param("id") Integer id,@Param("intValue") Integer intValue);

	int updateByPointAdd(@Param("id") Integer id, @Param("intValue") int intValue);

	int updateByNotAdd(@Param("id") Integer id,@Param("count") Integer count);

	/**
	 * 修改用戶基本信息
	 * @param updateObject
	 * @return
	 */
	int updateByCustUserInformation(ByCustUser updateObject);
}