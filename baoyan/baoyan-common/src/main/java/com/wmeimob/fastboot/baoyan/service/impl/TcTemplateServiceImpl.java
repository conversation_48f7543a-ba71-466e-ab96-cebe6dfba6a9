package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.mapper.TcTemplateMapper;
import com.wmeimob.fastboot.baoyan.service.TcTemplateService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-20 19:01:01
 */


@Service("tcTemplateService")
@Slf4j
public class TcTemplateServiceImpl implements TcTemplateService {
    @Resource
    private TcTemplateMapper tcTemplateDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcTemplate queryById(Integer id) {
        if (id == null) return null;
        return this.tcTemplateDao.queryById(id);
    }

    /**
     * 新增数据
     *
     * @param tcTemplate 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcTemplate tcTemplate) {
        if (null == tcTemplate
                || StringUtils.isEmpty(tcTemplate.getName())
                || (!JumpType.isExist(tcTemplate.getJumpType()))
                || StringUtils.isEmpty(tcTemplate.getTarget())
        ) throw new CustomException("参数不对");
        tcTemplate.setGmtCreate(new Date(System.currentTimeMillis()));
        if (tcTemplate.getIsHome() == null) {
            // 根据跳转类型 设置 模块显示位置
            tcTemplate.setIsHome(!JumpType.isTcExist(tcTemplate.getJumpType()));
        }
        loadTopTcTemplate(tcTemplate);
        return this.tcTemplateDao.insert(tcTemplate) > 0;
    }

    /**
     * 判断 淘潮玩 首页头部标签模块是否存在 不存在设置 显示位置
     */

    private void loadTopTcTemplate(TcTemplate tcTemplate)
    {
        if (tcTemplate.getJumpType().equals(JumpType.AMOY_PLAY_TOP.getId())) {
            TcTemplate temp = new TcTemplate();
            temp.setJumpType(JumpType.AMOY_PLAY_TOP.getId());
            List<TcTemplate> tcTemplates = queryPage(temp);
            if (tcTemplates != null && !tcTemplates.isEmpty()) {
                throw new CustomException("淘潮玩头部已经存在 不允许有多个");
            }
            tcTemplate.setIsHome(Boolean.FALSE);
        }else if (!JumpType.isTcExist(tcTemplate.getJumpType())){
            tcTemplate.setIsHome(Boolean.TRUE);
        }
    }

    /**
     *   判断 淘潮玩 首页头部标签模块是否存在 不存在设置 显示位置
     */
    private TcTemplate updateLoadTopTcTemplate(TcTemplate tcTemplate)
    {
        if (!( tcTemplate.getJumpType().equals(JumpType.AMOY_PLAY_TOP.getId())) )
        {
            if (!JumpType.isTcExist(tcTemplate.getJumpType())){
                tcTemplate.setIsHome(Boolean.TRUE);
            }
           return tcTemplate;
        }
        TcTemplate template = queryById(tcTemplate.getId());
        if (template == null || template.getJumpType() == null) throw new CustomException("参数错误");

        if (!isExistTcTop() || template.getJumpType().equals(tcTemplate.getJumpType()))
        {
            tcTemplate.setIsHome(Boolean.FALSE);
            tcTemplate.setIsShelves(Boolean.TRUE);
            return tcTemplate;
        }else throw new CustomException("淘潮玩头部已经存在 不允许有多个");
    }

    /**
     * 判断 淘潮玩首页板块是否存在 存在放回 true
     * @return
     */
    private Boolean isExistTcTop(){
        TcTemplate queryTop = new TcTemplate();
        queryTop.setJumpType(JumpType.AMOY_PLAY_TOP.getId());
        List<TcTemplate> tcTemplates = queryPage(queryTop);

        return tcTemplates !=null && !tcTemplates.isEmpty();
    }

    /**
     * 修改数据
     *
     * @param tcTemplate 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcTemplate tcTemplate) {
        if (tcTemplate == null
                || StringUtils.isEmpty(tcTemplate.getName())
                || (!JumpType.isExist(tcTemplate.getJumpType()))
                || StringUtils.isEmpty(tcTemplate.getTarget())
                || tcTemplate.getId() == null
                || queryById(tcTemplate.getId()) == null) {
            throw new CustomException("参数不对");
        }
        TcTemplate template = updateLoadTopTcTemplate(tcTemplate);
        template.setGmtUpdate(new Date(System.currentTimeMillis()));
        return this.tcTemplateDao.update(template) > 0;
    }

    @Override
    public Boolean onAndOffShelves(TcTemplate tcTemplate) {
        if (tcTemplate == null || queryById(tcTemplate.getId()) == null) {
            throw new CustomException("参数不对");
        }
        tcTemplate.setGmtUpdate(new Date(System.currentTimeMillis()));
        return tcTemplateDao.update(tcTemplate) > 0;
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Integer id) {
        return this.tcTemplateDao.deleteById(id) > 0;
    }

    @Override
    public List<TcTemplate> queryPage(TcTemplate queryObject) {
        List<TcTemplate> tcTemplates = tcTemplateDao.queryAll(queryObject);
        return tcTemplates;
    }
}
