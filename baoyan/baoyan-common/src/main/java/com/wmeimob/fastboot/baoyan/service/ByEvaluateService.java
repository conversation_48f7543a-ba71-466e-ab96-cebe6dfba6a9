package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByEvaluateService
 * @Description 评价表
 * <AUTHOR>
 * @Date Wed Jul 17 16:52:31 CST 2019
 * @version1.0
 **/
public interface ByEvaluateService extends CommonService<ByEvaluate>{

    /**
     * 评价表查询
     * @param id
     * @return
     */
    default ByEvaluate queryByEvaluateById(Object id){throw new NotImplementedException("queryByEvaluateById");};

    /**
     * 评价表添加
     * @param  byEvaluate
     * @return
     */
    default  void addByEvaluate(ByEvaluate byEvaluate){throw new NotImplementedException("addByEvaluate");};


    /**
     * 评价表删除
     * @param id
     * @return
     */
    default void removeByEvaluate(Object id){throw new NotImplementedException("removeByEvaluate");};


    /**
     * 评价表修改
     * @param byEvaluate
     * @return
     */
    default void modifyByEvaluate(ByEvaluate byEvaluate){throw new NotImplementedException("modifyByEvaluate");};
    /**
     * 评论内容回复
     * @param byEvaluate
     * @return
     */
    default void replyEval(ByEvaluate byEvaluate){throw new NotImplementedException("replyEval");};
}
