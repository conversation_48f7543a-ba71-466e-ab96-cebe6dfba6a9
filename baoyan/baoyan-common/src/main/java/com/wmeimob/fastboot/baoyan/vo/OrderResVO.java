package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: OrderVO
 * @projectName baoyan
 * @description: 订单返回vo
 * @date 2019/7/16 11:25
 */
public class OrderResVO extends ByOrders implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 订单商品list
     */
    private List<ByOrderGoods> list;

    public List<ByOrderGoods> getList() {
        return list;
    }

    public void setList(List<ByOrderGoods> list) {
        this.list = list;
    }


}
