package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (TcImgModule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-09-02 21:22:18
 */
@Mapper
public interface TcImgModuleMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcImgModule queryById(Integer id);



    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcImgModule 实例对象
     * @return 对象列表
     */
    List<TcImgModule> queryAll(TcImgModule tcImgModule);

    /**
     * 新增数据
     *
     * @param tcImgModule 实例对象
     * @return 影响行数
     */
    int insert(TcImgModule tcImgModule);

    /**
     * 修改数据
     *
     * @param tcImgModule 实例对象
     * @return 影响行数
     */
    int update(TcImgModule tcImgModule);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);


    List<TcImgModule> wxQueryALL();
}