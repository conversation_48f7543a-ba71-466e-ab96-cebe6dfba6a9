/*
 * ByStoreStaff.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 11 09:48:59 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

@Table(name = "by_store_staff")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByStoreStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 无符号 自动递增
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 员工姓名
     */
    @Column(name = "staff_name")
    private String staffName;
    /**
     * 员工手机号
     */
    @Column(name = "staff_phone")
    private String staffPhone;
    /**
     * 员工所属门店id
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 微信openid，同时用于是否绑定
     */
    @Column(name = "open_id")
    private String openId;
    /**
     * 是否逻辑删除;1:删除,0:未删除.
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;

}