package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsShoping;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 支付
 * @date 2019-08-13 17:07
 * @Version 1.0
 */
@Data
public class Goods {

    private List<ByGoodsShoping> goods; /*购物车*/

    private Integer type; /*1  商品支付 2 购物车支付 3 未支付订单支付*/

    /**
     * 支付方式 1微信支付 2余额支付
     */
    private Integer payType;

    private Integer goodsType ; /* 1 普通商品 2  次卡 3  联票 5规格*/

    private String id; /*商品id*/

    private Integer goodsCount;/*商品数量*/

    private BigDecimal payAmount; /*商品金额*/

    private BigDecimal orderAmount; /*订单金额*/

    private BigDecimal scoreAmount; /*积分抵用金额*/

    private BigDecimal couponAmount; /*优惠券抵用金额*/

    private Integer couponId; /*用户卷id*/

    private Integer teamType;

    private Integer teamId;

    private String orderNo;

    private String remark;

    private String fromId;

    private BigDecimal amount;
    /**
     * 是否 门票和淘潮玩混合支付
     * true ：混合支付
     * false : 只支付淘潮玩或者门票
     */
    private boolean isPayAll;

}
