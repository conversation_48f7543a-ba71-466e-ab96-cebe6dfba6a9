package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ByTeamOrderService
 * @Description 拼团订单表
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
public interface WeChatTeamOrderService extends CommonService<ByTeamOrder>{
    /**
     * 我的拼团列表
     * @param user
     * @return
     */
   default List<ByTeamOrder> myTeam(ByCustUser user){throw new NotImplementedException("myTeam");};

    /**
     * 拼团详情
     * @param user
     * @param id
     * @return
     */
    default Map<String,Object> teamOrderDetail(ByCustUser user, Integer id){throw new NotImplementedException("teamOrderDetail");};

    /**
     * 拼团订单评价
     * @param evaluateInfo
     */
    default void evalTeam(ByEvaluate evaluateInfo){throw new NotImplementedException("evalTeam");};
}
