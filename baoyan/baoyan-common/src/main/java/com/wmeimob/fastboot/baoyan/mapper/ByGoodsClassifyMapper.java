
/*
* ByGoodsClassifyMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Wed Jul 24 15:32:05 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByGoodsClassifyMapper extends Mapper<ByGoodsClassify> {
	/**
	 * 查询 商品分类 id 下是否关联商品
	 * @param id
	 * @return
	 */
    List<ByGoodsClassify> selectClassifyGoodsContact(@Param("id") Integer id);

    /**
     * 普通商品查询分类id
     * @param goodsId
     * @return
     */
    List<Integer> selectClassifyIdByGoods(Integer goodsId);

    /**List<ByGoodsClassify> select(ByGoodsClassify byGoodsClassify);

	ByGoodsClassify selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByGoodsClassify byGoodsClassify);

	int updateByPrimaryKeySelective(ByGoodsClassify byGoodsClassify);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByGoodsClassify byGoodsClassify);*/
	
}