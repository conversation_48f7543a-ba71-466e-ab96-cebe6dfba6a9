package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByPlatformSet;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByPlatformSetService
 * @Description 平台设置表
 * <AUTHOR>
 * @Date Mon Jul 22 17:18:53 CST 2019
 * @version1.0
 **/
public interface ByPlatformSetService extends CommonService<ByPlatformSet>{

    /**
     * 平台设置表查询
     * @param id
     * @return
     */
    default ByPlatformSet queryByPlatformSetById(Object id){throw new NotImplementedException("queryByPlatformSetById");};

    /**
     * 平台设置表添加
     * @param  byPlatformSet
     * @return
     */
    default  void addByPlatformSet(ByPlatformSet byPlatformSet){throw new NotImplementedException("addByPlatformSet");};


    /**
     * 平台设置表删除
     * @param id
     * @return
     */
    default void removeByPlatformSet(Object id){throw new NotImplementedException("removeByPlatformSet");};


    /**
     * 平台设置表修改
     * @param byPlatformSet
     * @return
     */
    default void modifyByPlatformSet(ByPlatformSet byPlatformSet){throw new NotImplementedException("modifyByPlatformSet");};
}
