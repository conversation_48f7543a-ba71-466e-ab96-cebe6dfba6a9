package com.wmeimob.fastboot.baoyan.utils.common;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.wmeimob.fastboot.autoconfigure.oss.AliyunOssProperties;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wechat.core.Wechat;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 生成小小程序二维码
 * @date 2019-05-23 15:02
 * @Version 1.0
 */


@Transactional
@Service
@Slf4j
public class QrCodeService {
    @Resource
    private WechatService wechatService;
    @Resource
    private AliyunOssProperties properties;

    public String exportQrCode(HttpServletResponse resp, Integer id, String urls) {
        log.info("------------------------正在生成二维码----------------");
        AccessTokenUtils agentAccessTokenUtils = new AccessTokenUtils();
        Wechat wechatMp = this.wechatService.getApiComponent();
        //小程序码信息
        JSONObject json = new JSONObject();
        json.put("page", urls);
        json.put("scene", id);
//        json.put("check_path",false);
//        json.put("env_version","trial");
        String accessToken = agentAccessTokenUtils.getAccessToken(wechatMp.getAppId(), wechatMp.getAppSecret());
        String name = id + "_" + UUID.randomUUID().toString() + ".jpg";
        try {
            //获取小程序二维码
            HttpResponse response = HttpRequest.post("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken)
                    .body(json.toJSONString()).execute();
            String url = AliOOSUtil.uploadFileByStream(properties, response.bodyStream(), name);
            return "https://" + properties.getBucket() + "." + properties.getEndpoint() + "/" + name;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "生成失败";
    }


    @SuppressWarnings("all")
    public String exportQrCode(HttpServletResponse resp, Map<String, Object> parameter, String urls) {
        log.info("------------------------正在生成二维码----------------");
        AccessTokenUtils agentAccessTokenUtils = new AccessTokenUtils();
        Wechat wechatMp = this.wechatService.getApiComponent();
        //小程序码信息
        String parameterString = loadParameter(parameter);
        if (null == parameterString) return null;
        JSONObject json = new JSONObject();
        Object id = parameter.get("id");
        json.put("page", urls);
        json.put("scene", parameterString);
        String accessToken = agentAccessTokenUtils.getAccessToken(wechatMp.getAppId(), wechatMp.getAppSecret());
        String name = id + "_" + UUID.randomUUID().toString() + ".jpg";
        try {
            //获取小程序二维码
            HttpResponse response = HttpRequest.post("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken)
                    .body(json.toJSONString()).execute();
            String url = AliOOSUtil.uploadFileByStream(properties, response.bodyStream(), name);
            return "https://" + properties.getBucket() + "." + properties.getEndpoint() + "/" + name;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String loadParameter(Map<String, Object> parameter) {
        if (null == parameter || parameter.size() == 0) return null;
        StringBuilder stringBuilder = new StringBuilder();
        parameter.keySet().forEach(key -> {
            if (null != key) stringBuilder.append(key).append("=").append(parameter.get(key)).append("&");
        });
        return stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
    }


}
