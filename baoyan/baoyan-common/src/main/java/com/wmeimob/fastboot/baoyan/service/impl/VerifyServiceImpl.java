package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import com.wmeimob.fastboot.baoyan.mapper.ByCombinationGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsInfoMapper;
import com.wmeimob.fastboot.baoyan.mapper.BySubCardGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.VerifyService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import tk.mybatis.mapper.entity.Example;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeLogMapper;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VerifyServiceImpl implements VerifyService {
    
    @Resource
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    
    @Resource
    private MemberCardService memberCardService;

    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;

    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;

    @Resource
    private ByCombinationGoodsMapper byCombinationGoodsMapper;

    /**
     * 处理首次核销赠送余额
     * @param memberId 会员ID
     * @param goodsId 商品ID
     * @param goodsType 商品类型
     * @param orderId 订单ID
     * @param orderNo 订单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFirstVerifyBalance(Integer memberId, Integer writeOffId, Integer goodsId, Integer goodsType, String orderNo) {
        
        // 1. 检查是否首次核销
        Example example = new Example(WriteOffCodeLog.class);
        example.createCriteria()
               .andEqualTo("custUserId", memberId)
               .andEqualTo("writeOffId", writeOffId); // 只查询之前的记录
        
        if (writeOffCodeLogMapper.selectCountByExample(example) > 1) {
            return; // 已有核销记录,不是首次核销
        }

        // 2. 获取赠送金额
        BigDecimal balanceAmount = getFirstVerifyBalance(goodsId, goodsType);
        if (balanceAmount == null || balanceAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return; // 没有设置赠送金额
        }

        // 3. 赠送会员卡余额
        memberCardService.addMemberBalance(
            memberId,
            balanceAmount,
            orderNo,
            String.format("订单号：%s，商品ID：%s，商品类型：%s，首次核销赠送余额-%s", orderNo, goodsId, getGoodsTypeName(goodsType), balanceAmount)
        );
    }

    /**
     * 获取首次核销赠送余额金额
     */
    @Override
    public BigDecimal getFirstVerifyBalance(Integer goodsId, Integer goodsType) {
        switch (goodsType) {
           case 1: // 普通商品
               ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goodsId);
               return goodsInfo != null ? goodsInfo.getFirstVerifyBalance() : null;

           case 2: // 次卡
               BySubCardGoods subCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(goodsId);
               return subCardGoods != null ? subCardGoods.getFirstVerifyBalance() : null;

           case 5: // 规格
               ByCombinationGoods ticketGoods = byCombinationGoodsMapper.selectByPrimaryKey(goodsId);
               return ticketGoods != null ? ticketGoods.getFirstVerifyBalance() : null;
            
            default:
                return null;
        }
    }

    private String getGoodsTypeName(Integer goodsType) {
        switch (goodsType) {
            case 1: return "普通商品";
            case 2: return "次卡";
            case 5: return "规格商品";
            default: return "未知类型";
        }
    }
}

