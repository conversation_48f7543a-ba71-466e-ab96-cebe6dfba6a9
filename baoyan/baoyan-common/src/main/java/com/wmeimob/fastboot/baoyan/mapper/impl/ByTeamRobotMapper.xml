<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTeamRobotMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeamRobot" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="obotImg" column="obot_img" jdbcType="VARCHAR"/>
		<result property="obotNickName" column="obot_nick_name" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamRobot">
		SELECT 
			`id`,
			`obot_img`,
			`obot_nick_name`,
			`gmt_create`,
			`gmt_update`
		FROM  by_team_robot
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="obotImg != '' and obotImg != null">
			  	AND `obot_img` = #{obotImg}
			</if>
			<if test="obotNickName != '' and obotNickName != null">
			  	AND `obot_nick_name` = #{obotNickName}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtUpdate != null">
			  	AND `gmt_update` = #{gmtUpdate}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`obot_img`,
			`obot_nick_name`,
			`gmt_create`,
			`gmt_update`
		FROM by_team_robot
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamRobot" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO by_team_robot
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="obotImg != '' and obotImg != null">
			`obot_img`,
		</if>
		<if test="obotNickName != '' and obotNickName != null">
			`obot_nick_name`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		<if test="gmtUpdate != null">
			`gmt_update`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="obotImg != '' and obotImg != null">
			#{obotImg},	
		</if>
		<if test="obotNickName != '' and obotNickName != null">
			#{obotNickName},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		<if test="gmtUpdate != null">
			#{gmtUpdate},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamRobot">
		UPDATE by_team_robot
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="obotImg != '' and obotImg != null">
				`obot_img` = #{obotImg},
			</if>
			<if test="obotNickName != '' and obotNickName != null">
				`obot_nick_name` = #{obotNickName},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
			<if test="gmtUpdate != null">
				`gmt_update` = #{gmtUpdate},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM by_team_robot WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamRobot">
		DELETE FROM by_team_robot
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="obotImg != '' and obotImg != null">
			  	AND `obot_img` = #{obotImg}
			</if>
			<if test="obotNickName != '' and obotNickName != null">
			  	AND `obot_nick_name` = #{obotNickName}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtUpdate != null">
			  	AND `gmt_update` = #{gmtUpdate}
			</if>
		</where>
	</delete> -->
</mapper>

