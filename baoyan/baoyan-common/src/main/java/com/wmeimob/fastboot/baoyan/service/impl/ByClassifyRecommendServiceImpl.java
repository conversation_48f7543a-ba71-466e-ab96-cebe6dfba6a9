package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.mapper.ByClassifyRecommendMapper;
import com.wmeimob.fastboot.baoyan.service.ByClassifyRecommendService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (ByClassifyRecommend)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:53
 */
@Service("byClassifyRecommendService")
public class ByClassifyRecommendServiceImpl implements ByClassifyRecommendService {
    @Resource
    private ByClassifyRecommendMapper byClassifyRecommendMapper;
    @Resource
    private TcGoodsService tcGoodsService;

    /**
     * 通过ID查询单条数据
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ByClassifyRecommend queryById(Integer id) {
        if (null == id) return null;
        return byClassifyRecommendMapper.queryById(id);
    }
    /**
     * 新增数据
     *
     * @return 实例对象
     */
    @Override
    public Boolean insert(ByClassifyRecommend addObject) {

        verifyTcRecommend(addObject);
        addObject.setGmtCreate(new Date(System.currentTimeMillis()));
        return byClassifyRecommendMapper.insert(addObject) > 0;
    }

    private void verifyTcRecommend(ByClassifyRecommend object) {
        if (object == null
                || object.getJumpType() == null
                || !JumpType.isExist(object.getJumpType())
                || JumpType.AMOY_PLAY_TOP.getId().equals(object.getJumpType())
                || StringUtils.isBlank(object.getText())
                || object.getSort() == null
        ) throw new CustomException("参数错误");

        if (JumpType.AMOY_PLAY_CATEGORY.getId().equals(object.getJumpType())) {
            if (null == object.getTcFlag()) throw new CustomException("未选择淘潮玩 商品 类别");

            if (null == object.getBrandId()
                    && null == object.getCateId()
                    && StringUtils.isBlank(object.getLabelId())
                    && null == tcGoodsService.queryById(object.getGoodsId())
            ) throw new CustomException("未选择淘潮玩 商品/类别");

            if (!object.getTcFlag().equals(2)
                    && !object.getTcFlag().equals(1)){
                throw new CustomException("淘潮玩 商品/类别错误");
            }

            if (object.getTcFlag().equals(2)) {
                if ( !Integer.valueOf(-1).equals(object.getCateId())
                        && Integer.valueOf(-1).equals(object.getBrandId())
                        && "-1".equals(object.getLabelId()))
                    throw new CustomException("未选择淘潮玩商品类别 ");
            }

        }
    }

    /**
     * 修改数据
     * @return 实例对象
     */
    @Override
    public Boolean update(ByClassifyRecommend updateObject) {
        verifyTcRecommend(updateObject);
        updateObject.setGmtUpdate(new Date(System.currentTimeMillis()));
        return this.byClassifyRecommendMapper.update(updateObject) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Integer id) {
        if (id == null) throw new CustomException("参数错误");
        ByClassifyRecommend byClassifyRecommend = queryById(id);
        if (null == byClassifyRecommend) throw new CustomException(String.format("编号:%d 系列已过期", id));
        return this.byClassifyRecommendMapper.deleteById(id) > 0;
    }


    @Override
    public List<ByClassifyRecommend> queryAll(ByClassifyRecommend queryObj) {
        return byClassifyRecommendMapper.queryAll(queryObj);
    }

    @Override
    public Boolean onAndOffShelves(ByClassifyRecommend updateObject) {
        if (null == updateObject || null == updateObject.getId()) {
            throw new CustomException("参数错误");
        }
        ByClassifyRecommend time = queryById(updateObject.getId());
        if (time == null) throw new CustomException("参数错误");
        time.setIsShelves(!time.getIsShelves());
        return this.byClassifyRecommendMapper.onAndOffShelves(time.getId(),time.getIsShelves()) > 0;
    }


}