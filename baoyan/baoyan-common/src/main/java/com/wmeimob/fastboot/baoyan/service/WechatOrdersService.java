package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.vo.OrderInfoVo;
import com.wmeimob.fastboot.baoyan.vo.OrderResVO;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByOrdersService
 * @Description 商品订单表
 * <AUTHOR>
 * @Date Mon Jul 15 15:41:21 CST 2019
 * @version1.0
 **/
public interface WechatOrdersService extends CommonService<ByOrders>{

    /**
     * 查询用户订单列表
     * @param user
     * @param orderStatus
     * @return
     */
    default List<OrderInfoVo> userOrderList(ByCustUser user, Integer orderStatus, Integer payType){throw new NotImplementedException("userOrderList");}

    /**
     * 获取用户订单详情
     * @param id
     * @return
     */
    default Map<String,Object> userOrderDetail(Integer id){throw new NotImplementedException("userOrderList");}

    /**
     * 查询订单商品的详情描述
     * @param orderGoods
     * @return
     */
    List<ByWriteOffInfo> queryWriteOffInfo(List<ByOrderGoods> orderGoods);

    /**
     * 取消訂單
     * @param user
     * @param id
     */
    default void cancelUserOrder(ByCustUser user, Integer id){throw new NotImplementedException("userOrderList");}
    /**
     * <AUTHOR>
     * @date 20:41 2019/5/9
     * @Description:修改商品库存
     * @param type 0 减少,1增加
     */
    default int updateGoodsStock(Integer type,Integer goodsId,Integer goodsNum){throw new NotImplementedException("updateGoodsStock");}

    /**
     * 评价订单
     * @param user
     * @param evaluateInfoList
     */
    default Map<String,Object> evalOrder(ByCustUser user, List<ByEvaluate> evaluateInfoList){throw new NotImplementedException("evalOrder");}

    /**
     * 我的 - 我的订单数
     * @param id
     */
    Map<String,Object> getUserOrderNum(Integer id);

    /**
     * 申请售后
     * @param user
     * @param byOrderAfter
     */
    default void applySale(ByCustUser user, ByOrderAfter byOrderAfter) throws IOException {throw new NotImplementedException("applySale");}
    /**
     * <AUTHOR>
     * @date 10:48 2019/5/14
     * @Description:售后订单详情
     */
    default ByOrderGoods saleDetail(Integer id,String orderNo, Integer afterType){throw new NotImplementedException("saleDetail");}

    /**
     * 删除订单
     * @param user
     * @param id
     */
    default void delOrder(ByCustUser user, Integer id){throw new NotImplementedException("delOrder");}

    default void applyTeam(ByCustUser user, ByOrderAfter byOrderAfter){
        return;
    };

    default ByOrders generateChannelOrder(ByChannelCust channelCust){
        throw new NotImplementedException("generateChannelOrder");
    }
}
