
/*
* ByIntegralLogMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Fri Jul 05 15:35:59 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.IntegraLogVO;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ByIntegralLogMapper extends Mapper<ByIntegralLog> {
	/**
	 * 添加积分记录
	 */
	default void addIntegralLog(Integer userId,Integer changeType,Integer amount,Integer before,String reason,String operator){
		ByIntegralLog log = new ByIntegralLog();
		log.setUserId(userId);
		log.setChangeType(changeType);
		log.setChangeNum(amount);
		log.setBeforeNum(before);
		log.setChangeReason(reason);
		log.setGmtCreate(new Date());
		this.insertSelective(log);
	}

	/**
	 * 查询我的积分记录
	 * @param userId
	 * @return
	 */
	IntegraLogVO queryIntegraLogByByUserId(Integer userId);
	/**
	 * 积分查询列表
	 * @param byIntegralLog
	 * @return
	 */
    List<ByIntegralLog> findByCondition(ByIntegralLog byIntegralLog);
}