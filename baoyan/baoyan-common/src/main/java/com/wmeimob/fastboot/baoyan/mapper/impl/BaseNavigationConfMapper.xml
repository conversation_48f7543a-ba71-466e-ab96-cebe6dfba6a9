<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseNavigationConfMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="title" column="title" jdbcType="VARCHAR"/>
		<result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
		<result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
		<result property="target" column="target" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="url" column="url" jdbcType="TIMESTAMP"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="isHome" column="is_home" jdbcType="TINYINT" />
    </resultMap>
	<select id="navigation" resultMap="BaseResultMap">
		select
		conf.id,
		conf.title,
		conf.img_url as url,
		conf.target,
		conf.jump_type,
		case conf.jump_type
		when 2 then conf.target
		when 1 then (select classify_img from base_classify where id = conf.target)
		when 3 then conf.target
		when 4 then conf.target
		END as  "img_url"
		 from base_navigation_conf conf
		 where conf.status=1
		 ORDER BY sort ASC
	</select>
	<select id="tcNavigation" resultMap="BaseResultMap">
		select
		conf.id,
		conf.title,
		conf.img_url as url,
		conf.target,
		conf.jump_type,
		conf.is_home,
		case conf.jump_type
		when 2 then conf.target
		when 1 then (select classify_img from base_classify where id = conf.target)
		when 3 then conf.target
		when 4 then conf.target
		END as  "img_url"
		 from base_navigation_conf conf
		 where conf.status=1 and conf.is_home=#{isHome,jdbcType=BOOLEAN}
		 ORDER BY sort ASC
	</select>
</mapper>

