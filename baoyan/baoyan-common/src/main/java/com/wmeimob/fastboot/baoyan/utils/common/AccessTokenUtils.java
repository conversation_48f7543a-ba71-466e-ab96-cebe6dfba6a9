package com.wmeimob.fastboot.baoyan.utils.common;

import com.alibaba.fastjson.JSONObject;
import com.wmeimob.fastboot.util.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

public class AccessTokenUtils {

    private static RestTemplate restTemplate = new RestTemplate();


    public  String getAccessToken(String appid,String secret) {

//        String accessToken = SpringRedisUtil.get("mps:" + appid + "access_token");

        String accessToken = null;
        if (StringUtils.isEmpty(accessToken)) {
            JSONObject accessTokenJson = restTemplate.getForObject("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}", JSONObject.class, new Object[]{appid,secret});
            accessToken = accessTokenJson.getString("access_token");
            Assert.notNull(accessToken, "[" + accessTokenJson.getInteger("errcode") + "]" + accessTokenJson.getString("errmsg"));
            if (accessToken == null) {
                return "";
            }
            SpringRedisUtil.save("mps:" + appid + "access_token", accessToken, (long) (accessTokenJson.getInteger("expires_in") - 1800) * 1000L);
        }
        return accessToken;
    }

}
