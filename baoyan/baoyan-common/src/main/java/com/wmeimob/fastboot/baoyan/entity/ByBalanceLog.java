package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员余额变更记录
 */
@Table(name = "by_balance_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByBalanceLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户ID
     */
    @Column(name = "member_id")
    private Integer memberId;

    /**
     * 变更金额
     */
    @Column(name = "change_balance")
    private BigDecimal changeBalance;

    /**
     * 变更后余额
     */
    @Column(name = "balance")
    private BigDecimal balance;

    /**
     * 变更类型：1充值 2消费 3退款 4 查询余额
     */
    @Column(name = "change_type")
    private Integer changeType;

    /**
     * 油菜花交易ID
     */
    @Column(name = "ych_trans_id")
    private String ychTransId;

    /**
     * 同步状态：0待同步 1已同步 2同步失败
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 业务单号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 变更说明
     */
    @Column(name = "summary")
    private String summary;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}