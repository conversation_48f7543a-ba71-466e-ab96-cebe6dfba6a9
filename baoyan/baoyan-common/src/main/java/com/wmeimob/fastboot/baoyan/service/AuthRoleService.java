package com.wmeimob.fastboot.baoyan.service;

import com.alibaba.fastjson.JSONArray;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.management.common.entity.SysRole;

import java.util.List;

public interface AuthRoleService extends CommonService<SysRole> {
    default void recursiveUpdatePermissions(JSONArray permissions, Integer roleId) {
        throw this.notImplementException("recursionUpdatePermissions");
    }

    default List<SysRole> findSysRolesByCreateUserIds(List<Integer> collect) {
        throw this.notImplementException("");
    }

    default List<SysRole> findByIdArr(List<Integer> roleIds) {
        throw this.notImplementException("findByIdArr");
    }

    default void recursiveUpdateMenus(JSONArray menuIdArr, Integer id) {
        throw this.notImplementException("recursiveUpdateMenus");
    }
}
