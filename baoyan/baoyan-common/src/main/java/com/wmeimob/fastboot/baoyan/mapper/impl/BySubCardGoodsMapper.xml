<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BySubCardGoodsMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BySubCardGoods" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="subCardGoodsName" column="sub_card_goods_name" jdbcType="VARCHAR"/>
		<result property="subCardGoodsNum" column="sub_card_goods_num" jdbcType="INTEGER"/>
		<result property="sellPrice" column="sell_price" jdbcType="DECIMAL"/>
		<result property="marketPrice" column="market_price" jdbcType="DECIMAL"/>
		<result property="initialSaleNum" column="initial_sale_num" jdbcType="INTEGER"/>
		<result property="actualSalesNum" column="actual_sales_num" jdbcType="INTEGER"/>
		<result property="goodsStock" column="goods_stock" jdbcType="INTEGER"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsBanner" column="goods_banner" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="richId" column="rich_id" jdbcType="INTEGER"/>
		<result property="hasVerificationDay" column="has_verification_day" jdbcType="TINYINT"/>
		<result property="verificationDay" column="verification_day" jdbcType="INTEGER"/>
		<result property="regularDownStatus" column="regular_down_status" jdbcType="TINYINT"/>
		<result property="effectiveStart" column="effective_start" jdbcType="DATE"/>
		<result property="effectiveEnd" column="effective_end" jdbcType="DATE"/>
		<result property="marketingStart" column="marketing_start" jdbcType="DATE"/>
		<result property="marketingEnd" column="marketing_end" jdbcType="DATE"/>
		<result property="preDepositId" column="pre_deposit_id" jdbcType="INTEGER"/>
		<result property="firstVerifyBalance" column="first_verify_balance" jdbcType="DECIMAL"/>
		<result property="limited" column="limited" jdbcType="INTEGER"/>
		<result property="isCoupon" column="is_coupon" jdbcType="INTEGER"/>
		<result property="isIntegral" column="is_integral" jdbcType="INTEGER"/>
		<result property="buyNotice" column="buy_notice" jdbcType="VARCHAR"/>
		<result property="useNotice" column="use_notice" jdbcType="VARCHAR"/>
		<result property="brights" column="brights" jdbcType="VARCHAR"/>
		<result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.BySubCardGoods" id="ListResultMap" extends="BaseResultMap">
		<result property="subGoodsName" column="subGoodsName" jdbcType="VARCHAR"/>
		<result property="richContent" column="richContent" jdbcType="VARCHAR"/>
	</resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.BySubCardGoods" id="DetailResultMap" extends="BaseResultMap">
		<result property="richContent" column="richContent" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="findByCondition" resultMap="ListResultMap">
		SELECT
			scg.*,
		c.storeName,
		t.classifyName,
		rt.content as richContent
		FROM
			by_sub_card_goods scg
		left join by_rich_text rt ON rt.data_id=scg.id and rt.data_type=2
		left join by_sub_goods_store sgs on sgs.sub_goods_id = scg.id

		LEFT JOIN (SELECT
		gs.sub_goods_id ,GROUP_CONCAT(bs.`name`) `storeName`,GROUP_CONCAT(bs.id) storeIds
		FROM
		by_sub_goods_store gs
		left JOIN base_store bs on gs.store_id =bs.id  where  bs.delete_status=0 GROUP BY gs.sub_goods_id ) c on c.sub_goods_id=scg.id
		left JOIN
		(
		SELECT
		gc.sub_goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
		GROUP_CONCAT(bc.id) as classifyIds
		FROM
		by_sub_goods_classify gc
		LEFT JOIN base_classify bc on bc.id=gc.classify_id
		where bc.is_del=0
		GROUP BY gc.sub_goods_id
		) t ON t.sub_goods_id=scg.id

		<where>
			and scg.is_del=0
			<if test="status !=null and status!=0">
				AND (
					scg.status=1
					OR (
						scg.status=0
						AND (
							scg.has_verification_day=1
							OR (scg.effective_start &lt;= NOW() AND scg.effective_end &gt; NOW())
						)
					)
				)
			</if>
			<if test="status !=null and status ==0">
				AND scg.status=0
			</if>

			<if test="searchName !=null and searchName!=''">
				AND ((scg.sub_car                                                                                                                                                                                                                                                 d_goods_name LIKE CONCAT('%',#{searchName},'%') OR
				(scg.id =#{searchName})))
			</if>

			<if test="storeId !=null and storeId !=''">
				AND sgs.store_id = #{storeId}
			</if>
		</where>
		group by scg.id
		order by scg.id desc
	</select>
	<select id="queryBySubCardGoodsById" resultMap="DetailResultMap">
	SELECT
			scg.*,
	       scg.goods_tag as goodsTag,
		rt.content as richContent,
		t.classifyIds as classifyIds,
		c.stores as stores
		FROM
			by_sub_card_goods scg
		left join by_rich_text rt ON rt.data_id=scg.id and rt.data_type=2
		LEFT JOIN (SELECT
		gs.sub_goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) as stores
		FROM
		by_sub_goods_store gs
		left JOIN base_store bs on gs.store_id =bs.id GROUP BY gs.sub_goods_id ) c on c.sub_goods_id=scg.id
		left JOIN
		(
		SELECT
		gc.sub_goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,GROUP_CONCAT(gc.classify_id) as classifyIds
		FROM
		by_sub_goods_classify gc
		LEFT JOIN base_classify bc on bc.id=gc.classify_id
		GROUP BY gc.sub_goods_id
		) t ON t.sub_goods_id=scg.id
		<where>
		and scg.is_del=0
		AND  scg.id=#{id}
		</where>
	</select>
	<update id="updateByAddStock" >
		update by_sub_card_goods
		set goods_stock = goods_stock + #{productCount}
		where id = #{productId}
	</update>


	<select id="showStore" resultType="com.wmeimob.fastboot.baoyan.entity.BaseStore">
		select store.id,store.name,store.mobile,store.address,store.business_time businessTime,store.longitude,store.latitude from base_store store left join by_sub_goods_store goods  on store.id = goods.store_id where goods.sub_goods_id = #{id}
	</select>

	<select id="selectList" resultType="com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo">
		  SELECT id id,sub_card_goods_name goodsName,goods_img goodsImg,sell_price sellPrice,market_price marketPrice FROM by_sub_card_goods
		  WHERE is_del != 1
		  AND status = 1
		  AND goods_stock > 0
		  AND effective_start &lt;= now()
		  AND effective_end &gt; now()
		  <if test="type == 1"> order by sort desc ,gmt_update DESC</if>
		  <if test="type == 2"> order by sell_price asc </if>
		  <if test="type == 3"> order by sell_price desc </if>
		  <!--<if test="type == 4"> order by distance asc </if>-->
		  <!--<if test="type == 5"> order by distance desc </if>-->
	</select>
</mapper>

