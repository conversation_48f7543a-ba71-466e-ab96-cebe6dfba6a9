<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByClassifyRecommendMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend" id="byClassifyRecommendMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="text" column="text" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="coverImg" column="cover_img" jdbcType="VARCHAR"/>
        <result property="cateId" column="cate_id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="INTEGER"/>
        <result property="isShelves" column="is_shelves" jdbcType="BOOLEAN"/>
        <result property="isDel" column="is_del" jdbcType="BOOLEAN"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="labelId" column="label_id" jdbcType="VARCHAR"/>
        <result property="tcFlag" jdbcType="INTEGER" column="tc_flag"/>
    </resultMap>

<!--    <resultMap id="queryAll" type="com.wmeimob.fastboot.baoyan.entity.TcRecommend" extends="TcRecommendMap">-->
<!--        <result property="tcTemplate.id" column="tid" jdbcType="INTEGER"/>-->
<!--        <result property="tcTemplate.name" column="tname" jdbcType="VARCHAR"/>-->
<!--        <result property="tcTemplate.isShelves" column="tshelves" jdbcType="TINYINT"/>-->
<!--    </resultMap>-->

    <!--查询单个-->
    <select id="queryById" resultMap="byClassifyRecommendMap">
        select
          by_classify_recommend.*
          from by_classify_recommend
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="byClassifyRecommendMap">
        select
        by_classify_recommend.*
        from by_classify_recommend

        where by_classify_recommend.is_del = 1

        <if test="text != null and text != ''">
            and by_classify_recommend.text like concat('%',#{text,jdbcType=VARCHAR},'%')
        </if>
        <if test="jumpType != null">
            and by_classify_recommend.jump_type = #{jumpType}
        </if>
        <if test="sort != null">
            and by_classify_recommend.sort = #{sort}
        </if>
        <if test="goodsId != null">
            and by_classify_recommend.goods_id = #{goodsId}
        </if>
        <if test="cateId != null">
            and by_classify_recommend.cate_id = #{cateId}
        </if>
        <if test="brandId != null">
            and by_classify_recommend.brand_id = #{brandId}
        </if>
        <if test="isShelves != null">
            and by_classify_recommend.is_shelves = #{isShelves}
        </if>
        <if test="isDel != null">
            and by_classify_recommend.is_del = #{isDel}
        </if>
        order by by_classify_recommend.id desc
    </select>

    <select id="wxQueryAll" resultMap="byClassifyRecommendMap">
         select
        by_classify_recommend.*
        from by_classify_recommend
        where by_classify_recommend.is_del = 1 and by_classify_recommend.is_shelves=1
        order by sort asc
    </select>

    <!--新增所有列-->
    <insert id="insert"  parameterType="com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend">
        insert into by_classify_recommend(text, jump_type, target, sort, goods_id, cover_img, cate_id, brand_id, gmt_create, gmt_update,label_id,tc_flag)
        values (#{text}, #{jumpType}, #{target}, #{sort}, #{goodsId}, #{coverImg},
                #{cateId}, #{brandId}, #{gmtCreate}, #{gmtUpdate},#{labelId},#{tcFlag})
    </insert>





    <!--通过主键修改数据-->
    <update id="update" parameterType="com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend">
        update by_classify_recommend
        <set>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType},
            </if>
            <if test="tcFlag == null ">
                tc_flag = null,
            </if>
            <!--            <if test="tcFlag == 1 and goodsId == null">-->
            <!--                target = #{target},-->
            <!--            </if>-->
            <if test="target != null and target != '' and tcFlag != 2 ">
                target = #{target},
            </if>
            <if test="goodsId != null and target == null">
                target = #{target},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="coverImg != null">
                cover_img = #{coverImg},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate},
            </if>
            <if test="tcFlag != null">
                tc_flag = #{tcFlag},
                target = #{target},
            </if>
            <if test="isShelves != null">
                is_shelves = #{isShelves},
            </if>
        </set>
        ,goods_id = #{goodsId},
        cate_id = #{cateId},
        label_id = #{labelId},
        brand_id = #{brandId}
        where is_del=1 and id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
         update by_classify_recommend set by_classify_recommend.is_del = 0
         where is_del=1 and  id =#{id,jdbcType=INTEGER}
    </update>
    <update id="onAndOffShelves">
        update by_classify_recommend set by_classify_recommend.is_shelves=#{type,jdbcType=BOOLEAN}
         where is_del=1 and  id =#{id,jdbcType=INTEGER}
    </update>
</mapper>

