package com.wmeimob.fastboot.baoyan.constant;

/**
 * 外部API接口路径常量定义
 */
public interface ExternalApiConstants {

    /**
     * 总部服务基础URL（获取门店列表接口）
     */
    public static final String HQ_BASE_URL = "http://y12340001.yun.youcaihua.net:88";

    /**
     * 应用ID
     */
    public static final String APP_ID = "WL00056472";

    /**
     * 私钥
     */
    public static final String PRIVATE_KEY = "abc123abc";

    /**
     * 默认-唐镇 商户ID
     */
    // public static final String DEFAULT_BUSINESS_ID = "1115b31d-dcb7-49aa-a22a-aa6d00d73e3e";
    public static final String DEFAULT_BUSINESS_ID = "949e7bea-2286-456e-ac53-aa9b00ea0d86";
    
    /**
     * API版本
     */
    static final String API_VERSION = "v1.2";
    static final String BASE_PATH = "/OnLine/" + API_VERSION;
    static final String BASE_PATH_13 = "/OnLine/v1.3";
        
    /**
     * 获取商户门店列表接口
     */
    public static final String GET_BUSINESS_LIST = HQ_BASE_URL + BASE_PATH + "/GetBusinessList";

    /**
     * 通过手机号查询会员ID
     */
    public static final String GET_LEAGUER_BY_PHONE = BASE_PATH + "/GetLeaguerByPhone";

    /**
     * 会员注册
     */
    public static final String LEAGUER_REGISTER_BY_LEVEL = BASE_PATH + "/LeaguerRegisterByLevel";

    /**
     * 获取会员信息
     */
    public static final String GET_LEAGUER_INFO = BASE_PATH + "/GetLeaguerInfo";

    /**
     * 获取会员余额
     */
    public static final String GET_LEAGUER_VALUES = BASE_PATH + "/GetLeaguerValues";

    /**
     * 获取储值变更记录-分页
     */
    public static final String GET_LEAGUER_VALUES_LOG_BY_PAGE = BASE_PATH + "/GetLeaguerValuesLogByPage";

    /**
     * 变更储值订单状态为已完成
     */
    public static final String ORDER_PAY_AND_COMPLETED = BASE_PATH + "/OrderPayAndCompleted";

    /**
     * 会员预存款变更
     */
    public static final String LEAGUER_PREPAID_CHANGE = BASE_PATH_13 + "/LeaguerPrepaidChange";

    /**
     * 会员预存款变更
     */
    public static final String ORDER_RETURN = BASE_PATH + "/OrderReturn";

    /**
     * 获取商品列表
     */
    public static final String GET_GOODS_LIST = BASE_PATH + "/GetGoodsList";

    /**
     * 创建订单
     */
    public static final String CREATE_ORDER = BASE_PATH_13 + "/CreateOrder";
    
}
