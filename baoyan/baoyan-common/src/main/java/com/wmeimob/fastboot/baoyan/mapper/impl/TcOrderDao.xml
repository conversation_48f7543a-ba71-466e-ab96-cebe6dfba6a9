<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcOrder" id="TcOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="payOrderNo" column="pay_order_no" jdbcType="VARCHAR"/>
        <result property="gtmCreate" column="gtm_create" jdbcType="TIMESTAMP"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="payType" column="pay_type" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="consignee" column="consignee" jdbcType="VARCHAR"/>
        <result property="mobilePhone" column="mobile_phone" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deliveryMode" column="delivery_mode" jdbcType="INTEGER"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="expressName" column="express_name" jdbcType="VARCHAR"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="payFlowNo" column="pay_flow_no" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="originAmount" column="origin_amount" jdbcType="NUMERIC"/>
        <result property="actualAmount" column="actual_amount" jdbcType="NUMERIC"/>
        <result property="integralAmount" column="integral_amount"/>
        <result property="couponAmount" column="coupon_amount" jdbcType="NUMERIC"/>
        <result property="goodsCount" column="goods_count" jdbcType="INTEGER"/>
        <result property="estimatedTime" column="estimated_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <resultMap id="tcOrderWithGoods" type="com.wmeimob.fastboot.baoyan.entity.TcOrder" extends="TcOrderMap">
        <collection property="orderGoods" column="id" select="com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper.queryByOrderId" />
    </resultMap>

    <sql id="selectColumn">
        select
        ord.id, ord.order_no, ord.pay_order_no, ord.gtm_create, ord.pay_time, ord.pay_type, ord.user_id, ord.consignee,
        ord.mobile_phone, ord.address, ord.remark, ord.delivery_mode, ord.logistics_no, ord.express_name, ord.finish_time,
        ord.pay_flow_no, ord.status, ord.origin_amount, ord.actual_amount, ord.integral_amount, ord.coupon_amount,
        ord.goods_count, ord.estimated_time, ord.deliver_time
    </sql>


    <select id="getCountByUserIdAndState" resultType="integer">
        SELECT COUNT(id)
        FROM tc_order
        WHERE user_id = #{userId}
        AND `status` = #{state}
    </select>
    
    <select id="selectUserOrder" resultMap="tcOrderWithGoods">
        <include refid="selectColumn"/>
        from tc_order ord
        where ord.user_id = #{userId}
        <if test="status != null">
            and ord.status = #{status}
        </if>
        order by ord.id desc
    </select>

    <select id="findById" resultMap="tcOrderWithGoods">
        <include refid="selectColumn"/>
        from tc_order ord
        where ord.id = #{id}
    </select>

    <select id="selectByCondition" resultMap="tcOrderWithGoods">
        <include refid="selectColumn"/>
        from tc_order ord
        left join by_cust_user user
        on ord.user_id = user.id
        <where>
            <if test="status!=null">
                and ord.status = #{status}
            </if>
            <if test="userId!=null">
                and ord.user_id = #{userId}
            </if>
            <if test="startDate!=null and startDate!=''">
                and DATE_FORMAT(ord.gtm_create,'%Y-%m-%d') >= #{startDate}
            </if>
            <if test="endDate!=null and endDate!=''">
                and DATE_FORMAT(ord.gtm_create,'%Y-%m-%d') &lt;= #{endDate}
            </if>
            <if test="keyWord!=null and keyWord!=''">
                and
                (
                ord.consignee  like concat('%',#{keyWord},'%')
                    or
                ord.mobile_phone like  concat('%',#{keyWord},'%')
                    or
                ord.order_no like concat('%',#{keyWord},'%')
                    or
                ord.logistics_no like  concat('%',#{keyWord},'%')
                    or
                user.id like  concat('%',#{keyWord},'%')
                <!--  or
               user.mobile like concat('%',#{keyWord},'%')-->
                )
            </if>
        </where>
        order by ord.id desc
    </select>

    <select id="findByPayOrderNo" resultMap="TcOrderMap">
        select id, order_no, pay_order_no
        from tc_order
        where pay_order_no = #{parentNo}
    </select>


    <select id="findByOrderNo" resultMap="TcOrderMap">
        <include refid="selectColumn"/>
        from tc_order ord
        where ord.order_no = #{orderNo}
    </select>

</mapper>