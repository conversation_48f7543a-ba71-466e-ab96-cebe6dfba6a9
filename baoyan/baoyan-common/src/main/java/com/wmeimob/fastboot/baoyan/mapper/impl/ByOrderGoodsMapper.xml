<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByOrderGoodsMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrderGoods" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="orderId" column="order_id" jdbcType="INTEGER"/>
		<result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
		<result property="firstClass" column="first_class" jdbcType="INTEGER"/>
		<result property="goodsPrice" column="goods_price" jdbcType="DECIMAL"/>
		<result property="goodsNum" column="goods_num" jdbcType="INTEGER"/>
		<result property="couponPrice" column="coupon_price" jdbcType="DECIMAL"/>
		<result property="integralPrice" column="integral_price" jdbcType="DECIMAL"/>
		<result property="isAfterSale" column="is_after_sale" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="orderStatus" column="order_status" jdbcType="TINYINT"/>
		<result property="productId" column="product_id" jdbcType="VARCHAR"/>
		<result property="productType" column="product_type" jdbcType="VARCHAR"/>
		<result property="productCount" column="product_count" jdbcType="VARCHAR"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrderGoods" id="BaseResultMap2" extends="BaseResultMap">
		<result property="refundType" column="refundType" jdbcType="VARCHAR"/>
		<result property="refundNum" column="refundNum" jdbcType="DECIMAL"/>
		<result property="subtotal" column="subtotal" jdbcType="DECIMAL"/>

	</resultMap>

	<update id="updatePayPrice">
		update by_order_goods set pay_unit_price = #{payUnitPrice} where id = #{id}
	</update>

	<select id="selectByOrderId" resultMap="BaseResultMap2">
		SELECT
			bog.*, (
				bog.goods_price * bog.goods_num
			) AS allPrice,
			bog.goods_id,
		(bog.goods_price * bog.goods_num)-bog.integral_price-bog.coupon_price as subtotal,
		IF (
			oa.order_goods_id IS NULL,
			0,
			1
		) AS refundType,
		 oa.goods_num AS refundNum,
		 write_off_date AS writeOffDate
		FROM
			by_order_goods bog
		LEFT JOIN by_order_after oa ON oa.order_no = bog.order_no AND oa.resouce_type = 1 AND  oa.detail_id = bog.id AND after_status = 1
		where bog.order_id=#{orderId}
	</select>




</mapper>

