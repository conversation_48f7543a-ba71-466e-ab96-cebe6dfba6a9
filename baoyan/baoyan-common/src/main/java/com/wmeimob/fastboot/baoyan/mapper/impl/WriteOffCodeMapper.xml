<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.WriteOffCode" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result property="writeOffName" column="write_off_name" jdbcType="VARCHAR"/>
        <result property="custUserId" column="cust_user_id" jdbcType="INTEGER"/>
        <result property="storeIds" column="store_ids" jdbcType="VARCHAR"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="sourceGoodsId" column="source_goods_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="surplusNum" column="surplus_num" jdbcType="INTEGER"/>
        <result property="totalNum" column="total_num" jdbcType="INTEGER"/>
        <result property="expiryDate" column="expiry_date" jdbcType="DATE"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="detailId" column="detail_id" jdbcType="INTEGER"/>
        <result property="orderState" column="order_state" jdbcType="INTEGER"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.WriteOffCode" id="WriteoffMap" extends="BaseResultMap">
        <result property="custUserName" column="custUserName" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="sourceGoodsName" column="sourceGoodsName" jdbcType="VARCHAR"/>
        <result property="channelSourceName" column="channelSourceName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryWriteOffInfo" resultType="com.wmeimob.fastboot.baoyan.entity.ByWriteOffInfo">
		SELECT
		SUM(woc.total_num) writeOffTotal, SUM(woc.surplus_num) writeOffSurplus,
		MAX(wocl.write_off_date) lastWriteDate,
        woc.expiry_date expiryDate, woc.end_date endDate
        FROM write_off_code woc
        LEFT JOIN write_off_code_log wocl
        ON woc.`id` = wocl.`write_off_id`
        WHERE detail_id = #{detailId};
	</select>

    <update id="deleteByDetailId">
		delete from write_off_code where detail_id = #{detailId} limit #{count}
	</update>

    <update id="updateDetailId">
		UPDATE write_off_code SET detail_id = #{newDetailId} WHERE detail_id = #{oldDetailId} LIMIT #{count};
	</update>

    <select id="findByCondition" resultMap="WriteoffMap">
        SELECT woc.*,
            cu.nick_name                                                                       as custUserName,
            sdi.item_name as channelSourceName,
            cu.mobile                                                                          as mobile,
            (select GROUP_CONCAT(bs.`name`) from base_store bs where bs.id in (woc.store_ids)) as storeName,
            IF(woc.order_type = 0, (SELECT GROUP_CONCAT(og.goods_name)
            FROM by_orders bo
            LEFT JOIN by_order_goods og on og.order_id = bo.id
            WHERE bo.order_no = woc.order_no),
            (SELECT tg.team_name
            FROM by_team_order tt
            LEFT JOIN by_team_goods tg ON tg.id = tt.team_goods_id
            WHERE tt.order_no = woc.order_no))                                             AS sourceGoodsName
        FROM write_off_code woc
        LEFT JOIN by_cust_user cu on cu.id = woc.cust_user_id
        LEFT JOIN by_orders bo ON bo.order_no = woc.order_no
        left join by_channel_cust cc on bo.id = cc.order_id
        left join sys_dict_item sdi on bo.is_channel =1 and sdi.item_value = cc.channel_source and sdi.dict_id = 1
        <where>
            and woc.order_state=0
            <if test="searchName !=null and searchName !='' ">
                and ((woc.cust_user_id like concat('%',#{searchName},'%')) or (cu.mobile like
                concat('%',#{searchName},'%'))
                or (woc.write_off_name like concat('%',#{searchName},'%')))
            </if>
            <if test="mobile !=null and mobile !='' ">
                and cu.mobile = #{mobile}
            </if>
            <if test="writeOffName !=null and writeOffName !='' ">
                and woc.write_off_name like concat('%',#{writeOffName},'%')
            </if>
            <if test="dateSum">
                and   #{expiryDate} >=  woc.end_date
                and woc.end_date >= now()
                and woc.status = 0
            </if>
            <if test="status != null">
                and status = 0
                and surplus_num > 0
            </if>
        </where>
        order by woc.gmt_create desc
    </select>

    <select id="selectList" resultType="int">
	select count(*) as `count` from  write_off_code where detail_id = #{orderGoodsId}  GROUP BY  source_goods_id   order by count asc limit 1
</select>
    <select id="selectCountList" resultType="int">
	select count(*) as `count` from  write_off_code where detail_id = #{orderGoodsId} and status != 0 and is_expire != 1
</select>
    <select id="checkWriteOffCodeExpire" resultMap="BaseResultMap">
		SELECT  t.* from write_off_code t
        where  t.end_date <![CDATA[<]]> CURRENT_DATE
        AND t.`status` = 0
	</select>
    <select id="selectById" resultType="int">
		select count(*) from write_off_code where detail_id = #{id} and status = 0 and end_date > CURRENT_DATE and order_type = 0
	</select>
    <select id="getPayPrdName" resultType="java.lang.String">
		SELECT
		GROUP_CONCAT(DISTINCT wc.write_off_name)
	FROM
		write_off_code wc
	where wc.order_no = #{orderNo}
	</select>
    <select id="queryNotWriteCodeList" resultMap="BaseResultMap">
		SELECT
			wc.*
		FROM
			write_off_code wc
		WHERE
			wc.order_no = #{orderNo}
		AND wc.surplus_num != 0
		AND wc.`status` = 0
	</select>

    <update id="updateWiff">
		update write_off_code
		set order_state = 1
		where detail_id = #{id}
        and order_type = #{orderType}
        and status = 0
	</update>

    <update id="updateWiffByIds">
        update write_off_code
        set order_state = 1
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and order_type = #{orderType}
        AND order_state = 0
    </update>

    <update id="updateWiffLimit">
        update write_off_code
        set order_state = 1
        WHERE detail_id = #{detailId}
        and order_type = #{orderType}
        AND order_state = 0
        ORDER BY surplus_num DESC
        limit #{count}
    </update>

    <update id="updateUserByOrdersId">
		UPDATE write_off_code SET cust_user_id = #{qo.userId} WHERE order_no = #{orderNo} AND status = 0
	</update>
    <select id="selectWriteStstus" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM write_off_code WHERE cust_user_id = #{id} AND order_no = #{orderNo} AND detail_id = #{orderGoodsId} AND is_expire = 1
	</select>

    <select id="countExpire" resultType="int">
		SELECT COUNT(*)
		FROM by_cust_user u
		RIGHT JOIN write_off_code woc ON u.id=woc.cust_user_id
		WHERE detail_id = #{detailId}
		AND NOW() > end_date
		AND (total_num-(SELECT COUNT(*) FROM write_off_code_log WHERE write_off_id=woc.id)) > 0;
	</select>
</mapper>

