package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TcGoodsShoping {
    private static final long serialVersionUID = 326455587216214149L;

    /**
     * 主键自增无意义
     */
    private Integer id;

    /**
     * 淘潮玩商品id
     */
    private Integer goodsId;

    /**
     * 商品
     */
    @Transient
    private TcGoods tcGoods;

    /**
     * 商品数量
     */
    private Integer goodsCount;

    /**
     * 所属用户id
     */
    private Integer userId;

    /**
     * 添加购物车时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 逻辑删除，是否删除
     */
    private Boolean isDel;
}
