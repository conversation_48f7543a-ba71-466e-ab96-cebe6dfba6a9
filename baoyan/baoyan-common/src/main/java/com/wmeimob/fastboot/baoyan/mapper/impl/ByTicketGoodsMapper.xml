<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTicketGoods" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="ticketGoodsName" column="ticket_goods_name" jdbcType="VARCHAR"/>
		<result property="sellPrice" column="sell_price" jdbcType="DECIMAL"/>
		<result property="marketPrice" column="market_price" jdbcType="DECIMAL"/>
		<result property="initialSaleNum" column="initial_sale_num" jdbcType="INTEGER"/>
		<result property="actualSalesNum" column="actual_sales_num" jdbcType="INTEGER"/>
		<result property="goodsStock" column="goods_stock" jdbcType="INTEGER"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsBanner" column="goods_banner" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="effectiveStart" column="effective_start" jdbcType="DATE"/>
		<result property="effectiveEnd" column="effective_end" jdbcType="DATE"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTicketGoods" id="ListResultMap" extends="BaseResultMap">
	</resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTicketGoods" id="DetailResultMap" extends="BaseResultMap">
		<result property="richContent" column="richContent" jdbcType="VARCHAR"/>
		<result property="goodsIds" column="goodsIds" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="findByCondition" resultMap="ListResultMap">
		SELECT
		tg.*, (
		SELECT
		GROUP_CONCAT(gi.goods_name)
		FROM
		by_ticket_goods tt
		LEFT JOIN by_ticket_goods_mapping gm ON gm.ticket_goods_id = tt.id
		LEFT JOIN by_goods_info gi ON gi.id = gm.goods_id
		GROUP BY gm.ticket_goods_id
		HAVING gm.ticket_goods_id=tg.id
		) as showGoodsName,
		rt.content as richContent
		FROM
		by_ticket_goods tg
		LEFT JOIN by_rich_text rt ON rt.data_id=tg.id AND rt.data_type=3
	  	<where>
			AND  tg.is_del=0
			<if test="searchName !=null and searchName!=''">
				AND ((tg.ticket_goods_name LIKE CONCAT('%',#{searchName},'%')) OR
				(tg.id =#{searchName}))
			</if>
		</where>

		order by tg.id desc

	</select>
	<select id="queryByTicketGoodsById" resultMap="DetailResultMap">
	SELECT
			tg.*, (
			SELECT
			GROUP_CONCAT(gi.goods_name)
			FROM
			by_ticket_goods tt
			LEFT JOIN by_ticket_goods_mapping gm ON gm.ticket_goods_id = tt.id
			LEFT JOIN by_goods_info gi ON gi.id = gm.goods_id
			GROUP BY gm.ticket_goods_id
			HAVING gm.ticket_goods_id=tg.id
			) as goodsName,
        (
        SELECT
        GROUP_CONCAT(gi.id)
        FROM
        by_ticket_goods tt
        LEFT JOIN by_ticket_goods_mapping gm ON gm.ticket_goods_id = tt.id
        LEFT JOIN by_goods_info gi ON gi.id = gm.goods_id
        GROUP BY gm.ticket_goods_id
        HAVING gm.ticket_goods_id=tg.id
        ) as goodsIds,
		rt.content as richContent
			FROM
		by_ticket_goods tg
		LEFT JOIN by_rich_text rt ON rt.data_id=tg.id AND rt.data_type=3
		<where>
			and tg.id=#{id}
		</where>
	</select>
	<update id="updateByAddStock" >
		update by_ticket_goods
		set goods_stock = goods_stock + #{productCount}
		where id = #{productId}
	</update>

	<select id="selectList" resultType="com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo">
		SELECT id,ticket_goods_name goodsName,goods_img goodsImg,sell_price sellPrice,market_price marketPrice FROM by_ticket_goods
		WHERE is_del != 1
		AND status = 1
		AND goods_stock >0
		AND effective_start &lt;= now()
	    AND effective_end &gt; now()
		<if test="type == 1"> order by sort desc ,gmt_update DESC</if>
		<if test="type == 2"> order by sell_price asc </if>
		<if test="type == 3"> order by sell_price desc </if>
		<!--<if test="type == 4"> order by distance asc </if>-->
		<!--<if test="type == 5"> order by distance desc </if>-->
	</select>

</mapper>

