<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByPlatformSetMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByPlatformSet" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="payTime" column="pay_time" jdbcType="INTEGER"/>
		<result property="integralReturn" column="integral_return"  jdbcType="DECIMAL"/>
		<result property="commentIntegral" column="comment_integral" jdbcType="INTEGER"/>
		<result property="integrationDeduction" column="integration_deduction" jdbcType="INTEGER"/>
		<result property="teamIntegralIsEnable" column="team_integral_is_enable" jdbcType="TINYINT"/>
		<result property="ticketIntegralIsEnable" column="ticket_integral_is_enable" jdbcType="TINYINT"/>
		<result property="subCardIntegralIsEnable" column="sub_card_integral_is_enable" jdbcType="TINYINT"/>
		<result property="teamCouponIsEnable" column="team_coupon_is_enable" jdbcType="TINYINT"/>
		<result property="ticketCouponIsEnable" column="ticket_coupon_is_enable" jdbcType="TINYINT"/>
		<result property="subCardCouponIsEnable" column="sub_card_coupon_is_enable" jdbcType="TINYINT"/>
		<result property="couponInvalid" column="coupon_invalid" jdbcType="INTEGER"/>
		<result property="birthdayCouponId" column="birthday_coupon_id" jdbcType="INTEGER"/>
		<result property="perfectInfoCouponId" column="perfect_info_coupon_id" jdbcType="INTEGER"/>
		<result property="generalIntegralIsEnable" column="general_integral_is_enable" jdbcType="TINYINT"/>
		<result property="generalCouponIsEnable" column="general_coupon_is_enable" jdbcType="TINYINT"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="updatePerfectCoupon">
		UPDATE by_platform_set
		SET
			    `perfect_info_coupon_id` = #{perfectInfoCouponId},
				`gmt_modified` = #{gmtModified}
		WHERE id=#{id}
	</update>
    <update id="updateBirthdayCoupon">
		UPDATE by_platform_set
		SET
				`birthday_coupon_id` = #{birthdayCouponId},
				`gmt_modified` = #{gmtModified}
		WHERE id=#{id}
	</update>

</mapper>

