package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
import com.wmeimob.fastboot.core.orm.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/4
 */
public interface ByOrderLogMapper extends Mapper<ByOrderLog> {

    List<ByOrderLog> findAll();

    /**
     * 添加一条订单日志
     * @return
     */
    int insertLog(ByOrderLog byOrderLog);

    /**
     * 根据订单号查询订单日志
     * @param orderNo
     * @return
     */
    List<ByOrderLog> queryByOrderNo(String orderNo);
}
