
/*
* ByGoodsInfoMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Fri Jul 12 13:41:20 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.vo.ByGoodsInfoVO;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByGoodsInfoMapper extends Mapper<ByGoodsInfo> {

	/**
	 * 商品列表查询
	 * @param byGoodsInfo
	 * @return
	 */
    List<ByGoodsInfo> findByCondition(ByGoodsInfo byGoodsInfo);
	/**
	 * 根据id 查询
	 * @param id
	 * @return
	 */
    ByGoodsInfoVO queryGoodDetailInfoById(Integer id);

	List<ByGoodsInfo> selectGoods(@Param("id") Integer id);

	List<ByGoodsInfo> selectGoodsByCombination(@Param("id") Integer id);

	List<ByGoodsInfo> selectProdoct(@Param("productName") String productName,@Param("id") Integer id);

	List<ByGoodsInfo> selectDistance(@Param("type")Integer type,@Param("goodsName") String goodsName,@Param("longitude") String longitude,@Param("latitude") String latitude,@Param("classId") Integer classId);

    List<BaseStore> showStore(@Param("id") Integer id);

    void updateByAddStock(@Param("productId") Integer productId,@Param("productCount") Integer productCount);

    Integer insertReturnId(@Param("qo") ByGoodsInfo byGoodsInfo);

//	List<ByGoodsInfo> selectGoodsList(Integer id);
}