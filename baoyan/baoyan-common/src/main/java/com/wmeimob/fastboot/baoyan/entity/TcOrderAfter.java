package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 淘潮玩订单售后(TcOrderAfter)实体类
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:32
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TcOrderAfter implements Serializable {
    private static final long serialVersionUID = -60888113841376642L;
    /**
     * 主键自增
     */
    @Id
    private Integer id;
    /**
     * 申请售后的用户id
     */
    private Integer userId;
    /**
     * 用户信息
     */
    @Transient
    private ByCustUser byCustUser;
    /**
     * 1.仅退款，2.退款退货
     */
    private Integer afterType;
    /**
     * 退款的商品id
     */
    private Integer goodsId;
    /**
     * 退款数量
     */
    private Integer refundCount;
    /**
     * 退款金额
     */
    private BigDecimal refundPrice;
    /**
     * 订单详情id
     */
    private Integer detailId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 退款地址id
     */
    private Integer refundAddrId;
    /**
     * 退货地址
     */
    private String refundAddr;
    /**
     * 退货寄回时的收件人
     */
    private String refundConsignee;
    /**
     * 退货寄回时的收货电话
     */
    private String refundPhone;
    /**
     * 退款原因
     */
    private String reason;
    /**
     * 1.申请中 2.退款中 3.已同意 4.已拒绝
     */
    private Integer status;
    /**
     * 申请时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date applyTime;
    /**
     * 处理时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date handleTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退款退货时，用户寄出的物流单号
     */
    private String logisticsNo;
    /**
     * 退货的快递公司名
     */
    private String refundExpress;
    /**
     * 拒绝售后原因
     */
    private String rejectMsg;
    /**
     * 订单详情信息
     */
    @Transient
    private TcOrderGoods tcOrderGoods;

    /**
     * 查询时的开始时间
     */
    @Transient
    private Date startTime;

    /**
     * 查询时的结束时间
     */
    @Transient
    private Date endTime;
    /**
     * 查询时的名字
     */
    @Transient
    private String searchName;
}