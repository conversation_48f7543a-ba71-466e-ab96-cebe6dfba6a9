
/*
* ByTeamOrderMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 15:59:56 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.TeamOrderVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ByTeamOrderMapper extends Mapper<ByTeamOrder> {
	/**
	 * <AUTHOR> @date
	 * @Description:查询拼团订单列表
	 */
    List<TeamOrderVo> queryTeamList(TeamOrderVo vo);

	/**
	 * 查询
	 * @param id
	 * @return
	 */
	TeamOrderVo getTeamOrderInfoById(@Param("id") Integer id);
	/**
	 * 拼团管理 拼团订单
	 */
	List<TeamOrderVo> getTeamOrderInfo(@Param("id")Integer id);

	/**
	 * 我的拼团
	 * @param sfTeamOrder
	 * @return
	 */
    List<ByTeamOrder> myTeam(ByTeamOrder sfTeamOrder);

    List<ByTeamOrder> selectList(@Param("id") Integer id);

	List<Map<String,String>> selectTeamList(@Param("id") Integer id);

    int selectCountList(@Param("userId") Integer userId);
}