package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: OrderInfoVo
 * @projectName baoyan
 * @description: 订单返回vo
 * @date 2019/8/6 16:57
 */
@Data
public class OrderInfoVo extends ByOrders implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer allGoodsNum;
    private List<ByOrderGoods> orderGoodsList;
    /**
     * 订单是否已赠送
     */
    private Integer isPresenter;
    /**
     * 订单是否属于本人
     */
    private Integer isCurrent;
}
