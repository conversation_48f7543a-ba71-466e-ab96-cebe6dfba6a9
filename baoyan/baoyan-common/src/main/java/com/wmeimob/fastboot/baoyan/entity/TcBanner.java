package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;
import java.io.Serializable;

/**
 * Banner(TcBanner)实体类
 *
 * <AUTHOR>
 * @since 2021-08-08 16:00:24
 */
@Table(name = "tc_banner")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TcBanner implements Serializable {
    private static final long serialVersionUID = -48132741445862914L;
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 图片地址
     */
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 是否上架 0 不上架 1上架
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 跳转类型id
     */
    @Column(name = "jump_type")
    private Integer jumpType;
    /**
     * 跳转content
     */
    @Column(name = "target")
    private String target;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 是否删除 0 未删除 1删除
     */
    @Column(name = "is_del")
    private Integer isDel;

}
