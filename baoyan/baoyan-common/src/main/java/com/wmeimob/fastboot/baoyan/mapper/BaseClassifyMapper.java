
/*
* BaseClassifyMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Fri Jul 12 10:13:14 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseClassify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseClassifyMapper extends Mapper<BaseClassify> {
       /**List<BaseClassify> select(BaseClassify baseClassify);

	BaseClassify selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseClassify baseClassify);

	int updateByPrimaryKeySelective(BaseClassify baseClassify);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseClassify baseClassify);*/
	
}