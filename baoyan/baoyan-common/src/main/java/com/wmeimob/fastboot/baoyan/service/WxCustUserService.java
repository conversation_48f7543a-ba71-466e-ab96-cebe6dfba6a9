package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.Map;

/**
 * wx 用户service
 */
public interface WxCustUserService extends CommonService<ByCustUser> {
    /**
     * 用户注册
     * @param mobile
     */
    default RestResult registerUser(String mobile, Integer storeId){throw new NotImplementedException("registerUser");};

    /**
     * 完善信息
     * @param user
     * @return
     */
    default Map<String, Object> completeUser(ByCustUser user){throw new NotImplementedException("completeUser");};

    /**
     * 用户查询id
     * @param user
     * @return
     */
    default ByCustUser findUserById(ByCustUser user){throw new NotImplementedException("findUserById");};

    /**
     * 查询用户信息
     * @param openid
     * @return
     */
    default  ByCustUser queryUserInfo(String openid){throw new NotImplementedException("queryUserInfo");};

    /**
     *保存授权信息
     * */
    default ByCustUser saveByCustUser(ByCustUser byCustUser){
        throw new NotImplementedException("queryUserInfo");
    };

    default  Boolean updateByCustUserInformation(ByCustUser byCustUser){
        throw new NotImplementedException("updateByCustUserInformation");
    }
}
