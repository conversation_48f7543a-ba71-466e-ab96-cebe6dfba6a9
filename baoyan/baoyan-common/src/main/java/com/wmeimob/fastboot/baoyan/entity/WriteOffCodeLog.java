/*
 * WriteOffCodeLog.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 23 13:40:35 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "write_off_code_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class WriteOffCodeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 核销卡id
     */
    @Column(name = "write_off_id")
    private Integer writeOffId;
    /**
     * 核销门店
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 核销员工
     */
    @Column(name = "staff_id")
    private Integer staffId;
    /**
     * 核销时间
     */
    @Column(name = "write_off_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date writeOffDate;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;

    /**
     * 核销次数
     */
    @Column(name = "write_off_num")
    private Integer writeOffNum;

    /**
     *核销员工
     */
    @Transient
    private String staffName;
    @Transient
    private String searchName;

    @Transient
    private String storeName;
    @Transient
    private String actualAmount;
    @Transient
    private String userName;

    @Column(name = "cust_user_id")
    private Integer custUserId;

    /**
     * 来源商品
     */
    @Transient
    private String goodsName;
    /**
     * 核销卡名称
     */
    @Transient
    private String writeOffName;
    /**
     * 渠道来源名称
     */
    @Transient
    private String channelSourceName;
    /**
     * 来源订单
     */
    @Transient
    private String orderNo;
    @Transient
    public Integer orderType;
    /**
     * 来源商品ID
     */
    @Transient
    private Integer sourceGoodsId;
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;
    @Transient
    private String startTime;
    @Transient
    private String endTime;
    @Transient
    private String custUserName;
    /**
     * 核销次数 （当前用于前端展示 紧急处理==因字段有重复）
     */
    @Transient
    private Integer surplusNum;
    /**
     * 核销时间（当前用于前端展示 紧急处理==因字段有重复）
     */
    @Transient
    private Date expiryDate;

}