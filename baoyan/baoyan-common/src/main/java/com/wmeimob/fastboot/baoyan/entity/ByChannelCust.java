package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "by_channel_cust")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByChannelCust  implements Serializable,Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 会员id
     */
    @Column(name = "cust_id")
    private Integer custId;
    /**
     * 手机号
     */
    @Column(name = "mobile")
    private String mobile;
    /**
     * 渠道门店
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 可使用次数
     */
    @Column(name = "usable_num")
    private Integer usableNum;
    /**
     * 是否生成订单,0未生成，1已生成
     */
    @Column(name = "is_generate_order")
    private Boolean isGenerateOrder;
    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 渠道来源
     */
    @Column(name = "channel_source")
    private String channelSource;

    /**
     * 是否删除;0:否,1:是
     */
    @Column(name = "delete_status")
    private Boolean deleteStatus;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;

    @Override
    public ByChannelCust clone() throws CloneNotSupportedException {
        return (ByChannelCust) super.clone();
    }
}
