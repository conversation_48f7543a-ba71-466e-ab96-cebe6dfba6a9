package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TcTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-20 18:53:29
 */
public interface TcTemplateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcTemplate queryById(Integer id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcTemplate 实例对象
     * @return 对象列表
     */
    List<TcTemplate> queryAll(TcTemplate tcTemplate);

    /**
     * 新增数据
     *
     * @param tcTemplate 实例对象
     * @return 影响行数
     */
    int insert(TcTemplate tcTemplate);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TcTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TcTemplate> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TcTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<TcTemplate> entities);

    /**
     * 修改数据
     *
     * @param tcTemplate 实例对象
     * @return 影响行数
     */
    int update(TcTemplate tcTemplate);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<TcTemplate> wxQueryAll(TcTemplate queryObject);
}

