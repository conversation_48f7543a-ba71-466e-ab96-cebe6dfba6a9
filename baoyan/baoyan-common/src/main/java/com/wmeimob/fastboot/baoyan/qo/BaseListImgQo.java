package com.wmeimob.fastboot.baoyan.qo;

import com.wmeimob.fastboot.baoyan.entity.BaseListImg;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 多图设置请求参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseListImgQo extends BaseListImg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 跳转图片地址
     */
    private String targetImgUrl;
    /**
     * 类型id
     */
    private String classifyId;


}
