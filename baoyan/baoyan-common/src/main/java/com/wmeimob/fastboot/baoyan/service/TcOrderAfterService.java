package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcOrderAfter;
import com.wmeimob.fastboot.core.exception.CustomException;


/**
 * 淘潮玩订单售后(TcOrderAfter)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:33
 */
public interface TcOrderAfterService {




    /**
     * 管理员指定用户退货的地址
     * @param tcOrderAfter
     * @return
     */
    default boolean appointAddress(TcOrderAfter tcOrderAfter){
        throw new CustomException("appointAddress");
    }

    /**
     * 用户申请一个淘潮玩售后
     * @param tcOrderAfter
     * @return
     */
   default boolean applyAfter(TcOrderAfter tcOrderAfter){
       throw new CustomException("applyAfter");
   }

    /**
     * 同意售后并退款
     * @param id 售后订单id
     * @return
     */
    default boolean audit(Integer id){
        throw new CustomException("audit");
    };

    /**
     * 退款一个订单商品
     * @param orderGoodsId
     * @return
     */
    default boolean refundSingeOrder(Integer orderGoodsId, Integer refundCount){
        throw new CustomException("refundSingeOrder");
    }

    /**
     * 拒绝售后
     * @param tcOrderAfter
     * @return
     */
    default boolean rejectAfter(TcOrderAfter tcOrderAfter){
        throw new CustomException("rejectAfter");
    }
}