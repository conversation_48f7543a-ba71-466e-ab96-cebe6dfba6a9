package com.wmeimob.fastboot.baoyan.service;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import com.wmeimob.fastboot.core.exception.CustomException;

import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 商品
 * @date 2019-08-07 17:46
 * @Version 1.0
 */
public interface GoodsService {

    /**
     * 检查参数goodsId 的 stock 是否足够。不够直接抛出异常
     * 以前二期的代码！坑啊
     * @param goodsId
     * @param stock
     */
    default void checkGoodsStock(Integer goodsId, Integer stock) {
        throw new CustomException("checkGoodsStock");
    }

    default Map<String,Object> combinationGoods(Integer id){
        return null;
    };

    default Map<String,Object> detail(Integer id){
        return null;
    };


    default Map<String,Object> cardGoods(Integer id){
        return null;
    };

    default Map<String, Object> votingGoods(Integer id){
        return null;
    };

    /*商品评价*/
    default PageInfo evaluate(Integer id, Integer type, Integer pageIndex, Integer pageSize){
        return null;
    };


}
