package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByArticle;
import com.wmeimob.fastboot.baoyan.vo.GoodsVo;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByArticleService
 * @Description 文章表
 * <AUTHOR>
 * @Date Tue Jul 09 16:59:24 CST 2019
 * @version1.0
 **/
public interface ByArticleService extends CommonService<ByArticle>{

    /**
     * 文章表查询
     * @param id
     * @return
     */
    default ByArticle queryByArticleById(Object id){throw new NotImplementedException("queryByArticleById");};

    /**
     * 文章表添加
     * @param  byArticle
     * @return
     */
    default  void addByArticle(ByArticle byArticle){throw new NotImplementedException("addByArticle");};


    /**
     * 文章表删除
     * @param id
     * @return
     */
    default void removeByArticle(Object id){throw new NotImplementedException("removeByArticle");};


    /**
     * 文章表修改
     * @param byArticle
     * @return
     */
    default void modifyByArticle(ByArticle byArticle){throw new NotImplementedException("modifyByArticle");};

    /**
     * 删除
     * @param id
     */
    default void deleteByArticle(Integer id){throw new NotImplementedException("deleteByArticle");};

    /**
     * 商品选择返回
     * @return
     */
    List<GoodsVo> getGoods(String name);
}
