package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.core.exception.CustomException;

import java.util.List;

/**
 * 淘潮玩订单表(TcOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:34
 */
public interface TcOrderService {

    /**
     * 查看某个淘潮订单是否全部都退款了
     * @param orderId
     * @return
     */
    default boolean allRefund(Integer orderId){
        throw new CustomException("allRefund");
    }

    /**
     * 某个订单确认收货
     * @param id
     * @return
     */
    default boolean receiving(Integer id){
        throw new CustomException("receiving");
    }

    /**
     * 更新淘潮玩订单至已付款的状态
     * @param orderNo
     * @param transactionId
     * @return
     */
    default boolean updatePaid(String orderNo, String transactionId){
        throw new CustomException("updatePaid");
    }

    /**
     * 发货的方法
     * @param tcOrder
     * @return
     */
    default boolean deliver(TcOrder tcOrder){
        throw new CustomException("deliver");
    }

    /**
     * 查看某个订单详情的核销码是否全部核销完毕
     * @param detailId
     * @return
     */
    boolean allWriteOff(Integer detailId);

    /**
     * 检查某个订单的订单详情是否全部已完成
     * @param orderNo
     * @return
     */
    boolean allComplete(String orderNo);

    /**
     * 取消用户的未支付订单
     * @param id
     * @return
     */
    boolean userCancelOrder(Integer id);

    /**
     * 用户手动取消订单，定时器取消订单时的操作
     * 返回积分 | 返回优惠券
     * @param id
     * @return
     */
    boolean cancelOrder(Integer id);
}