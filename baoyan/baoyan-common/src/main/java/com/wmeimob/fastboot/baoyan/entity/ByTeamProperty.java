/*
* ByTeamProperty.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 15:59:56 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_team_property")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByTeamProperty implements Serializable {
	
   private static final long serialVersionUID = 1L;
	
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 拼团商品id
     */
    @Column(name = "team_id")
    private Integer teamId;
    /**
     * 属性id
     */
    @Column(name = "property_id")
    private Integer propertyId;
    /**
     * 商品属性值
     */
    @Column(name = "property_val")
    private String propertyVal;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

}