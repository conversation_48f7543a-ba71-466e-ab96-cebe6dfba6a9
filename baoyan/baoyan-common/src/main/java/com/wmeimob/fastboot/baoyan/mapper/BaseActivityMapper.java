
/*
* BaseActivityMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 30 16:58:36 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseActivity;

import java.util.List;

public interface BaseActivityMapper extends Mapper<BaseActivity> {
	List<BaseActivity> selectByExampleDel();
	/**List<BaseActivity> select(BaseActivity baseActivity);

	BaseActivity selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseActivity baseActivity);

	int updateByPrimaryKeySelective(BaseActivity baseActivity);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseActivity baseActivity);*/
	
}