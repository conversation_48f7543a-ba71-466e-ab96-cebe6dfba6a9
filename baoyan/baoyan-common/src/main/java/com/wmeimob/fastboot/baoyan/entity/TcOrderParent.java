package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付时用来聚合淘潮玩和门票(TcOrderParent)实体类
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TcOrderParent implements Serializable {
    private static final long serialVersionUID = 530669070532776718L;
    /**
     * 主键自增id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商家订单流水号
     */
    private String orderParentNo;
    /**
     * 订单原价
     */
    private BigDecimal orderAmount;
    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;
    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponAmount;
    /**
     * 积分抵扣金额
     */
    private BigDecimal integralAmount;
    /**
     * 创建时间
     */
    private Date gmtCreate;



}