package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.util.List;

/**
 * (TcImgModule)表服务接口
 *
 * <AUTHOR>
 * @since 2021-09-02 21:22:18
 */
public interface TcImgModuleService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    default TcImgModule queryById(Integer id) {
        throw new NotImplementedException("queryById");
    }



    /**
     * 新增数据
     *
     * @param tcImgModule 实例对象
     * @return 实例对象
     */
    default Boolean insert(TcImgModule tcImgModule) {
        throw new NotImplementedException("insert");
    }


    /**
     * 修改数据
     *
     * @param tcImgModule 实例对象
     * @return 实例对象
     */
    default Boolean update(TcImgModule tcImgModule) {
        throw new NotImplementedException("update");
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    default boolean deleteById(Integer id) {
        throw new NotImplementedException("deleteById");
    }


    default List<TcImgModule> queryAll(TcImgModule tcImgModule) {
        throw new NotImplementedException("queryAll");
    }


   default Boolean onAndOffShelves(TcImgModule object){
       throw new NotImplementedException("onAndOffShelves");
   }
}