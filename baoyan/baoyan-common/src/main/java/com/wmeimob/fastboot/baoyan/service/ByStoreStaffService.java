package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByStoreStaffService
 * @Description 门店员工
 * <AUTHOR>
 * @Date Thu Jul 11 09:48:59 CST 2019
 * @version1.0
 **/
public interface ByStoreStaffService extends CommonService<ByStoreStaff>{

    /**
     * 门店员工查询
     * @param id
     * @return
     */
    default ByStoreStaff queryByStoreStaffById(Object id){throw new NotImplementedException("queryByStoreStaffById");};

    /**
     * 门店员工添加
     * @param  byStoreStaff
     * @return
     */
    default  void addByStoreStaff(ByStoreStaff byStoreStaff){throw new NotImplementedException("addByStoreStaff");};


    /**
     * 门店员工删除
     * @param id
     * @return
     */
    default void removeByStoreStaff(Object id){throw new NotImplementedException("removeByStoreStaff");};


    /**
     * 门店员工修改
     * @param byStoreStaff
     * @return
     */
    default void modifyByStoreStaff(ByStoreStaff byStoreStaff){throw new NotImplementedException("modifyByStoreStaff");};
}
