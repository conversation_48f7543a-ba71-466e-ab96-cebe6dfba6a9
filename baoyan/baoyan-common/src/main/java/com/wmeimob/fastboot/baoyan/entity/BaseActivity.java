/*
 * BaseActivity.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 30 16:58:36 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "base_activity")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BaseActivity<K,V> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 活动名称
     */
    @Column(name = "activity_name")
    private String activityName;
    /**
     * 图片地址
     */
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 内轮图
     * */
    @Column(name="base_banner")
    private String baseBanner;
    /**
     * 跳转类型id
     */
    @Column(name = "jump_type")
    private Integer jumpType;
    /**
     * 跳转content
     */
    @Column(name = "target")
    private String target;
    /**
     * 上下架 0下架 1上架
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 上下架 0下架 1上架
     */
    @Column(name = "is_del")
    private Integer isDel;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * targetImgUrl
     */
    @Transient
    private String targetImgUrl;
    /**
     * 类型id
     */
    @Transient
    private Integer classifyId;

    @Transient
    private String title;

    public String getTitle() {
        return activityName;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}