package com.wmeimob.fastboot.baoyan.utils;

import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.util.StringUtil;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/28
 */
public class Assert {

    /**
     * 集合为null 抛出异常
     * @param coll
     * @param msg
     */
    public static void isEmpty(Collection coll, String msg){
        if (CollectionUtils.isEmpty(coll))
            throw new CustomException(msg);
    }

    /**
     * 如果两个obj 相同，抛出异常
     * @param obj1
     * @param obj2
     * @param msg
     */
    public static void eq(Object obj1, Object obj2, String msg){
        if ( Objects.equals(obj1,obj2) )
            throw new CustomException(msg);
    }

    /**
     * 如果两个obj 不相等，抛出异常
     * @param obj1
     * @param obj2
     * @param msg
     */
    public static void notEq(Object obj1, Object obj2, String msg){
        if ( !Objects.equals(obj1, obj2) )
            throw new CustomException(msg);
    }

    /**
     * 如果两个decimal1 不相等，抛出异常，小心空指针
     * @param decimal1
     * @param decimal2
     * @param msg
     */
    public static void notEq(BigDecimal decimal1, BigDecimal decimal2, String msg){
        if ( decimal1 == decimal2 ) return;
        if ( decimal1.compareTo( decimal2 ) != 0 )
            throw new CustomException(msg);
    }

    /**
     * 如果参数1 比 参数2 小，抛出异常
     * @param number1
     * @param number2
     * @param msg
     */
    public static void lessThen(int number1, int number2, String msg){
        if ( number1 < number2 )
            throw new CustomException(msg);
    }

    /**
     * 对象为空，抛出异常
     * @param obj
     * @param msg
     */
    public static void isNull(Object obj,String msg){
        if (obj==null || StringUtils.isEmpty(obj))
            throw new CustomException(msg);
    }

    public static void isNull(String msg, Object...objs){
        for (Object obj : objs) {
            isNull(obj,msg);
        }
    }

    /**
     * 如果对象全部都是空的，抛出异常
     * @param msg
     * @param objs
     */
    public static void allNull(String msg, Object...objs){
        //是否全部为空
        boolean flag = true;
        for (Object obj : objs) {
            if (obj!=null){
                flag = false;
                break;
            }
        }

        if (flag){
            throw new CustomException(msg);
        }
    }

}
