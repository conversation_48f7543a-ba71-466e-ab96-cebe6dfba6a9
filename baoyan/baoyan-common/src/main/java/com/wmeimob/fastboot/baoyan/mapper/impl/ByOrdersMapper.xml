<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByOrdersMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrders" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="integralAmount" column="integral_amount" jdbcType="DECIMAL"/>
        <result property="couponAmount" column="coupon_amount" jdbcType="DECIMAL"/>
        <result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="actualAmount" column="actual_amount" jdbcType="DECIMAL"/>
        <result property="couponId" column="coupon_id" jdbcType="INTEGER"/>
        <result property="goodsType" column="goods_type" jdbcType="INTEGER"/>
        <result property="evalType" column="eval_type" jdbcType="TINYINT"/>
        <result property="amountBeforeDiscount" column="amount_before_discount" jdbcType="DECIMAL"/>
        <result property="amountAfterDiscount" column="amount_after_discount" jdbcType="DECIMAL"/>
        <result property="discountAmount" column="discount_amount" jdbcType="DECIMAL"/>
        <result property="isRefund" column="is_refund" jdbcType="TINYINT"/>
        <result property="isDel" column="is_del" jdbcType="TINYINT"/>
        <result property="isChannel" column="is_channel" jdbcType="INTEGER"/>
        <result property="channelId" column="channel_id" jdbcType="INTEGER"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="cancelTime" column="cancel_time" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="isAfterSale" column="is_after_sale" jdbcType="TINYINT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="fromId" column="from_id" jdbcType="VARCHAR"/>
        <result property="payFlowNo" column="pay_flow_no" jdbcType="VARCHAR"/>
        <result property="orderCloseType" column="order_close_type" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrders" id="BaseResultMap2" extends="BaseResultMap">
        <result property="custUserName" column="cust_user_name" jdbcType="VARCHAR"/>
        <result property="currentUserId" column="current_user_id" jdbcType="INTEGER"/>
        <result property="currentUserName" column="current_user_name" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>

        <result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>
        <result property="goodsPrice" column="goodsPrice" jdbcType="DECIMAL"/>
        <result property="goodsNum" column="goodsNum" jdbcType="INTEGER"/>
        <result property="couponPrice" column="couponPrice" jdbcType="DECIMAL"/>
        <result property="integralPrice" column="integralPrice" jdbcType="DECIMAL"/>
    </resultMap>
    <resultMap type="com.wmeimob.fastboot.baoyan.vo.OrderInfoVo" id="ListResultMap" extends="BaseResultMap">
        <result property="allGoodsNum" column="allGoodsNum" jdbcType="INTEGER"/>
        <result property="isPresenter" column="isPresenter" jdbcType="INTEGER"/>
        <result property="isCurrent" column="isCurrent" jdbcType="INTEGER"/>
        <collection property="orderGoodsList" ofType="com.wmeimob.fastboot.baoyan.entity.ByOrderGoods"
                    column="{orderId=id}"
                    select="com.wmeimob.fastboot.baoyan.mapper.ByOrderGoodsMapper.selectByOrderId"/>
    </resultMap>

    <select id="findByOrderNo" resultMap="BaseResultMap">
        select *
        from by_orders
        where order_no = #{orderNo}
    </select>

    <select id="queryOrderStatistics" resultMap="BaseResultMap2">
        SELECT tem.* from (

        SELECT
        b.order_no,
        u.nick_name as cust_user_name,
        u.mobile,
        b.order_time,
        b.order_amount,
        b.coupon_amount,
        b.integral_amount,
        b.actual_amount,
        ifnull(wc.total_num - wc.surplus_num,0) as writeOffNum
        FROM
        by_orders b
        LEFT JOIN by_cust_user u ON u.id = b.user_id
        left join by_order_after c on b.id = c.detail_id
        left join write_off_code wc on wc.detail_id = u.id
        <where>
            and b.order_status in(2,3,4)and b.order_no is not NULL
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((b.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
            <if test="orderStatus !=null">
                AND b.order_status= #{orderStatus}
            </if>
            <if test="returnStatus !=null">
                AND c.after_status= #{returnStatus}
            </if>
            <if test="writeStatus !=null">
                AND wc.status= #{writeStatus}
            </if>
            <if test="couponAmount == 1">
                and b.integral_amount != 0
            </if>
            <if test="couponAmount == 2">
                and b.coupon_amount != 0
            </if>
            <if test="minMoney != null">
                and b.order_amount >= #{minMoney}
            </if>
            <if test="maxMoney != null">
                and #{minMoney} >= b.order_amount
            </if>
        </where>
            group by u.id
        UNION ALL
        SELECT
        tt.order_no,
        u.nick_name as cust_user_name,
        u.mobile,
        tt.pay_time,
        tt.order_amount,
        tt.coupon_amount ,
        tt.integral_amount,
        tt.pay_amount as actual_amount,
        0 as writeOffNum
        FROM
        by_team_order tt
        LEFT JOIN by_cust_user u ON u.id = tt.user_id
        <where>
            and tt.order_status in(2,3,4) and tt.order_no is not NULL
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(tt.gmt_create,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(tt.gmt_create,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((tt.order_no like concat('%',#{searchName},'%')) or (u.nick_name like
                concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
            <if test="couponAmount == 1">
                and tt.integral_amount != 0
            </if>
            <if test="couponAmount == 2">
                and tt.coupon_amount != 0
            </if>
            <if test="minMoney != null">
                and tt.order_amount >= #{minMoney}
            </if>
            <if test="maxMoney != null">
                and #{minMoney} >= tt.order_amount
            </if>
        </where>
        )tem
        ORDER BY tem.order_time DESC
    </select>

    <sql id="orderColumn">
        b.id ,b.order_no ,b.goods_id ,b.integral_amount ,b.coupon_amount ,b.order_amount ,b.user_id ,b.retired_amount
        ,b.current_user_id ,b.order_time ,b.cancel_time ,
        b.is_channel,b.channel_id,
        if( b.order_status=4 and b.pay_flow_no is null, 3, b.order_status) order_status  ,
        b.actual_amount ,b.coupon_id ,
        b.goods_type ,b.amount_before_discount ,b.amount_after_discount ,b.discount_amount ,
        b.is_refund ,b.is_del ,b.eval_type ,b.is_after_sale ,b.pay_type ,b.order_close_type ,b.pay_time ,b.gmt_update
        ,b.gmt_create ,b.pay_flow_no ,b.remark ,b.from_id

    </sql>

    <select id="findByCondition" resultMap="BaseResultMap2">
        SELECT
        <include refid="orderColumn"/>
        ,
        u.nick_name as cust_user_name,
        u.mobile,
        IF(cu.nick_name IS NOT NULL ,cu.nick_name,'') as current_user_name,
        og.goods_name as goodsName
        FROM
        by_orders b
        left join by_order_goods og on b.id = og.order_id
        LEFT JOIN by_cust_user u ON u.id = b.user_id
        LEFT JOIN by_cust_user cu ON cu.id = b.current_user_id
        <where>
            <if test="id !=null">
                AND b.id= #{id}
            </if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="orderStatus !=null">
                AND b.order_status= #{orderStatus}
            </if>
            <if test="isChannel !=null and isChannel ==1">
                AND b.is_channel= 1
            </if>
            <if test="isChannel !=null and isChannel ==0">
                AND (b.is_channel= 0 or b.is_channel is null)
            </if>
            <if test="goodsName !=null and goodsName !='' ">
                and og.goods_name like concat('%',#{goodsName},'%')
            </if>

            <if test="couponAmount == 1">
                and b.integral_amount != 0
            </if>
            <if test="couponAmount == 2">
                and b.coupon_amount != 0
            </if>
            <if test="minMoney != null">
                and b.order_amount >= #{minMoney}
            </if>
            <if test="maxMoney != null">
                and #{maxMoney} >= b.order_amount
            </if>
            <if test="orderNo !=null and orderNo !='' ">
                and b.order_no = #{orderNo}
            </if>
            <if test="mobile !=null and mobile !='' ">
                and u.mobile = #{mobile}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((b.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
        </where>
        order by b.order_time desc
    </select>
    <select id="getCountByUserIdAndState" resultType="java.lang.Integer">
                    SELECT COUNT(id) FROM by_orders
                    WHERE is_del = 0 AND order_status=${state}
                    AND IF (
                    current_user_id IS NOT NULL,
                    current_user_id = ${userId},
                    user_id = ${userId}
                    )

    </select>
    <select id="userOrderList" resultMap="ListResultMap">
        <if test="orderStatus != null and orderStatus == 2">
            SELECT * FROM (
        </if>
        SELECT
        IF(bo.user_id = #{userId} AND bo.current_user_id IS NOT NULL AND bo.user_id != bo.current_user_id,1,0)
        isPresenter,
        IF(bo.current_user_id IS NULL,1,0) isCurrent,
        bo.id,
        bo.order_no,
        bo.order_status,
        bo.order_amount,
        bo.pay_time,
        bo.pay_type,
        bo.actual_amount,
        bo.eval_type,
        (SELECT SUM(og.goods_num) FROM by_order_goods og WHERE og.order_id = bo.id) as allGoodsNum
        FROM
        by_orders bo
        WHERE bo.is_del = 0
        AND
        <!-- IF(bo.current_user_id IS NULL,bo.user_id = #{userId},bo.current_user_id = #{userId}) -->
        ( bo.user_id = #{userId} or bo.current_user_id = #{userId} )
        <choose>
            <!-- 查询已完成订单时也查看赠送的订单 -->
            <when test="orderStatus == 3">
                and (bo.order_status = #{orderStatus} or bo.current_user_id is not null )
            </when>
            <when test="orderStatus != null">
                and bo.order_status = #{orderStatus}
            </when>
        </choose>
        <if test="payType != null">
        and bo.pay_type = #{payType}
        </if>
        order by bo.id desc
        <if test="orderStatus != null and orderStatus == 2">
            )a WHERE a.isCurrent=1 OR (a.isCurrent=0 AND a.isPresenter = 0)
        </if>
    </select>


    <select id="queryGoodsStatistics-1" resultType="com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO">
        SELECT
        g.goods_no as goodsNo,
        og.goods_name as goodsName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 1
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1 AND
        order_goods_id = og.goods_id
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_goods_info g ON g.id = og.goods_id
        left JOIN
        (
        SELECT
        gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.goods_id
        ) t ON t.goods_id=og.goods_id
        LEFT JOIN (SELECT
        gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.goods_id ) c on c.goods_id=og.goods_id
        <where>
            and bo.order_status in(2,3,4) and og.product_type=1
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY og.goods_id
        UNION
        SELECT
        g.goods_no as goodsNo,
        g.goods_name as goodsName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        g.sell_price as goodsPrice,
        (SUM(tg.goods_id))as salesNum,
        SUM(tt.order_amount) as amount,
        (SUM(tt.coupon_amount)) as couponAmount,
        (SUM(tt.integral_amount)) as integralAmount,
        (
        SUM(tt.pay_amount) - (
        SELECT
        SUM(`after_amount`)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` = 2
        AND order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        )
        ) actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` =2 AND
        order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ) as refundNum
        FROM
        by_team_order tt
        LEFT JOIN by_team_goods tg ON tt.team_goods_id=tg.id
        LEFT JOIN by_goods_info g on g.id=tg.goods_id
        left JOIN
        (
        SELECT
        gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.goods_id
        ) t ON t.goods_id=tg.goods_id
        LEFT JOIN (SELECT
        gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.goods_id ) c on c.goods_id=tg.goods_id
        <where>
            and tt.order_status in(2,3,4)
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime!='' ">
                AND tt.order_time&gt;=#{startTime}
            </if>
            <if test="endTime !=null and endTime!=''">
                AND tt.order_time &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="searchName !=null and searchName!=''">
                AND ((g.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (g.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY tg.goods_id
    </select>


    <select id="queryGoodsStatistics" resultType="com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO">
        SELECT
        g.goods_no AS goodsNo,
        g.goods_name as goodsName,
        cg.name combinationName,
        t.classifyName AS classifyName,
        t.classifyIds,
        c.`name` AS storeName,
        c.storeIds,
        og.goods_price AS goodsPrice,
        (SUM(og.goods_num)) AS salesNum,
        SUM(
        og.goods_price * og.goods_num
        ) AS amount,
        (SUM(og.coupon_price)) AS couponAmount,
        (SUM(og.integral_price)) AS integralAmount,
        (
        IFNULL(
        SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),
        0
        ) - IFNULL(
        (
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 5
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (
        SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ) AS refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_combination_goods cg ON cg.id = og.goods_id
        LEFT JOIN by_goods_info g ON g.id = cg.goods_id
        LEFT JOIN (
        SELECT
        gc.goods_id,
        GROUP_CONCAT(bc.classify_title) AS classifyName,
        GROUP_CONCAT(bc.id) AS classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc ON bc.id = gc.classify_id
        GROUP BY
        gc.goods_id
        ) t ON t.goods_id = g.id
        LEFT JOIN (
        SELECT
        gs.goods_id,
        GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        LEFT JOIN base_store bs ON bs.id = gs.store_id
        GROUP BY
        gs.goods_id
        ) c ON c.goods_id = g.id
        <where>
            AND bo.order_status IN (2, 3, 4) AND og.product_type = 5
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
            <if test="combinationName !=null and combinationName!=''">
                AND cg.name LIKE CONCAT('%',#{combinationName},'%')
            </if>
        </where>
        GROUP BY
        og.goods_id
        UNION
        -- ------------------------
        SELECT
        g.goods_no as goodsNo,
        g.goods_name as goodsName,
        g.spec_name as combinationName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 1
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1 AND
        order_goods_id = og.goods_id
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_goods_info g ON g.id = og.goods_id
        left JOIN
        (
        SELECT
        gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.goods_id
        ) t ON t.goods_id=og.goods_id
        LEFT JOIN (SELECT
        gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.goods_id ) c on c.goods_id=og.goods_id
        <where>
            and bo.order_status in(2,3,4) and og.product_type=1
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
            <if test="combinationName !=null and combinationName!=''">
                AND g.spec_name LIKE CONCAT('%',#{combinationName},'%')
            </if>
        </where>
        GROUP BY og.goods_id
        UNION
        SELECT
        g.goods_no as goodsNo,
        g.goods_name as goodsName,
        g.spec_name as combinationName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        g.sell_price as goodsPrice,
        (SUM(tg.goods_id))as salesNum,
        SUM(tt.order_amount) as amount,
        (SUM(tt.coupon_amount)) as couponAmount,
        (SUM(tt.integral_amount)) as integralAmount,
        (
        SUM(tt.pay_amount) - (
        SELECT
        SUM(`after_amount`)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` = 2
        AND order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        )
        ) actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` =2 AND
        order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ) as refundNum
        FROM
        by_team_order tt
        LEFT JOIN by_team_goods tg ON tt.team_goods_id=tg.id
        LEFT JOIN by_goods_info g on g.id=tg.goods_id
        left JOIN
        (
        SELECT
        gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.goods_id
        ) t ON t.goods_id=tg.goods_id
        LEFT JOIN (SELECT
        gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.goods_id ) c on c.goods_id=tg.goods_id
        <where>
            and tt.order_status in(2,3,4)
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime!='' ">
                AND tt.order_time&gt;=#{startTime}
            </if>
            <if test="endTime !=null and endTime!=''">
                AND tt.order_time &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="searchName !=null and searchName!=''">
                AND ((g.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (g.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
            <if test="combinationName !=null and combinationName!=''">
                AND g.spec_name LIKE CONCAT('%',#{combinationName},'%')
            </if>
        </where>
        GROUP BY tg.goods_id
    </select>


    <select id="getFinanceOrderStatisticsSum" resultType="java.util.Map">
        SELECT
        SUM(orderAmount) orderAmount,
        SUM(couponAmount) couponAmount,
        SUM(integralAmount) integralAmount,
        SUM(orderAmount) - SUM(returnAmonut)-SUM(couponAmount)-SUM(integralAmount) AS actualAmount
        FROM
        (
        SELECT
        IFNULL((SUM(b.order_amount)), 0) AS orderAmount,
        IFNULL((SUM(b.coupon_amount)), 0) AS couponAmount,
        IFNULL((SUM(b.integral_amount)), 0) AS integralAmount,
        IFNULL((
        SELECT
        SUM(oa.after_amount)
        FROM
        by_order_after oa
        LEFT JOIN by_orders bo ON bo.order_no = oa.order_no
        LEFT JOIN by_cust_user u ON u.id = bo.user_id
        WHERE
        oa.after_status = 1
        AND oa.resouce_type = 1
        <if test="startTime != null and startTime != ''">
            AND DATE_FORMAT(bo.order_time,'%Y-%m-%d')>= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE_FORMAT(bo.order_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="searchName !=null and searchName !='' ">
            and ((bo.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
            or (u.mobile like concat('%',#{searchName},'%')))
        </if>


        <if test="couponAmount == 1">
            and bo.integral_amount != 0
        </if>
        <if test="couponAmount == 2">
            and bo.coupon_amount != 0
        </if>
        <if test="minMoney != null">
            and bo.order_amount >= #{minMoney}
        </if>
        <if test="maxMoney != null">
            and #{minMoney} >= bo.order_amount
        </if>


        ),0) AS returnAmonut
        FROM
        by_orders b
        LEFT JOIN by_cust_user u ON u.id = b.user_id
        <where>
            and b.order_status in(2,3,4)and b.order_no is not NULL
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((b.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>

            <if test="couponAmount == 1">
                and b.integral_amount != 0
            </if>
            <if test="couponAmount == 2">
                and b.coupon_amount != 0
            </if>
            <if test="minMoney != null">
                and b.order_amount >= #{minMoney}
            </if>
            <if test="maxMoney != null">
                and #{minMoney} >= b.order_amount
            </if>
        </where>
        UNION ALL
        SELECT
        IFNULL((SUM(tt.order_amount)), 0) AS orderAmount,
        IFNULL((SUM(tt.coupon_amount)), 0) AS couponAmount,
        IFNULL(
        (SUM(tt.integral_amount)),
        0
        ) AS integralAmount,
        IFNULL((
        SELECT
        SUM(oa.after_amount)
        FROM
        by_order_after oa
        LEFT JOIN by_team_order tt ON tt.order_no = oa.order_no
        LEFT JOIN by_cust_user u ON u.id = tt.user_id
        WHERE
        oa.after_status = 1
        AND oa.resouce_type = 2
        <if test="startTime != null and startTime != ''">
            AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d')>= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="searchName !=null and searchName !='' ">
            and ((tt.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
            or (u.mobile like concat('%',#{searchName},'%')))
        </if>

        <if test="couponAmount == 1">
            and tt.integral_amount != 0
        </if>
        <if test="couponAmount == 2">
            and tt.coupon_amount != 0
        </if>
        <if test="minMoney != null">
            and tt.order_amount >= #{minMoney}
        </if>
        <if test="maxMoney != null">
            and #{minMoney} >= tt.order_amount
        </if>
        ),0) AS returnAmonut
        FROM
        by_team_order tt
        LEFT JOIN by_cust_user u ON u.id = tt.user_id
        <where>
            and tt.order_status in(2,3,4) and tt.order_no is not NULL
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(tt.gmt_create,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(tt.gmt_create,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((tt.order_no like concat('%',#{searchName},'%')) or (u.nick_name like
                concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
            <if test="couponAmount == 1">
                and tt.integral_amount != 0
            </if>
            <if test="couponAmount == 2">
                and tt.coupon_amount != 0
            </if>
            <if test="minMoney != null">
                and tt.order_amount >= #{minMoney}
            </if>
            <if test="maxMoney != null">
                and #{minMoney} >= tt.order_amount
            </if>
        </where>
        ) a
    </select>





    <select id="getFinanceGoodsStatisticsSum-1" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(b.salesNum),0) salesNum,
        IFNULL(SUM(refundNum),0) refundNum,
        IFNULL(SUM(amount),0) amount,
        IFNULL(SUM(couponAmount),0) couponAmount,
        IFNULL(SUM(integralAmount),0) integralAmount,
        IFNULL(SUM(amount)-IFNULL(SUM(refundAmount),0)-SUM(couponAmount)-SUM(integralAmount), 0) actualAmount
        FROM (
        SELECT
        g.goods_no AS goodsNo,
        g.goods_name AS goodsName,
        t.classifyName AS classifyName,
        t.classifyIds,
        c.`name` AS storeName,
        c.storeIds,
        og.goods_price AS goodsPrice,
        (SUM(og.goods_num)) AS salesNum,
        SUM(
        og.goods_price * og.goods_num
        ) AS amount,
        (SUM(og.coupon_price)) AS couponAmount,
        (SUM(og.integral_price)) AS integralAmount,
        (
        SELECT
        SUM(oa.after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        oa.`resouce_type` = 1 AND oa.after_status=1 and oo.product_type=1
        AND oa.order_goods_id = og.goods_id
        <if test="storeId !=null">
            AND FIND_IN_SET(#{storeId},c.storeIds)
        </if>
        <if test="classifyId !=null">
            AND FIND_IN_SET(#{classifyId},t.classifyIds)
        </if>
        <if test="startTime !=null and startTime !=''">
            AND og.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        <if test="searchName !=null and searchName!=''">
            AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
            (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
        </if>
        ) AS refundAmount,
        (
        SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        `resouce_type` = 1 and oa.after_status=1 and oo.product_type=1
        AND order_goods_id = og.goods_id
        <if test="storeId !=null">
            AND FIND_IN_SET(#{storeId},c.storeIds)
        </if>
        <if test="classifyId !=null">
            AND FIND_IN_SET(#{classifyId},t.classifyIds)
        </if>
        <if test="startTime !=null and startTime !=''">
            AND og.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        <if test="searchName !=null and searchName!=''">
            AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
            (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
        </if>
        ) as refundNum,
        g.id
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no=bo.order_no
        LEFT JOIN by_goods_info g ON g.id = og.order_id
        LEFT JOIN (
        SELECT
        gc.goods_id,
        GROUP_CONCAT(bc.classify_title) AS classifyName,
        GROUP_CONCAT(bc.id) AS classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc ON bc.id = gc.classify_id
        GROUP BY
        gc.goods_id
        ) t ON t.goods_id = og.goods_id
        LEFT JOIN (
        SELECT
        gs.goods_id,
        GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        LEFT JOIN base_store bs ON bs.id = gs.store_id
        GROUP BY
        gs.goods_id
        ) c ON c.goods_id = og.goods_id
        <where>
            and bo.order_status in(2,3,4) and og.product_type=1
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY
        og.goods_id
        UNION
        SELECT
        g.goods_no AS goodsNo,
        g.goods_name AS goodsName,
        t.classifyName AS classifyName,
        t.classifyIds,
        c.`name` AS storeName,
        c.storeIds,
        g.sell_price AS goodsPrice,
        (SUM(tg.goods_id)) AS salesNum,
        SUM(tt.order_amount) AS amount,
        (SUM(tt.coupon_amount)) AS couponAmount,
        (SUM(tt.integral_amount)) AS integralAmount,
        (
        SUM(tt.pay_amount) - (
        SELECT
        SUM(`after_amount`)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` = 2
        AND order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        )
        ) actualAmount,

        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` =2 AND
        order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ) as refundNum,
        g.id
        FROM
        by_team_order tt
        LEFT JOIN by_team_goods tg ON tt.team_goods_id = tg.id
        LEFT JOIN by_goods_info g ON g.id = tg.goods_id
        LEFT JOIN (
        SELECT
        gc.goods_id,
        GROUP_CONCAT(bc.classify_title) AS classifyName,
        GROUP_CONCAT(bc.id) AS classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc ON bc.id = gc.classify_id
        GROUP BY
        gc.goods_id
        ) t ON t.goods_id = tg.goods_id
        LEFT JOIN (
        SELECT
        gs.goods_id,
        GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        LEFT JOIN base_store bs ON bs.id = gs.store_id
        GROUP BY
        gs.goods_id
        ) c ON c.goods_id = tg.goods_id
        <where>
            and tt.order_status in(2,3,4)
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName!=''">
                AND ((g.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (g.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY
        tg.goods_id
        )b
    </select>


    <select id="getFinanceGoodsStatisticsSum" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(b.salesNum),0) salesNum,
        IFNULL(SUM(refundNum),0) refundNum,
        IFNULL(SUM(amount),0) amount,
        IFNULL(SUM(couponAmount),0) couponAmount,
        IFNULL(SUM(integralAmount),0) integralAmount,
        IFNULL(SUM(amount)-IFNULL(SUM(refundAmount),0)-SUM(couponAmount)-SUM(integralAmount), 0) actualAmount
        FROM (
        SELECT
        g.goods_no AS goodsNo,
        g.goods_name AS goodsName,
        t.classifyName AS classifyName,
        t.classifyIds,
        c.`name` AS storeName,
        c.storeIds,
        og.goods_price AS goodsPrice,
        (SUM(og.goods_num)) AS salesNum,
        SUM(
        og.goods_price * og.goods_num
        ) AS amount,
        (SUM(og.coupon_price)) AS couponAmount,
        (SUM(og.integral_price)) AS integralAmount,
        (
        SELECT
        SUM(oa.after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        oa.`resouce_type` = 1 AND oa.after_status=1 and (oo.product_type=1 OR oo.product_type=5)
        AND oa.order_goods_id = og.goods_id
        <if test="storeId !=null">
            AND FIND_IN_SET(#{storeId},c.storeIds)
        </if>
        <if test="classifyId !=null">
            AND FIND_IN_SET(#{classifyId},t.classifyIds)
        </if>
        <if test="startTime !=null and startTime !=''">
            AND og.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        <if test="searchName !=null and searchName!=''">
            AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
            (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
        </if>
        ) AS refundAmount,
        (
        SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        `resouce_type` = 1 and oa.after_status=1 and (oo.product_type=1 OR oo.product_type=5)
        AND order_goods_id = og.goods_id
        <if test="storeId !=null">
            AND FIND_IN_SET(#{storeId},c.storeIds)
        </if>
        <if test="classifyId !=null">
            AND FIND_IN_SET(#{classifyId},t.classifyIds)
        </if>
        <if test="startTime !=null and startTime !=''">
            AND og.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        <if test="searchName !=null and searchName!=''">
            AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
            (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
        </if>
        ) as refundNum,
        g.id
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no=bo.order_no
        LEFT JOIN by_goods_info g ON g.id = og.order_id
        LEFT JOIN (
        SELECT
        gc.goods_id,
        GROUP_CONCAT(bc.classify_title) AS classifyName,
        GROUP_CONCAT(bc.id) AS classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc ON bc.id = gc.classify_id
        GROUP BY
        gc.goods_id
        ) t ON t.goods_id = og.goods_id
        LEFT JOIN (
        SELECT
        gs.goods_id,
        GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        LEFT JOIN base_store bs ON bs.id = gs.store_id
        GROUP BY
        gs.goods_id
        ) c ON c.goods_id = og.goods_id
        <where>
            and bo.order_status in(2,3,4) and (og.product_type=1 OR og.product_type=5)
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((og.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (og.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY
        og.goods_id
        UNION
        SELECT
        g.goods_no AS goodsNo,
        g.goods_name AS goodsName,
        t.classifyName AS classifyName,
        t.classifyIds,
        c.`name` AS storeName,
        c.storeIds,
        g.sell_price AS goodsPrice,
        (SUM(tg.goods_id)) AS salesNum,
        SUM(tt.order_amount) AS amount,
        (SUM(tt.coupon_amount)) AS couponAmount,
        (SUM(tt.integral_amount)) AS integralAmount,
        (
        SUM(tt.pay_amount) - (
        SELECT
        SUM(`after_amount`)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` = 2
        AND order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        )
        ) actualAmount,

        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        left join by_team_order t1 ON oa.detail_id=t1.id
        WHERE
        `resouce_type` =2 AND
        order_goods_id = tg.goods_id
        <if test="startTime !=null and startTime !=''">
            AND t1.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND t1.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ) as refundNum,
        g.id
        FROM
        by_team_order tt
        LEFT JOIN by_team_goods tg ON tt.team_goods_id = tg.id
        LEFT JOIN by_goods_info g ON g.id = tg.goods_id
        LEFT JOIN (
        SELECT
        gc.goods_id,
        GROUP_CONCAT(bc.classify_title) AS classifyName,
        GROUP_CONCAT(bc.id) AS classifyIds
        FROM
        by_goods_classify gc
        LEFT JOIN base_classify bc ON bc.id = gc.classify_id
        GROUP BY
        gc.goods_id
        ) t ON t.goods_id = tg.goods_id
        LEFT JOIN (
        SELECT
        gs.goods_id,
        GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_goods_store gs
        LEFT JOIN base_store bs ON bs.id = gs.store_id
        GROUP BY
        gs.goods_id
        ) c ON c.goods_id = tg.goods_id
        <where>
            and tt.order_status in(2,3,4)
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(tt.pay_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName!=''">
                AND ((g.goods_no LIKE CONCAT('%',#{searchName},'%')) OR
                (g.goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY
        tg.goods_id
        )b
    </select>

    <select id="findExportByCondition" resultMap="BaseResultMap2">
        SELECT
        b.*,
        u.nick_name as cust_user_name,
        u.mobile,
        og.goods_name as goodsName,
        og.goods_num as goodsNum,
        og.goods_price as goodsPrice,
        og.integral_price as integralPrice,
        og.coupon_price as couponPrice
        FROM
        by_order_goods og
        LEFT JOIN by_orders b ON b.id=og.order_id
        LEFT JOIN by_cust_user u ON u.id = b.user_id
        <where>
            b.id >0
            <if test="id !=null">
                AND b.id= #{id}
            </if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(b.order_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="orderStatus !=null">
                AND b.order_status= #{orderStatus}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((b.order_no like concat('%',#{searchName},'%')) or (u.nick_name like concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
            <if test="goodsName !=null and goodsName !='' ">
                and og.goods_name like concat('%',#{goodsName},'%')
            </if>
            <if test="isChannel !=null and isChannel ==1">
                AND b.is_channel= 1
            </if>
            <if test="isChannel !=null and isChannel ==0">
                AND (b.is_channel= 0 or b.is_channel is null)
            </if>
        </where>
        order by b.order_time desc;
    </select>
    <select id="queryFinanceStatistics" resultType="com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO">
        SELECT
        wc.write_off_name as writeOffName,
        cu.nick_name as custUserName,
        cu.mobile as mobile,
        CASE wc.order_type
        when 0 THEN CONCAT('普通订单：',bo.order_no)
        when 1 THEN CONCAT('拼团订单：',tt.order_no)
        END as orderNo,
        CASE wc.order_type
        when 0 THEN bo.order_time
        when 1 THEN tt.gmt_create
        END as orderTime,
        CASE wc.order_type
        when 0 THEN bo.pay_time
        when 1 THEN tt.pay_time
        END as payTime,
        IF(og.product_type = 5,(SELECT gi.goods_name FROM by_combination_goods cg LEFT JOIN by_goods_info gi ON
        cg.goods_id = gi.id WHERE cg.id = wc.source_goods_id limit 1),wc.goods_name) AS goodsName,
        CASE wc.order_type
        when 0 THEN og.goods_price
        when 1 THEN tt.order_amount
        END as amount,
        CASE wc.order_type
        when 0 THEN og.coupon_price
        when 1 THEN tt.coupon_amount
        END as couponAmount,
        CASE wc.order_type
        when 0 THEN og.integral_price
        when 1 THEN tt.integral_amount
        END as integralAmount,
        CASE wc.order_type
        when 0 THEN og.goods_price-og.coupon_price-og.integral_price
        when 1 THEN tt.pay_amount
        END as actualAmount,
        CASE wc.order_type
        when 0 THEN bo.order_status
        when 1 THEN tt.order_status
        END as orderStatus,
        wc.`status` as writeOffStatus,
        boa.after_status as refundStatus,
        boa.gmt_create as refundTime,
        IF(wc.order_type=0,(
        SELECT
        CASE og.product_type
        when 1 then CONCAT('普通商品ID:',og.product_id)
        when 2 then CONCAT('次卡商品ID:',og.product_id)
        when 3 then CONCAT('联票商品ID:',og.product_id)
        when 5 then CONCAT('规格商品ID:',og.product_id)
        END
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og on og.order_id =bo.id
        WHERE
        bo.order_no = wc.order_no and wc.source_goods_id=og.goods_id
        limit 1
        ),
        (
        SELECT
        CONCAT('拼团商品ID:',tt.team_goods_id)
        FROM
        by_team_order tt
        LEFT JOIN by_team_goods tg ON tg.id=tt.team_goods_id
        WHERE
        tt.order_no = wc.order_no
        limit 1
        ))AS writeOffGoodsNo,

        CONCAT('普通商品ID:',wc.source_goods_id) as sourceGoodsNo,
        wc.id as writeOffId,
        wc.order_type as orderType,
        wc.detail_id as detailId,
        bo.id as orderId,
        wc.store_ids as storeIds,
        bs.`name` as writeOffStore,
        cu2.staff_name as writeOffStaff,
        wc.gmt_modified as writeOffTime

        FROM
        write_off_code wc
        LEFT JOIN by_orders bo on bo.order_no=wc.order_no
        LEFT JOIN by_cust_user cu ON cu.id = bo.user_id
        LEFT JOIN by_order_goods og on og.id=wc.detail_id
        LEFT JOIN by_team_order tt on tt.order_no=wc.order_no
        LEFT JOIN by_order_after boa ON boa.order_no=wc.order_no
        LEFT JOIN (
        SELECT
        cc.staff_id,
        cc.write_off_id,
        cc.store_id,
        MAX(cc.gmt_create)
        FROM
        write_off_code_log cc
        GROUP BY
        cc.write_off_id
        )cl ON cl.write_off_id=wc.id
        LEFT JOIN base_store bs ON bs.id=cl.store_id
        LEFT JOIN by_store_staff cu2 ON cu2.id = cl.staff_id
        <where>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(bo.pay_time,'%Y-%m-%d')>= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(bo.pay_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="orderStatus !=null">
                AND (bo.order_status= #{orderStatus} or tt.order_status= #{orderStatus})
            </if>
            <if test="writeStatus !=null">
                AND wc.status= #{writeStatus}
            </if>
            <if test="returnStatus !=null">
                AND boa.after_status= #{returnStatus}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((wc.goods_name like concat('%',#{searchName},'%')) or (cu.nick_name like
                concat('%',#{searchName},'%'))
                or (wc.order_no like concat('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY wc.id
        ORDER BY wc.gmt_create DESC
    </select>
    <select id="queryWriteStoreAndStaffByWriteId"
            resultType="com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO">

                SELECT
                    bs.`name` AS writeOffStore,
                    cu2.staff_name AS writeOffStaff,
                    cl.write_off_date AS writeOffTime
                FROM
                    write_off_code_log cl
                LEFT JOIN base_store bs ON bs.id = cl.store_id
                LEFT JOIN by_store_staff cu2 ON cu2.id = cl.staff_id
                where  cl.write_off_id=#{writeOffId}
                ORDER BY cl.write_off_date DESC
                LIMIT 1

    </select>
    <select id="querySubGoodsStatistics" resultType="com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO">
        SELECT
        sc.id as goodsNo,
        sc.sub_card_goods_name as goodsName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 2
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        AND oo.product_type = 2
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_sub_card_goods sc ON sc.id=og.goods_id
        left JOIN
        (
        SELECT
        gc.sub_goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_sub_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.sub_goods_id
        ) t ON t.sub_goods_id= sc.id
        LEFT JOIN (SELECT
        gs.sub_goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_sub_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.sub_goods_id ) c on c.sub_goods_id= sc.id
        <where>
            and bo.order_status in(2,3,4) AND og.product_type = 2
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((sc.id LIKE CONCAT('%',#{searchName},'%')) OR
                (sc.sub_card_goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY og.goods_id
    </select>
    <select id="getFinanceSubGoodsStatisticsSum" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(b.salesNum),0) salesNum,
        IFNULL(SUM(refundNum),0) refundNum,
        IFNULL(SUM(amount),0) amount,
        IFNULL(SUM(couponAmount),0) couponAmount,
        IFNULL(SUM(integralAmount),0) integralAmount,
        IFNULL(SUM(amount)-SUM(refundAmount)-SUM(couponAmount)-SUM(integralAmount), 0) actualAmount
        FROM (

        SELECT
        sc.goods_no as goodsNo,
        sc.sub_card_goods_name as goodsName,
        t.classifyName as classifyName,
        t.classifyIds,
        c.`name` as storeName,
        c.storeIds,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 2
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (
        SELECT
        SUM(oa.after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id = oo.id
        WHERE
        oa.`resouce_type` = 1
        AND oa.after_status=1
        and oo.product_type=2
        AND oa.order_goods_id = og.goods_id
        ) AS refundAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        AND oo.product_type = 2
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_sub_card_goods sc ON sc.id=og.goods_id
        left JOIN
        (
        SELECT
        gc.sub_goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
        GROUP_CONCAT(bc.id) as classifyIds
        FROM
        by_sub_goods_classify gc
        LEFT JOIN base_classify bc on bc.id=gc.classify_id
        GROUP BY gc.sub_goods_id
        ) t ON t.sub_goods_id= sc.id
        LEFT JOIN (SELECT
        gs.sub_goods_id ,GROUP_CONCAT(bs.`name`) `name`,
        GROUP_CONCAT(bs.id) storeIds
        FROM
        by_sub_goods_store gs
        left JOIN base_store bs on bs.id =gs.store_id GROUP BY gs.sub_goods_id ) c on c.sub_goods_id= sc.id
        <where>
            and bo.order_status in(2,3,4) AND og.product_type = 2
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((sc.id LIKE CONCAT('%',#{searchName},'%')) OR
                (sc.sub_card_goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY og.goods_id
        )b
    </select>
    <select id="queryTicketGoodsStatistics" resultType="com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO">
        SELECT
        tg.id AS goodsNo,
        tg.ticket_goods_name AS goodsName,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 3
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        AND oo.product_type = 3
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_ticket_goods tg ON tg.id = og.goods_id

        <where>
            and bo.order_status in(2,3,4) AND og.product_type = 3
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((tg.id LIKE CONCAT('%',#{searchName},'%')) OR
                (tg.ticket_goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY og.goods_id
    </select>
    <select id="getFinanceTicketGoodsStatisticsSum" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(b.salesNum),0) salesNum,
        IFNULL(SUM(refundNum),0) refundNum,
        IFNULL(SUM(amount),0) amount,
        IFNULL(SUM(couponAmount),0) couponAmount,
        IFNULL(SUM(integralAmount),0) integralAmount,
        IFNULL(SUM(amount)-SUM(refundAmount)-SUM(couponAmount)-SUM(integralAmount), 0) actualAmount
        FROM (

        SELECT
        tg.id AS goodsNo,
        tg.ticket_goods_name AS goodsName,
        og.goods_price as goodsPrice,
        (SUM(og.goods_num)) as salesNum,
        SUM(og.goods_price*og.goods_num) as amount,
        (SUM(og.coupon_price)) as couponAmount,
        (SUM(og.integral_price)) as integralAmount,
        (
        IFNULL(SUM(
        (
        og.goods_price * og.goods_num
        ) - (
        og.coupon_price + og.integral_price
        )
        ),0) - IFNULL((
        SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` = 1
        AND order_goods_id = og.goods_id
        AND oo.product_type = 3
        AND oa.after_status = 1
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>
        ),0)
        ) AS actualAmount,
        (SELECT
        COUNT(oa.id)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        AND oo.product_type = 3
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundNum,
        (SELECT
        SUM(after_amount)
        FROM
        `by_order_after` oa
        LEFT JOIN by_order_goods oo ON oa.detail_id=oo.id
        WHERE
        `resouce_type` =1
        AND order_goods_id = og.goods_id
        AND oa.after_status = 1
        AND oo.product_type = 3
        <if test="startTime !=null and startTime !=''">
            AND oo.gmt_create>=#{startTime}
        </if>
        <if test="endTime !=null and endTime !=''">
            AND oo.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
        </if>

        ) as refundAmount
        FROM
        by_orders bo
        LEFT JOIN by_order_goods og ON og.order_no = bo.order_no
        LEFT JOIN by_ticket_goods tg ON tg.id = og.goods_id

        <where>
            and bo.order_status in(2,3,4) AND og.product_type = 3
            <if test="storeId !=null">
                AND FIND_IN_SET(#{storeId},c.storeIds)
            </if>
            <if test="classifyId !=null">
                AND FIND_IN_SET(#{classifyId},t.classifyIds)
            </if>
            <if test="startTime !=null and startTime !=''">
                AND og.gmt_create>=#{startTime}
            </if>
            <if test="endTime !=null and endTime !=''">
                AND og.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>

            <if test="searchName !=null and searchName!=''">
                AND ((tg.id LIKE CONCAT('%',#{searchName},'%')) OR
                (tg.ticket_goods_name LIKE CONCAT('%',#{searchName},'%')))
            </if>
        </where>
        GROUP BY og.goods_id
        )b
    </select>

    <select id="selectGoodsNum" resultType="java.lang.Integer">

                SELECT COUNT(1) FROM by_orders o LEFT JOIN by_order_goods og ON o.order_no = og.order_no
                WHERE o.user_id = #{userId} AND og.product_type = #{type} AND og.goods_id = #{goodsId} AND o.order_status IN (2,3)

    </select>

    <select id="selectNow" resultType="java.lang.String">

                SELECT order_no FROM by_orders WHERE order_status IN (1,4) AND gmt_create &gt;= #{dateTime} AND gmt_create &lt;= #{endTime}

    </select>

    <select id="findByPayOrderNo" resultMap="BaseResultMap">

                select id, order_no, pay_order_no, goods_id, coupon_amount,
                integral_amount, order_amount, user_id, order_time, order_status,
                actual_amount, coupon_id, goods_type, amount_before_discount,
                amount_after_discount, discount_amount, is_refund, is_del, pay_type,
                pay_time, cancel_time, gmt_create, gmt_update, is_after_sale,
                eval_type, remark, from_id, pay_flow_no, order_close_type, current_user_id,
                accomplish_type, retired_amount
                from by_orders
                where pay_order_no = #{parentNo}

    </select>

    <update id="updateStatusByOrderNo">
        update by_orders
        set order_status = #{orderStatus}
        where order_no = #{orderNo}
    </update>
</mapper>

