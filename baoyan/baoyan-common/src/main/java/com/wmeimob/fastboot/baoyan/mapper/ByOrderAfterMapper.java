
/*
* ByOrderAfterMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 13:48:35 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import com.wmeimob.fastboot.core.orm.Mapper;

import java.util.List;

public interface ByOrderAfterMapper extends Mapper<ByOrderAfter> {
	/**
	 * 售后分页查询
	 * @param byOrderAfter
	 * @return
	 */
	List<ByOrderAfter> queryAfterByCondition(ByOrderAfter byOrderAfter);

}