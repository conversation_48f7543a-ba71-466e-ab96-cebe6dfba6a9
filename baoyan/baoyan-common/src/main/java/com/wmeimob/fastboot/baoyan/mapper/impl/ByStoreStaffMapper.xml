<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByStoreStaffMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByStoreStaff" id="BaseResultMap">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result property="staffName" column="staff_name" jdbcType="VARCHAR"/>
		<result property="staffPhone" column="staff_phone" jdbcType="CHAR"/>
		<result property="storeId" column="store_id"/>
		<result property="openId" column="open_id" jdbcType="VARCHAR"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>


	<select id="findByOpenId" resultMap="BaseResultMap">
		select *
		from by_store_staff
		where open_id = #{wxOpenId}
	</select>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.ByStoreStaff">
		SELECT 
			`id`,
			`staff_name`,
			`staff_phone`,
			`open_id`,
			`is_del`,
			`gmt_create`,
			`gmt_modified`
		FROM  by_store_staff
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="staffName != '' and staffName != null">
			  	AND `staff_name` = #{staffName}
			</if>
			<if test="staffPhone != null">
			  	AND `staff_phone` = #{staffPhone}
			</if>
			<if test="openId != '' and openId != null">
			  	AND `open_id` = #{openId}
			</if>
			<if test="isDel != null">
			  	AND `is_del` = #{isDel}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`staff_name`,
			`staff_phone`,
			`open_id`,
			`is_del`,
			`gmt_create`,
			`gmt_modified`
		FROM by_store_staff
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByStoreStaff" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO by_store_staff
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="staffName != '' and staffName != null">
			`staff_name`,
		</if>
		<if test="staffPhone != null">
			`staff_phone`,
		</if>
		<if test="openId != '' and openId != null">
			`open_id`,
		</if>
		<if test="isDel != null">
			`is_del`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		<if test="gmtModified != null">
			`gmt_modified`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="staffName != '' and staffName != null">
			#{staffName},	
		</if>
		<if test="staffPhone != null">
			#{staffPhone},	
		</if>
		<if test="openId != '' and openId != null">
			#{openId},	
		</if>
		<if test="isDel != null">
			#{isDel},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		<if test="gmtModified != null">
			#{gmtModified},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByStoreStaff">
		UPDATE by_store_staff
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="staffName != '' and staffName != null">
				`staff_name` = #{staffName},
			</if>
			<if test="staffPhone != null">
				`staff_phone` = #{staffPhone},
			</if>
			<if test="openId != '' and openId != null">
				`open_id` = #{openId},
			</if>
			<if test="isDel != null">
				`is_del` = #{isDel},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
			<if test="gmtModified != null">
				`gmt_modified` = #{gmtModified},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM by_store_staff WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.ByStoreStaff">
		DELETE FROM by_store_staff
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="staffName != '' and staffName != null">
			  	AND `staff_name` = #{staffName}
			</if>
			<if test="staffPhone != null">
			  	AND `staff_phone` = #{staffPhone}
			</if>
			<if test="openId != '' and openId != null">
			  	AND `open_id` = #{openId}
			</if>
			<if test="isDel != null">
			  	AND `is_del` = #{isDel}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
	</delete> -->
</mapper>

