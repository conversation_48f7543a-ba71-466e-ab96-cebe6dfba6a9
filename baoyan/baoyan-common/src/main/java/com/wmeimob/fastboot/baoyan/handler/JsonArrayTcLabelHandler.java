package com.wmeimob.fastboot.baoyan.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wmeimob.fastboot.baoyan.entity.TcLabel;
import com.wmeimob.fastboot.baoyan.service.TcLabelService;
import com.wmeimob.fastboot.baoyan.utils.SpringContextHelper;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/7/20
 * packageName:com.wmeimob.fastboot.baoyan.handler
 * 转换 标签
 */
@MappedTypes({JSONArray.class,})
@Slf4j
public class JsonArrayTcLabelHandler extends BaseTypeHandler<JSONArray> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, JSONArray objects, JdbcType jdbcType)
            throws SQLException {
        preparedStatement.setString(i, getJsonArrayTcLabelIds(objects));
    }
    @Override
    public JSONArray getNullableResult(ResultSet resultSet, String s)
            throws SQLException {
        return getJsonArrayTcLabelNames(
                JSON.parseArray(resultSet.getString(s))
        );
    }
    @Override
    public JSONArray getNullableResult(ResultSet resultSet, int i)
            throws SQLException {
        return getJsonArrayTcLabelNames(
                JSON.parseArray(resultSet.getString(i))
        );
    }
    @Override
    public JSONArray getNullableResult(CallableStatement callableStatement, int i)
            throws SQLException {
        return getJsonArrayTcLabelNames(
                JSON.parseArray(callableStatement.getString(i))
        );
    }
    private JSONArray getJsonArrayTcLabelNames(JSONArray objects)
    {
        if (null ==objects ||objects.isEmpty()) {
            return objects;
        }
        List<Integer> integerList;
        try {
             integerList = objects.toJavaList(Integer.class);
        }catch (Exception e){
            throw new CustomException("数据异常");
        }
        JSONArray values = new JSONArray();
        TcLabelService tcLabelService = SpringContextHelper.getBean(TcLabelService.class);
        integerList.forEach(obj -> {
            TcLabel label = tcLabelService.queryById(obj);
            if (null != label) {
                values.add(label.getLabelName());
            }
        });
        return values;
    }

    private String getJsonArrayTcLabelIds(JSONArray objects)
    {
        if (null == objects || objects.isEmpty()) return JSONArray.toJSONString(objects);
        TcLabelService tcLabelService = SpringContextHelper.getBean(TcLabelService.class);
        JSONArray ids = new JSONArray();
        objects.forEach(object ->{
            if (object instanceof Integer) {
                ids.add(object);
            }else  if (object instanceof String){
             String tcLabelName = (String) object;
                TcLabel tcLabel = tcLabelService.queryByName(tcLabelName);
                if (null == tcLabel){
                    tcLabel = new TcLabel();
                    tcLabel.setLabelName(tcLabelName);
                    tcLabelService.insert(tcLabel);
                    tcLabel = tcLabelService.queryByName(tcLabelName);
                }
                ids.add(tcLabel.getId());
            }
        });

        return ids.toJSONString();
    }

}
