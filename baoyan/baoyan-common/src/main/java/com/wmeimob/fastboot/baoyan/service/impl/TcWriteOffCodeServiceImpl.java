package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcOrderGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.baoyan.service.TcWriteOffCodeService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 淘潮玩核销码表(TcWriteOffCode)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-11 15:25:01
 */
@Service
public class TcWriteOffCodeServiceImpl implements TcWriteOffCodeService {
    @Resource
    private TcWriteOffCodeMapper tcWriteOffCodeMapper;

    @Resource
    private TcWriteOffCodeLogMapper tcWriteOffCodeLogMapper;

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private QrCodeService qrCodeService;

    @Resource
    private TcOrderService tcOrderService;

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private TcGoodsMapper tcGoodsMapper;

    @Resource
    private BaseStoreMapper baseStoreMapper;

    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;

    /**
     * 根据订单号查询用户核销码
     * @param orderNo
     * @return
     */
    @Override
    public List<TcWriteOffCode> findByOrderNo(String orderNo) {
        ByCustUser user = SecurityContext.getUser();
//        ByCustUser user = ByCustUser.builder().id(124904).build();

        //获得订单
        TcOrder dbOrder = tcOrderMapper.findByOrderNo(orderNo);
        Assert.notEq(user.getId(), dbOrder.getUserId(), "这不是你的订单");
        Assert.notEq(dbOrder.getDeliveryMode(),1,"只有到店自提才有核销码");
        Assert.notEq(dbOrder.getStatus(),2,"已付款订单才能查看核销码");

        //查询核销码
        List<TcWriteOffCode> list = tcWriteOffCodeMapper.findByOrderNo(orderNo);
        //如果没有二维码就生成二维码
        for (TcWriteOffCode writeOffCode : list) {
            if (StringUtils.isEmpty(writeOffCode.getCode()) || Objects.equals(writeOffCode.getCode(),"生成失败")){
                //生成二维码（核销码二维码）
                String qrCode = qrCodeService.exportQrCode(null, writeOffCode.getId(), "pages/tcwriteoff/main");
                writeOffCode.setCode(qrCode);
                //更新数据
                tcWriteOffCodeMapper.updateByPrimaryKeySelective(
                        TcWriteOffCode.builder()
                                .id(writeOffCode.getId())
                                .code(qrCode)
                                .build()
                );
            }
        }


        return list;
    }

    /**
     * 根据订单商品 保存核销码
     * @param tcOrderGoods
     * @param userId
     * @return
     */
    @Override
    public boolean saveOffCode(TcOrderGoods tcOrderGoods, Integer userId) {
        int count = tcOrderGoods.getGoodsCount();
        for (int i = 0; i < count; i++) {
            TcWriteOffCode code = TcWriteOffCode.builder()
                    .writeOffName(tcOrderGoods.getTcGoodsName())
                    .custUserId( userId )
                    .goodsId( tcOrderGoods.getTcGoodsId() )
                    .orderNo( tcOrderGoods.getTcOrderNo() )
                    .surplusNum( 1 )
                    .totalNum( 1 )
                    .status( 0 )
                    .gmtCreate( new Date() )
                    .detailId( tcOrderGoods.getId() )
                    .build();

            tcWriteOffCodeMapper.insertSelective( code );
        }
        return true;
    }

    /**
     * 核销某个淘潮玩核销码
     *
     * @param writeOffCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean writeOff(TcWriteOffCode writeOffCode, Integer staffId, Integer storeId) {
        //查询当前核销码的情况
        TcWriteOffCode dbWriteOff = tcWriteOffCodeMapper.selectByPrimaryKey(writeOffCode.getId());
        //检查次数
        Assert.lessThen(dbWriteOff.getSurplusNum(),1,"这个核销码已经没有次数了");
        //检查状态
        Assert.notEq(dbWriteOff.getStatus(), 0, "当前核销码不是待处理状态");
        //查询当前订单，检查订单状态
        TcOrderGoods orderGoods = tcOrderGoodsMapper.selectByPrimaryKey(dbWriteOff.getDetailId());
        Assert.notEq(orderGoods.getStatus(), 2, "当前核销码不能核销 status != 2");
        //门店 和 员工是否正确
        List<BaseStore> stores = baseStoreMapper.findStoreByTcId(dbWriteOff.getGoodsId());
        boolean anyMatch = stores.stream().anyMatch(baseStore -> Objects.equals(baseStore.getId(), storeId));
        if (!anyMatch){
            throw new CustomException("这个淘潮玩不能在该店铺核销");
        }
        ByStoreStaff dbStaff = byStoreStaffMapper.selectByPrimaryKey(staffId);
        System.out.println(dbStaff);
        Assert.notEq(dbStaff.getStoreId(), storeId, "这个员工不是该店铺");
        Assert.eq(dbStaff.getIsDel(), true, "这个员工已经停用");


        int writeOffNum = 1;
        //修改核销码
        tcWriteOffCodeMapper.updateByPrimaryKeySelective(
                TcWriteOffCode.builder()
                        .id( dbWriteOff.getId() )
                        .surplusNum( dbWriteOff.getSurplusNum()-writeOffNum )
                        .status( 1 )
                        .build()
        );

        //添加销量
        tcGoodsMapper.addSellCount(dbWriteOff.getGoodsId(), writeOffNum);

        //，增加核销记录，
        tcWriteOffCodeLogMapper.insertSelective(
                TcWriteOffCodeLog.builder()
                        .staffId( staffId )
                        .storeId( storeId )
                        .custUserId( dbWriteOff.getCustUserId() )
                        .writeOffNum( writeOffNum )
                        .writeOffId( dbWriteOff.getId() )
                        .writeOffDate( new Date() )
                        .build()
        );

        //检查该订单核销码是否全部核销完毕
        boolean allWriteOff = tcOrderService.allWriteOff( dbWriteOff.getDetailId() );

        //检查该订单商品是否全部已完成
        boolean allComplete = tcOrderService.allComplete(dbWriteOff.getOrderNo());

        return true;
    }

    /**
     * 根据id查询核销码的详细信息
     * @param id
     * @return
     */
    @Override
    public TcWriteOffCode findById(Integer id) {
        return tcWriteOffCodeMapper.findById(id);
    }
}