package com.wmeimob.fastboot.baoyan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告返回
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseAdvertisingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片地址
     */
    private String imgUrl;
    /**
     * 跳转类型id
     */
    private Integer jumpType;
    /**
     * 跳转content
     */
    private String target;
    /**
     * 分类url
     */
    private String targetImgUrl;
    /**
     * 分类名称
     */
    private String classifyTitle;
}
