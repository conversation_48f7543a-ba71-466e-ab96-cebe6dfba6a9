package com.wmeimob.fastboot.baoyan.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 暂时定义注解，实际上没有用，因为框架封装的死死的，需要逐步拆解
 * <AUTHOR>
 * @date 2024-03-17
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiVersion {
    // 标识版本号
    int value();

    // 兼容平台（后台，小程序）
    int platform() default 0;
}
