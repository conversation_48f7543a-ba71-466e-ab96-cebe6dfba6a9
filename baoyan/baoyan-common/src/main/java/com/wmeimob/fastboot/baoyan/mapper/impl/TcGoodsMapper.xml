<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper" >
    <resultMap id="BaseResultMap" type="com.wmeimob.fastboot.baoyan.entity.TcGoods">
        <!--@Table tc_goods-->
        <result property="id" column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="brandId" column="brand_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="cateId" column="cate_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="title" column="title" typeHandler="com.wmeimob.fastboot.baoyan.handler.JsonArrayTcLabelHandler" javaType="com.alibaba.fastjson.JSONArray"/>
        <result property="salesArea" column="sales_area" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="salePrice" column="sale_price" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result property="doorPrice" column="door_price" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result property="stock" column="stock" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="codeImg" column="code_img" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="coverImg" column="cover_img" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="bannerImg" column="banner_img" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="detail" column="detail" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="introduction" column="introduction" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="isHome" column="is_home" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result property="status" column="status" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="sellCount" column="sell_count" jdbcType="TINYINT" javaType="java.lang.Long"/>
        <result property="orderBy" column="order_by" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="isCoupon" column="is_coupon" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="isIntegral" column="is_integral" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
        <result property="isMailing" column="is_mailing" jdbcType="TINYINT" javaType="java.lang.Boolean"/>

        <result property="tcGoodsStock.id" column="st_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="tcGoodsStock.goodsId" column="goods_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="tcGoodsStock.stock" column="st_stock" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="tcGoodsStock.warningCount" column="warning_count" jdbcType="INTEGER"
                javaType="java.lang.Integer"/>
        <result property="tcGoodsStock.warningMsg" column="warning_msg" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="tcGoodsStock.gmtCreate" column="st_gmt_create" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>

    <resultMap id="queryAllMap" type="com.wmeimob.fastboot.baoyan.entity.TcGoods" extends="BaseResultMap">

        <result property="tcCate.id" column="c_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="tcCate.name" column="c_name" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="tcCate.remark" column="c_remark" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="tcCate.isDel" column="c_is_del" jdbcType="TINYINT" javaType="java.lang.Boolean"/>

        <result property="tcBrand.id" column="b_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="tcBrand.name" column="b_name" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="tcBrand.remark" column="b_remark" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="tcBrand.isDel" column="b_is_del" jdbcType="TINYINT" javaType="java.lang.Boolean"/>
    </resultMap>


    <update id="addSellCount">
        update tc_goods
        set sell_count = sell_count+#{sellCount}
        where id = #{id}
    </update>

    <update id="reduceStock" >
        update tc_goods
        set stock = stock+#{change}
        where id = #{id}
    </update>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
            SELECT
                tc_goods.*,tc_goods_stock.id as st_id,tc_goods_stock.goods_id,
                tc_goods_stock.gmt_create as st_gmt_create,
                c.stores as stores,
                tc_goods_stock.stock as st_stock,tc_goods_stock.warning_count,tc_goods_stock.warning_msg
                from tc_goods   LEFT JOIN  tc_goods_stock  on tc_goods_stock.goods_id = tc_goods.id
                LEFT JOIN (SELECT
        gs.tc_goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) stores
        FROM
        tc_comtion gs
        left JOIN base_store bs on gs.tc_shop_id =bs.id  where  bs.delete_status=0 GROUP BY gs.tc_goods_id ) c on c.tc_goods_id=tc_goods.id
            where tc_goods.status = 1 and tc_goods.id = #{id}
    </select>

    <select id="findByIds" resultMap="BaseResultMap">
        SELECT
            tc_goods.*,tc_goods_stock.id as st_id,tc_goods_stock.goods_id,
            tc_goods_stock.gmt_create as st_gmt_create,
            tc_goods_stock.stock as st_stock,tc_goods_stock.warning_count,tc_goods_stock.warning_msg
            from tc_goods   LEFT JOIN  tc_goods_stock  on tc_goods_stock.goods_id = tc_goods.id
        where tc_goods.status = 1
        and tc_goods.id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.wmeimob.fastboot.baoyan.entity.TcGoods" resultMap="queryAllMap">
        SELECT tc_goods.*,
        c.storeIds,
        c.`name` as storeName,
        tc_goods_stock.id as st_id,tc_goods_stock.goods_id,
        tc_goods_stock.gmt_create as st_gmt_create,
        tc_goods_stock.stock as st_stock,
        tc_goods_stock.warning_count,
        tc_goods_stock.warning_msg,
        tc_cate.id as c_id,
        tc_cate.`name` as c_name,
        tc_cate.is_del as c_del,
        tc_cate.remark as c_remark,
        tc_brand.id as b_id,
        tc_brand.`name` as b_name,
        tc_brand.is_del as b_del,
        tc_brand.remark as b_remark
        from tc_goods
        LEFT JOIN tc_goods_stock on tc_goods_stock.goods_id = tc_goods.id
        LEFT JOIN tc_cate on tc_goods.cate_id = tc_cate.id
        LEFT JOIN tc_brand on tc_goods.brand_id = tc_brand.id
        LEFT JOIN (SELECT
        gs.tc_goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) storeIds
        FROM
        tc_comtion gs
        left JOIN base_store bs on gs.tc_shop_id =bs.id  where  bs.delete_status=0 GROUP BY gs.tc_goods_id ) c on c.tc_goods_id=tc_goods.id
        where tc_goods.status = 1
        <if test="storeId !=null">
            AND	FIND_IN_SET(#{storeId},c.storeIds)
        </if>
        <if test="searchName != null and searchName != ''">
            AND ((tc_goods.goods_name LIKE CONCAT('%',#{searchName},'%')) OR
            (tc_goods.id =#{searchName}))
        </if>
        <if test="brandId != null">
            and tc_goods.brand_id = #{brandId}
        </if>
        <if test="cateId != null">
            and tc_goods.cate_id = #{cateId}
        </if>
<!--        <if test="title != null">-->
<!--            AND (JSON_CONTAINS(tc_goods.title,'[<foreach collection="title" item="item" separator=",">${item}</foreach>]'))-->
<!--        </if>-->
        <if test="title != null and !title.isEmpty()">
            and
            <foreach collection="title" open="  " item="item" separator=" or " close=" " >
                 JSON_CONTAINS(tc_goods.title,JSON_ARRAY(
                #{item}
                ))
            </foreach>
        </if>
        <if test="salesArea != null and salesArea != ''">
            and tc_goods.sales_area = #{salesArea}
        </if>
        <if test="minPrice !=null and maxPrice != null">
            and tc_goods.sale_price between #{minPrice} and #{maxPrice}
        </if>
        <if test="stock != null">
            and tc_goods.stock >= #{stock}
        </if>
        <if test="isIntegral != null">
           and tc_goods.is_integral = 1
        </if>
        <if test="isCoupon != null">
           and tc_goods.is_coupon = 1
        </if>
        <if test="sellCount != null">
           and tc_goods.sell_count = #{sellCount},
        </if>
        <if test="detail != null and detail != ''">
            and tc_goods.detail = #{detail}
        </if>
        <if test="introduction != null and introduction != ''">
            and tc_goods.introduction = #{introduction}
        </if>
        <if test="isDeleted != null">
            and tc_goods.is_deleted = #{isDeleted}
        </if>
        <if test="isHome != null">
            and tc_goods.is_home = #{isHome}
        </if>
        <if test="gmtCreate != null">
            and tc_goods.gmt_create between #{gmtCreate} and #{gmtCreateEnd}
        </if>
        <if test="orderBy != null">
            and tc_goods.order_by = #{orderBy}
        </if>

        <if test="sortSearch == 1">
            order by tc_goods.sell_count desc
        </if>
        <if test="sortSearch == 2">
            order by tc_goods.sell_count asc
        </if>
        <if test="sortSearch != 2 and sortSearch != 1">
            order by tc_goods.id desc
        </if>
    </select>

    <select id="wxQueryAll" parameterType="com.wmeimob.fastboot.baoyan.entity.TcGoods" resultMap="queryAllMap">

        SELECT tc_goods.*,
        tc_goods_stock.id as st_id,tc_goods_stock.goods_id,
        tc_goods_stock.gmt_create as st_gmt_create,
        tc_goods_stock.stock as st_stock,
        tc_goods_stock.warning_count,
        tc_goods_stock.warning_msg,

        tc_cate.id as c_id,
        tc_cate.`name` as c_name,
        tc_cate.is_del as c_del,
        tc_cate.remark as c_remark,

        tc_brand.id as b_id,
        tc_brand.`name` as b_name,
        tc_brand.is_del as b_del,
        tc_brand.remark as b_remark

        from tc_goods
        LEFT JOIN tc_goods_stock on tc_goods_stock.goods_id = tc_goods.id
        LEFT JOIN tc_cate on tc_goods.cate_id = tc_cate.id
        LEFT JOIN tc_brand on tc_goods.brand_id = tc_brand.id
        where tc_goods.status = 1 and tc_goods.is_deleted = 1

        <if test="searchName != null and searchName != ''">
            AND tc_goods.goods_name LIKE CONCAT('%',#{searchName},'%')
        </if>
        <if test="brandId != null">
            and tc_goods.brand_id = #{brandId}
        </if>
        <if test="cateId != null">
            and tc_goods.cate_id = #{cateId}
        </if>

        <if test="title != null">
            and
            <foreach collection="title" open="  " item="item" separator=" or " close=" " >
                JSON_CONTAINS(tc_goods.title,JSON_ARRAY(
                #{item}
                ))
            </foreach>
        </if>
        <if test="salesArea != null and salesArea != ''">
            and tc_goods.sales_area  LIKE CONCAT('%',#{salesArea},'%')
        </if>
        <if test="minPrice !=null and maxPrice != null">
            and tc_goods.sale_price between #{minPrice} and #{maxPrice}
        </if>
        <if test="isIntegral != null">
            and tc_goods.is_integral = 1
        </if>
        <if test="isCoupon != null">
            and tc_goods.is_coupon = 1
        </if>

        <if test="sortSearch == 1">
            order by tc_goods.sell_count desc
        </if>
        <if test="sortSearch == 2">
            order by tc_goods.sell_count asc
        </if>
        <if test="sortSearch == 3">
            order by tc_goods.sale_price desc
        </if>
        <if test="sortSearch == 4">
            order by tc_goods.sale_price asc
        </if>
        <if test="sortSearch == 5">
            order by tc_goods.id desc
        </if>
        <if test="sortSearch == null">
            order by tc_goods.order_by asc
        </if>
    </select>

    <select id="queryAllCount" parameterType="com.wmeimob.fastboot.baoyan.entity.TcGoods" resultType="long">
        SELECT
        count(tc_goods.id)
        from tc_goods
        LEFT JOIN tc_goods_stock on tc_goods_stock.goods_id = tc_goods.id
        LEFT JOIN tc_cate on tc_goods.cate_id = tc_cate.id
        LEFT JOIN tc_brand on tc_goods.brand_id = tc_brand.id
        where tc_goods.status = 1

        <if test="searchName != null and searchName != ''">
            AND ((tc_goods.goods_name LIKE CONCAT('%',#{searchName},'%')) OR
            (tc_goods.id =#{searchName}))
        </if>
        <if test="brandId != null">
            and tc_goods.brand_id = #{brandId}
        </if>
        <if test="cateId != null">
            and tc_goods.cate_id = #{cateId}
        </if>
        <if test="title != null and !title.isEmpty()">
            and
            <foreach collection="title" open="  " item="item" separator=" or " close=" " >
                JSON_CONTAINS(tc_goods.title,JSON_ARRAY(
                #{item}
                ))
            </foreach>
        </if>
        <if test="salesArea != null and salesArea != ''">
            and tc_goods.sales_area = #{salesArea}
        </if>
        <if test="minPrice !=null and maxPrice != null">
            and tc_goods.sale_price between #{minPrice} and #{maxPrice}
        </if>
        <if test="stock != null">
            and tc_goods.stock >= #{stock}
        </if>
        <if test="isIntegral != null">
            and tc_goods.is_integral = 1
        </if>
        <if test="isCoupon != null">
            and tc_goods.is_coupon = 1
        </if>
        <if test="sellCount != null">
            and tc_goods.sell_count = #{sellCount},
        </if>
        <if test="detail != null and detail != ''">
            and tc_goods.detail = #{detail}
        </if>
        <if test="introduction != null and introduction != ''">
            and tc_goods.introduction = #{introduction}
        </if>
        <if test="isDeleted != null">
            and tc_goods.is_deleted = #{isDeleted}
        </if>
        <if test="isHome != null">
            and tc_goods.is_home = #{isHome}
        </if>
        <if test="gmtCreate != null">
            and tc_goods.gmt_create between #{gmtCreate} and #{gmtCreateEnd}
        </if>
        <if test="orderBy != null">
            and tc_goods.order_by = #{orderBy}
        </if>

        <if test="sortSearch == 1">
            order by tc_goods.sell_count desc
        </if>
        <if test="sortSearch == 2">
            order by tc_goods.sell_count asc
        </if>
        <if test="sortSearch != 2 and sortSearch != 1">
            order by tc_goods.id desc
        </if>



    </select>

    <select id="wxQueryAllCount" parameterType="com.wmeimob.fastboot.baoyan.entity.TcGoods" resultType="long">
        SELECT
        count(tc_goods.id)
        from tc_goods
        LEFT JOIN tc_goods_stock on tc_goods_stock.goods_id = tc_goods.id
        LEFT JOIN tc_cate on tc_goods.cate_id = tc_cate.id
        LEFT JOIN tc_brand on tc_goods.brand_id = tc_brand.id
        where tc_goods.status = 1 and tc_goods.is_deleted = 1

        <if test="searchName != null and searchName != ''">
            AND tc_goods.goods_name LIKE CONCAT('%',#{searchName},'%')
        </if>
        <if test="brandId != null">
            and tc_goods.brand_id = #{brandId}
        </if>
        <if test="cateId != null">
            and tc_goods.cate_id = #{cateId}
        </if>

        <if test="title != null">
            and
            <foreach collection="title" open="  " item="item" separator=" or " close=" " >
                JSON_CONTAINS(tc_goods.title,JSON_ARRAY(
                #{item}
                ))
            </foreach>
        </if>
        <if test="salesArea != null and salesArea != ''">
            and tc_goods.sales_area  LIKE CONCAT('%',#{salesArea},'%')
        </if>
        <if test="minPrice !=null and maxPrice != null">
            and tc_goods.sale_price between #{minPrice} and #{maxPrice}
        </if>
        <if test="isIntegral != null">
            and tc_goods.is_integral = 1
        </if>
        <if test="isCoupon != null">
            and tc_goods.is_coupon = 1
        </if>

        <if test="sortSearch == 1">
            order by tc_goods.sell_count desc
        </if>
        <if test="sortSearch == 2">
            order by tc_goods.sell_count asc
        </if>
        <if test="sortSearch == 3">
            order by tc_goods.sale_price desc
        </if>
        <if test="sortSearch == 4">
            order by tc_goods.sale_price asc
        </if>
        <if test="sortSearch == 5">
            order by tc_goods.gmt_create desc
        </if>
        <if test="sortSearch == null">
            order by tc_goods.order_by asc
        </if>
    </select>

    <select id="wxQueryById" resultMap="queryAllMap">
             SELECT tc_goods.*,
        tc_goods_stock.id as st_id,tc_goods_stock.goods_id,
        tc_goods_stock.gmt_create as st_gmt_create,
        tc_goods_stock.stock as st_stock,
        tc_goods_stock.warning_count,
        tc_goods_stock.warning_msg,

        tc_cate.id as c_id,
        tc_cate.`name` as c_name,
        tc_cate.is_del as c_del,
        tc_cate.remark as c_remark,

        tc_brand.id as b_id,
        tc_brand.`name` as b_name,
        tc_brand.is_del as b_del,
        tc_brand.remark as b_remark

        from tc_goods
        LEFT JOIN tc_goods_stock on tc_goods_stock.goods_id = tc_goods.id
        LEFT JOIN tc_cate on tc_goods.cate_id = tc_cate.id
        LEFT JOIN tc_brand on tc_goods.brand_id = tc_brand.id
        where tc_goods.status = 1 and tc_goods.is_deleted = 1
        and tc_goods.id=#{id}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
     insert into tc_goods(goods_name, brand_id, cate_id, title, sales_area,
        sale_price, door_price, stock, code_img, cover_img, banner_img, detail, introduction,is_integral, is_coupon,
        gmt_create, sell_count, order_by)
     values (#{goodsName}, #{brandId}, #{cateId}, #{title,typeHandler=com.wmeimob.fastboot.baoyan.handler.JsonArrayTcLabelHandler}, #{salesArea},
            #{salePrice}, #{doorPrice}, #{stock}, #{codeImg}, #{coverImg}, #{bannerImg},
             #{detail}, #{introduction},#{isIntegral},#{isCoupon},#{gmtCreate},#{sellCount}, #{orderBy})
    </insert>
    <!--通过主键修改数据-->
    <update id="update">
        update tc_goods
        <set>
            <if test="goodsName != null and goodsName != ''">
                goods_name = #{goodsName},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId},
            </if>
            <if test="cateId != null">
                cate_id = #{cateId},
            </if>
            <if test="title != null">
                title = #{title,typeHandler=com.wmeimob.fastboot.baoyan.handler.JsonArrayTcLabelHandler},
            </if>
            <if test="salesArea != null and salesArea != ''">
                sales_area = #{salesArea},
            </if>
            <if test="salePrice != null">
                sale_price = #{salePrice},
            </if>
            <if test="doorPrice != null">
                door_price = #{doorPrice},
            </if>
            <if test="stock != null">
                stock = #{stock},
            </if>
            <if test="codeImg != null and codeImg != ''">
                code_img = #{codeImg},
            </if>
            <if test="coverImg != null">
                cover_img = #{coverImg},
            </if>
            <if test="bannerImg != null">
                banner_img = #{bannerImg},
            </if>
            <if test="detail != null">
                detail = #{detail},
            </if>
            <if test="introduction != null">
                introduction = #{introduction},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="isHome != null">
                is_home = #{isHome},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="isIntegral != null">
                is_integral = #{isIntegral,jdbcType=BOOLEAN},
            </if>
            <if test="isCoupon != null">
                is_coupon = #{isCoupon,jdbcType=BOOLEAN},
            </if>
            <if test="isMailing != null">
                is_mailing = #{isMailing,jdbcType=BOOLEAN},
            </if>
            <if test="sellCount != null">
                sell_count = #{sellCount},
            </if>
            <if test="orderBy != null">
                order_by = #{orderBy},
            </if>
        </set>
        where status = 1 and id = #{id}
    </update>


</mapper>