package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName WriteOffCodeService
 * @Description 核销码表
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/
public interface WriteOffCodeService extends CommonService<WriteOffCode>{

    /**
     * 核销码表查询
     * @param id
     * @return
     */
    default WriteOffCode queryWriteOffCodeById(Object id){throw new NotImplementedException("queryWriteOffCodeById");}

    /**
     * 核销码表添加
     * @param  writeOffCode
     * @return
     */
    default  void addWriteOffCode(WriteOffCode writeOffCode){throw new NotImplementedException("addWriteOffCode");};


    /**
     * 核销码表删除
     * @param id
     * @return
     */
    default void removeWriteOffCode(Object id){throw new NotImplementedException("removeWriteOffCode");};


    /**
     * 核销码表修改
     * @param writeOffCode
     * @return
     */
    default void modifyWriteOffCode(WriteOffCode writeOffCode){throw new NotImplementedException("modifyWriteOffCode");};

    default void updateOff(WriteOffCode writeOffCode){

    }

    default List<WriteOffCode> queryByDetailId(Integer detailId){
        throw new NotImplementedException("queryByDetailId");
    }
}
