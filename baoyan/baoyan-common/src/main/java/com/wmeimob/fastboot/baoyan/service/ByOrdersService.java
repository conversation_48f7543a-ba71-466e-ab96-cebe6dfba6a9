package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.OrderResVO;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByOrdersService
 * @Description 商品订单表
 * <AUTHOR>
 * @Date Mon Jul 15 15:41:21 CST 2019
 * @version1.0
 **/
public interface ByOrdersService extends CommonService<ByOrders>{

    /**
     * 商品订单表查询
     * @param id
     * @return
     */
    default OrderResVO queryByOrdersById(Integer id){throw new NotImplementedException("queryByOrdersById");};

    /**
     * 商品订单表添加
     * @param  byOrders
     * @return
     */
    default  void addByOrders(ByOrders byOrders){throw new NotImplementedException("addByOrders");};


    /**
     * 商品订单表删除
     * @param id
     * @return
     */
    default void removeByOrders(Object id){throw new NotImplementedException("removeByOrders");};


    /**
     * 商品订单表修改
     * @param byOrders
     * @return
     */
    default void modifyByOrders(ByOrders byOrders){throw new NotImplementedException("modifyByOrders");};
    /**
     * 商品订单统计
     * @param byOrders
     * @return
     */
    default List<ByOrders> queryOrderStatistics(ByOrders byOrders){throw new NotImplementedException("queryOrderStatistics");};
    /**
     * 商品统计
     * @param byOrders
     * @return
     */
    default List<GoodsStatisticsVO> queryGoodsStatistics(ByOrders byOrders){throw new NotImplementedException("queryGoodsStatistics");};

    /**
     * 订单统计表头
     * @param byOrders
     * @return
     */
    default Map<String,Object> getFinanceOrderStatisticsSum(ByOrders byOrders){throw new NotImplementedException("getFinanceOrderStatisticsSum");};

    /**
     * 商品统计表头
     * @param byOrders
     * @return
     */
    default Map<String,Object> getFinanceGoodsStatisticsSum(ByOrders byOrders){throw new NotImplementedException("getFinanceGoodsStatisticsSum");};

    /**
     * 订单明细导出 by新增需求  2019年10月18日16:05:30
     * @param byOrders
     * @return
     */
    default List<ByOrders> findExportByCondition(ByOrders byOrders){throw new NotImplementedException("findExportByCondition");};

    /**
     * 财务统计
     * @param byOrders
     * @return
     */
    default List<FinanceStatisticsVO> queryFinanceStatistics(ByOrders byOrders){throw new NotImplementedException("queryFinanceStatistics");};

    /**
     * 次卡商品统计
     * @param byOrders
     * @return
     */
    default List<GoodsStatisticsVO> querySubGoodsStatistics(ByOrders byOrders){throw new NotImplementedException("querySubGoodsStatistics");};

    /**
     * 次卡商品统计 表头
     * @param byOrders
     * @return
     */
    default Map<String,Object> getFinanceSubGoodsStatisticsSum(ByOrders byOrders){throw new NotImplementedException("getFinanceSubGoodsStatisticsSum");};

    /**
     * 联票商品统计
     * @param byOrders
     * @return
     */
    default List<GoodsStatisticsVO> queryTicketGoodsStatistics(ByOrders byOrders){throw new NotImplementedException("getFinanceSubGoodsStatisticsSum");};

    /**
     * 联票商品表头
     * @param byOrders
     * @return
     */
    default Map<String,Object> getFinanceTicketGoodsStatisticsSum(ByOrders byOrders){throw new NotImplementedException("getFinanceTicketGoodsStatisticsSum");};

    void refund(Integer id);

    String refunds();
}
