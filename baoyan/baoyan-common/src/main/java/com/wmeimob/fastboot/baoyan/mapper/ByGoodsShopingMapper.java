
/*
* ByGoodsShopingMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Aug 13 13:32:55 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsShoping;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ByGoodsShopingMapper extends Mapper<ByGoodsShoping> {

    List<ByGoodsShoping> show(@Param("id") Integer id);

    void update(@Param("id")Integer id);

    Integer selectShopCount(@Param("id")Integer id);

    Integer selectGoodsCount(@Param("userId")Integer id,@Param("type") Integer type,@Param("goodsId") Integer goodsId);
}