
/*
* BySubCardGoodsMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 25 10:13:01 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BySubCardGoodsMapper extends Mapper<BySubCardGoods> {
	/**
	 * 查询次卡商品列表
	 * @param bySubCardGoods
	 * @return
	 */
	List<BySubCardGoods> findByCondition(BySubCardGoods bySubCardGoods);

	/**
	 * id 查询
	 * @param id
	 * @return
	 */
    BySubCardGoods queryBySubCardGoodsById(@Param("id") Integer id);

    void updateByAddStock(@Param("productId") Integer productId,@Param("productCount") Integer productCount);

	List<BaseStore> showStore(@Param("id") Integer id);

    List<ByGoodsInfo> selectList(@Param("type") Integer type);

}