package com.wmeimob.fastboot.baoyan.entity;

import com.wmeimob.fastboot.baoyan.vo.Goods;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 购物车支付时请求用到的类
 * <AUTHOR>
 * @date 2021/7/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayAllOrder {

    /**
     * 门票商品信息
     */
    private Goods goods;

    /**
     * 参考下面链接
     * {@link com.wmeimob.fastboot.baoyan.vo.Goods.type}
     */
    private Integer type;

    /**
     * 淘潮玩购物车商品信息
     */
    private List<TcGoodsShoping> tcGoods;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 抵扣后的金额
     */
    private BigDecimal payAmount;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 优惠券抵扣的金额
     */
    private BigDecimal couponAmount;

    /**
     * 积分抵扣的金额
     */
    private BigDecimal scoreAmount;

    /**
     * 商品总数量
     */
    private Integer goodsCount;

    /**
     * 只有一件淘潮商品，也就是通过淘潮详情页进行的付款
     * 只有一件淘潮商品时，请把购买的商品放到 {@link tcGoods} 中
     * tcGoods 中的 goodsId 和 goodsCount是需要的数据
     * true: 淘潮玩商品页进行购买
     * false：购物车进行购买
     */
    private boolean justAloneTc;

    /**
     * 是否 门票和淘潮玩混合支付
     * 为什么不用上面那个，因为上面那个是前端传的，可能会乱
     * true ：混合支付
     * false : 只支付淘潮玩或者门票
     */
    private boolean isPayAll;

    /**
     * 不知道干嘛的，以前有这样一个属性。似乎前端传递过来的
     */
    private String fromId;

    /**
     * 配送方式
     * 1：到店自提
     * 2：快递邮寄
     */
    private Integer deliveryMode;
    /**
     *预计到店时间
     */
    private Date estimatedTime;
    /**
     * 收货地址
     */
    private String address;

    /**
     * 收货人电话
     */
    private String mobilePhone;

    /**
     * 收货人姓名
     */
    private String consignee;

    /**
     * 备注信息
     */
    private String remark;
}
