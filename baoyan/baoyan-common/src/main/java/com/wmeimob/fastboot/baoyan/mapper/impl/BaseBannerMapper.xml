<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseBannerMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseBanner" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
		<result property="status" column="status" jdbcType="INTEGER"/>
		<result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
		<result property="target" column="target" jdbcType="VARCHAR"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="title" column="title" jdbcType="VARCHAR"/>
    </resultMap>
	<select id="selectByExampleList" resultMap="BaseResultMap">
		select base.*,
		case base.jump_type
		when 1 then (select classify_title from base_classify where id = base.target)
		when 2 then "联票列表"
		when 3 then "次卡列表"
		when 4 then "拼团列表"
		END as  "title"
		 from base_banner base where base.`status` = 1 and base.is_del = 0
		order by base.sort desc
	</select>

	<select id="selectPrivacyAgreement" resultType="java.lang.String">
		SELECT agreement FROM agreement WHERE id = 1
	</select>

	<select id="selectPurchaseAgreement" resultType="java.lang.String">
		SELECT agreement FROM agreement WHERE id = 2
	</select>
</mapper>

