<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTeamMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeam" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="teamGoodsId" column="team_goods_id" jdbcType="INTEGER"/>
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="teamPrice" column="team_price" jdbcType="DECIMAL"/>
		<result property="teamNum" column="team_num" jdbcType="INTEGER"/>
		<result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="teamName" column="team_name" jdbcType="VARCHAR"/>
		<result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
		<result property="headImg" column="head_img" jdbcType="VARCHAR"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeam" id="ListResultMap" extends="BaseResultMap">
		<result property="nowPerson" column="nowPerson" jdbcType="INTEGER"/>
		<result property="lessNum" column="lessNum" jdbcType="INTEGER"/>
	</resultMap>

	<select id="findByCondition" resultMap="ListResultMap">
		SELECT st.*,
		(SELECT COUNT(*) FROM by_team_order sto WHERE sto.team_id = st.id AND sto.order_status>=0 )AS nowPerson
		FROM by_team st
		WHERE st.order_status!=-1
		<if test="orderStatus !=null">
			AND st.order_status =#{orderStatus}
		</if>
		<if test="startTime !=null ">
			AND st.gmt_create&gt;=#{startTime}
		</if>
		<if test="endTime !=null ">
			AND st.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
		</if>
		<if test="goodsName !=null and goodsName!=''">
			AND ((st.goods_name LIKE CONCAT('%',#{goodsName},'%')) OR
			(st.team_goods_id =#{goodsName}) OR (st.team_name =#{goodsName}))
		</if>
		ORDER BY st.id DESC
	</select>
	<select id="queryById" resultMap="ListResultMap">
		SELECT
			bt.*, (
				bt.team_num - (
					SELECT
						count(*)
					FROM
						by_team_order
					WHERE
						team_id = bt.id
					AND order_status >= 0
				)
			) AS lessNum
		FROM
			`by_team` bt
		WHERE
		bt.id =#{id}
	</select>
	<select id="teamDetailByOrder" resultMap="BaseResultMap">
			SELECT
				bt.*
				FROM `by_team` bt
				WHERE
				bt.order_status !=-1
				and
				bt.id = #{id}
	</select>
	<select id="selectList" resultMap="ListResultMap">
		SELECT
			bt.*,(
				bt.team_num - (
					SELECT
						count(*)
					FROM
						by_team_order
					WHERE
						team_id = bt.id
					AND order_status = 2
				)
			) AS lessNum,
			cust.nick_name,
			cust.head_img
		FROM
			`by_team` bt
		left join by_cust_user cust on cust.id = bt.user_id
		WHERE
		bt.team_goods_id =#{id} and bt.order_status = 0
	</select>
	<select id="presonTeam" resultMap="BaseResultMap">
		SELECT
			bt.*,(
				bt.team_num - (
					SELECT
						count(*)
					FROM
						by_team_order
					WHERE
						team_id = bt.id
					AND order_status = 2
				)
			) AS lessNum,
			cust.nick_name,
			cust.head_img
		FROM
			`by_team` bt
		left join by_cust_user cust on cust.id = bt.user_id
		left join by_team_order byt on byt.team_id = bt.id
		WHERE
		byt.user_id =#{id} and bt.order_status != -1 and byt.order_status > 1 order by bt.gmt_create desc
	</select>
	<select id="userTeam" resultMap="BaseResultMap">
		SELECT
			bt.*,(
				bt.team_num - (
					SELECT
						count(*)
					FROM
						by_team_order
					WHERE
						team_id = bt.id
					AND order_status = 2
				)
			) AS lessNum,
			cust.nick_name,
			cust.head_img
		FROM
			`by_team` bt
		left join by_cust_user cust on cust.id = bt.user_id
		WHERE
		bt.id =#{id} and bt.order_status != -1
	</select>
	<select id="selectByExampleLIst" resultMap="BaseResultMap">
		SELECT
			bt.*,(
				bt.team_num - (
					SELECT
						count(*)
					FROM
						by_team_order
					WHERE
						team_id = bt.id
					AND order_status = 2
				)
			) AS lessNum,
			cust.nick_name,
			cust.head_img
		FROM
			`by_team` bt
		left join by_cust_user cust on cust.id = bt.user_id
		WHERE
		 bt.is_del = 0 and bt.order_status = 0 and bt.order_status != -1
	</select>
</mapper>

