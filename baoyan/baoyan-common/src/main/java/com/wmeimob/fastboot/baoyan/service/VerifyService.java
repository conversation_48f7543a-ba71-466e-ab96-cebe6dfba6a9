package com.wmeimob.fastboot.baoyan.service;

import java.math.BigDecimal;

public interface VerifyService {
    /**
     * 处理首次核销赠金
     * @param memberId 会员ID
     * @param writeOffId 核销ID
     * @param goodsId 商品ID
     * @param goodsType 商品类型
     * @param orderNo 订单编号
     */
    void handleFirstVerifyBalance(Integer memberId, Integer writeOffId, Integer goodsId, Integer goodsType, String orderNo);
    /**
     * 获取首次核销赠金
     * @param goodsId
     * @param goodsType
     * @return
     */
    BigDecimal getFirstVerifyBalance(Integer goodsId, Integer goodsType);
}
