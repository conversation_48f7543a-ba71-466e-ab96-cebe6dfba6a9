
/*
* ByOrdersMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Mon Jul 15 15:41:21 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.OrderInfoVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
public interface ByOrdersMapper extends Mapper<ByOrders> {

	/**
	 * 根据订单号查询订单
	 * @param orderNo
	 * @return
	 */
	ByOrders findByOrderNo(String orderNo);

	/**
	 * 订单统计
	 * @param byOrders
	 * @return
	 */
    List<ByOrders> queryOrderStatistics(ByOrders byOrders);

	/**
	 * 订单管理查询
	 * @param byOrders
	 * @return
	 */
    List<ByOrders> findByCondition(ByOrders byOrders);

	/**
	 *
	 * @param userId
	 * @param state
	 * @return
	 */
	int getCountByUserIdAndState(@Param("userId") Integer userId, @Param("state") Integer state);

	/**
	 * 查询用户订单列表
	 * @param orders
	 * @return
	 */
	List<OrderInfoVo> userOrderList(ByOrders orders);

	/**
	 * 商品统计
	 * @param byOrders
	 * @return
	 */
    List<GoodsStatisticsVO> queryGoodsStatistics(ByOrders byOrders);
	/**
	 * 订单统计表头
	 * @param byOrders
	 * @return
	 */
	Map<String,Object> getFinanceOrderStatisticsSum(ByOrders byOrders);
	/**
	 * 商品统计表头
	 * @param byOrders
	 * @return
	 */
    Map<String,Object> getFinanceGoodsStatisticsSum(ByOrders byOrders);

	/**
	 * 订单明细导出 by新增需求  2019年10月18日16:05:30
	 * @param byOrders
	 * @return
	 */
    List<ByOrders> findExportByCondition(ByOrders byOrders);

	/**
	 * 财务统计
	 * @param byOrders
	 * @return
	 */
    List<FinanceStatisticsVO> queryFinanceStatistics(ByOrders byOrders);

	/**
	 * 查询核销 门店 和核销员工 根据 核销表id
	 * @param writeOffId
	 * @return
	 */
    FinanceStatisticsVO queryWriteStoreAndStaffByWriteId(@Param("writeOffId") Integer writeOffId);

	/**
	 * 查询次卡商品统计
	 * @param byOrders
	 * @return
	 */
    List<GoodsStatisticsVO> querySubGoodsStatistics(ByOrders byOrders);

	/**
	 * 查询次卡商品统计表头
	 * @param byOrders
	 * @return
	 */
	Map<String,Object> getFinanceSubGoodsStatisticsSum(ByOrders byOrders);

	/**
	 * 查询 联票商品
	 * @param byOrders
	 * @return
	 */
    List<GoodsStatisticsVO> queryTicketGoodsStatistics(ByOrders byOrders);

	/**
	 * 联票商品统计
	 * @param byOrders
	 * @return
	 */
	Map<String,Object> getFinanceTicketGoodsStatisticsSum(ByOrders byOrders);

	/**
	 * 已购买商品数量
	 * @param userId
	 * @param goodsId
	 * @param type
	 * @return
	 */
    Integer selectGoodsNum(@Param("userId")Integer userId,@Param("goodsId") Integer goodsId, @Param("type")Integer type);

    List<String> selectNow(@Param("dateTime")Date dateTime,@Param("endTime")Date endTime);

	/**
	 * 根据支付订单号（父订单号查找一个门票订单）
	 * @param parentNo
	 * @return
	 */
	ByOrders findByPayOrderNo(String parentNo);

    int updateStatusByOrderNo(ByOrders build);
}