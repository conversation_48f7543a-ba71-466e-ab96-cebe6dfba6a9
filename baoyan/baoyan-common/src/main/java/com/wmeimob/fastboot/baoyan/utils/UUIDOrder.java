package com.wmeimob.fastboot.baoyan.utils;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 生成订单号
 * @date 2019-05-30 09:50
 * @Version 1.0
 */
public class UUIDOrder {
    public static String getUUID(){
        long now = System.currentTimeMillis();
        //获取4位年份数字
        SimpleDateFormat dateFormat=new SimpleDateFormat("yyyy");
        //获取时间戳
        String time=dateFormat.format(now);
        String info=now+"";
        //获取三位随机数
        int ran=(int) ((Math.random()*9+1)*100);
        //要是一段时间内的数据连过大会有重复的情况，所以做以下修改

        return time+info.substring(2, info.length())+ran;
    }
}
