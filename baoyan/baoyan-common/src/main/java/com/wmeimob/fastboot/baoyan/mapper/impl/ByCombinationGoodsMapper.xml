<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCombinationGoodsMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="goodsStock" column="goods_stock" jdbcType="INTEGER"/>
        <result property="price" column="price" jdbcType="DECIMAL"/>
        <result property="verificationStart" column="verification_start" jdbcType="DATE"/>
        <result property="verificationEnd" column="verification_end" jdbcType="DATE"/>
        <result property="marketingStart" column="marketing_start" jdbcType="DATE"/>
        <result property="marketingEnd" column="marketing_end" jdbcType="DATE"/>
        <result property="hasVerificationDay" column="has_verification_day" jdbcType="TINYINT"/>
        <result property="verificationDay" column="verification_day" jdbcType="INTEGER"/>
        <result property="regularDownStatus" column="regular_down_status" jdbcType="TINYINT"/>
        <result property="preDepositId" column="pre_deposit_id" jdbcType="INTEGER"/>
        <result property="firstVerifyBalance" column="first_verify_balance" jdbcType="DECIMAL"/>
        <result property="limited" column="limited" jdbcType="INTEGER"/>
        <result property="isCoupon" column="is_coupon" jdbcType="INTEGER"/>
        <result property="isIntegral" column="is_integral" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods" id="ListResultMap" extends="BaseResultMap">
    </resultMap>

    <select id="findByCondition" resultMap="ListResultMap">
        SELECT bcg.*,bgi.goods_name AS goodsNames
        FROM by_combination_goods bcg
        LEFT JOIN by_goods_info bgi ON bcg.goods_id = bgi.id
        WHERE bcg.is_del = 0
        <if test="goodsId !=null">
            AND bcg.goods_id = #{goodsId}
        </if>
    </select>

    <select id="selectAndGoods" resultMap="ListResultMap">
        SELECT bcg.*,goodsName FROM by_combination_goods bcg

        WHERE bcg.id = #{id}
    </select>

    <select id="getCombination" resultType="com.wmeimob.fastboot.baoyan.vo.CombinationVo">
        SELECT bcg.id,bcg.name,bcg.price,bcg.limited,
        bcg.is_coupon isCoupon,bcg.is_integral isIntegral,
        bcg.has_verification_day as hasVerificationDay,bcg.verification_day as verificationDay,
        bcg.verification_start verificationStart,
        bcg.verification_end verificationEnd,bcg.goods_stock goodsStock
        FROM by_combination_goods bcg
        WHERE bcg.is_del = 0
        AND bcg.status = 1
        AND bcg.goods_stock > 0
        AND (bcg.has_verification_day = 1 or now() &lt; bcg.verification_end )
        AND bcg.goods_id = #{goodsId}
        order by price
    </select>

</mapper>