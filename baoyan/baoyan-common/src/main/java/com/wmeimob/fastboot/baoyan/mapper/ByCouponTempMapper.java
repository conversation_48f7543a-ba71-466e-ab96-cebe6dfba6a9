
/*
* ByCouponTempMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 17:55:47 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByCouponTemp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByCouponTempMapper extends Mapper<ByCouponTemp> {
    List<ByCouponTemp> getCouponTempList(ByCouponTemp byCouponTemp);


    ByCouponTemp selectHome(@Param("formatDate") String formatDate);
}