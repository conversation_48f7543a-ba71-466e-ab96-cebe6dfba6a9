<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCouponTempMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCouponTemp" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="name" column="name" jdbcType="VARCHAR"/>
		<result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>
		<result property="targetId" column="target_id" jdbcType="INTEGER"/>
		<result property="discount" column="discount" jdbcType="DECIMAL"/>
		<result property="full" column="full" jdbcType="DECIMAL"/>
		<result property="startDate" column="start_date" jdbcType="DATE"/>
		<result property="endDate" column="end_date" jdbcType="DATE"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="type" column="type" jdbcType="INTEGER"/>
		<result property="couponType" column="coupon_type" jdbcType="INTEGER"/>
		<result property="effectiveType" column="effective_type" jdbcType="INTEGER"/>
		<result property="dayNum" column="day_num" jdbcType="INTEGER"/>
        <result property="singleGoodsType" column="single_goods_type" jdbcType="INTEGER"/>
    </resultMap>
    <select id="getCouponTempList" resultMap="BaseResultMap">
        SELECT t.id,t.`name`,t.discount,t.`full`,t.start_date,t.end_date,t.target_id,t.day_num,t.effective_type,t.type,t.coupon_type,t.single_goods_type,
        CASE t.type
        when 1 THEN c.`classify_title`
        when 2 THEN(
        CASE t.single_goods_type
        when 1 THEN g.goods_name
        when 2 THEN sub.sub_card_goods_name
        when 3 THEN ticket.ticket_goods_name
        END
        )
        END as goodsName
        FROM by_coupon_temp t
        LEFT JOIN base_classify c ON c.id=t.target_id
        LEFT JOIN by_goods_info g ON g.id=t.target_id
        LEFT JOIN by_ticket_goods ticket ON ticket.id=t.target_id
        LEFT JOIN by_sub_card_goods sub ON sub.id=t.target_id
        WHERE t.is_del=0
        <if test="id != null">
            AND t.end_date >= CURRENT_DATE
        </if>
        <if test="name != null and name != ''">
            AND t.`name` LIKE CONCAT('%',#{name},'%')
        </if>
        ORDER BY t.id DESC

	</select>

    <select id="selectHome" resultMap="BaseResultMap">
      select t.*,
        CASE t.type
        when 1 THEN cc.`classify_title`
        when 2 THEN g.goods_name
        END as goodsName
        FROM by_coupon c
	    	LEFT JOIN by_coupon_temp t ON t.id=c.temp_id
        LEFT JOIN base_classify cc ON cc.id=t.target_id
        LEFT JOIN by_goods_info g ON g.id=t.target_id
        where
        t.is_del = 0 and t.start_date &lt;= #{formatDate} order by t.gmt_create desc limit 1
    </select>

</mapper>

