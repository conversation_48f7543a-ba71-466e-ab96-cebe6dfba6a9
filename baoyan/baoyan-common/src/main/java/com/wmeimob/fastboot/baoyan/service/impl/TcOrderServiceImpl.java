package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.baoyan.entity.TcOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/8
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class TcOrderServiceImpl implements TcOrderService {

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private TcWriteOffCodeMapper tcWriteOffCodeMapper;

    /**
     * 用户手动取消订单，定时器取消订单时的操作
     * 返回积分 | 返回优惠券
     * @param id
     * @return
     */
    @Override
    @Async
    public boolean cancelOrder(Integer id) {
//        TcOrder order = tcOrderMapper.selectByPrimaryKey(id);
        //返回积分

        //返回优惠券
//        order

        return false;
    }

    @Override
    public boolean userCancelOrder(Integer id) {
        //查询订单
        TcOrder dbOrder = tcOrderMapper.selectByPrimaryKey(id);
        Assert.notEq(dbOrder.getStatus(), 1, "只有未支付订单才可以取消");

        ByCustUser user = SecurityContext.getUser();
//        ByCustUser user = ByCustUser.builder().id(124904).build();
        Assert.notEq(user.getId(), dbOrder.getUserId(), "这不是你的订单");

        tcOrderMapper.updateByPrimaryKeySelective(
                TcOrder.builder()
                        .id( id )
                        .status( 0 )
                        .build()
        );

        tcOrderGoodsMapper.updateStatus(id, 0);

        cancelOrder(id);

        return true;
    }


    /**
     * 查看某个淘潮订单是否全部都退款了
     * @param orderId
     * @return
     */
    @Override
    public boolean allRefund(Integer orderId) {
        //根据id查询淘潮订单商品
        List<TcOrderGoods> orderGoods = tcOrderGoodsMapper.queryByOrderId(orderId);

        boolean allRefund = orderGoods.stream().allMatch(og -> Objects.equals(og.getStatus(), 5));

        if (allRefund){
            tcOrderMapper.updateByPrimaryKeySelective(
                    TcOrder.builder()
                            .id( orderId )
                            .status( 5 )
                            .build()
            );
        }
        return allRefund;
    }

    /**
     * 更新淘潮玩订单至已付款的状态
     *
     * @param orderNo
     * @param transactionId
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updatePaid(String orderNo, String transactionId) {
        //根据订单号查询淘潮玩订单
        TcOrder tcOrder = tcOrderMapper.findByOrderNo(orderNo);

        //更新他的状态
        if ( tcOrder.getStatus()!=1 ){
            log.error("订单号:{},目前的状态是:{},支付成功的回调在尝试改变订单状态");
            return false;
        }

        TcOrder upOrder = TcOrder.builder()
                .id(tcOrder.getId())
                .status( 2 )
                .payFlowNo(transactionId)
                .payTime(new Date())
                .payType("微信支付")
                .build();

        tcOrderMapper.updateByPrimaryKeySelective(upOrder);

        //更新全部的订单商品状态
        tcOrderGoodsMapper.updateToPaid(tcOrder.getId());

        return true;
    }

    /**
     * 某个订单确认收货
     * @param id
     * @return
     */
    @Override
    public boolean receiving(Integer id) {
        //根据id查询订单
        TcOrder dbOrder = tcOrderMapper.selectByPrimaryKey(id);

        if ( Objects.equals(dbOrder.getDeliveryMode(),1) ){
            //自提订单 核销后自动收货
            throw new CustomException("自提订单全部核销后自动收货");
        }else if ( Objects.equals(dbOrder.getDeliveryMode(),2) ){
            //邮寄订单 必须是已发货才能确认收货
            Assert.notEq(dbOrder.getStatus(),3,"邮寄订单必须是已发货状态才能确认收货");
        }else {
            throw new CustomException("配送方式异常");
        }

        //修改订单状态
        int update = tcOrderMapper.updateByPrimaryKeySelective(
                TcOrder.builder()
                        .id(id)
                        .status(4)
                        .finishTime(new Date())
                        .build()
        );


        //修改订单商品状态
        Example example = new Example(TcOrderGoods.class);
        example.createCriteria()
                .andEqualTo("tcOrderId", id)
                .andNotEqualTo("status", 5);

        //除了已退款的，全部修改成已完成。
        tcOrderGoodsMapper.updateByExampleSelective(
                TcOrderGoods.builder().status(4).build()
                ,example
        );

        return update>0;
    }

    /**
     * 发货的方法
     * @param tcOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deliver(TcOrder tcOrder){
        TcOrder dbOrder = tcOrderMapper.selectByPrimaryKey(tcOrder.getId());
        Assert.isNull(dbOrder,"该订单不存在");
        //订单状态必须是 待发货
        Assert.notEq( dbOrder.getStatus(),2,"当前状态不能发货" );
        //配送方式必须是 邮寄
        Assert.notEq( dbOrder.getDeliveryMode(),2,"当前订单配送方式不是邮寄" );


        int orderUpdate = tcOrderMapper.updateByPrimaryKeySelective(
                TcOrder.builder()
                        .id(tcOrder.getId())
                        .expressName(tcOrder.getExpressName())
                        .logisticsNo(tcOrder.getLogisticsNo())
                        .deliverTime(new Date())
                        .status(3)
                        .build()
        );

        tcOrderGoodsMapper.updateStatus(dbOrder.getId(), 3);

        //修改订单状态
        return orderUpdate > 0;
    }

    /**
     * 查看某个订单详情的核销码是否全部核销完毕
     *
     * @param detailId
     * @return
     */
    @Override
    public boolean allWriteOff(Integer detailId) {
        //查询该订单详情的核销码
        List<TcWriteOffCode>  writeOffCodes = tcWriteOffCodeMapper.select(TcWriteOffCode.builder().detailId(detailId).build());

        boolean allWriteOff = writeOffCodes.stream().allMatch(writeOffCode -> writeOffCode.getStatus() != 0);

        if (allWriteOff){
            //如果全部核销了，该订单详情应该是已完成状态
            tcOrderGoodsMapper.updateByPrimaryKeySelective(
                    TcOrderGoods.builder()
                            .status(4)
                            .id(detailId)
                            .build()
            );
        }


        return allWriteOff;
    }

    /**
     * 检查某个订单的订单详情是否全部已完成
     *
     * @param orderNo
     * @return
     */
    @Override
    public boolean allComplete(String orderNo) {
        TcOrder dbOrder = tcOrderMapper.findByOrderNo(orderNo);

        List<TcOrderGoods> orderGoods = tcOrderGoodsMapper.queryByOrderId(dbOrder.getId());

        boolean allComplete = orderGoods.stream().allMatch(tcOrderGoods -> tcOrderGoods.getStatus() == 4 || tcOrderGoods.getStatus() == 5);

        if (allComplete){
            tcOrderMapper.updateByPrimaryKeySelective(
                    TcOrder.builder()
                            .id( dbOrder.getId() )
                            .finishTime( new Date() )
                            .status( 4 )
                            .build()
            );
        }

        return allComplete;
    }

}
