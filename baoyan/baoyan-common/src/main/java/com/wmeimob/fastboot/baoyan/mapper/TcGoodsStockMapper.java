package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TcGoodsStock)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcGoodsStockMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcGoodsStock queryById(Integer id);




    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcGoodsStock 实例对象
     * @return 对象列表
     */
    List<TcGoodsStock> queryAll(TcGoodsStock tcGoodsStock);

    /**
     * 新增数据
     *
     * @param tcGoodsStock 实例对象
     * @return 影响行数
     */
    int insert(TcGoodsStock tcGoodsStock);

    /**
     * 修改数据
     *
     * @param tcGoodsStock 实例对象
     * @return 影响行数
     */
    int update(TcGoodsStock tcGoodsStock);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 根据 商品id 查库
     * @param id
     * @return
     */
    TcGoodsStock queryByGoodsId(@Param("id") Integer id);
}