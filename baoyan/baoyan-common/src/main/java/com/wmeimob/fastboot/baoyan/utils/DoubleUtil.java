package com.wmeimob.fastboot.baoyan.utils;

/**
 * <AUTHOR>
 * @date 2021/7/8
 */
public class DoubleUtil {

    /**
     * 如果double为空或者为0，返回true
     * @param d
     * @return
     */
    public static boolean blankDouble(Double d){
        return d==null || d==0;
    }

    /**
     * 如果double不为空或者不为0，返回true
     * @param d
     * @return
     */
    public static boolean notBlankDouble(Double d){
        return !blankDouble(d);
    }

}
