<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseStoreMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseStore" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="code" column="code" jdbcType="VARCHAR"/>
		<result property="name" column="name" jdbcType="VARCHAR"/>
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
		<result property="address" column="address" jdbcType="VARCHAR"/>
		<result property="storeImg" column="store_img" jdbcType="VARCHAR"/>
		<result property="point" column="point" jdbcType="VARCHAR"/>
		<result property="appointmentNum" column="appointment_num" jdbcType="INTEGER"/>
		<result property="fullNum" column="full_num" jdbcType="INTEGER"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="payPwd" column="pay_pwd" jdbcType="VARCHAR"/>
		<result property="deleteStatus" column="delete_status" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="distance" column="distance" jdbcType="VARCHAR"/>
		<result property="longitude" column="longitude" jdbcType="VARCHAR"/>
		<result property="latitude" column="latitude" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findStoreByTcId" resultMap="BaseResultMap">
        SELECT st.*
        FROM tc_comtion cm
        LEFT JOIN base_store st
        ON cm.`tc_shop_id` = st.`id`
        WHERE cm.`tc_goods_id` = #{tcId}
    </select>

	<select id="selectList" resultMap="BaseResultMap">
		select s.*,ifnull(
          ROUND(
              6378.138 * 2 * ASIN(
                  SQRT(
                      POW(
                          SIN(
                              (
                                #{latitude} * PI() / 180 - s.latitude * PI() / 180
                              ) / 2
                          ),
                          2
                      ) + COS(#{latitude} * PI() / 180) * COS(s.latitude * PI() / 180) * POW(
                          SIN(
                              (
                                  #{longitude} * PI() / 180 - s.longitude * PI() / 180
                              ) / 2
                          ),
                          2
                      )
                  )
              ) * 1,1
          ),0) AS distance from  base_store s  where s.delete_status = 0 and s.status=1
          <if test="storeName != null and storeName != ''">
		  	and s.name like concat("%",#{storeName},"%")
		  </if>
          <if test="set != null">
              and s.id in
              <foreach collection="set" index="index" item="labelId" open="(" separator="," close=")">
                  #{labelId}
              </foreach>
          </if>
		order by distance asc
	</select>
    <select id="showDate" resultType="map">
        SELECT
   date_format(p.apponintment_date, '%Y-%m-%d') AS time,
	 sum(p.apponintment_num) as apponintmentNum

FROM
   by_cust_appointment p
   where p.apponintment_status != 1 and p.store_id = #{id} and date_format(p.apponintment_date, '%Y-%m') = date_format(#{date}, '%Y-%m')
GROUP BY
   date_format(p.apponintment_date, '%Y-%m-%d')
    </select>
    <select id="selectCountList" resultType="int">
        select ifnull(sum(p.apponintment_num),0) from
        by_cust_appointment p
        where p.apponintment_status != 1 and p.store_id = #{id} and date_format(p.apponintment_date,'%Y-%m-%d') = date_format(#{date}, '%Y-%m-%d')
    </select>
    <select id="showWriteOffStore" resultMap="BaseResultMap">
        SELECT
            bs.*
        FROM
            base_store bs
        where
         FIND_IN_SET(bs.id,(
                SELECT
                    oc.store_ids
                FROM
                    write_off_code oc
                WHERE
                    oc.id=#{writeOffId}
            ))
        AND delete_status = 0
        AND STATUS = 1
    </select>
    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseStore">
		SELECT 
			`id`,
			`code`,
			`name`,
			`mobile`,
			`address`,
			`store_img`,
			`point`,
			`appointment_num`,
			`full_num`,
			`status`,
			`pay_pwd`,
			`delete_status`,
			`gmt_create`,
			`gmt_modified`
		FROM  base_store
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="code != '' and code != null">
			  	AND `code` = #{code}
			</if>
			<if test="name != '' and name != null">
			  	AND `name` = #{name}
			</if>
			<if test="mobile != '' and mobile != null">
			  	AND `mobile` = #{mobile}
			</if>
			<if test="address != '' and address != null">
			  	AND `address` = #{address}
			</if>
			<if test="storeImg != '' and storeImg != null">
			  	AND `store_img` = #{storeImg}
			</if>
			<if test="point != '' and point != null">
			  	AND `point` = #{point}
			</if>
			<if test="appointmentNum != null">
			  	AND `appointment_num` = #{appointmentNum}
			</if>
			<if test="fullNum != null">
			  	AND `full_num` = #{fullNum}
			</if>
			<if test="status != null">
			  	AND `status` = #{status}
			</if>
			<if test="payPwd != '' and payPwd != null">
			  	AND `pay_pwd` = #{payPwd}
			</if>
			<if test="deleteStatus != null">
			  	AND `delete_status` = #{deleteStatus}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`code`,
			`name`,
			`mobile`,
			`address`,
			`store_img`,
			`point`,
			`appointment_num`,
			`full_num`,
			`status`,
			`pay_pwd`,
			`delete_status`,
			`gmt_create`,
			`gmt_modified`
		FROM base_store
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseStore" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO base_store
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="code != '' and code != null">
			`code`,
		</if>
		<if test="name != '' and name != null">
			`name`,
		</if>
		<if test="mobile != '' and mobile != null">
			`mobile`,
		</if>
		<if test="address != '' and address != null">
			`address`,
		</if>
		<if test="storeImg != '' and storeImg != null">
			`store_img`,
		</if>
		<if test="point != '' and point != null">
			`point`,
		</if>
		<if test="appointmentNum != null">
			`appointment_num`,
		</if>
		<if test="fullNum != null">
			`full_num`,
		</if>
		<if test="status != null">
			`status`,
		</if>
		<if test="payPwd != '' and payPwd != null">
			`pay_pwd`,
		</if>
		<if test="deleteStatus != null">
			`delete_status`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		<if test="gmtModified != null">
			`gmt_modified`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="code != '' and code != null">
			#{code},	
		</if>
		<if test="name != '' and name != null">
			#{name},	
		</if>
		<if test="mobile != '' and mobile != null">
			#{mobile},	
		</if>
		<if test="address != '' and address != null">
			#{address},	
		</if>
		<if test="storeImg != '' and storeImg != null">
			#{storeImg},	
		</if>
		<if test="point != '' and point != null">
			#{point},	
		</if>
		<if test="appointmentNum != null">
			#{appointmentNum},	
		</if>
		<if test="fullNum != null">
			#{fullNum},	
		</if>
		<if test="status != null">
			#{status},	
		</if>
		<if test="payPwd != '' and payPwd != null">
			#{payPwd},	
		</if>
		<if test="deleteStatus != null">
			#{deleteStatus},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		<if test="gmtModified != null">
			#{gmtModified},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseStore">
		UPDATE base_store
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="code != '' and code != null">
				`code` = #{code},
			</if>
			<if test="name != '' and name != null">
				`name` = #{name},
			</if>
			<if test="mobile != '' and mobile != null">
				`mobile` = #{mobile},
			</if>
			<if test="address != '' and address != null">
				`address` = #{address},
			</if>
			<if test="storeImg != '' and storeImg != null">
				`store_img` = #{storeImg},
			</if>
			<if test="point != '' and point != null">
				`point` = #{point},
			</if>
			<if test="appointmentNum != null">
				`appointment_num` = #{appointmentNum},
			</if>
			<if test="fullNum != null">
				`full_num` = #{fullNum},
			</if>
			<if test="status != null">
				`status` = #{status},
			</if>
			<if test="payPwd != '' and payPwd != null">
				`pay_pwd` = #{payPwd},
			</if>
			<if test="deleteStatus != null">
				`delete_status` = #{deleteStatus},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
			<if test="gmtModified != null">
				`gmt_modified` = #{gmtModified},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM base_store WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseStore">
		DELETE FROM base_store
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="code != '' and code != null">
			  	AND `code` = #{code}
			</if>
			<if test="name != '' and name != null">
			  	AND `name` = #{name}
			</if>
			<if test="mobile != '' and mobile != null">
			  	AND `mobile` = #{mobile}
			</if>
			<if test="address != '' and address != null">
			  	AND `address` = #{address}
			</if>
			<if test="storeImg != '' and storeImg != null">
			  	AND `store_img` = #{storeImg}
			</if>
			<if test="point != '' and point != null">
			  	AND `point` = #{point}
			</if>
			<if test="appointmentNum != null">
			  	AND `appointment_num` = #{appointmentNum}
			</if>
			<if test="fullNum != null">
			  	AND `full_num` = #{fullNum}
			</if>
			<if test="status != null">
			  	AND `status` = #{status}
			</if>
			<if test="payPwd != '' and payPwd != null">
			  	AND `pay_pwd` = #{payPwd}
			</if>
			<if test="deleteStatus != null">
			  	AND `delete_status` = #{deleteStatus}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
	</delete> -->
</mapper>

