package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.core.exception.CustomException;

import java.util.List;

/**
 * (TcRecommend)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-20 19:01:00
 */
public interface TcRecommendService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    default TcRecommend queryById(Integer id) {
        throw new CustomException("TcRecommendService");
    }


    /**
     * 新增数据
     *
     * @param tcRecommend 实例对象
     * @return 实例对象
     */
    default Boolean insert(TcRecommend tcRecommend) {
        throw new CustomException("TcRecommendService");
    }

    /**
     * 修改数据
     *
     * @param tcRecommend 实例对象
     * @return 实例对象
     */
    default Boolean update(TcRecommend tcRecommend) {
        throw new CustomException("TcRecommendService");
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    default Boolean deleteById(Integer id) {
        throw new CustomException("TcRecommendService");
    }


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcRecommend 实例对象
     * @return 对象列表
     */

    default List<TcRecommend> queryAll(TcRecommend tcRecommend) {
        throw new CustomException("TcRecommendService");
    }

    default Boolean onAndOffShelves(TcRecommend updateObject) {
        throw new CustomException("TcRecommendService");
    }


}
