<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseAdvertisingMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.vo.BaseAdvertisingVO" id="BaseResultMap">
        <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectBySetting" resultMap="BaseResultMap">
        SELECT * FROM base_advertising WHERE status = 1
        AND id = 1
        AND DATE_FORMAT(now()+INTERVAL 8 HOUR,'%Y-%m-%d %H:%i:%S') &gt;= DATE_FORMAT(start_time,'%Y-%m-%d %H:%i:%S')
        AND DATE_FORMAT(now()+INTERVAL 8 HOUR,'%Y-%m-%d %H:%i:%S') &lt;= DATE_FORMAT(end_time,'%Y-%m-%d %H:%i:%S')
    </select>
</mapper>

