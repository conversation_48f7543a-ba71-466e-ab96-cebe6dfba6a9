<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseManyImgMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.vo.ImgVO" id="BaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="layout" column="layout" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT * FROM base_list_img WHERE status = 1
    </select>

</mapper>