<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTeamOrderMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeamOrder" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="teamId" column="team_id" jdbcType="INTEGER"/>
		<result property="teamGoodsId" column="team_goods_id" jdbcType="INTEGER"/>
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
		<result property="payType" column="pay_type" jdbcType="INTEGER"/>
		<result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
		<result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
		<result property="num" column="num" jdbcType="INTEGER"/>
		<result property="orderRemark" column="order_remark" jdbcType="VARCHAR"/>
		<result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
		<result property="orderState" column="order_state" jdbcType="INTEGER"/>
		<result property="isTeam" column="is_team" jdbcType="TINYINT"/>
		<result property="isAfter" column="is_after" jdbcType="TINYINT"/>
		<result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
		<result property="cancelTime" column="cancel_time" jdbcType="TIMESTAMP"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="point" column="point" jdbcType="INTEGER"/>
        <result property="integralAmount" column="integral_amount" jdbcType="DECIMAL"/>
        <result property="couponAmount" column="coupon_amount" jdbcType="DECIMAL"/>
        <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
		<result property="payFlowNo" column="pay_flow_no" jdbcType="VARCHAR"/>
		<result property="orderCloseType" column="order_close_type" jdbcType="INTEGER"/>
    </resultMap>

	<resultMap type="com.wmeimob.fastboot.baoyan.vo.TeamOrderVo" id="TeamOrderVoMap" extends="BaseResultMap">
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="teamPrice" column="team_price" jdbcType="DECIMAL"/>
		<result property="receiveName" column="receive_name" jdbcType="VARCHAR"/>
		<result property="receivePhone" column="receive_phone" jdbcType="VARCHAR"/>
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
		<result property="teamStatus" column="team_status" jdbcType="INTEGER"/>
		<result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
		<result property="teamName" column="teamName" jdbcType="VARCHAR"/>
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
		<result property="subtotal" column="subtotal" jdbcType="DECIMAL"/>
	</resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeamOrder" id="MyResultMap" extends="BaseResultMap">
		<association property="byTeam" javaType="com.wmeimob.fastboot.baoyan.entity.ByTeam"
					 select="com.wmeimob.fastboot.baoyan.mapper.ByTeamMapper.queryById"
					 column="{id=team_id}">
		</association>
	</resultMap>
    <select id="queryTeamList" resultMap="TeamOrderVoMap">
		SELECT o.*
		,b.`nick_name`,
		bt.team_name as teamName,
		b.mobile
		FROM by_team_order o
		LEFT JOIN by_team bt ON bt.id=o.team_id
		LEFT JOIN by_cust_user b ON b.id=o.user_id
		WHERE o.order_status !=-1 and o.order_state=0
		<if test="orderStatus != null">
			AND o.order_status=${orderStatus}
		</if>
		<if test="startTime != null and startTime != ''">
			AND DATE_FORMAT(o.order_time,'%Y-%m-%d') >=#{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			AND DATE_FORMAT(o.order_time,'%Y-%m-%d') &lt;=#{endTime}
		</if>
		<if test="orderNo != null and orderNo != ''">
			AND o.order_no=#{orderNo}
		</if>
        <if test="searchName !=null and searchName !='' ">
            and ((o.order_no like concat('%',#{searchName},'%')) or  (b.nick_name like concat('%',#{searchName},'%'))
            or (b.mobile like concat('%',#{searchName},'%')))
        </if>
		ORDER BY o.id DESC

	</select>
	<select id="getTeamOrderInfoById" resultMap="TeamOrderVoMap">
		SELECT o.*
		,u.nick_name,u.mobile
		,t.team_price,t.goods_name,
		o.order_amount-o.coupon_amount-o.integral_amount as subtotal
		FROM by_team_order o
		LEFT JOIN by_team t ON t.id=o.team_id
		LEFT JOIN by_cust_user u ON u.id=o.user_id
		WHERE o.id=${id} LIMIT 1
	</select>
	<select id="getTeamOrderInfo"  resultMap="TeamOrderVoMap">
		SELECT o.id,o.order_no,o.order_amount,o.pay_amount,o.order_status,
		t.goods_name,t.goods_img
		,u.nick_name,u.mobile
		,t.order_status team_status
		FROM by_team_order o
		LEFT JOIN by_team t ON t.id=o.team_id
		LEFT JOIN by_cust_user u on u.id = o.user_id
		WHERE o.team_id = ${id} AND o.order_status !=-1
	</select>
	<select id="myTeam" resultMap="MyResultMap">
		SELECT sto.*
		FROM `by_team_order` sto
		WHERE sto.user_id = ${userId} AND sto.order_status !=-1
		AND sto.is_del=0 order by sto.gmt_create desc
	</select>

	<select id="selectList" resultMap="BaseResultMap">
SELECT
	team.id,
	cust.nick_name,
	cust.head_img,
	team.order_count
FROM
	by_team_order team
	LEFT JOIN by_cust_user cust ON cust.id = team.user_id
WHERE
	team.order_status != - 2
	AND team.order_status != - 1
	AND team.order_status != 1
	AND is_team = 1
	and team.order_count != 0
	order by team.gmt_create desc
	</select>
	<select id="selectTeamList" resultType="map">
		select team.is_team as isTeam,cust.nick_name as nickName,cust.head_img as headImg,team.pay_time as payTime from  by_team_order team
		left join by_cust_user cust on cust.id = team.user_id
		where team.team_id = #{id} and order_status > 1
	</select>
	<select id="selectCountList" resultType="int">
		select count(*) from by_team team
		left join by_team_order team_order on team.id =  team_order.id
		where team.order_status = 1 and team_order.order_status = 2 and team.user_id = #{userId}
	</select>
</mapper>

