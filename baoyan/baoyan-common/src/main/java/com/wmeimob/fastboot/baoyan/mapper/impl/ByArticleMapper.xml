<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByArticleMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByArticle" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="articleName" column="article_name" jdbcType="VARCHAR"/>
		<result property="content" column="content" jdbcType="LONGVARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="type" column="data_type" jdbcType="INTEGER" />
		<result property="goodsId" column="data_id" jdbcType="INTEGER" />
		<result property="state" column="state" jdbcType="INTEGER" />
    </resultMap>
	<select id="selectArticleList" resultMap="BaseResultMap">
		SELECT
		`id`,
		`article_name`,
		`content`,
		`gmt_create`,
		`gmt_modified`,
		`data_type`,
		`data_id`,
		`state`
		FROM  by_article
		<where>
			1 = 1
			<if test="name != null and name != ''">
				AND `id` LIKE CONCAT('%',#{name},'%') or  `article_name` LIKE CONCAT('%',#{name},'%')
			</if>
		</where>
		ORDER  BY id DESC

	</select>

	<select id="selectAllForGoods" resultType="com.wmeimob.fastboot.baoyan.vo.GoodsVo">
		SELECT * FROM (
			SELECT 1 type,g1.id id,g1.goods_name name FROM by_goods_info g1 WHERE g1.is_del = 0
			<if test="qo.state != null and qo.state == 1">
				AND g1.status = 1
			</if>
			UNION ALL 
			SELECT 2 type,g2.id id,g2.sub_card_goods_name name FROM by_sub_card_goods g2 WHERE g2.is_del = 0
			<if test="qo.state != null and qo.state == 1">
				AND g2.status = 1
			</if>
			UNION ALL 
			SELECT 3 type,g3.id id,g3.ticket_goods_name name FROM by_ticket_goods g3 WHERE g3.is_del = 0
			<if test="qo.state != null and qo.state == 1">
				AND g3.status = 1
			</if>
			UNION ALL 
			SELECT 4 type,g4.id id,g4.team_name name FROM by_team_goods g4 WHERE g4.is_del = 0
		) re
		<where>
			<if test="qo.goodsName != null and qo.goodsName != ''">
				AND re.name LIKE CONCAT('%',#{qo.goodsName},'%')
			</if>
			<if test="qo.type != null">
				AND re.type = #{qo.type}
			</if>
			<if test="qo.goodsId != null">
				AND re.id = #{qo.goodsId}
			</if>
		</where>
	</select>

	<select id="selectOtherState" resultType="Long">
		SELECT COUNT(1) FROM by_article WHERE state = 1 AND id != #{id}
	</select>


	<resultMap type="com.wmeimob.fastboot.baoyan.vo.ByArticlesVO" id="WxByStateMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="articleName" column="article_name" jdbcType="VARCHAR"/>
		<result property="content" column="content" jdbcType="LONGVARCHAR"/>
	</resultMap>

	<select id="selectByState" resultMap="WxByStateMap">
		SELECT id,article_name,content FROM by_article WHERE state = 1
	</select>
	<update id="updateState">
		update  by_article set state=0
	</update>
</mapper>

