package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsStockMapper;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsStockService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (TcGoodsStock)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Service("tcGoodsStockService")
public class TcGoodsStockServiceImpl implements TcGoodsStockService {
    @Resource
    private TcGoodsStockMapper tcGoodsStockDao;

    @Resource
    private TcGoodsService tcGoodsService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcGoodsStock queryById(Integer id) {
        return this.tcGoodsStockDao.queryById(id);
    }

    @Override
    public TcGoodsStock queryByGoodsId(Integer id) {
        if (null == id) return null;
        return tcGoodsStockDao.queryByGoodsId(id);
    }


    /**
     * 新增数据
     *
     * @param tcGoodsStock 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcGoodsStock tcGoodsStock) {
        if (null == tcGoodsStock || null == tcGoodsStock.getGoodsId())
            throw new CustomException("参数不对");

        TcGoods tcGoods = tcGoodsService.queryById(tcGoodsStock.getGoodsId());
        if (null == tcGoods)
            throw new CustomException("未查询到商品");

        tcGoodsStock.setGmtCreate(new Date());
        tcGoodsStock.setStock(tcGoods.getStock());

        return this.tcGoodsStockDao.insert(tcGoodsStock) > 0;
    }

    /**
     * 修改数据
     *
     * @param tcGoodsStock 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcGoodsStock tcGoodsStock) {
        if (null == tcGoodsStock || null == tcGoodsStock.getGoodsId()) throw new CustomException("参数不对");
        TcGoods tcGoods = tcGoodsService.queryById(tcGoodsStock.getGoodsId());
        if (null == tcGoods)
            throw new CustomException("未查询到商品");
        tcGoodsStock.setStock(tcGoods.getStock());
        return this.tcGoodsStockDao.update(tcGoodsStock) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.tcGoodsStockDao.deleteById(id) > 0;
    }

    @Override
    public List<TcGoodsStock> queryPageList(TcGoodsStock queryObject) {
        return tcGoodsStockDao.queryAll(queryObject);
    }
}