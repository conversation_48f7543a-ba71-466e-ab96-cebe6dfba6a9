<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByEvaluateMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByEvaluate" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="orderId" column="order_id" jdbcType="INTEGER"/>
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="mark" column="mark" jdbcType="INTEGER"/>
		<result property="userName" column="user_name" jdbcType="VARCHAR"/>
		<result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
		<result property="evaluateText" column="evaluate_text" jdbcType="VARCHAR"/>
		<result property="img" column="img" jdbcType="VARCHAR"/>
		<result property="isUsed" column="is_used" jdbcType="INTEGER"/>
		<result property="isShow" column="is_show" jdbcType="INTEGER"/>
		<result property="returnDate" column="return_date" jdbcType="TIMESTAMP"/>
		<result property="returnName" column="return_name" jdbcType="VARCHAR"/>
		<result property="returnDesc" column="return_desc" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="isAnonymous" column="is_anonymous" jdbcType="TINYINT"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="resouceType" column="resouce_type" jdbcType="TINYINT"/>

    </resultMap>
	<resultMap id="BaseResultMap2" type="com.wmeimob.fastboot.baoyan.entity.ByEvaluate" extends="BaseResultMap">
		<result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
		<result property="headImg" column="head_img" jdbcType="VARCHAR"/>
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="findEvalListByCondition" resultMap="BaseResultMap2">
		SELECT
		sei.*,
		sui.nick_name,
		sui.mobile
		FROM
		by_evaluate sei
		LEFT JOIN by_cust_user sui ON sui.id = sei.user_id
		WHERE
		sei.is_del = 0
		<if test="startTime !=null ">
			and sei.gmt_create&gt;=#{startTime}
		</if>
		<if test="endTime !=null ">
			and sei.gmt_create &lt;=date_add(#{endTime}, interval 1 day)
		</if>
		<if test="searchName !=null and searchName !='' ">
			and ((sei.goods_name like concat('%',#{searchName},'%')) or  (sui.`nick_name` like concat('%',#{searchName},'%'))
			or (sui.mobile like concat('%',#{searchName},'%')))
		</if>
		order by sei.gmt_create desc

	</select>

	<select id="getEvaluate" resultMap="BaseResultMap2">
		select
		 e.*,
		 b.head_img
		 from by_evaluate e
		left join by_cust_user b on b.id = e.user_id
		where e.resouce_type = #{type} and e.goods_id = #{id} and e.is_show = 1 and e.is_del = 0 order by e.gmt_create desc
	</select>

	<select id="selectList" resultMap="BaseResultMap2">
		select
		 e.mark,e.user_name,e.evaluate_text,e.img,e.gmt_create,e.is_anonymous,
		 b.head_img
		 from by_evaluate e
		  left join by_cust_user b on b.id = e.user_id
		where e.resouce_type = 4 and e.goods_id = #{id} and e.is_show = 1 and e.is_del = 0 order by e.gmt_create desc
	</select>

</mapper>

