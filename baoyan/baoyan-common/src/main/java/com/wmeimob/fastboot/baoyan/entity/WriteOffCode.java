/*
 * WriteOffCode.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 23 13:40:35 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "write_off_code")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class WriteOffCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 核销卡名称
     */
    @Column(name = "write_off_name")
    private String writeOffName;
    /**
     * 用户id
     */
    @Column(name = "cust_user_id")
    private Integer custUserId;
    /**
     * 适用门店
     */
    @Column(name = "store_ids")
    private String storeIds;
    /**
     * 来源商品
     */
    @Column(name = "goods_name")
    private String goodsName;
    /**
     * 来源商品ID
     */
    @Column(name = "source_goods_id")
    private Integer sourceGoodsId;
    /**
     * 来源订单
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 剩余次数
     */
    @Column(name = "surplus_num")
    private Integer surplusNum;
    /**
     * 总次数
     */
    @Column(name = "total_num")
    private Integer totalNum;
    /**
     * 有效期开始时间
     */
    @Column(name = "expiry_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date expiryDate;
    /**
     * 状态(0 待处理 1已使用 2已过期)
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 1普通商品2次卡商品5:规格商品
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 客户姓名
     */
    @Transient
    private String custUserName;
    /**
     * 手机号
     */
    @Transient
    private String mobile;
    /**
     * 商品名称
     */
    @Transient
    private String goodName;
    /**
     * 门店名称
     */
    @Transient
    private String storeName;
    /**
     * 来源商品
     */
    @Transient
    private String sourceGoodName;
    /**
     * 来源渠道
     */
    @Transient
    private String channelSourceName;
    @Transient
    private String searchName;
    /**
     * 剩余显示
     */
    @Transient
    private String surplusShow;
    @Transient
    private String startTime;
    @Transient
    private String endTime;

    /**。。。。。。。
     * 查询 核销码有效期天数
     */
    @Transient
    private Integer dateSum;

    @Column(name = "end_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;
    @Column(name = "detail_id")
    public Integer detailId;
    /**
     * 订单状态类型 为0正常 1订单取消  （当前适用核销业务 为了处理订单取消情况字段）
     */
    @Column(name = "order_state")
    private Integer orderState;

    /**
     * 订单类型 0普通订单 1拼团订单
     */
    @Column(name = "order_type")
    public Integer orderType;


    @Column(name = "code")
    private String code;
    /**
     * 来源商品名称 modifyby 2019年12月18日11:43:40
     */
    @Transient
    private String sourceGoodsName;
    /**
     * 是否过期自动核销 0否 1是
     */
    @Column(name = "is_expire")
    private Integer isExpire;


}