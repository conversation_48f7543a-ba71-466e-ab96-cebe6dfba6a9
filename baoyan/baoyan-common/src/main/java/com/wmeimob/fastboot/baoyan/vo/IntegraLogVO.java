package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: IntegraLogVO
 * @projectName baoyan
 * @description: 积分记录VO
 * @date 2019/8/5 14:12
 */
public class IntegraLogVO extends ByIntegralLog implements Serializable {
    /**
     * 当前积分
     */
    private Integer nowPoint;
    /**
     * 即将获得积分
     */
    private Integer bePoint;
    /**
     * 历史总获得积分
     */
    private Integer historyPoint;

    /**
     * 积分列表
     */
    private List<ByIntegralLog> logList;

    public Integer getNowPoint() {
        return nowPoint;
    }

    public void setNowPoint(Integer nowPoint) {
        this.nowPoint = nowPoint;
    }

    public Integer getBePoint() {
        return bePoint;
    }

    public void setBePoint(Integer bePoint) {
        this.bePoint = bePoint;
    }

    public Integer getHistoryPoint() {
        return historyPoint;
    }

    public void setHistoryPoint(Integer historyPoint) {
        this.historyPoint = historyPoint;
    }

    public List<ByIntegralLog> getLogList() {
        return logList;
    }

    public void setLogList(List<ByIntegralLog> logList) {
        this.logList = logList;
    }
}
