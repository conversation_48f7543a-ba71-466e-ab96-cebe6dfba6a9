package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.util.List;

/**
 * (ByClassifyRecommend)表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:53
 */
public interface ByClassifyRecommendService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    default ByClassifyRecommend queryById(Integer id) {
        throw new NotImplementedException("queryById");
    }
    /**
     * 新增数据
     *
     * @param byClassifyRecommend 实例对象
     * @return 实例对象
     */
    default Boolean insert(ByClassifyRecommend byClassifyRecommend){
        throw new NotImplementedException("insert");
    }
    /**
     * 修改数据
     *
     * @param byClassifyRecommend 实例对象
     * @return 实例对象
     */
    default Boolean update(ByClassifyRecommend byClassifyRecommend){
        throw new NotImplementedException("update");
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
   default Boolean deleteById(Integer id){
       throw new NotImplementedException("update");
   }

    default List<ByClassifyRecommend> queryAll(ByClassifyRecommend queryItem) {
        throw new NotImplementedException("queryAll");
    }

    default Boolean onAndOffShelves(ByClassifyRecommend updateObject) {
        throw new NotImplementedException("onAndOffShelves");
    }

}