
/*
* ByEvaluateMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Wed Jul 17 16:52:31 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByEvaluateMapper extends Mapper<ByEvaluate> {
	/**
	 * 评价列表
	 * @param byEvaluate
	 * @return
	 */
    List<ByEvaluate> findEvalListByCondition(ByEvaluate byEvaluate);

    List<ByEvaluate> getEvaluate(@Param("type") Integer type,@Param("id") Integer id);

	List<ByEvaluate> selectList(@Param("id")Integer id);
}