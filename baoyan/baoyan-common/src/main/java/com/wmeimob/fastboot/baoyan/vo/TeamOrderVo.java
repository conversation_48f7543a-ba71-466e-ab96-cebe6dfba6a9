/*
* SfTeamOrder.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version 1.0
* 作者：lzx
* Sun Apr 28 18:40:28 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* <AUTHOR>
* @date 16:38 2019/5/19
* @Description: 拼团订单vo
*/
@Data
public class TeamOrderVo extends ByTeamOrder implements Serializable{
	private Integer goodsId;
	private BigDecimal teamPrice;
	private String mobile;
	private Integer teamStatus;
	private String receiveName;
	private String receivePhone;
	private String startTime;
	private String endTime;
	private String goodsName;
	private String goodsImg;
	private String nickName;
	private String searchName;
	private String teamName;
	private BigDecimal subtotal;
}