package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcBanner;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.List;

public interface TcBannerService extends  CommonService<TcBanner>{
    /**
     * Banner查询
     * @param id
     * @return
     */
    default TcBanner queryTcBannerById(Object id){throw new NotImplementedException("queryTcBannerById");};

    /**
     * Banner添加
     * @param  tcBanner
     * @return
     */
    default  void addTcBanner(TcBanner tcBanner){throw new NotImplementedException("addTcBanner");};


    /**
     * Banner删除
     * @param id
     * @return
     */
    default void removeTcBanner(Object id){throw new NotImplementedException("removeTcBanner");};


    /**
     * Banner修改
     * @param tcBanner
     * @return
     */
    default void modifyTcBanner(TcBanner tcBanner){throw new NotImplementedException("modifyTcBanner");}

    /**
     * 小程序 查看轮播图
     */
    default List<? extends TcBanner> wxQueryAll(){throw new NotImplementedException("wxQueryAll");};
}
