package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "ych_value_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchValueLog implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private Integer id;

    /**
     * 会员ID
     */
    private String leaguerBaseId;

    /**
     * 储值类型编码
     */
    private String valueCode;

    /**
     * 储值类型名称
     */
    private String valueCodeName;

    /**
     * 储值变更类型名称
     */
    private String orderTypeName;

    /**
     * 变更时间
     */
    private String logTime;

    /**
     * 变更数量
     */
    private String changeAmount;

    /**
     * Summary
     */
    private String summary;
}
