package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTeamProperty;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTeamPropertyService
 * @Description 预售商品属性
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
public interface ByTeamPropertyService extends CommonService<ByTeamProperty>{

    /**
     * 预售商品属性查询
     * @param id
     * @return
     */
    default ByTeamProperty queryByTeamPropertyById(Object id){throw new NotImplementedException("queryByTeamPropertyById");};

    /**
     * 预售商品属性添加
     * @param  byTeamProperty
     * @return
     */
    default  void addByTeamProperty(ByTeamProperty byTeamProperty){throw new NotImplementedException("addByTeamProperty");};


    /**
     * 预售商品属性删除
     * @param id
     * @return
     */
    default void removeByTeamProperty(Object id){throw new NotImplementedException("removeByTeamProperty");};


    /**
     * 预售商品属性修改
     * @param byTeamProperty
     * @return
     */
    default void modifyByTeamProperty(ByTeamProperty byTeamProperty){throw new NotImplementedException("modifyByTeamProperty");};
}
