package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.BySubGoodsClassify;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BySubGoodsClassifyMapper extends Mapper<BySubGoodsClassify> {
    /**
     * 次卡商品类型
     * @param goodsId
     * @return
     */
    List<Integer> selectClassifyIdByGoods(@Param("id") Integer goodsId);
}
