package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "by_store_table")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByStoreTable implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 无符号 自动递增
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 所属门店id
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 桌码编号前缀
     */
    @Column(name = "table_prefix")
    private String tablePrefix;
    /**
     * 桌码编号
     */
    @Column(name = "table_no")
    private String tableNo;
    /**
     * 座位数
     */
    @Column(name = "people_num")
    private Integer peopleNum;
    /**
     * 状态：0启用，1 禁用
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否逻辑删除;1:删除,0:未删除.
     */
    @Column(name = "is_del")
    private Integer isDel;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;

}