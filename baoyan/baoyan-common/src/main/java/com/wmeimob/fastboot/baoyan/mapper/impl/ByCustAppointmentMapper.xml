<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCustAppointmentMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCustAppointment" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="apponintmentType" column="apponintment_type" jdbcType="INTEGER"/>
		<result property="apponintmentDate" column="apponintment_date" jdbcType="DATE"/>
		<result property="apponintmentNum" column="apponintment_num" jdbcType="INTEGER"/>
		<result property="ticketChannel" column="ticket_channel" jdbcType="INTEGER"/>
		<result property="custUserId" column="cust_user_id" jdbcType="INTEGER"/>
		<result property="address" column="address" jdbcType="VARCHAR"/>
		<result property="storeId" column="store_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="apponintmentStatus" column="apponintment_status" jdbcType="INTEGER"/>
		<result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
		<result property="childName" column="child_name" jdbcType="VARCHAR"/>
		<result property="birthday" column="birthday" jdbcType="DATE"/>
		<result property="childSex" column="child_sex" jdbcType="TINYINT"/>
		<result property="productId" column="product_id" jdbcType="INTEGER"/>
		<result property="productName" column="product_name" jdbcType="VARCHAR"/>
		<result property="tel" column="tel" jdbcType="VARCHAR"/>

	</resultMap>
    <select id="queryAppoinmentList" resultType="com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO" parameterType="com.wmeimob.fastboot.baoyan.entity.ByCustAppointment">
		  SELECT
		bca.id as id,
		bcu.nick_name as custUserName,
		bca.tel as  tel,
		bs.`name` as storeName,
		bca.apponintment_date as apponintmentDate,
		bca.ticket_channel as ticketChannel,
		bca.apponintment_num as apponintmentNum,
		bca.parent_name as parentName,
		bca.tel as contactTel,
		-- bca.child_age as childAge,
		bca.child_sex as childSex,
		bca.child_name as childName,
		bca.apponintment_status as apponintmentStatus,
		gi.goods_name as apponintmentProduct
		FROM
		by_cust_appointment bca
		LEFT JOIN base_store bs on bs.id =bca.store_id
		LEFT JOIN by_cust_user bcu on bcu.id=bca.cust_user_id
		LEFT JOIN by_goods_info gi on gi.id=bca.product_id
		<where>
			1 = 1
			<if test="storeId != null">
				AND bca.`store_id` = #{storeId}
			</if>
			<if test="productId != null">
				AND bca.product_id = #{productId}
			</if>
			<if test="startDate != null">
				AND bca.`apponintment_date` &gt;= #{startDate}
			</if>
			<if test="endDate != null">
				AND bca.`apponintment_date` &lt;= #{endDate}
			</if>
			<if test="name != null and name!=''">
				AND bcu.nick_name like concat('%',#{name},'%') or  bca.tel like concat('%',#{name},'%') or bcu.parent_name like concat('%',#{name},'%')
			</if>


		</where>
		ORDER  BY bca.id DESC
	</select>
    <select id="custApponintByUserId" resultType="com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO">
		SELECT
			bca.id as id,
			bcu.nick_name as custUserName,
			bs.`name` as storeName,
			bs.mobile as storeMobile,
			bs.address as address,
			bca.apponintment_date as apponintmentDate,
			bca.ticket_channel as ticketChannel,
			bca.apponintment_num as apponintmentNum,
			bca.parent_name as parentName,
		    bca.tel as contactTel,
		    bca.child_sex as childSex,
		    bca.child_name as childName,
		    bca.birthday as birthday,
		    bca.apponintment_status as apponintmentStatus,
			bca.gmt_modified as gmtModified,
			CASE bca.apponintment_status
			when 0 then '已预约'
			when 1 then '已取消'
			when 2 then '已结束'
			end as apponintmentStatusStr,
			bca.gmt_create  as gmtCreate,
			gi.goods_name as apponintmentProduct,
			CASE bca.ticket_channel
			when 0 then '大众点评'
			when 1 then '麦淘亲子'
		    when 2 then '抖音'
			when 3 then '闲鱼'
			end as ticketChannelName,
		    bs.store_img as storeImg,
			bca.store_id as storeId
		FROM
		by_cust_appointment bca
		LEFT JOIN base_store bs on bs.id =bca.store_id
		LEFT JOIN by_cust_user bcu on bcu.id=bca.cust_user_id
		LEFT JOIN by_goods_info gi on gi.id=bca.product_id
		<where>
			and bca.is_del=0
			<if test="userId != null">
				AND bca.`cust_user_id` = #{userId}
			</if>
			<if test="apponintmentStatus != null">
				AND bca.`apponintment_status` = #{apponintmentStatus}
			</if>
			<if test="id != null">
				AND bca.`id` = #{id}
			</if>
		</where>
		ORDER  BY bca.id DESC
	</select>

</mapper>

