/*
* ByTeamRobot.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 11:18:31 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_team_robot")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByTeamRobot implements Serializable {
	
   private static final long serialVersionUID = 1L;
	
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 机器人头像图片
     */
    @Column(name = "obot_img")
    private String obotImg;
    /**
     * 机器人昵称
     */
    @Column(name = "obot_nick_name")
    private String obotNickName;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;

}