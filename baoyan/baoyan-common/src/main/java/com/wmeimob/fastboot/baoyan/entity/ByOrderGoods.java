/*
 * ByOrderGoods.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Aug 06 17:02:57 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_order_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByOrderGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 主订单id
     */
    @Column(name = "order_id")
    private Integer orderId;
    /**
     * 主订单编号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;
    /**
     * 商品编号
     */
    @Column(name = "goods_no")
    private String goodsNo;
    /**
     * 一级分类id
     */
    @Column(name = "first_class")
    private Integer firstClass;
    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;
    /**
     * 购买数量
     */
    @Column(name = "goods_num")
    private Integer goodsNum;
    /**
     * 是否售后;1:是,0:否
     */
    @Column(name = "is_after_sale")
    private Boolean isAfterSale;

    /**
     * 退款中：0否，1，是（因为是否售后默认是1才加的字段）
     */
    @Column(name = "is_refund")
    private Boolean isRefund;

    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 订单状态:-1订单取消,0:待付款,1:待发货,2:待收货,3:确认收货(待评价),4:已完成，5已关闭
     */
    @Column(name = "order_status")
    private Integer orderStatus;
    /**
     *商品总价格 总数量*总单价
     */
    @Transient
    private BigDecimal allPrice;

    @Transient
    private Boolean allRefund;

    /**
     * 优惠券金额
     */
    @Column(name = "coupon_price")
    private BigDecimal couponPrice;
    /**
     * 积分金额
     */
    @Column(name = "integral_price")
    private BigDecimal integralPrice;

    /**
     * 核销时间
     */
    @Column(name = "write_off_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date writeOffDate;

    /**
     * 商品实付单价
     */
    @Column(name = "pay_unit_price")
    private BigDecimal payUnitPrice;

    /**
     * 临时 实付单价，单位分
     */
    @Transient
    private Integer tempUnitPrice;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsImg() {
        return goodsImg;
    }

    public void setGoodsImg(String goodsImg) {
        this.goodsImg = goodsImg;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Integer getFirstClass() {
        return firstClass;
    }

    public void setFirstClass(Integer firstClass) {
        this.firstClass = firstClass;
    }

    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }

    public Boolean getAfterSale() {
        return isAfterSale;
    }

    public void setAfterSale(Boolean afterSale) {
        isAfterSale = afterSale;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public BigDecimal getAllPrice() {
        return allPrice;
    }

    public void setAllPrice(BigDecimal allPrice) {
        this.allPrice = allPrice;
    }

    public BigDecimal getCouponPrice() {
        return couponPrice==null?new BigDecimal(0):couponPrice;
    }

    public void setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
    }

    public BigDecimal getIntegralPrice() {
        return integralPrice ==null?new BigDecimal(0):integralPrice;
    }

    public void setIntegralPrice(BigDecimal integralPrice) {
        this.integralPrice = integralPrice;
    }

//    @Column(name = "start_date")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date startDate;
//
//    @Column(name = "end_date")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date endDate;
//
//    @Column(name = "write_count")
//    private Integer writeCount;
//

    /**商品类型 1 普通商品 2 次卡 3 联票*/
    @Column(name = "product_type")
    public String productType;
    /**商品id*/
    @Column(name = "product_id")
    public String productId;
    /**购买数量*/
    @Column(name = "product_count")
    public Integer productCount;

    @Transient
    private Integer refundNum;
    /**
     * 是否显示退款类型和售后按钮
     * 0 申请售后
     * 1 已退款
     */
    @Transient
    private Integer refundType;

    public Integer getProductCount() {
        return productCount;
    }

    public void setProductCount(Integer productCount) {
        this.productCount = productCount;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    @Transient
    private Integer teamType;
    /**
     * 小计
     */
    @Transient
    private BigDecimal subtotal;
    /**
     * 是否时间到期完成 0否，1是
     */
    @Transient
    private Integer isExpire;

}