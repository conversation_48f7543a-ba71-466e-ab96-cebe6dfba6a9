package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.core.exception.CustomException;

import java.util.List;

/**
 * 淘潮玩核销码表(TcWriteOffCode)表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-11 15:25:01
 */
public interface TcWriteOffCodeService {


    /**
     * 根据订单号查询核销码
     * @param orderNo
     * @return
     */
    default List<TcWriteOffCode> findByOrderNo(String orderNo){
        throw new CustomException("findByOrderNo");
    }

    /**
     * 根据订单商品 保存核销码
     * @param tcOrderGoods
     * @return
     */
    default boolean saveOffCode(TcOrderGoods tcOrderGoods, Integer userId){
        throw new CustomException("createOffCode");
    }

    /**
     * 核销某个淘潮玩核销码
     * @param writeOffCode
     * @param staffId 核销员工id
     * @param storeId 核销的门店id
     * @return
     */
    boolean writeOff(TcWriteOffCode writeOffCode, Integer staffId, Integer storeId);

    /**
     * 根据id查询核销码的详细信息
     * @param id
     * @return
     */
    TcWriteOffCode findById(Integer id);
}