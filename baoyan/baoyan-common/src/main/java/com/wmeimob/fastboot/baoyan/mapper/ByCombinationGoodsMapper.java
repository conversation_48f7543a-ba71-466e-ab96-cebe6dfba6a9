package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods;
import com.wmeimob.fastboot.baoyan.vo.CombinationVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * 商品规格管理
 * <AUTHOR>
 */
public interface ByCombinationGoodsMapper extends Mapper<ByCombinationGoods> {

    List<ByCombinationGoods> findByCondition(ByCombinationGoods byCombinationGoods);
    /**
     * 编辑
     * @param id
     * @return
     */
    ByCombinationGoods selectAndGoods(@Param("id") Integer id);
    /**
     * wx-获取商品规格
     * @param id
     * @return
     */
    List<CombinationVo> getCombination(Integer id);
}
