package com.wmeimob.fastboot.baoyan.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Arrays;

/**
 * 订单日志类型枚举
 * <AUTHOR>
 * @date 2021/7/4
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OrderLogType {

    /**
     * 操作类型 1.创建订单 2.付款 3.申请售后 4.同意售后 5.拒绝售后 6.管理员退款 7.核销 8.赠送给好友 9.删除订单
     */
    CREATE(1,"创建订单"),
    PAY(2,"支付订单"),
    APPLY_AFTER(3,"申请售后"),
    AGREE_AFTER(4,"同意售后"),
    REFUSE_AFTER(5,"拒绝售后"),
    ADMIN_REFUND(6,"管理员退款"),
    WRITE_OFF(7,"核销"),
    GIVE(8,"赠送给好友"),
    DELETE(9,"删除订单");
//    操作类型 1.创建订单 2.支付订单 3.申请售后 4.同意售后 5.拒绝售后 6.管理员退款 7.核销 8.赠送给好友 9.删除订单
//    原来
//    操作类型 1.创建订单 2.付款 3.申请售后 4.同意售后/退款 5.核销 6.赠送给好友 7.删除订单

    /**
     * 根据id获得实例
     * @param id
     * @return
     */
    public static OrderLogType getInstance(Integer id) {
        return Arrays.stream(OrderLogType.values())
                .filter(orderLogType -> orderLogType.id.equals(id))
                .findFirst().orElse(null);
    }


    private Integer id;

    private String name;

    OrderLogType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    @Override
    public String toString() {
        return "OrderLogType{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }

    public Integer getId() {
        return id;
    }

//    @JsonValue
    public String getName() {
        return name;
    }
}
