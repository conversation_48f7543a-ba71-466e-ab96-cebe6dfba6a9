/*
 * ByCoupon.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 11 17:55:47 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_coupon")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ByCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * TempId
     */
    @Column(name = "temp_id")
    private Integer tempId;
    /**
     * 每人？次
     */
    @Column(name = "total_limit")
    private Integer totalLimit;
    /**
     * 每人每天？次
     */
    @Column(name = "day_limit")
    private Integer dayLimit;
    /**
     * 优惠券总数
     */
    @Column(name = "total")
    private Integer total;
    /**
     * 已领取数
     */
    @Column(name = "taked")
    private Integer taked;
    /**
     * Sort
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 0下架1上架
     */
    @Column(name = "state")
    private Boolean state;
    /**
     * 0否1是
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * TargetId
     */
    @Column(name = "target_id")
    private Integer targetId;
    /**
     * GmtCreate
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * GmtUpdate
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 1优惠券（抵扣卷） 2折扣卷
     */
    @Transient
    private Integer couponType;
    @Transient
    private Integer userId;
    @Transient
    private Integer takeId;
    @Transient
    private String targetName;
    /**
     * 限制类型
     */
    @Transient
    private String limitation;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getTakeId() {
        return takeId;
    }

    public void setTakeId(Integer takeId) {
        this.takeId = takeId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    /**
     * 是否显示领取 已领取
     */
    @Transient
    private Boolean disFlag=true;

    /**
     * 优惠券名称
     */
    @Transient
    private String name;
    /**
     * Discount
     */
    @Transient
    private BigDecimal discount;
    /**
     * 满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）
     */
    @Transient
    private BigDecimal full;
    /**
     * 有效期
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;
    /**
     * 有效期
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;


    /**
     * type 0 全部商品 1 分类商品 2单个商品
     */
    @Transient
    private Integer type;

    @Transient
    private Integer registerType;
    @Transient
    private Integer singleGoodsType;
    @Transient
    private Integer effectiveType;
    @Transient
    private Integer dayNum;

}