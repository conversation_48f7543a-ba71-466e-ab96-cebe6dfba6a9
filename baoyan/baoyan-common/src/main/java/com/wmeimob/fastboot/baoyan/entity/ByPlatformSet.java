/*
 * ByPlatformSet.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Mon Jul 22 17:18:53 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "by_platform_set")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByPlatformSet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 支付时间(小时)
     */
    @Column(name = "pay_time")
    private Integer payTime;
    /**
     * 积分返赠
     */
    @Column(name = "integral_return")
    private BigDecimal integralReturn;
    /**
     * 评论获得积分
     */
    @Column(name = "comment_integral")
    private Integer commentIntegral;
    /**
     * 积分抵扣
     */
    @Column(name = "integration_deduction")
    private Integer integrationDeduction;
    /**
     * 拼团商品是否可用积分 默认 0不使用
     */
    @Column(name = "team_integral_is_enable")
    private Integer teamIntegralIsEnable;
    /**
     * 联票商品是否可用积分 默认 0不使用
     */
    @Column(name = "ticket_integral_is_enable")
    private Integer ticketIntegralIsEnable;
    /**
     * 次卡商品是否可用积分 默认 0不使用
     */
    @Column(name = "sub_card_integral_is_enable")
    private Integer subCardIntegralIsEnable;
    /**
     * 拼团商品是否可用优惠券 默认 0不使用
     */
    @Column(name = "team_coupon_is_enable")
    private Integer teamCouponIsEnable;
    /**
     * 联票商品是否可用优惠券 默认 0不使用
     */
    @Column(name = "ticket_coupon_is_enable")
    private Integer ticketCouponIsEnable;
    /**
     * 次卡商品是否可用优惠券 默认 0不使用
     */
    @Column(name = "sub_card_coupon_is_enable")
    private Integer subCardCouponIsEnable;
    /**
     * 优惠券失效前x天提醒用户/天
     */
    @Column(name = "coupon_invalid")
    private Integer couponInvalid;
    /**
     * 生日礼遇 赠品优惠券
     */
    @Column(name = "birthday_coupon_id")
    private Integer birthdayCouponId;
    /**
     * 完善有礼 赠品优惠券
     */
    @Column(name = "perfect_info_coupon_id")
    private Integer perfectInfoCouponId;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 优惠券 选择类型 1生日 2完善信息
     */
    @Transient
    private Integer choiceType;

    /**
     * 普通商品是否可用积分 默认 0不使用
     */
    @Column(name = "general_integral_is_enable")
    private Integer generalIntegralIsEnable;
    /**
     * 普通商品是否可用优惠券 默认 0不使用
     */
    @Column(name = "general_coupon_is_enable")
    private Integer generalCouponIsEnable;

    /**
     * 文章数量(最小为1)
     */
    @Column(name = "article_num")
    private Integer articleNum;
    /**
     * 文章滚动时间（单位：秒）
     */
    @Column(name = "article_seconds")
    private Integer articleSeconds;
    /**
     * 文章上下架 0下架 1上架
     */
    @Column(name = "article_status")
    private Integer articleStatus;

}