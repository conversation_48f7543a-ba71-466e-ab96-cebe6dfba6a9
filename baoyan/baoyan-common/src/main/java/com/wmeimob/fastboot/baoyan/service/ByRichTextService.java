package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByRichText;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByRichTextService
 * @Description 富文本
 * <AUTHOR>
 * @Date Tue Jul 30 09:01:33 CST 2019
 * @version1.0
 **/
public interface ByRichTextService extends CommonService<ByRichText>{

    /**
     * 富文本查询
     * @param id
     * @return
     */
    default ByRichText queryByRichTextById(Object id){throw new NotImplementedException("queryByRichTextById");};

    /**
     * 富文本添加
     * @param  byRichText
     * @return
     */
    default  void addByRichText(ByRichText byRichText){throw new NotImplementedException("addByRichText");};


    /**
     * 富文本删除
     * @param id
     * @return
     */
    default void removeByRichText(Object id){throw new NotImplementedException("removeByRichText");};


    /**
     * 富文本修改
     * @param byRichText
     * @return
     */
    default void modifyByRichText(ByRichText byRichText){throw new NotImplementedException("modifyByRichText");};
}
