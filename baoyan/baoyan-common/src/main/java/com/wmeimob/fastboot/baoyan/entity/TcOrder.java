package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 淘潮玩订单表(TcOrder)实体类
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tc_order")
public class TcOrder implements Serializable {
    private static final long serialVersionUID = 298591222608195147L;
    /**
     * 主键自增，无意义
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 支付订单号，微信支付时的商家订单号
     */
    @Column(name = "pay_order_no")
    private String payOrderNo;
    /**
     * 创建时间，下单时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gtmCreate;
    /**
     * 支付时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;
    /**
     * 支付方法，目前微信支付
     */
    private String payType;
    /**
     * 下单用户id
     */
    private Integer userId;
    /**
     * 收货人
     */
    private String consignee;
    /**
     * 收货人联系电话
     */
    private String mobilePhone;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 订单备注
     */
    private String remark;
    /**
     * 配送方式。1.到店自提，2，邮寄
     */
    private Integer deliveryMode;
    /**
     * 预计到店时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date estimatedTime;
    /**
     * 物流单号
     */
    private String logisticsNo;
    /**
     * 快递公司名
     */
    private String expressName;
    /**
     * 发货时间
     */
    private Date deliverTime;
    /**
     * 完成时间 | 收货时间 | 自提2时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date finishTime;
    /**
     * 支付流水号
     */
    private String payFlowNo;
    /**
     * 订单状态:
     * -1订单超时自动取消 0-已关闭 1-待付款,
     * 2-已付款(待发货|待自提), 3-已发货, 4-已完成, 5-已退款
     */
    private Integer status;
    /**
     * 订单原价，抵扣前价格
     */
    private BigDecimal originAmount;
    /**
     * 实际支付金额
     */
    private BigDecimal actualAmount;
    /**
     *积分抵扣金额
     */
    private BigDecimal integralAmount;
    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponAmount;
    /**
     * 商品数量
     */
    private Integer goodsCount;

    @Transient
    private String startDate;

    @Transient
    private String endDate;

    @Transient
    private String keyWord;

    @Transient
    private List<TcOrderGoods> orderGoods;
}