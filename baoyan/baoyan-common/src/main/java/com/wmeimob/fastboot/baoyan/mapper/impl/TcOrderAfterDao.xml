<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcOrderAfterMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcOrderAfter" id="baseTcOrderAfter">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="afterType" column="after_type" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="refundCount" column="refund_count" jdbcType="INTEGER"/>
        <result property="refundPrice" column="refund_price" jdbcType="DECIMAL"/>
        <result property="detailId" column="detail_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="refundAddrId" column="refund_addr_id" jdbcType="INTEGER"/>
        <result property="refundPhone" column="refund_phone" jdbcType="VARCHAR"/>
        <result property="refundConsignee" column="refund_consignee" jdbcType="VARCHAR"/>
        <result property="refundAddr" column="refund_addr" jdbcType="VARCHAR"/>
        <result property="reason" column="reason" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="refundExpress" column="refund_express" jdbcType="VARCHAR"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="rejectMsg" column="reject_msg" jdbcType="VARCHAR"/>
        <result property="byCustUser.nickName" column="nick_name"/>
        <result property="byCustUser.mobile" column="mobile"/>
    </resultMap>

    <resultMap id="withOrderDetail" type="com.wmeimob.fastboot.baoyan.entity.TcOrderAfter" extends="baseTcOrderAfter">
        <association property="tcOrderGoods" column="detail_id" select="com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper.selectByPrimaryKey"/>
    </resultMap>

    <sql id="afterColumn">
        after.id, after.user_id, after.after_type, after.goods_id,
        after.refund_count, after.refund_price, after.detail_id, after.order_no,
        after.refund_addr_id, after.refund_consignee, after.refund_phone,
        after.refund_addr, after.reason, after.status, after.apply_time, after.handle_time,
        after.remark, after.refund_express, after.logistics_no, after.reject_msg
    </sql>

    <select id="findDetail" resultMap="withOrderDetail">
        select
        <include refid="afterColumn" />
        from tc_order_after after
        where user_id = #{userId}
        order by after.id desc
    </select>

    <select id="findDetailById" resultMap="withOrderDetail">
        select
        <include refid="afterColumn" />
        from tc_order_after after
        where id = #{id}
    </select>

    <select id="findAll" resultMap="withOrderDetail">
        select
        <include refid="afterColumn" />
        ,user.nick_name, user.mobile
        from tc_order_after after
        left join by_cust_user user
        on user.id = after.user_id
        <where>
            <if test="startTime!=null and startTime!=''">
                and after.apply_time >= #{startTime}
            </if>
            <if test="endTime!=null and endTime!=''">
                and after.apply_time &lt;= #{endTime}
            </if>
            <if test="searchName!=null and searchName!=''">
                and
                (
                after.order_no = #{searchName}
                or
                user.mobile = #{searchName}
                or
                user.id = #{searchName}
                or
                user.nick_name like concat('%',#{searchName},'%')
                )
            </if>
        </where>
        order by after.id desc
    </select>
</mapper>