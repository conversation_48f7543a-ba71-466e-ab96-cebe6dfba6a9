<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByGoodsStoreMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByGoodsStore" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="storeId" column="store_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>
	<select id="selectContactGoods" resultMap="BaseResultMap">
		SELECT
			gs.*
		FROM
		by_goods_store gs
		LEFT JOIN by_goods_info gi on gi.id=gs.goods_id
		where gi.is_del=0 and gs.store_id=#{storeId}
	</select>
	<!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsStore">
        SELECT
            `id`,
            `goods_id`,
            `store_id`,
            `gmt_create`,
            `gmt_modified`
        FROM  by_goods_store
        <where>
            1 = 1
            <if test="id != null">
                  AND `id` = #{id}
            </if>
            <if test="goodsId != null">
                  AND `goods_id` = #{goodsId}
            </if>
            <if test="storeId != null">
                  AND `store_id` = #{storeId}
            </if>
            <if test="gmtCreate != null">
                  AND `gmt_create` = #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                  AND `gmt_modified` = #{gmtModified}
            </if>
        </where>
        ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        SELECT
            `id`,
            `goods_id`,
            `store_id`,
            `gmt_create`,
            `gmt_modified`
        FROM by_goods_store
        WHERE id = #{id}
    </select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsStore" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO by_goods_store
        <trim prefix="(" suffixOverrides=",">
        <if test="id != null">
            `id`,
        </if>
        <if test="goodsId != null">
            `goods_id`,
        </if>
        <if test="storeId != null">
            `store_id`,
        </if>
        <if test="gmtCreate != null">
            `gmt_create`,
        </if>
        <if test="gmtModified != null">
            `gmt_modified`,
        </if>
        </trim>
        ) VALUES
        <trim prefix="(" suffixOverrides=",">
        <if test="id != null">
            #{id},
        </if>
        <if test="goodsId != null">
            #{goodsId},
        </if>
        <if test="storeId != null">
            #{storeId},
        </if>
        <if test="gmtCreate != null">
            #{gmtCreate},
        </if>
        <if test="gmtModified != null">
            #{gmtModified},
        </if>
        </trim>
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsStore">
        UPDATE by_goods_store
        <trim prefix="set" suffixOverrides=",">
            <if test="id != null">
                `id` = #{id},
            </if>
            <if test="goodsId != null">
                `goods_id` = #{goodsId},
            </if>
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="gmtCreate != null">
                `gmt_create` = #{gmtCreate},
            </if>
            <if test="gmtModified != null">
                `gmt_modified` = #{gmtModified},
            </if>
        </trim>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="deleteByPrimaryKey" parameterType="map">
        DELETE FROM by_goods_store WHERE id = #{id}
    </delete>

    <delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsStore">
        DELETE FROM by_goods_store
        <where>
            1 = 1
            <if test="id != null">
                  AND `id` = #{id}
            </if>
            <if test="goodsId != null">
                  AND `goods_id` = #{goodsId}
            </if>
            <if test="storeId != null">
                  AND `store_id` = #{storeId}
            </if>
            <if test="gmtCreate != null">
                  AND `gmt_create` = #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                  AND `gmt_modified` = #{gmtModified}
            </if>
        </where>
    </delete> -->
</mapper>

