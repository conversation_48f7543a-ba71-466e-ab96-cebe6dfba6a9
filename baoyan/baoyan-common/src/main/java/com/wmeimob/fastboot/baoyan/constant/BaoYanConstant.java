package com.wmeimob.fastboot.baoyan.constant;

/**
 * <AUTHOR>
 * @title: BaoyanConstant
 * @projectName baoyan
 * @description: 常量类
 * @date 2019/7/2 11:50
 */
public class  BaoYanConstant {
    /**
     * 客户预约状态  0 已预约
     */
    public static final Integer  APPONINTMENT_STATUS_0 = 0;
    /**
     * 客户预约状态  1 已取消
     */
    public static final Integer  APPONINTMENT_STATUS_1 = 1;
    /**
     * 客户预约状态  2 已结束
     */
    public static final Integer  APPONINTMENT_STATUS_2 = 2;

    /**
     * 未删除 0
     */
    public static final Integer  IS_DEL_0 = 0;
    /**
     * 刪除  1
     */
    public static final Integer  IS_DEL_1 = 1;
    /**
     * 1.商品分类
     */
    public static final Integer  JUMP_TYPE_1 = 1;
    /**
     * 2.联票列表
     */
    public static final Integer  JUMP_TYPE_2 = 2;
    /**
     * 次卡列表
     */
    public static final Integer  JUMP_TYPE_3 = 3;
    /**
     * 拼团列表
     */
    public static final Integer  JUMP_TYPE_4 = 4;
    /**
     * 文章
     */
    public static final Integer  JUMP_TYPE_5 = 5;
    /**
     * 普通商品
     */
    public static final Integer  JUMP_TYPE_6 = 6;
    /**
     * 联票商品
     */
    public static final Integer  JUMP_TYPE_7 = 7;
    /**
     * 次卡商品
     */
    public static final Integer  JUMP_TYPE_8 = 8;
    /**
     * 拼团商品
     */
    public static final Integer  JUMP_TYPE_9 = 9;
    /**
     * 富文本类型 1商品
     */
    public static final Integer  RICH_TYPE_1 = 1;
    /**
     * 富文本类型 2 联票
     */
    public static final Integer  RICH_TYPE_2 = 2;
    /**
     * 富文本类型 3 次卡
     */
    public static final Integer  RICH_TYPE_3 = 3;
    /**
     * 富文本类型 4 拼团商品
     */
    public static final Integer  RICH_TYPE_4 = 4;
    /**
     * '领取方式 1后台发放
     */
    public static final Integer  GET_TYPE_1 = 1;
    /**
     * '领取方式 2前端领取',
     */
    public static final Integer  GET_TYPE_2 = 2;
    /**
     * 优惠券状态  0未使用
     */
    public static final Integer  IS_USE_0 = 0;
    /**
     * 优惠券状态 1使用
     */
    public static final Integer  IS_USE_1 = 1;
    /**
     * 优惠券状态 2过期
     */
    public static final Integer  IS_USE_2 = 2;

    /**
     * 常量 1
     */
    public static final Integer  CONSTANT_ONE = 1;

    /**
     * 常量 2
     */
    public static final Integer  CONSTANT_TWO = 2;

    /**
     * 常量 3
     */
    public static final Integer  CONSTANT_THREE= 3;
    /**
     * 常量 0
     */
    public static final Integer  CONSTANT_ZERO= 0;



}
