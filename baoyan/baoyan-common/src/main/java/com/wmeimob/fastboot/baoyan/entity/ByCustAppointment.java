/*
 * ByCustAppointment.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 09 14:34:33 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_cust_appointment")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByCustAppointment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 预约类型
     */
    @Column(name = "apponintment_type")
    private Integer apponintmentType;
    /**
     * 预约日期
     */
    @Column(name = "apponintment_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date apponintmentDate;
    /**
     * 预约数量
     */
    @Column(name = "apponintment_num")
    private Integer apponintmentNum;
    /**
     * 购票渠道
     */
    @Column(name = "ticket_channel")
    private Integer ticketChannel;
    /**
     * 预约用户id
     */
    @Column(name = "cust_user_id")
    private Integer custUserId;
    /**
     * 预约门店地址
     */
    @Column(name = "address")
    private String address;
    /**
     * 门店id
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 是否删除 0 未删除 1删除
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 预约状态 0 已预约 1已取消 2已结束
     */
    @Column(name = "apponintment_status")
    private Integer apponintmentStatus;

    /**
     * 起始时间
     */
    @Transient
    private Date startDate;
    /**
     * 起始时间
     */
    @Transient
    private Date endDate;
    /**
     * 查询字段
     */
    @Transient
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }


    @Column(name = "parent_name")
    private String parentName;
    @Column(name = "child_name")
    private String childName;
    @Column(name = "birthday")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date birthday;
    @Column(name = "child_sex")
    private Integer childSex;
    @Column(name = "product_id")
    private Integer productId;
    @Column(name = "product_name")
    private String productName;
    @Column(name = "tel")
    public String tel;
}