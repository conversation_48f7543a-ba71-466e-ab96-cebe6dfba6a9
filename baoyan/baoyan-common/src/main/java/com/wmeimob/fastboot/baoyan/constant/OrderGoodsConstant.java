package com.wmeimob.fastboot.baoyan.constant;

/**
 * <AUTHOR>
 * @title: OrderGoodConstant
 * @projectName baoyan
 * @description: 订单商品 常量类
 * @date 2019/7/2 13:51
 */
public class OrderGoodsConstant {
    /**
     * 订单状态  1 待支付
     */
    public static final Integer  ORDER_STATUS_1 = 1;

    /**
     * 订单状态  2 待支付
     */
    public static final Integer  ORDER_STATUS_2 = 2;

    /**
     * 订单状态  3 待支付
     */
    public static final Integer  ORDER_STATUS_3 = 3;

    /**
     * 订单状态  4 已发货
     */
    public static final Integer  ORDER_STATUS_4 = 4;
    /**
     * 订单状态  5 待使用
     */
    public static final Integer  ORDER_STATUS_5 = 5;
    /**
     * 订单状态  6 已收货(待评价)
     */
    public static final Integer  ORDER_STATUS_6 = 6;
    /**
     * 订单状态  7 已完成
     */
    public static final Integer  ORDER_STATUS_7 = 7;

    /**
     * 售后类型  0 仅退款
     */
    public static final Integer  AFTER_TYPE_0 = 0;

    /**
     * 售后类型  1 换货
     */
    public static final Integer  AFTER_TYPE_1 = 1;
    /**
     * 售后类型  2 退货退款
     */
    public static final Integer  AFTER_TYPE_2 = 2;

    /**
     * 售后状态  0 待审核
     */
    public static final Integer  AFTER_STATUS_0 = 0;
    /**
     * 售后状态  1 已通过
     */
    public static final Integer  AFTER_STATUS_1 = 1;
    /**
     * 售后状态  2 已拒绝
     */
    public static final Integer  AFTER_STATUS_2 = 2;
    /**
     * 一件商品的最低价
     */
    public static final Double LOW_PRICE_GOODS = 0.01;

}
