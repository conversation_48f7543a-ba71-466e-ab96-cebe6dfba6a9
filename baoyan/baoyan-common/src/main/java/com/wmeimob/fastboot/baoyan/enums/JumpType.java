package com.wmeimob.fastboot.baoyan.enums;

import java.util.Arrays;

/**
 * @author: wang<PERSON>hun
 * systemName king
 * CreationDate:2021/7/25
 * packageName:com.wmeimob.fastboot.baoyan.enums
 * <p>
 * 标识 淘潮玩 模块 和 系列跳转类型
 */

public enum JumpType {
    /**
     * 跳转类型（
     * 1.商品分类2.联票列表3.次卡列表
     * 4.拼团列表5.文章6.普通商品7.联票商品
     * 8.次卡商品9.拼团商品10.优惠券11.淘潮玩品类
     * 12.淘潮玩品牌 13.淘潮玩标签 14.淘潮玩首页头部
     * ）
     */
    GOODS_TYPE(1, "商品分类"), PRESENT_LIST(2, "联票列表"), TIME_CARD_LIST(3, "次卡列表"),
    JOINING_GROUP_LIST(4, "拼团列表"), ARTICLE(5, "文章"), GENERAL_GOODS(6, "普通商品"),
    PRESENT_GOODS(7, "联票商品"), TIME_CARD_GOODS(8, "次卡商品"), JOINING_GROUP_GOODS(9, "拼团商品"),
    COUPON(10, "优惠券"), AMOY_PLAY_CATEGORY(11, "淘潮玩品类"), AMOY_PLAY_BRAND(12, "淘潮玩品牌"),
    AMOY_PLAY_LABEL(13, "淘潮玩标签"), AMOY_PLAY_TOP(14, "淘潮玩首页头部");


    private Integer id;
    private String name;

    @Override
    public String toString() {
        return "TcTemplateJumpType{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }

      /*
      * 查看 跳转类型是否存在
      */

    public static Boolean isExist(Integer id) {
        return id != null && queryInstance(id) != null;
    }

        /*
        * 查看 跳转类型 是否属于淘潮玩
        *
        */

    public static Boolean isTcExist(Integer id) {
        return AMOY_PLAY_BRAND.id.equals(id) || AMOY_PLAY_CATEGORY.id.equals(id) || AMOY_PLAY_LABEL.id.equals(id);
    }

    /**
     * 查找 对应跳转类型
     */
    public static JumpType queryInstance(Integer id) {
        return Arrays.stream(values())
                .filter(jumpType -> jumpType.id.equals(id))
                .findFirst().orElse(null);
    }

    JumpType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

}
