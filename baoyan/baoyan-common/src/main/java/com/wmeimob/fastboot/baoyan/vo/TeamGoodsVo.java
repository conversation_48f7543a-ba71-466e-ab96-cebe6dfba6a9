package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByTeamGoods;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TeamGoodsVo extends ByTeamGoods {

    private String tagIds;
    private String firstName;
    private String secondName;
    private String threeName;
    private Integer varietyId;
    private String varietyName;
    private String goodsName;
    private String content;
    private String goodsImg;
    private String supplierName;
    private String goodsNumber;
    private String goodsBanner;
    private Integer allTeamPerson;
    private Integer sales;
    private Integer refund;
    private BigDecimal amount;
    private BigDecimal refundAmount;
    private Integer teamId;
    private Integer saleNum;

    public static String checkParam(TeamGoodsVo teamGoodsVo){
     /*   InputValidator.checkEmpty(teamGoodsVo.getFreightId(),"运费模板");
        InputValidator.checkEmpty(teamGoodsVo.getSkuName(),"规格名称");
        if(!InputValidator.isDecimal(teamGoodsVo.getVolume().toString())){
            return "请正确填写体积";
        }
        if(!InputValidator.isDecimal(teamGoodsVo.getWeight().toString())){
            return "请正确填写重量";
        }
        InputValidator.checkEmpty(teamGoodsVo.getStockNum(),"库存");
        if(teamGoodsVo.getStockNum()< CommonFinal.ZERO){
            return "请正确填写库存";
        }
        if(!InputValidator.isDecimal(teamGoodsVo.getTeamPrice().toString())){
            return "请正确填写拼团价";
        }
        if(!InputValidator.isDecimal(teamGoodsVo.getOrgPrice().toString())){
            return"请正确填写原价";
        }
        if(!InputValidator.isNumeric(teamGoodsVo.getTeamPerson().toString())){
            return"请正确填写拼团人数,只能为整数";
        }
        if(teamGoodsVo.getTeamPerson() < CommonFinal.TWO){
            return"拼团人数最少两人";
        }
        if(!InputValidator.isNumeric(teamGoodsVo.getTeamNum().toString())){
            return"请正确填写拼团次数,只能为整数";
        }
        if(!InputValidator.isNumeric(teamGoodsVo.getTeamHour().toString())){
            return"请正确填写拼团时间,只能为整数";
        }
        if(teamGoodsVo.getContent() == null){
            return"请填写拼团商品详情";
        }*/
        return null;
    }
}
