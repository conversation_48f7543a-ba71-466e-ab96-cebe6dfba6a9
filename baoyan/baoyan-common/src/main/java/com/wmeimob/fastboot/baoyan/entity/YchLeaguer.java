package com.wmeimob.fastboot.baoyan.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Table(name = "ych_leaguer")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchLeaguer implements Serializable {

    private static final long serialVersionUID = 1L;

     /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 会员ID
     */
    private String leaguerId;

    /**
     * 会员ID
     */
    private String businessId;

    /**
     * 是否已取卡
     */
    private String isTake;

    /**
     * 会员卡号
     */
    private String leaguerCode;

    /**
     * 会员级别Id
     */
    private String leaguerLevelId;

    /**
     * 会员级别名称
     */
    private String leaguerLevelName;

    /**
     * 会员状态ID
     */
    private Integer leaguerStatus;

    /**
     * 会员状态描述
     */
    private String leaguerStatusDesc;

    /**
     * 会员姓名
     */
    private String realName;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verifyTime;

    /**
     * 开卡商户mc
     */
    private String openBusinessName;

    /**
     * 性别 男：1 女：0
     */
    private Integer sex;

    /**
     * 地址
     */
    private String address;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime birthday;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 子女姓名
     */
    private String childName;

    /**
     * 子女生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime childBirthday;

    /**
     * 性别 男：1 女：0
     */
    private Integer childSex;

    /**
     * 子女姓名
     */
    private String child2Name;

    /**
     * 子女生日
     */
    private LocalDateTime child2Birthday;

    /**
     * 性别 男：1 女：0
     */
    private Integer child2Sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime regTime;

    /**
     * 即时通号
     */
    private String imno;

    /**
     * QQ
     */
    private String qq;

    /**
     * 会员头像URL
     */
    private String photoUrl;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
    
}
