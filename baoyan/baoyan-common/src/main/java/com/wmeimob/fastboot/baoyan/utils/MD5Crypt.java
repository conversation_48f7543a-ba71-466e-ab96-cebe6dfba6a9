package com.wmeimob.fastboot.baoyan.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Random;

/**
 * Created by jim on 2016/12/12.
 */
public class MD5Crypt {

    private static final String SALTCHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    private static final String itoa64 = "./0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final char[] hexDigits = {
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * 获取token
     *
     * @return String
     */
    public static String getToken() {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmm");//设置日期格式
        Calendar nowTime = Calendar.getInstance();
        String before = df.format(nowTime.getTime());
        String s = getMD5("123456".getBytes());
        s = s + before;
        s = getMD5(s.getBytes());
        return s;
    }


    public static String getMD5(byte[] source) {
        String s = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(source);
            byte[] tmp = md.digest();

            char[] str = new char[32];

            int k = 0;
            for (int i = 0; i < 16; i++) {
                byte byte0 = tmp[i];
                str[(k++)] = hexDigits[(byte0 >>> 4 & 0xF)];

                str[(k++)] = hexDigits[(byte0 & 0xF)];
            }
            s = new String(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }

    public static final String crypt(String password) {
        StringBuffer salt = new StringBuffer();
        Random rnd = new Random();
        while (salt.length() < 8) {
            int index = (int) (rnd.nextFloat() * "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".length());
            salt.append("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".substring(index, index + 1));
        }
        return crypt(password, salt.toString(), "$1$");
    }

    public static final String crypt(String password, String salt) {
        return crypt(password, salt, "$1$");
    }

    public static final String crypt(String password, String salt, String magic) {
        MessageDigest ctx;
        MessageDigest ctx1;
        try {
            ctx = MessageDigest.getInstance("md5");
            ctx1 = MessageDigest.getInstance("md5");
        } catch (NoSuchAlgorithmException ex) {
            System.err.println(ex);
            return null;
        }
        if (salt.startsWith(magic)) {
            salt = salt.substring(magic.length());
        }
        if (salt.indexOf('$') != -1) {
            salt = salt.substring(0, salt.indexOf('$'));
        }
        if (salt.length() > 8) {
            salt = salt.substring(0, 8);
        }
        ctx.update(password.getBytes());
        ctx.update(magic.getBytes());
        ctx.update(salt.getBytes());

        ctx1.update(password.getBytes());
        ctx1.update(salt.getBytes());
        ctx1.update(password.getBytes());
        byte[] finalState = ctx1.digest();
        for (int pl = password.length(); pl > 0; pl -= 16) {
            ctx.update(finalState, 0, pl > 16 ? 16 : pl);
        }
        clearbits(finalState);
        for (int i = password.length(); i != 0; i >>>= 1) {
            if ((i & 0x1) != 0) {
                ctx.update(finalState, 0, 1);
            } else {
                ctx.update(password.getBytes(), 0, 1);
            }
        }
        finalState = ctx.digest();
        for (int i = 0; i < 1000; i++) {
            try {
                ctx1 = MessageDigest.getInstance("md5");
            } catch (NoSuchAlgorithmException e0) {
                return null;
            }
            if ((i & 0x1) != 0) {
                ctx1.update(password.getBytes());
            } else {
                ctx1.update(finalState, 0, 16);
            }
            if (i % 3 != 0) {
                ctx1.update(salt.getBytes());
            }
            if (i % 7 != 0) {
                ctx1.update(password.getBytes());
            }
            if ((i & 0x1) != 0) {
                ctx1.update(finalState, 0, 16);
            } else {
                ctx1.update(password.getBytes());
            }
            finalState = ctx1.digest();
        }
        StringBuffer result = new StringBuffer();
        result.append(magic);
        result.append(salt);
        result.append("$");

        long l = bytes2u(finalState[0]) << 16 | bytes2u(finalState[6]) << 8 | bytes2u(finalState[12]);
        result.append(to64(l, 4));

        l = bytes2u(finalState[1]) << 16 | bytes2u(finalState[7]) << 8 | bytes2u(finalState[13]);
        result.append(to64(l, 4));

        l = bytes2u(finalState[2]) << 16 | bytes2u(finalState[8]) << 8 | bytes2u(finalState[14]);
        result.append(to64(l, 4));

        l = bytes2u(finalState[3]) << 16 | bytes2u(finalState[9]) << 8 | bytes2u(finalState[15]);
        result.append(to64(l, 4));

        l = bytes2u(finalState[4]) << 16 | bytes2u(finalState[10]) << 8 | bytes2u(finalState[5]);
        result.append(to64(l, 4));

        l = bytes2u(finalState[11]);
        result.append(to64(l, 2));

        clearbits(finalState);

        return result.toString();
    }

    private static final void clearbits(byte[] bits) {
        for (int i = 0; i < bits.length; i++) {
            bits[i] = 0;
        }
    }

    private static final int bytes2u(byte inp) {
        return inp & 0xFF;
    }

    private static final String to64(long v, int size) {
        StringBuffer result = new StringBuffer();
        do {
            result.append("./0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".charAt((int) (v & 0x3F)));
            v >>>= 6;
            size--;
        } while (size >= 0);
        return result.toString();
    }

    public static String JM(String inStr) {
        String pass = null;
        try {
            pass = URLDecoder.decode(inStr, "utf-8");
        } catch (UnsupportedEncodingException e) {
            pass = inStr;
        }
        char[] a = pass.toCharArray();
        if (a.length % 3 != 0) {
            return pass;
        }
        char[] des = new char[a.length / 3];
        int i = 1;
        for (int j = 0; i < a.length; i += 3) {
            des[(j++)] = ((char) (a[i] ^ 0x74));
        }
        return new String(des);
    }

    public static String KL(String inStr, long type) {
        String s = new String(getMD5(String.valueOf(type).getBytes()));
        char[] a = inStr.toCharArray();
        char[] des = new char[a.length * 3];
        int i = 0;
        int j = 0;
        for (int k = 0; i < a.length; i++) {
            des[(j++)] = s.charAt(k++);
            des[(j++)] = ((char) (a[i] ^ 0x74));
            des[(j++)] = s.charAt(k++);
        }
        String k = new String(des);
        try {
            return URLEncoder.encode(k, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return k;
    }
}
