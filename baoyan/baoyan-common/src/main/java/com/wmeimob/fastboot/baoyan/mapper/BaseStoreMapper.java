
/*
* BaseStoreMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Wed Jul 10 11:37:09 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface BaseStoreMapper extends Mapper<BaseStore> {
	/**
	 * 根据淘潮玩商品id查询门店
	 * @param tcId
	 * @return
	 */
	List<BaseStore> findStoreByTcId(Integer tcId);

    List<BaseStore> selectList(@Param("storeName") String storeName, @Param("longitude") String longitude, @Param("latitude") String latitude, @Param("set")Set<Integer> set);

	List<Map<String, Object>> showDate(@Param("id") Integer id,@Param("date") String date);

	Integer selectCountList(@Param("date")Date apponintmentDate,@Param("id") Integer id);

	/**
	 * 查看核销码 可核销门店list
	 * @param writeOffId
	 * @return
	 */
    List<BaseStore> showWriteOffStore(@Param("writeOffId") Integer writeOffId);



    /**List<BaseStore> select(BaseStore baseStore);

	BaseStore selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseStore baseStore);

	int updateByPrimaryKeySelective(BaseStore baseStore);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseStore baseStore);*/
	
}