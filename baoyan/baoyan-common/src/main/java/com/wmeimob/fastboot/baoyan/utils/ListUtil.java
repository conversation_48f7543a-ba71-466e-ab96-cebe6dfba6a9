package com.wmeimob.fastboot.baoyan.utils;


import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/27
 */
public class ListUtil {

    /**
     * 将两个集合链接起来
     * @param list1
     * @param list2
     * @param <T>
     * @return
     */
    public static <T> List<T> concat(List<T> list1, List<T> list2){

        //集合1如果是空的，就返回集合2
        if ( CollectionUtils.isEmpty(list1) ){
            return list2;
        }

        if ( CollectionUtils.isEmpty(list2) ){
            return list1;
        }

        List<T> returnArr = new ArrayList<>(list1);
        returnArr.addAll(list2);

        //如果集合2不为空，就把集合2添加到集合1中去
        return returnArr.stream().distinct().collect(Collectors.toList());
    }

}
