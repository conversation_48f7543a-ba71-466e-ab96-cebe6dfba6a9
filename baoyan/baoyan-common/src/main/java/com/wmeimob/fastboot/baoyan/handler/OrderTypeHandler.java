package com.wmeimob.fastboot.baoyan.handler;

import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2021/7/4
 */
@MappedTypes({OrderLogType.class,})
public class OrderTypeHandler extends BaseTypeHandler<OrderLogType> {
    @Override
    public void setNonNullParameter(PreparedStatement stmt, int index, OrderLogType orderLogType, JdbcType jdbcType) throws SQLException {
        stmt.setObject(index,orderLogType.getId());
    }

    @Override
    public OrderLogType getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        int typeId = resultSet.getInt(columnName);

        return OrderLogType.getInstance(typeId);
    }

    @Override
    public OrderLogType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int typeId = resultSet.getInt(i);
        return OrderLogType.getInstance(typeId);
    }

    @Override
    public OrderLogType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int typeId = callableStatement.getInt(i);
        return OrderLogType.getInstance(typeId);
    }
}
