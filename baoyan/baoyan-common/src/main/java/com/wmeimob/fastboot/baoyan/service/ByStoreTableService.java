package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByStoreTable;
import com.wmeimob.fastboot.baoyan.qo.StoreTableQo;
import com.wmeimob.fastboot.baoyan.vo.StoreTableVo;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.List;

/**
 * 桌台管理
 */
public interface ByStoreTableService extends CommonService<ByStoreTable>{

    /**
     * 门店列表，如果有桌台则返回和桌台数量
     * @param id
     * @return
     */
    default List<StoreTableVo> queryStoreNoList(StoreTableQo storeTableQo){throw new NotImplementedException("queryStoreNoList");};

    default Boolean addByStoreTable(ByStoreTable byStoreTable) {
        throw new NotImplementedException("addByStoreTable");
    }
}
