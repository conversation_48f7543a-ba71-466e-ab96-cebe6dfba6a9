package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;
import java.util.function.Consumer;

/**
 * @ClassName ByCouponUserService
 * @Description 优惠券领取
 * <AUTHOR>
 * @Date Thu Jul 11 17:28:52 CST 2019
 * @version1.0
 **/
public interface ByCouponUserService extends CommonService<ByCouponUser>{



    /**
     * 优惠券领取查询
     * @param id
     * @return
     */
    default ByCouponUser queryByCouponUserById(Object id){throw new NotImplementedException("queryByCouponUserById");};

    /**
     * 优惠券领取添加
     * @param  byCouponUser
     * @return
     */
    default  void addByCouponUser(ByCouponUser byCouponUser){throw new NotImplementedException("addByCouponUser");};


    /**
     * 优惠券领取删除
     * @param id
     * @return
     */
    default void removeByCouponUser(Object id){throw new NotImplementedException("removeByCouponUser");};


    /**
     * 优惠券领取修改
     * @param byCouponUser
     * @return
     */
    default void modifyByCouponUser(ByCouponUser byCouponUser){throw new NotImplementedException("modifyByCouponUser");};

    /**
     * 后台发放优惠券
     * @param  couponUser
     * @return
     */
    default void addBitchCouponUser(String batch,ByCouponUser couponUser){throw new NotImplementedException("addBitchCouponUser");}

    default List<ByCouponUser> bitchRest(String batch){throw new NotImplementedException("bitchRest");}

    default int bitchDone(String batch){throw new NotImplementedException("bitchDone");}

    /**
     * 查询优惠券审核列表
     * @param byCouponUser
     * @return
     */
    default List<ByCouponUser> queryAuditListByCouponUser(ByCouponUser byCouponUser){throw new NotImplementedException("queryAuditListByCouponUser");}

    /**
     * 优惠卷审核
     * @param byCouponUser
     */
    default void openAdopt(ByCouponUser byCouponUser){throw new NotImplementedException("openAdopt");}


}
