<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseActivityMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseActivity" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
		<result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
		<result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
		<result property="target" column="target" jdbcType="VARCHAR"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="baseBanner" column="base_banner"/>
    </resultMap>

	<select  id="selectByExampleDel" resultMap="BaseResultMap">
		select * from  base_activity where status = 1 and is_del = 0 order by sort desc
	</select>
</mapper>

