package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.vo.ByGoodsInfoVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName ByGoodsInfoService
 * @Description 商品信息表
 * <AUTHOR>
 * @Date Fri Jul 12 13:41:20 CST 2019
 * @version1.0
 **/
public interface ByGoodsInfoService extends CommonService<ByGoodsInfo>{




    /**
     * 商品信息表查询
     * @param id
     * @return
     */
    default ByGoodsInfo queryByGoodsInfoById(Object id){throw new NotImplementedException("queryByGoodsInfoById");};

    /**
     * 商品信息表添加
     * @param  byGoodsInfo
     * @return
     */
    default ByGoodsInfo addByGoodsInfo(ByGoodsInfo byGoodsInfo) throws IOException {throw new NotImplementedException("addByGoodsInfo");};


    /**
     * 商品信息表删除
     * @param id
     * @return
     */
    default void removeByGoodsInfo(Integer id){throw new NotImplementedException("removeByGoodsInfo");};


    /**
     * 商品信息表修改
     * @param byGoodsInfo
     * @return
     */
    default void modifyByGoodsInfo(ByGoodsInfo byGoodsInfo) throws IOException {throw new NotImplementedException("modifyByGoodsInfo");};

    /**
     * 商品上下架
     * @param byGoodsInfo
     */
    default void updateShelf(ByGoodsInfo byGoodsInfo){throw new NotImplementedException("updateShelf");};

    /**
     * 根据 id 查询商品 详情
     * @param id
     * @return
     */
    default  ByGoodsInfoVO queryGoodDetailInfoById(Integer id) {throw new NotImplementedException("queryGoodDetailInfoById");};
    /**
     * 查看小程序二维码
     * @param id
     * @return
     */
    default  String queryCodeImg(Integer id,Boolean flag) {throw new NotImplementedException("queryCodeImg");};
}
