<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByGoodsClassifyMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="classifyId" column="classify_id" jdbcType="INTEGER"/>
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="selectClassifyGoodsContact" resultMap="BaseResultMap">
		SELECT
		 gc.*
		  FROM by_goods_classify gc
		left join  by_goods_info gi on gi.id=gc.goods_id
		where gc.id=#{id} and gi.is_del=0

	</select>


	<select id="selectClassifyIdByGoods" resultType="Integer">
		SELECT classify_id FROM by_goods_classify WHERE goods_id = #{id}
	</select>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify">
		SELECT 
			`id`,
			`classify_id`,
			`goods_id`,
			`gmt_create`,
			`gmt_modified`
		FROM  by_goods_classify
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="classifyId != null">
			  	AND `classify_id` = #{classifyId}
			</if>
			<if test="goodsId != null">
			  	AND `goods_id` = #{goodsId}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`classify_id`,
			`goods_id`,
			`gmt_create`,
			`gmt_modified`
		FROM by_goods_classify
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO by_goods_classify
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="classifyId != null">
			`classify_id`,
		</if>
		<if test="goodsId != null">
			`goods_id`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		<if test="gmtModified != null">
			`gmt_modified`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="classifyId != null">
			#{classifyId},	
		</if>
		<if test="goodsId != null">
			#{goodsId},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		<if test="gmtModified != null">
			#{gmtModified},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify">
		UPDATE by_goods_classify
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="classifyId != null">
				`classify_id` = #{classifyId},
			</if>
			<if test="goodsId != null">
				`goods_id` = #{goodsId},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
			<if test="gmtModified != null">
				`gmt_modified` = #{gmtModified},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM by_goods_classify WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify">
		DELETE FROM by_goods_classify
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="classifyId != null">
			  	AND `classify_id` = #{classifyId}
			</if>
			<if test="goodsId != null">
			  	AND `goods_id` = #{goodsId}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtModified != null">
			  	AND `gmt_modified` = #{gmtModified}
			</if>
		</where>
	</delete> -->
</mapper>

