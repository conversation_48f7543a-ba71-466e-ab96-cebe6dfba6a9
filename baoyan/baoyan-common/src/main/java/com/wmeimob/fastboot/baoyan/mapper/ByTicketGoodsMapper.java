
/*
* ByTicketGoodsMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 25 13:46:23 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByTicketGoods;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

public interface ByTicketGoodsMapper extends Mapper<ByTicketGoods> {
	/**
	 * 查询联票商品表
	 * @param byTicketGoods
	 * @return
	 */
	List<ByTicketGoods> findByCondition(ByTicketGoods byTicketGoods);

	/**
	 * id 查询联票
	 * @param id
	 * @return
	 */
    ByTicketGoods queryByTicketGoodsById(@Param("id") Integer id);

	List<ByTicketGoods> selectByExampleIsDel();

    void updateByAddStock(@Param("productId") Integer productId,@Param("productCount") Integer productCount);

    List<ByGoodsInfo> selectList(@Param("type") Integer type);

}