package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcLabel;
import java.util.List;

/**
 * (TcLabel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-20 14:08:58
 */
public interface TcLabelMapper {

    /**
     * 通过ID查询单条数据
     * @param id 主键
     * @return 实例对象
     */
    TcLabel queryById(Integer id);
    /**
     * 通过标签名查询单条数据
     */
    TcLabel queryByName(String name);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcLabel 实例对象
     * @return 对象列表
     */
    List<TcLabel> queryAll(TcLabel tcLabel);

    /**
     * 新增数据
     *
     * @param tcLabel 实例对象
     * @return 影响行数
     */
    int insert(TcLabel tcLabel);


}