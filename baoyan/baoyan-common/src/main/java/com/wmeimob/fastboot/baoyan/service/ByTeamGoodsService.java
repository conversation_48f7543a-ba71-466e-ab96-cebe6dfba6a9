package com.wmeimob.fastboot.baoyan.service;


import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.baoyan.entity.ByTeamGoods;
import com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByTeamGoodsService
 * @Description 拼团商品表
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
public interface ByTeamGoodsService extends CommonService<ByTeamGoods>{

    /**
     * 拼团商品表查询
     * @param id
     * @return
     */
    default ByTeamGoods queryByTeamGoodsById(Integer id){throw new NotImplementedException("queryByTeamGoodsById");};

    /**
     * 拼团商品表添加
     * @param  byTeamGoods
     * @return
     */
    default  void addByTeamGoods(ByTeamGoods byTeamGoods){throw new NotImplementedException("addByTeamGoods");};


    /**
     * 拼团商品表删除
     * @param id
     * @return
     */
    default void removeByTeamGoods(Object id){throw new NotImplementedException("removeByTeamGoods");};


    /**
     * 拼团商品表修改
     * @param byTeamGoods
     * @return
     */
    default void modifyByTeamGoods(ByTeamGoods byTeamGoods){throw new NotImplementedException("modifyByTeamGoods");};

    /**
     * 拼团统计
     * @param teamGoods
     * @return
     */
    default List<TeamGoodsVo> forms(ByTeamGoods teamGoods){throw new NotImplementedException("forms");}
    /**
     * 拼团商品上下架修改
     * @param byTeamGoods
     * @return
     */
    default void updateShelf(ByTeamGoods byTeamGoods){throw new NotImplementedException("updateShelf");}

    default PageInfo ticketList(Integer pageIndex, Integer pageSize){
        return null;
    };


    default ByTeamGoods ticket(Integer id){
        return null;
    };

    /**
     * 全部拼团
     * */
    default List<ByTeam> getTeam(Integer id){
        return null;
    };

    /**
     * @Description 拼团详情
     * <AUTHOR>
     * @Date        2019-08-21 14:14
     * @Version    1.0
     */
    default Map<String, Object> teamDetail(Integer id){
        return null;
    };

    /**
     * @Description 订单详情
     * <AUTHOR>
     * @Date        2019-08-21 14:59
     * @Version    1.0
     */
    default Map<String,Object> teamGoodsDetail(Integer id,Integer type){
        return null;
    };

    default Map<String, Object> pay(Integer id, Integer type, String remark, BigDecimal payAmount,String fromId,Integer couponId,BigDecimal couponAmount,BigDecimal scoreAmount,BigDecimal orderAmount){
        return null;
    };

    default PageInfo presonTeam(Integer pageIndex, Integer pageSize){
        return null;
    };

    default Map<String, Object> teamDetails(Integer id){
        return null;
    };
}
