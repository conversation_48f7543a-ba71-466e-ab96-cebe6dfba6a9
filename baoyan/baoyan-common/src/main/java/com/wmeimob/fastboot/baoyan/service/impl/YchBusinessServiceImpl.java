package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.service.YchBusinessService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wmeimob.fastboot.baoyan.entity.YchBusiness;
import com.wmeimob.fastboot.baoyan.mapper.YchBusinessMapper;
import tk.mybatis.mapper.entity.Example;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class YchBusinessServiceImpl implements YchBusinessService {
    @Autowired
    private YchBusinessMapper ychBusinessMapper;
    
    /**
     * 根据门店ID获取门店URL
     * @param businessId 门店ID
     * @return 门店URL
     */
    public String getBusinessUrl(String businessId) {
        Example example = new Example(YchBusiness.class);
        example.createCriteria().andEqualTo("businessId", businessId);
        YchBusiness business = ychBusinessMapper.selectOneByExample(example);
        if (business == null || business.getBusinessUrl() == null) {
            log.error("未找到门店URL配置，门店ID：{}", businessId);
            return "";
        }
        log.info("门店URL：{}", business.getBusinessUrl());
        return business.getBusinessUrl();
    }
    
    /**
     * 获取完整的API调用地址
     * @param businessId 门店ID
     * @param apiPath API路径
     * @return 完整的API调用地址
     */
    public String getFullApiUrl(String businessId, String apiPath) {
        String baseUrl = getBusinessUrl(businessId);
        if(StringUtils.isBlank(baseUrl)){
            return "";
        }
        return baseUrl + apiPath;
    }
}
