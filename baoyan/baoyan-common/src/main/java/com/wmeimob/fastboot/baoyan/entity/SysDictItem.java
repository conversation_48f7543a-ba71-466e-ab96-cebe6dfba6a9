package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "sys_dict_item")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SysDictItem implements Serializable,Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "dict_id")
    private Integer dictId;

    @Column(name = "item_value")
    private String itemValue;

    @Column(name = "item_name")
    private String itemName;

    @Override
    public SysDictItem clone() throws CloneNotSupportedException {
        return (SysDictItem) super.clone();
    }
}
