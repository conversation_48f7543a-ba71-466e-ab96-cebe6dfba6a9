<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCustUser" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="headImg" column="head_img" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="parentName" column="parent_name" jdbcType="VARCHAR"/>
        <result property="parentPhone" column="parent_phone" jdbcType="VARCHAR"/>
        <result property="childName" column="child_name" jdbcType="VARCHAR"/>
        <result property="childAge" column="child_age" jdbcType="INTEGER"/>
        <result property="childSex" column="child_sex" jdbcType="TINYINT"/>
        <result property="yearCouponFlag" column="year_coupon_flag" jdbcType="TINYINT"/>
        <result property="birthday" column="birthday" jdbcType="DATE"/>
        <result property="nowPoint" column="now_point" jdbcType="INTEGER"/>
        <result property="registerTime" column="register_time" jdbcType="TIMESTAMP"/>
        <result property="isDisable" column="is_disable" jdbcType="TINYINT"/>
        <result property="deleteStatus" column="delete_status" jdbcType="TINYINT"/>
        <result property="wxOpenId" column="wx_open_id" jdbcType="VARCHAR"/>
        <result property="historyPoint" column="history_point" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="bePoint" column="be_point" jdbcType="INTEGER"/>
        <result property="showRedCount" column="show_red_count" jdbcType="INTEGER"/>
    </resultMap>
    <update id="updateUserIntegralAdd">
		UPDATE by_cust_user SET history_point=history_point+${amount},now_point=now_point+${amount} WHERE id=${userId}
	</update>
    <update id="updateAddUserPoint">
		update by_cust_user
			set history_point = history_point + #{intValue},
			now_point=now_point+#{intValue}
		where id = #{id}
	</update>
    <update id="updateByPointAdd">
		update by_cust_user
			set now_point = now_point + #{intValue}
		where id = #{id}
	</update>
    <update id="updateByNotAdd">
		update by_cust_user
		set now_point = now_point - #{count}
		where id = #{id} and now_point >= #{count}
	</update>
    <update id="updateByCustUserInformation" parameterType="com.wmeimob.fastboot.baoyan.entity.ByCustUser">
        update by_cust_user
        <set>
            <if test="nickName !=null and nickName.length !=0">
                nick_name = #{nickName},
            </if>
            <if test="mobile !=null and mobile.length !=0">
                mobile = #{mobile},
            </if>
            <if test="gmtModified !=null">
                gmt_modified = #{gmtModified},
            </if>
            <if test="headImg !=null and headImg.length !=0">
                head_img = #{headImg},
            </if>
            <if test="parentName !=null and parentName.length !=0">
                parent_name = #{parentName},
            </if>
            <if test="parentPhone !=null and parentPhone.length !=0">
				parent_phone = #{parentPhone},
            </if>
			<if test="childName !=null and childName.length !=0">
				child_name = #{childName},
			</if>
			<if test="childAge !=null">
				child_age = #{childAge},
			</if>
			<if test="childSex !=null">
				child_sex = #{childSex},
			</if>
			<if test="birthday !=null">
				birthday = #{birthday},
			</if>
        </set>
        where id = #{id}
    </update>
</mapper>

