<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCouponMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCoupon" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="tempId" column="temp_id" jdbcType="INTEGER"/>
		<result property="totalLimit" column="total_limit" jdbcType="INTEGER"/>
		<result property="dayLimit" column="day_limit" jdbcType="INTEGER"/>
		<result property="total" column="total" jdbcType="INTEGER"/>
		<result property="taked" column="taked" jdbcType="INTEGER"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="state" column="state" jdbcType="TINYINT"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="targetId" column="target_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
    </resultMap>

	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCoupon" id="BaseResultMap2" extends="BaseResultMap">
		<result property="name" column="name" jdbcType="VARCHAR"/>
		<result property="discount" column="discount" jdbcType="DECIMAL"/>
		<result property="full" column="full" jdbcType="DECIMAL"/>
		<result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
		<result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
		<result property="takeId" column="takeId" jdbcType="INTEGER"/>
		<result property="targetId" column="target_id" jdbcType="INTEGER"/>
		<result property="couponType" column="coupon_type" jdbcType="INTEGER"/>
		<result property="type" column="type" jdbcType="INTEGER"/>
		<result property="registerType" column="registerType" jdbcType="INTEGER"/>
		<result property="singleGoodsType" column="single_goods_type" jdbcType="INTEGER"/>
		<result property="effectiveType" column="effective_type" jdbcType="INTEGER"/>
		<result property="dayNum" column="day_num" jdbcType="INTEGER"/>
	</resultMap>
    <select id="getCouponInfo" resultMap="BaseResultMap2">
		SELECT c.id,c.total_limit,c.day_limit,c.total,c.taked,t.effective_type,t.day_num,
		t.`name`,t.target_id,t.discount,t.`full`,t.start_date,t.end_date,t.coupon_type,t.type,IF(t.type != 0,t.single_goods_type,NULL) single_goods_type
		FROM `by_coupon` c
		LEFT JOIN by_coupon_temp t ON t.id=c.temp_id
		WHERE c.id=${id} LIMIT 1

	</select>
	<select id="getCouponList" resultMap="BaseResultMap2">
		SELECT c.id,c.temp_id,c.total_limit,c.day_limit,c.total,c.sort,c.state
		,t.`name`
		FROM by_coupon c
		LEFT JOIN by_coupon_temp t ON t.id=c.temp_id
		WHERE c.is_del=0
		<if test="name != null and name != ''">
			AND t.`name` LIKE concat('%',#{name},'%')
		</if>
		<if test="state != null">
			AND c.state = ${state} AND t.end_date >= CURRENT_DATE
		</if>
		ORDER BY c.sort ASC

	</select>
	<select id="getWechatCouponList" resultMap="BaseResultMap2">
		SELECT c.id,c.temp_id,c.total_limit,c.day_limit,c.total,c.sort,c.state,c.taked,
		<if test="userId != null">
			IF
			(c.total_limit != 0,
			c.total_limit - (SELECT count( 1 ) FROM by_coupon_user buser WHERE buser.coupon_id = c.id AND buser.user_id = ${userId}),
	 		c.day_limit - (SELECT count( 1 ) FROM by_coupon_user buser WHERE buser.coupon_id = c.id AND buser.user_id = ${userId} AND DATE_FORMAT( buser.gmt_create, '%Y-%m-%d' ) = DATE_FORMAT( #{gmtCreate}, '%Y-%m-%d' ))) as registerType,
		</if>
		t.`name`,
		t.discount,
		t.coupon_type,
		t.start_date,
		t.end_date,
		CASE t.type
		when 1 THEN CONCAT(bc.`classify_title`,'类')
		when 2 THEN(
		CASE t.single_goods_type
		when 1 THEN g.goods_name
		when 2 THEN sub.sub_card_goods_name
		when 3 THEN ti.ticket_goods_name
		END
		)
		when 5 then tcg.goods_name
		when 6 THEN '全部淘潮玩'
		when 7 THEN '全部商品'
		when 0 THEN '全部门票'
		END as limitation,
		t.type,
        t.full
		FROM by_coupon c
		LEFT JOIN by_coupon_temp t ON t.id=c.temp_id
		LEFT JOIN base_classify bc ON bc.id=t.target_id
		LEFT JOIN by_goods_info g ON g.id=t.target_id
		LEFT JOIN by_sub_card_goods  sub ON sub.id=t.target_id
		LEFT JOIN by_ticket_goods  ti ON ti.id=t.target_id
		LEFT JOIN tc_goods tcg ON tcg.id=t.target_id
		<!--<if test="userId != null">
			LEFT JOIN by_coupon_user u ON u.user_id =${userId} AND u.coupon_id=c.id
		</if>-->
		WHERE c.is_del=0 and c.state =1 AND t.end_date >= CURRENT_DATE
		ORDER BY c.sort desc
	</select>


	<select id="selectByState" resultType="com.wmeimob.fastboot.baoyan.entity.ByCoupon">
		SELECT id,state FROM by_coupon WHERE id = #{id} AND is_del = 0
	</select>



	<resultMap type="com.wmeimob.fastboot.baoyan.vo.CouponListByGoodsVo" id="couponListByGoodsMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="name" column="name" jdbcType="VARCHAR"/>
		<result property="discount" column="discount" jdbcType="DECIMAL"/>
		<result property="full" column="full" jdbcType="DECIMAL"/>
		<result property="couponType" column="coupon_type" jdbcType="INTEGER"/>
		<result property="effectiveType" column="effective_type" jdbcType="INTEGER"/>
		<result property="dayNum" column="day_num" jdbcType="INTEGER"/>
		<result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
		<result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
	</resultMap>

	<select id="couponListByGoods" resultMap="couponListByGoodsMap">
		SELECT
		c.id,ct.name,ct.discount,ct.full,ct.coupon_type,ct.effective_type,ct.day_num,ct.start_date,ct.end_date
		FROM
		by_coupon c
		LEFT JOIN by_coupon_temp ct ON c.temp_id = ct.id
		WHERE
		c.is_del = 0
		AND c.state = 1
		AND c.taked &lt; c.total
	    AND ct.is_del = 0
		AND IF (ct.effective_type = 1,IF(DATE_FORMAT(NOW(), '%Y-%m-%d') &lt; ct.end_date,TRUE,FALSE) ,TRUE )
		AND IF (ct.type = 1,IF(
		<if test="list != null">
			ct.target_id IN (
			<foreach collection="list" item="item" separator=",">
				#{item}
			</foreach>)
		</if>
		<if test="list == null">
			FALSE
		</if>
		,TRUE,FALSE) ,TRUE )
		AND IF(ct.type = 2,IF(ct.target_id = #{goodsId} AND ct.single_goods_type = #{type},TRUE,FALSE),TRUE )
	</select>


</mapper>

