package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.CartIds;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/21
 */
public interface TcGoodsShopingService {

    /**
     * 添加到购物车
     * @return
     */
    boolean addCart(TcGoodsShoping tcShoping);

    /**
     * 展示用户的淘潮玩购物车
     * @return
     */
    List<TcGoodsShoping> show();

    /**
     * 根据id删除购物车
     * @param cartDelete
     * @return
     */
    boolean deleteByIds(CartIds cartDelete);

    /**
     * 根据购物车id查询商品
     * 如果是商品详情直接付款，会创建一个临时购物车对象
     * 放心使用这个方法
     * @param tcGoods
     * @return
     */
    List<TcGoodsShoping> findGoodsByCart(Integer userId, List<TcGoodsShoping> tcGoods, boolean justAloneTc);
}
