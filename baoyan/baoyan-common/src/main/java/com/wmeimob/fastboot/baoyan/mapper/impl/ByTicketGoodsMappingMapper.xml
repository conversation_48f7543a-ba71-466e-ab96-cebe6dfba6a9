<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMappingMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTicketGoodsMapping" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="ticketGoodsId" column="ticket_goods_id" jdbcType="INTEGER"/>
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

