package com.wmeimob.fastboot.baoyan.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 上下架状态
 */
public enum Status {
    /**
     * 下架
     */
    off(0),
    /**
     * 上架
     */
    on(1),

    /**
     * 上架隐藏
     *
     */
    hide(2),
    /**
     * 3 定时上架
     */
    on_time(3);

    Status(Integer code) {
        this.code = code;
    }

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public static List<Integer> getStatus(){
        List<Integer> list = new ArrayList<>();
        list.add(off.getCode());
        list.add(on.getCode());
        list.add(hide.getCode());
        list.add(on_time.getCode());
        return list;
    }


}
