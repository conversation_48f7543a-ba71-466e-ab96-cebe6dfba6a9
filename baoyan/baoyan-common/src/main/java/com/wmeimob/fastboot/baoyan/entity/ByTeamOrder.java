/*
 * ByTeamOrder.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 16 15:59:56 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_team_order")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByTeamOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 拼团id
     */
    @Column(name = "team_id")
    private Integer teamId;
    /**
     * 拼团商品id
     */
    @Column(name = "team_goods_id")
    private Integer teamGoodsId;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 定单编号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 支付方式;1:微信
     */
    @Column(name = "pay_type")
    private Integer payType;
    /**
     * 拼团金额
     */
    @Column(name = "order_amount")
    private BigDecimal orderAmount;
    /**
     * 实付金额
     */
    @Column(name = "pay_amount")
    private BigDecimal payAmount;
    /**
     * 数量
     */
    @Column(name = "num")
    private Integer num;
    /**
     * 订单备注
     */
    @Column(name = "order_remark")
    private String orderRemark;
    /**
     * 订单状态;-2取消,-1:定单未支付(作废),0:待发货,1:待收货,2,待评价,3:订单完成,4:订单关闭
     */
    @Column(name = "order_status")
    private Integer orderStatus;
    /**
     * 是否是团长;1:是,0:否
     */
    @Column(name = "is_team")
    private Boolean isTeam;
    /**
     * 是否为售后订单;1:是,0:否
     */
    @Column(name = "is_after")
    private Boolean isAfter;
    /**
     * 支付时间
     */
    @Column(name = "pay_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;
    /**
     * 取消时间
     */
    @Column(name = "cancel_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;
    /**
     * 下单时间
     */
    @Column(name = "order_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderTime;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 订单关闭类型 1.取消订单/订单超时 2.全部退款
     */
    @Column(name = "order_close_type")
    private Integer orderCloseType;

    /**
     * 是否删除;1:是,0:否
     */
    @Column(name = "is_del")
    private Boolean isDel;

    /**
     * 积分
     */
    @Column(name = "point")
    private Integer point;
    /**
     * 积分抵扣金额
     */
    @Column(name = "integral_amount")
    private BigDecimal integralAmount;
    /**
     * 优惠券抵扣金额
     */
    @Column(name = "coupon_amount")
    private BigDecimal couponAmount;
    /**
     * 支付流水号
     */
    @Column(name = "pay_flow_no")
    private String payFlowNo;
    @Transient
    private ByTeam byTeam;

    @Column(name = "order_count")
    private Integer orderCount;
    @Column(name = "form_id")
    private String fromId;
    @Column(name = "user_name")
    private String userName;
    @Column(name = "user_img")
    private String userImg;
    @Column(name = "coupon_id")
    private Integer couponId;
    @Column(name = "order_state")
    private Integer orderState;
    /**
     * 使用完成时间
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date useTime;

}