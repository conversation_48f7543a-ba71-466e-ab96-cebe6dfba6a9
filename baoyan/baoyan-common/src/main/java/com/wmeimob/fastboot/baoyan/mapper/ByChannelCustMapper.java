package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByChannelCust;
import com.wmeimob.fastboot.baoyan.vo.ChannelCustWriteOffTotalVo;
import com.wmeimob.fastboot.core.orm.Mapper;

import java.util.List;

public interface ByChannelCustMapper extends Mapper<ByChannelCust> {

    /**
     * 查询渠道核销明细
     * @param byChannelCust
     * @return
     */
    List<ChannelCustWriteOffTotalVo> exportChannelCustTotal(ByChannelCust byChannelCust);
}