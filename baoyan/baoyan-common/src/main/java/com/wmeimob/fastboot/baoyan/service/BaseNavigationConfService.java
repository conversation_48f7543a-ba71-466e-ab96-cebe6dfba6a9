package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseNavigationConfService
 * @Description 导航设置
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
public interface BaseNavigationConfService extends CommonService<BaseNavigationConf>{

    /**
     * 导航设置查询
     * @param id
     * @return
     */
    default BaseNavigationConf queryBaseNavigationConfById(Object id){throw new NotImplementedException("queryBaseNavigationConfById");};

    /**
     * 导航设置添加
     * @param  baseNavigationConf
     * @return
     */
    default  void addBaseNavigationConf(BaseNavigationConf baseNavigationConf){throw new NotImplementedException("addBaseNavigationConf");};


    /**
     * 导航设置删除
     * @param id
     * @return
     */
    default void removeBaseNavigationConf(Object id){throw new NotImplementedException("removeBaseNavigationConf");};


    /**
     * 导航设置修改
     * @param baseNavigationConf
     * @return
     */
    default void modifyBaseNavigationConf(BaseNavigationConf baseNavigationConf){throw new NotImplementedException("modifyBaseNavigationConf");};

    /**
     * 上下架修改
     * @param baseNavigationConf
     */
    default void updateShelf(BaseNavigationConf baseNavigationConf){
        throw new NotImplementedException("updateShelf");};

    /**
     * 查询导航
     * @param isHome 1 首页  0 分类
     */
    default List<BaseNavigationConf> wxQueryAll(Boolean isHome){
        throw new NotImplementedException("wxQueryAll");
    }

}
