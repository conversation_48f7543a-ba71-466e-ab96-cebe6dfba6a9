
/*
* WriteOffCodeLogMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 23 13:40:35 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WriteOffCodeLogMapper extends Mapper<WriteOffCodeLog> {
	/**
	 * 核销记录列表
	 * @param writeOffCodeLog
	 * @return
	 */
    List<WriteOffCodeLog> findByCondition(WriteOffCodeLog writeOffCodeLog);

	
}