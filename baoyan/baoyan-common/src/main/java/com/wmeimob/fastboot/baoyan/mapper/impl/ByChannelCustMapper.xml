<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByChannelCustMapper">
    <select id="exportChannelCustTotal" resultType="com.wmeimob.fastboot.baoyan.vo.ChannelCustWriteOffTotalVo">
        select b.name 'storeName',
               case when a.channel_source =0 then '大众点评'
                    when a.channel_source =1 then '麦淘亲子'
                    when a.channel_source =2 then '抖音'
                    when a.channel_source =3 then '闲鱼' end
                      'channelSourceName',
               d.goods_name 'goodsName',
               d.end_date 'endDate',
               f.name 'writeOffStoreName',
               sum(case when d.status = 0 then 1 else 0 end) 'unWriteOffCount',
               sum(case when d.status = 1 then 1 else 0 end) 'writeOffCount'
        from by_channel_cust as a
                 join base_store as b on a.store_id = b.id
                 join by_orders as c on a.order_id = c.id
                 left join write_off_code as d on c.order_no = d.order_no
                 left join write_off_code_log as e on d.id = e.write_off_id
                 left join base_store as f on e.store_id = f.id
        where a.is_generate_order = 1 and b.name is not null and goods_name is not null
        <if test="mobile != null and mobile != ''">
            and a.mobile = #{mobile}
        </if>
        <if test="storeId != null and storeId != ''">
            and a.store_id = #{storeId}
        </if>
        <if test="goodsId != null and goodsId != ''">
            and a.goods_id = #{goodsId}
        </if>
        <if test="channelSource != null and channelSource != ''">
            and a.channel_source = #{channelSource}
        </if>
        group by a.store_id,a.channel_source,d.goods_name,d.end_date,e.store_id
    </select>
</mapper>