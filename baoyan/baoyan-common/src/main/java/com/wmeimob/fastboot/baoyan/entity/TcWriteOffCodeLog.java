package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 核销码记录表(TcWriteOffCodeLog)实体类
 * <AUTHOR>
 * @since 2021-08-11 15:25:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TcWriteOffCodeLog implements Serializable {
    private static final long serialVersionUID = 383151304965115876L;

    private Integer id;
    /**
     * 核销卡id
     */
    private Integer writeOffId;
    /**
     * 核销门店
     */
    private Integer storeId;
    /**
     * 核销门店名
     */
    private String storeName;
    /**
     * 核销员工
     */
    private Integer staffId;
    /**
     * 核销员工姓名
     */
    @Transient
    private String staffName;
    /**
     * 核销时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date writeOffDate;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 用户id
     */
    private Integer custUserId;
    /**
     * 核销次数
     */
    private Integer writeOffNum;
    /**
     * 商品核销码名称
     */
    @Transient
    private String writeOffName;
    /**
     * 订单号
     */
    @Transient
    private String orderNo;
    @Transient
    private String nickName;
    /**
     * 查询时的开始时间
     */
    @Transient
    private String startTime;
    /**
     * 查询时的结束时间
     */
    @Transient
    private String endTime;
    /**
     * 查询时的订单号，手机号
     */
    @Transient
    private String searchName;

}