package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/21
 */
public interface TcGoodsShopingMapper extends Mapper<TcGoodsShoping> {


    /**
     * 查找用户有没有把对应商品添加到购物车
     * @param userId
     * @param goodsId
     * @return
     */
    List<TcGoodsShoping> findGoodsShoping(@Param("userId") Integer userId, @Param("goodsId") Integer goodsId);

    /**
     * 查找用户有没有把对应商品添加到购物车
     * @param userId
     * @param ids
     * @return
     */
    List<TcGoodsShoping> findGoodsByIds(@Param("userId") Integer userId, @Param("ids") List<Integer> ids);

    /**
     * 给用户购物车添加数量
     * @param cartId 购物车id
     * @param addCount 改变的数量
     * @return
     */
    int addCartCount(@Param("cartId") Integer cartId, @Param("addCount") Integer addCount);

    /**
     * 查看用户的购物车
     * @param userId
     * @return
     */
    List<TcGoodsShoping> findByUserId(Integer userId);

    /**
     * 删除用户购物车里面的商品
     * @param id
     * @param ids
     * @return
     */
    int deleteByIds(@Param("userId") Integer id, @Param("ids") List<Integer> ids);

    /**
     * 查询用户淘潮玩购物车商品数量
     * @param userId
     * @return
     */
    Integer selectshopCount(Integer userId);
}
