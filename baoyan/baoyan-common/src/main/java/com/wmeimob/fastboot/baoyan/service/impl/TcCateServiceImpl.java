package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcCate;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.mapper.TcCateMapper;
import com.wmeimob.fastboot.baoyan.service.TcCateService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 淘潮玩品类表(TcCate)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Service("tcCateService")
public class TcCateServiceImpl implements TcCateService {
    @Resource
    private TcCateMapper tcCateDao;

    @Resource
    private TcGoodsService tcGoodsService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcCate queryById(Integer id) {
        if (null == id) return null;
        return this.tcCateDao.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param tcCate 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcCate tcCate) {
        if (null == tcCate || null == tcCate.getName()) throw new CustomException("参数不对");
        return this.tcCateDao.insert(tcCate) > 0;
    }

    /**
     * 修改数据
     *
     * @param tcCate 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcCate tcCate) {
        if (null == tcCate || null == queryById(tcCate.getId())) throw new CustomException("参数不对");
        return this.tcCateDao.update(tcCate) > 0;
    }

    @Override
    public boolean deleteById(Integer id) {
        if (id == null) throw new CustomException("参数不对");
        TcGoods tcGoods = new TcGoods();
        tcGoods.setCateId(id);
        if (!(tcGoodsService.queryPage(tcGoods).isEmpty())){
            throw new CustomException("商品品类有引用 不支持删除");
        }
        return tcCateDao.deleteById(id) > 0;
    }

    @Override
    public List<TcCate> queryPage(TcCate queryCate) {
        return this.tcCateDao.queryAll(queryCate);
    }


}