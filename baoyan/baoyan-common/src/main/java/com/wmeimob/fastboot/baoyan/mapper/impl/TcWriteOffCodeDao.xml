<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode" id="tcWriteOffCodeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="writeOffName" column="write_off_name" jdbcType="VARCHAR"/>
        <result property="custUserId" column="cust_user_id" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="surplusNum" column="surplus_num" jdbcType="INTEGER"/>
        <result property="totalNum" column="total_num" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="detailId" column="detail_id" jdbcType="INTEGER"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
    </resultMap>

    <update id="updateRefund">
        UPDATE tc_write_off_code
        SET `status` = 3
        WHERE detail_id = #{detailId}
        ORDER BY surplus_num
        DESC LIMIT #{hopeRefund}
    </update>

    <select id="findById"  resultMap="tcWriteOffCodeMap">
        SELECT
            detail.`status` orderStatus, writeOff.id, writeOff.write_off_name, writeOff.cust_user_id,
            writeOff.goods_id, writeOff.order_no, writeOff.surplus_num, writeOff.total_num, writeOff.status,
            writeOff.gmt_create, writeOff.gmt_modified, writeOff.detail_id, writeOff.code,
            USER.nick_name, USER.mobile, detail.tc_goods_img goodsImg
        FROM tc_write_off_code writeOff
        LEFT JOIN by_cust_user USER
        ON writeOff.cust_user_id = USER.id
        LEFT JOIN tc_order_goods detail
        ON detail.`id` = writeOff.`detail_id`
        where writeOff.id = #{id}
    </select>

    <select id="findByOrderNo" resultMap="tcWriteOffCodeMap">
        SELECT
        detail.`status` orderStatus,writeOff.id, writeOff.write_off_name, writeOff.cust_user_id,
        writeOff.goods_id, writeOff.order_no, writeOff.surplus_num, writeOff.total_num, writeOff.status,
        writeOff.gmt_create, writeOff.gmt_modified, writeOff.detail_id, writeOff.code
        FROM tc_write_off_code writeOff
        LEFT JOIN tc_order_goods detail
        ON detail.`id` = writeOff.`detail_id`
        where detail.`status` IN (2)
        and writeOff.order_no = #{orderNo}
    </select>

    <select id="findByCondition" resultMap="tcWriteOffCodeMap">
        SELECT
        detail.`status` orderStatus,writeOff.id, writeOff.write_off_name, writeOff.cust_user_id,
        writeOff.goods_id, writeOff.order_no, writeOff.surplus_num, writeOff.total_num, writeOff.status,
        writeOff.gmt_create, writeOff.gmt_modified, writeOff.detail_id, writeOff.code,
        USER.nick_name, USER.mobile
        FROM tc_write_off_code writeOff
        LEFT JOIN by_cust_user USER
        ON writeOff.cust_user_id = USER.id
        LEFT JOIN tc_order_goods detail
        ON detail.`id` = writeOff.`detail_id`
        where detail.`status` IN (2, 4, 5)
        <if test="searchName!=null and searchName!=''">
            and
            (
            USER.id = #{searchName}
            or
            writeOff.write_off_name like concat('%', #{searchName} ,'%')
            or
            USER.mobile = #{searchName}
            )
        </if>
        ORDER BY writeOff.id DESC

    </select>
</mapper>