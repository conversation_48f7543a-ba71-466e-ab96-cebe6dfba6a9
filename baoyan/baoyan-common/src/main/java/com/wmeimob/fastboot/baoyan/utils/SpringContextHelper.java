package com.wmeimob.fastboot.baoyan.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * spring工具类
 *
 * <AUTHOR>
 * @date 2020/10/31 8:43 AM
 */
@Component
public class SpringContextHelper implements ApplicationContextAware {

    /**
     * spring上下文对象
     */
    private static ApplicationContext ctx;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        //注入
        ctx=applicationContext;

    }

    public static<T> T getBean(Class<T> c){
        return ctx.getBean(c);
    }

    public static List<Object> getBeansWithAnnotation(Class<? extends Annotation> clazz){
        Map<String, Object> beans = ctx.getBeansWithAnnotation(clazz);
        List<Object> list = new ArrayList<>();
        for (String name : beans.keySet()) {
            list.add( beans.get(name) );
        }
        return list;
    }

    public static <T> List<T> getBeansByType(Class<T> c){
        //获取指定类型的全部bean
        Map<String, Object> beansMap = ctx.getBeansOfType((Class<Object>) c);
        //返回的集合
        ArrayList<T> list = new ArrayList<>();
        for (String name : beansMap.keySet()) {
            list.add( c.cast(beansMap.get(name)) );
        }

        return list;
    }

    /**
     *
     * @param beanId
     * @return
     */
    public static Object getBeanById(String beanId){
        return ctx.getBean(beanId);
    }
}
