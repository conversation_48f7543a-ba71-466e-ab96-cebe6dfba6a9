package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcOrderAfter;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 淘潮玩订单售后(TcOrderAfter)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:32
 */
public interface TcOrderAfterMapper extends Mapper<TcOrderAfter> {


    /**
     * 根据条件查询售后
     * @param tcOrderAfter
     * @return
     */
    List<TcOrderAfter> findAll(TcOrderAfter tcOrderAfter);

    /**
     * 根据条件查询售后
     * @param tcOrderAfter
     * @return
     */
    List<TcOrderAfter> findDetail(TcOrderAfter tcOrderAfter);


    /**
     * 根据id查询售后单
     * @param id
     * @return
     */
    TcOrderAfter findDetailById(Integer id);
}