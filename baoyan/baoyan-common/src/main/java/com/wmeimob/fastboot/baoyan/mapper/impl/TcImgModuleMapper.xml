<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcImgModuleMapper">

    <resultMap id="BaseResultMap" type="com.wmeimob.fastboot.baoyan.entity.TcImgModule">
        <!--@Table tc_img_module-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="isDel" column="is_del" jdbcType="TINYINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="layout" column="layout" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="tcImgModule-query">
         id, img_url, jump_type, target, is_del, status,layout,sort
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
        <include  refid="tcImgModule-query"/>
        from tc_img_module
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
        <include refid="tcImgModule-query"/>
        from tc_img_module where is_del = 0
            <if test="jumpType != null">
                and jump_type = #{jumpType,jdbcType=INTEGER}
            </if>
            <if test="layout != null">
                and layout = #{layout,jdbcType=BOOLEAN}
            </if>
        order by status desc ,id desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tc_img_module(img_url, layout,jump_type, target, status,sort)
        values (#{imgUrl}, #{layout},#{jumpType}, #{target}, #{status},#{sort})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tc_img_module
        <set>
            <if test="imgUrl != null and imgUrl != ''">
                img_url = #{imgUrl},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType,jdbcType=INTEGER},
            </if>
            <if test="layout != null">
                layout = #{layout,jdbcType=BOOLEAN},
            </if>
            <if test="target != null and target != ''">
                target = #{target},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER}
            </if>
        </set>
        where is_del = 0 and id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        update tc_img_module set is_del =1 where is_del = 0 and id = #{id}
    </delete>
    <select id="wxQueryALL" resultMap="BaseResultMap">
        select
        <include refid="tcImgModule-query"/>
        from tc_img_module where is_del = 0 and status = 1
         order by sort asc
    </select>

</mapper>