package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.mapper.TcBrandMapper;
import com.wmeimob.fastboot.baoyan.service.TcBrandService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;


/**
 * 淘潮玩品牌表(TcBrand)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */

@Service("tcBrandService")
public class TcBrandServiceImpl implements TcBrandService {
    @Resource
    private TcBrandMapper tcBrandMapper;
    @Resource
    private TcGoodsService tcGoodsService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcBrand queryById(Integer id) {
        if (null == id) return null;
        return this.tcBrandMapper.queryById(id);
    }

    /**
     * 新增数据
     *
     * @param tcBrand 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcBrand tcBrand) {
        if (null == tcBrand || null == tcBrand.getName()) throw new CustomException("参数不对");
        return this.tcBrandMapper.insert(tcBrand) > 0;
    }


    /**
     * 修改数据
     *
     * @param tcBrand 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcBrand tcBrand) {
        if (null == tcBrand || null == queryById(tcBrand.getId())) throw new CustomException("参数不对");
        return this.tcBrandMapper.update(tcBrand) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        TcGoods goods = new TcGoods();
        goods.setBrandId(id);
        List<TcGoods> tcGoods = tcGoodsService.queryPage(goods);
        if (!tcGoods.isEmpty())
        {
            throw new CustomException("商品品牌有引用 不支持删除");
        }
        return this.tcBrandMapper.deleteById(id) > 0;
    }

    /**
     * 条件查询
     *
     * @param tcBrand 条件对象
     * @return
     */
    @Override
    public List<TcBrand> queryPage(TcBrand tcBrand) {
        return tcBrandMapper.queryAll(tcBrand);
    }

}