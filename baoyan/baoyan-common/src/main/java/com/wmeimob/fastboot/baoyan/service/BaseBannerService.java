package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseBanner;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseBannerService
 * @Description Banner
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
public interface BaseBannerService extends CommonService<BaseBanner>{

    /**
     * Banner查询
     * @param id
     * @return
     */
    default BaseBanner queryBaseBannerById(Object id){throw new NotImplementedException("queryBaseBannerById");};

    /**
     * Banner添加
     * @param  baseBanner
     * @return
     */
    default  void addBaseBanner(BaseBanner baseBanner){throw new NotImplementedException("addBaseBanner");};


    /**
     * Banner删除
     * @param id
     * @return
     */
    default void removeBaseBanner(Object id){throw new NotImplementedException("removeBaseBanner");};


    /**
     * Banner修改
     * @param baseBanner
     * @return
     */
    default void modifyBaseBanner(BaseBanner baseBanner){throw new NotImplementedException("modifyBaseBanner");};
}
