package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseBannerType;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseBannerTypeService
 * @Description Banner跳转类型表
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
public interface BaseBannerTypeService extends CommonService<BaseBannerType>{

    /**
     * Banner跳转类型表查询
     * @param id
     * @return
     */
    default BaseBannerType queryBaseBannerTypeById(Object id){throw new NotImplementedException("queryBaseBannerTypeById");};

    /**
     * Banner跳转类型表添加
     * @param  baseBannerType
     * @return
     */
    default  void addBaseBannerType(BaseBannerType baseBannerType){throw new NotImplementedException("addBaseBannerType");};


    /**
     * Banner跳转类型表删除
     * @param id
     * @return
     */
    default void removeBaseBannerType(Object id){throw new NotImplementedException("removeBaseBannerType");};


    /**
     * Banner跳转类型表修改
     * @param baseBannerType
     * @return
     */
    default void modifyBaseBannerType(BaseBannerType baseBannerType){throw new NotImplementedException("modifyBaseBannerType");};
}
