package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 淘潮玩核销码表(TcWriteOffCode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-08-11 15:25:01
 */
public interface TcWriteOffCodeMapper extends Mapper<TcWriteOffCode> {


    /**
     * 根据订单号查询核销码
     * @param orderNo
     * @return
     */
    List<TcWriteOffCode> findByOrderNo(String orderNo);

    /**
     * 根据条件查询核销码
     * @param writeOffCode
     * @return
     */
    List<TcWriteOffCode> findByCondition(TcWriteOffCode writeOffCode);

    /**
     * 根据id查询核销码状态
     * @param id
     * @return
     */
    TcWriteOffCode findById(Integer id);

    /**
     * 将hopeRefund 个核销码 更新为已退款状态
     * @param detailId
     * @param hopeRefund
     * @return
     */
    int updateRefund(@Param("detailId") Integer detailId, @Param("hopeRefund") Integer hopeRefund);
}