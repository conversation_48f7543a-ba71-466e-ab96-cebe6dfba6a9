package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import java.util.List;

/**
 * 淘潮玩品牌表(TcBrand)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */

public interface TcBrandService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcBrand queryById(Integer id);

    /**
     * 新增数据
     *
     * @param tcBrand 实例对象
     * @return 实例对象
     */
    Boolean insert(TcBrand tcBrand);

    /**
     * 修改数据
     *
     * @param tcBrand 实例对象
     * @return 实例对象
     */
    Boolean update(TcBrand tcBrand);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);


    List<TcBrand> queryPage(TcBrand tcBrand);

}