<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcGoodsShopingMapper">

    <resultMap id="goodsShopingMap" type="com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping">
        <id column="id" property="id"/>
        <result column="goods_id" property="goodsId"/>
        <result column="user_id" property="userId"/>
        <result column="goods_count" property="goodsCount"/>
        <result column="is_del" property="isDel"/>
        <result column="gmt_create" property="gmtCreate"/>
    </resultMap>

    <!--  查询购物车带有商品信息  -->
    <resultMap id="cartGoodsMap" type="com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping" extends="goodsShopingMap">
        <association property="tcGoods" column="goods_id" select="com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper.queryById"/>
    </resultMap>


    <select id="findGoodsByIds" resultMap="cartGoodsMap">
        select * from tc_goods_shoping
        where user_id = #{userId}
        and is_del = 0
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

	<select id="findGoodsShoping" resultMap="goodsShopingMap">
        select * from tc_goods_shoping
        where user_id = #{userId}
        and goods_id = #{goodsId}
        and is_del = 0
    </select>

    <insert id="addCartCount">
        update tc_goods_shoping set goods_count = goods_count+#{addCount} where id = #{cartId}
    </insert>

    <select id="findByUserId" resultMap="cartGoodsMap">
        select * from tc_goods_shoping
        where user_id = #{userId}
        and is_del = 0
    </select>

    <update id="deleteByIds">
        update tc_goods_shoping set is_del = 1
        where user_id = #{userId}
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectshopCount" resultType="integer">
        select ifnull(sum(goods_count),0) goodsCount
        from tc_goods_shoping
        where user_id = #{userId}
        and is_del = 0
    </select>

</mapper>

