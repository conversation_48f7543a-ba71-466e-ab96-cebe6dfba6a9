/*
 * ByTeamGoods.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 16 15:59:56 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_team_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByTeamGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 排序值
     */
    @Column(name = "sort_num")
    private Integer sortNum;
    /**
     * 库存
     */
    @Column(name = "stock_num")
    private Integer stockNum;
    /**
     * 拼团价
     */
    @Column(name = "team_price")
    private BigDecimal teamPrice;
    /**
     * 原价
     */
    @Column(name = "org_price")
    private BigDecimal orgPrice;
    /**
     * 拼团人数
     */
    @Column(name = "team_person")
    private Integer teamPerson;
    /**
     * 拼团次数
     */
    @Column(name = "team_num")
    private Integer teamNum;
    /**
     * 拼团时间
     */
    @Column(name = "team_hour")
    private Integer teamHour;
    /**
     * 状态;:0:待审核,1:通过,2:未通过.
     */
    @Column(name = "team_status")
    private Boolean teamStatus;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 是否删除;1:是,0:否
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 富文本id
     */
    @Column(name = "rich_id")
    private Integer richId;
    /**
     * 拒绝原因
     */
    @Column(name = "reason")
    private String reason;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 商品banner图片
     */
    @Column(name = "goods_banner")
    private String goodsBanner;
    /**
     * 自动拼团成功时间/小时'
     */
    @Column(name = "auto_hour")
    private Integer autoHour;
    /**
     * 拼团名称
     */
    @Column(name = "team_name")
    private String teamName;

    @Transient
    private String searchName;
    @Transient
    private String richContent;

    @Transient
    private  List<ByTeam> byTeamOrders;

    @Transient
    private  List<ByEvaluate> byEvaluates;

    @Transient
    private Integer tickCount;

    @Transient
    private Integer byEvaluatesCount;
}