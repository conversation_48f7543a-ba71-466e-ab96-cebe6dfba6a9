package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: OrderStatisticsVO
 * @projectName baoyan
 * @description:
 * @date 2019/8/20 14:19
 */
@Data
public class OrderStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商品编号
     */
    @Transient
    private String goodsNo;
    /**
     * 商品名称
     */
    @Transient
    private String goodsName;
    /**
     * 商品分类
     */
    @Transient
    private String classifyName;
    /**
     * 适用门店
     */
    @Transient
    private String storeName;
    /**
     * 单价
     */
    @Transient
    private BigDecimal goodsPrice;
    /**
     * 出售数量
     */
    @Transient
    private Integer salesNum;
    /**
     * 退款数量
     */
    @Transient
    private Integer refundNum;
    /**
     * 应收金额
     */
    @Transient
    private BigDecimal amount;
    /**
     * 优惠券抵扣
     */
    @Transient
    private BigDecimal couponAmount;
    /**
     * 积分抵扣
     */
    @Transient
    private BigDecimal integralAmount;
    /**
     * 实收金额
     */
    @Transient
    private BigDecimal actualAmount;
}
