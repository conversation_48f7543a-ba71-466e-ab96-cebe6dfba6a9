package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 淘潮玩订单表(TcOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:34
 */
public interface TcOrderMapper extends Mapper<TcOrder> {


    /**
     * 查询用户订单数量
     * @param userId
     * @param state
     * @return
     */
    Integer getCountByUserIdAndState(@Param("userId") Integer userId, @Param("state") Integer state);

     /**
     * 根据 支付订单号查询订单
     * @param parentNo
     * @return
     */
    TcOrder findByPayOrderNo(String parentNo);

    /**
     * 根据 淘潮玩订单编号查询订单
     * @param orderNo
     * @return
     */
    TcOrder findByOrderNo(String orderNo);

    /**
     * 根据实体类作为条件查询
     * @param tcOrder
     * @return
     */
    List<TcOrder> selectByCondition(TcOrder tcOrder);

    /**
     * 根据id 查询订单信息，带有订单商品信息
     * @param id
     * @return
     */
    TcOrder findById(Integer id);

    /**
     * 查找用户的淘潮玩订单
     * @param tcOrder
     * @return
     */
    List<TcOrder> selectUserOrder(TcOrder tcOrder);
}