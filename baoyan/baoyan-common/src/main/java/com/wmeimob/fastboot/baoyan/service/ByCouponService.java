package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByCouponService
 * @Description 优惠券表
 * <AUTHOR>
 * @Date Tue Jul 09 13:58:11 CST 2019
 * @version1.0
 **/
public interface ByCouponService extends CommonService<ByCoupon>{

    /**
     * 优惠券表查询
     * @param id
     * @return
     */
    default ByCoupon queryByCouponById(Object id){throw new NotImplementedException("queryByCouponById");};


    /**
     * 优惠券表删除
     * @param id
     * @return
     */
    default void removeByCoupon(Integer id){throw new NotImplementedException("removeByCoupon");}



    /**
     * 优惠券添加
     * @param  coupon
     * @return
     */
    default void addCoupon(ByCoupon coupon){throw new NotImplementedException("addCoupon");}


    /**
     * 优惠券修改
     * @param coupon
     * @return
     */
    default void modifyCoupon(ByCoupon coupon){throw new NotImplementedException("modifyCoupon");};

    default void updateByPrimaryKeyeSlective(ByCoupon coupon){throw new NotImplementedException("updateByPrimaryKeySelective");};
}
