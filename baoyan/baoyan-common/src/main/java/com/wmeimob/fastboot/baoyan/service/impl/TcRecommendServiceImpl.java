package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.mapper.TcRecommendMapper;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcRecommendService;
import com.wmeimob.fastboot.baoyan.service.TcTemplateService;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (TcRecommend)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-20 19:01:00
 */
@Service("tcRecommendService")
@SuppressWarnings("all")
@Slf4j
public class TcRecommendServiceImpl implements TcRecommendService {
    @Resource
    private TcRecommendMapper tcRecommendDao;

    @Resource
    private TcTemplateService tcTemplateService;
    @Resource
    private TcGoodsService tcGoodsService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcRecommend queryById(Integer id) {
        if (null == id) return null;
        return this.tcRecommendDao.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param tcRecommend 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(TcRecommend tcRecommend) {
        verifyTcRecommend(tcRecommend);
        tcRecommend.setGmtCreate(new Date(System.currentTimeMillis()));
        return this.tcRecommendDao.insert(tcRecommend) > 0;
    }

    private void verifyTcRecommend(TcRecommend tcRecommend) {
        TcTemplate template = tcTemplateService.queryById(tcRecommend.getTemplateId());
        if (null == template) throw new CustomException("模版已经过期");
        if (tcRecommend == null
                || tcRecommend.getJumpType() == null
                || JumpType.AMOY_PLAY_TOP.getId().equals(tcRecommend.getJumpType())
                || tcRecommend.getTemplateId() == null
                || JumpType.AMOY_PLAY_TOP.getId().equals(template.getJumpType())
                || StringUtils.isBlank(tcRecommend.getText())
                || tcRecommend.getSort() == null
        )
            throw new CustomException("参数错误");

        if (JumpType.AMOY_PLAY_CATEGORY.getId().equals(tcRecommend.getJumpType())) {
            if (null == tcRecommend.getTcFlag()) throw new CustomException("未选择淘潮玩 商品 类别");
            if (null == tcRecommend.getBrandId()
                    && null == tcRecommend.getCateId()
                    && StringUtils.isBlank(tcRecommend.getLabelId())
                    && null == tcGoodsService.queryById(tcRecommend.getGoodsId())
            ) throw new CustomException("未选择淘潮玩 商品/类别");

            if (!tcRecommend.getTcFlag().equals(2)
                    && !tcRecommend.getTcFlag().equals(1)){
                 throw new CustomException("淘潮玩 商品/类别错误");
            }

            if (tcRecommend.getTcFlag().equals(2)) {
                if ( !Integer.valueOf(-1).equals(tcRecommend.getCateId())
                        && Integer.valueOf(-1).equals(tcRecommend.getBrandId())
                        && "-1".equals(tcRecommend.getLabelId()))
                    throw new CustomException("未选择淘潮玩商品类别 ");

            }

        }
    }

    /**
     * 修改数据
     *
     * @param tcRecommend 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcRecommend tcRecommend) {
        verifyTcRecommend(tcRecommend);
        tcRecommend.setGmtUpdate(new Date(System.currentTimeMillis()));
        return this.tcRecommendDao.update(tcRecommend) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Integer id) {
        if (id == null) throw new CustomException("参数错误");
        TcRecommend tcRecommend = queryById(id);
        if (null == tcRecommend) throw new CustomException(String.format("编号为%d已过期", id));
        return this.tcRecommendDao.deleteById(id) > 0;
    }


    @Override
    public List<TcRecommend> queryAll(TcRecommend tcRecommend) {
        if (null != tcRecommend){
            if (null == tcRecommend.getTemplateId()){
                tcRecommend.setTemplateId(tcRecommend.getSearchTemplateId());
            }
        }
        return tcRecommendDao.queryAll(tcRecommend);
    }

    @Override
    public Boolean onAndOffShelves(TcRecommend updateObject) {
        if (null == updateObject || null == updateObject.getId()) {
            throw new CustomException("参数错误");
        }
        TcRecommend tcRecommend = queryById(updateObject.getId());
        if (null == tcRecommend) {
            throw new CustomException("参数错误");
        }
        tcRecommend.setGmtUpdate(new Date(System.currentTimeMillis()));
        log.info("修改对象 =============> {}",updateObject.getIsShelves());
        log.info("待修改对象 =============> {}",tcRecommend.getIsShelves());
        tcRecommend.setIsShelves(updateObject.getIsShelves());


        return this.tcRecommendDao.onAndOffShelves(tcRecommend.getId(),tcRecommend.getIsShelves()) > 0;
    }
}
