<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTeamPropertyMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeamProperty" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="teamId" column="team_id" jdbcType="INTEGER"/>
		<result property="propertyId" column="property_id" jdbcType="INTEGER"/>
		<result property="propertyVal" column="property_val" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamProperty">
		SELECT 
			`id`,
			`team_id`,
			`property_id`,
			`property_val`,
			`gmt_create`
		FROM  by_team_property
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="teamId != null">
			  	AND `team_id` = #{teamId}
			</if>
			<if test="propertyId != null">
			  	AND `property_id` = #{propertyId}
			</if>
			<if test="propertyVal != '' and propertyVal != null">
			  	AND `property_val` = #{propertyVal}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`team_id`,
			`property_id`,
			`property_val`,
			`gmt_create`
		FROM by_team_property
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamProperty" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO by_team_property
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="teamId != null">
			`team_id`,
		</if>
		<if test="propertyId != null">
			`property_id`,
		</if>
		<if test="propertyVal != '' and propertyVal != null">
			`property_val`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="teamId != null">
			#{teamId},	
		</if>
		<if test="propertyId != null">
			#{propertyId},	
		</if>
		<if test="propertyVal != '' and propertyVal != null">
			#{propertyVal},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamProperty">
		UPDATE by_team_property
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="teamId != null">
				`team_id` = #{teamId},
			</if>
			<if test="propertyId != null">
				`property_id` = #{propertyId},
			</if>
			<if test="propertyVal != '' and propertyVal != null">
				`property_val` = #{propertyVal},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM by_team_property WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.ByTeamProperty">
		DELETE FROM by_team_property
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="teamId != null">
			  	AND `team_id` = #{teamId}
			</if>
			<if test="propertyId != null">
			  	AND `property_id` = #{propertyId}
			</if>
			<if test="propertyVal != '' and propertyVal != null">
			  	AND `property_val` = #{propertyVal}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
		</where>
	</delete> -->
</mapper>

