
/*
* ByCustAppointmentMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 09 14:34:33 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ByCustAppointmentMapper extends Mapper<ByCustAppointment> {
	/**
	 * 预约列表查询
	 * @param byCustAppointment
	 * @return
	 */
    List<ByCustAppointmentVO> queryAppoinmentList(ByCustAppointment byCustAppointment);

	/**
	 * 查询客户预约记录
	 * @param map
	 * @return
	 */
    List<ByCustAppointmentVO> custApponintByUserId(Map<String,Object> map);
}