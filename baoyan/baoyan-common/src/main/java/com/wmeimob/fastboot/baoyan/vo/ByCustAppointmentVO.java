package com.wmeimob.fastboot.baoyan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: ByCustAppointmentVO
 * @projectName baoyan
 * @description: 预约记录vo
 * @date 2019/7/10 17:31
 */
@Data
public class ByCustAppointmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * 预约类型
     */
    private Integer apponintmentType;

    /**
     * 预约人
     */
    private String custUserName;
    /**
     * 手机号
     */
    private String tel;
    /**
     * 预约门店
     */
    private String storeName;
    /**
     * address
     */
    private String storeMobile;
    /**
     * 门店电话
     */
    private String address;

    /**
     * 预约产品
     */
    private String apponintmentProduct;

    /**
     * 购票来源
     */
    private String ticketChannelName;

    /**
     * 购票来源Integer
     */
    private Integer ticketChannel;
    /**
     * 预约数量
     */
    private Integer apponintmentNum;
    /**
     * 家长姓名
     */
    private String parentName;
    /**
     * 联系方式
     */
    private String contactTel;
    /**
     * 门店图片
     */
    private String storeImg;
    /**
     * 小朋友姓名
     */
    private String childName;
    /**
     * 小朋友性别
     */
    private String childSexStr;
    /**
     * 小朋友性别
     */
    private Integer childSex;
    /**
     * 小朋友年龄
     */
    private Integer childAge;
    /**
     * 小朋友生日
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date birthday;
    /**
     * 预约日期
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date apponintmentDate;
    /**
     * 预约日期
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 状态
     */
    private String apponintmentStatusStr;
    /**
     * 状态 int
     */
    private Integer apponintmentStatus;
    /**
     * 创建时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

    private Integer storeId;

}
