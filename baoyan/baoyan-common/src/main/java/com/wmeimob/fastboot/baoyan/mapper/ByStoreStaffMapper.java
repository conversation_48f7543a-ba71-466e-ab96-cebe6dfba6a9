
/*
* ByStoreStaffMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 09:48:59 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByStoreStaffMapper extends Mapper<ByStoreStaff> {

	/**
	 * 根据 openid查找员工
	 * @param wxOpenId
	 * @return
	 */
	ByStoreStaff findByOpenId(String wxOpenId);

       /**List<ByStoreStaff> select(ByStoreStaff byStoreStaff);

	ByStoreStaff selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByStoreStaff byStoreStaff);

	int updateByPrimaryKeySelective(ByStoreStaff byStoreStaff);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByStoreStaff byStoreStaff);*/

}