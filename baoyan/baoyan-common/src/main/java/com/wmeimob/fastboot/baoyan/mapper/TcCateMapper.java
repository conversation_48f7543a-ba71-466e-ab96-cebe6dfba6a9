package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcCate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 淘潮玩品类表(TcCate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcCateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcCate queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<TcCate> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcCate 实例对象
     * @return 对象列表
     */
    List<TcCate> queryAll(TcCate tcCate);

    /**
     * 新增数据
     *
     * @param tcCate 实例对象
     * @return 影响行数
     */
    int insert(TcCate tcCate);

    /**
     * 修改数据
     *
     * @param tcCate 实例对象
     * @return 影响行数
     */
    int update(TcCate tcCate);

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}