package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.qo.GiftsQo;
import com.wmeimob.fastboot.baoyan.vo.Goods;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 支付
 * @date 2019-08-13 17:12
 * @Version 1.0
 */
public interface PayService {
    default Map<String, Object> pay(Goods goods) {
        return null;
    };

    /**
    * @Description 支付
    * <AUTHOR>
    * @Date        2019-08-15 15:34
    * @Version    1.0
    */
    default Map<String, Object> payOrder(Goods goods){
        return null;
    };

    default void update(Object out_trade_no,Object transaction_id){
        return;
    };

    default Boolean updateChannel(String orderNo){return null;}

    default void updateTicketOrder(Object out_trade_no,Object transaction_id){
        return;
    };

    default BigDecimal calculation(Goods goods){
        return null;
    };

    void gifts(GiftsQo gifts);

    /**
     * 查询准备支付的订单详情
     * @param payAllOrder
     * @return
     */
    OrderDetailVo payAllDetail(PayAllOrder payAllOrder);


    /**
     * 购买门票时 展示订单详情
     * @param goods
     * @return
     */
    Map<String,Object> computeDetailMap(Goods goods);

    /**
     * 查看准备支付的淘潮玩订单详情
     * @param payAllOrder
     * @return
     */
    OrderDetailVo payTcDetail(PayAllOrder payAllOrder);

    /**
     * 支付，可以同时支付淘潮玩和门票
     * @param payAllOrder
     * @return
     */
    PayVo payAll(PayAllOrder payAllOrder);

    /**
     * 淘潮玩付款
     * tcGoods 必须有东西
     * ustAloneTc 必须有东西
     * orderAmount 必须有东西
     * @param payAllOrder
     * @return
     */
    PayVo payTc(PayAllOrder payAllOrder);

    /**
     * 油菜花-充值会员卡
     * @param ychOrder
     * @return
     */
    PayVo payYch(YchOrder ychOrder);

    /**
     * 支付门票订单，参数和返回值进行了优化
     * @param allOrder
     * @return
     */
    PayVo payOrderNew(PayAllOrder allOrder);

    /**
     * 根据父订单号 更新订单的状态
     * @param parentNo
     * @param transactionId
     * @return
     */
    boolean updateByParentNo(String parentNo, String transactionId);

    /**
     * 未支付的淘潮订单进行付款
     * @param orderId
     * @return
     */
    PayVo payUnpaidTc(Integer orderId);

}
