package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.vo.CouponListByGoodsVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Consumer;

/**
 * @ClassName ByCouponService
 * @Description 优惠券表
 * <AUTHOR>
 * @Date Tue Jul 09 13:58:11 CST 2019
 * @version1.0
 **/
public interface WechatCouponUserService extends CommonService<ByCouponUser>{


    /**
     *  当前优惠券
     *  如果是门票的优惠券，调用第一个Consumer
     *  如果是淘潮玩优惠券，调用第二个consumer
     * @param couponId
     * @param userId
     * @param consumer1
     * @param consumer2
     */
    default void consumerCoupon(Integer couponId,
                                Integer userId,
                                Consumer<ByCouponUser> consumer1,
                                Consumer<ByCouponUser> consumer2)
    {throw new NotImplementedException("consumerCoupon");}

    /**
     * 找到一批淘潮玩商品可以使用的优惠券
     * @param userId
     * @param tcGoods
     * @return
     */
    List<ByCouponUser> findUserTcCoupon(Integer userId, List<TcGoodsShoping> tcGoods, BigDecimal goodsAmount);

    /**
     * 优惠券表查询
     * @param id
     * @return
     */
    default ByCoupon queryByCouponById(Object id){throw new NotImplementedException("queryByCouponById");};

    /**
     * 优惠券领取添加
     * @param couponUser
     */
    default void addCouponUser(ByCouponUser couponUser){throw new NotImplementedException("addCouponUser");}

    /**
     * 可领取优惠券列表
     * @param coupon
     * @return
     */
    default List<ByCoupon> couponList(ByCoupon coupon){throw new NotImplementedException("couponList");}

    /**
     * 商品可以领取的优惠卷列表
     * @param type
     * @param goodsId
     * @return
     */
    default List<CouponListByGoodsVo> couponListByGoods(Integer type, Integer goodsId){throw new NotImplementedException("couponListByGoods");}
}
