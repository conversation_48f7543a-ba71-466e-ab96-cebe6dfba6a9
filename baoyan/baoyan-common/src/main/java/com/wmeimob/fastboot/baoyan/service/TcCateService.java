package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.entity.TcCate;
import org.hibernate.validator.constraints.Email;

import java.util.List;

/**
 * 淘潮玩品类表(TcCate)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcCateService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcCate queryById(Integer id);



    /**
     * 新增数据
     *
     * @param tcCate 实例对象
     * @return 实例对象
     */
    Boolean insert(TcCate tcCate);

    /**
     * 修改数据
     *
     * @param tcCate 实例对象
     * @return 实例对象
     */
    Boolean update(TcCate tcCate);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    List<TcCate> queryPage(TcCate queryCate);
}