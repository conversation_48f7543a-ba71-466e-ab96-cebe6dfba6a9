package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 广告列表返回
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ByArticlesVO implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     *标题
     */
    private String articleName;
    /**
     * 内容
     */
    private String content;
    /**
     * 滚动时间单位/秒
     */
    private Integer time;
}
