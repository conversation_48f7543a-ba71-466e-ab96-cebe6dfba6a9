package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;

import java.math.BigDecimal;

public interface MemberCardService {
    // 获取会员卡信息（优先本地，本地没有则调用油菜花并同步）
    ByCustUser getMemberCardInfo(String phone);
    
    // 获取会员余额（优先本地，本地没有则调用油菜花并同步）
    ByCustUser getMemberBalance();
    
    // 会员开卡（本地创建并异步调用油菜花）
    void registerMemberCard(String phone);
    
    // 增加会员余额（本地记录并异步同步到油菜花）
    void addMemberBalance(Integer memberId, BigDecimal amount, String orderNo, String summary);

    // 扣除会员余额（本地记录并异步同步到油菜花）
    void reduceMemberBalance(Integer memberId, BigDecimal amount, String orderNo, String summary);
    
    // 同步单个会员信息（供定时任务调用）
    void syncMemberInfo(Integer userId);
    
    // 重试失败的同步任务
    void retryFailedSyncTasks();
}
