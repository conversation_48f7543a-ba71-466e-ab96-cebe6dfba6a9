package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByIntegralLogService
 * @Description 积分明细记录表
 * <AUTHOR>
 * @Date Fri Jul 05 15:35:59 CST 2019
 * @version1.0
 **/
public interface ByIntegralLogService extends CommonService<ByIntegralLog>{

    /**
     * 积分明细记录表查询
     * @param id
     * @return
     */
    default ByIntegralLog queryByIntegralLogById(Object id){throw new NotImplementedException("queryByIntegralLogById");};

    /**
     * 积分明细记录表添加
     * @param  byIntegralLog
     * @return
     */
    default  void addByIntegralLog(ByIntegralLog byIntegralLog){throw new NotImplementedException("addByIntegralLog");};


    /**
     * 积分明细记录表删除
     * @param id
     * @return
     */
    default void removeByIntegralLog(Object id){throw new NotImplementedException("removeByIntegralLog");};


    /**
     * 积分明细记录表修改
     * @param byIntegralLog
     * @return
     */
    default void modifyByIntegralLog(ByIntegralLog byIntegralLog){throw new NotImplementedException("modifyByIntegralLog");};
}
