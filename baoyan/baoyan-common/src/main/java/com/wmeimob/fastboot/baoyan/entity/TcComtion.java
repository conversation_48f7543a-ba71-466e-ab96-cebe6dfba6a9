package com.wmeimob.fastboot.baoyan.entity;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

/**
 * 淘潮商品关联表(TcComtion)表实体类
 *
 * <AUTHOR>
 * @since 2021-10-12 21:16:14
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TcComtion implements Serializable {
    private static final long serialVersionUID = -89468345644747840L;
    //自增ID
    private Integer id;
    //商品ID
    private Integer tcGoodsId;
    //店铺ID
    private Integer tcShopId;

}
