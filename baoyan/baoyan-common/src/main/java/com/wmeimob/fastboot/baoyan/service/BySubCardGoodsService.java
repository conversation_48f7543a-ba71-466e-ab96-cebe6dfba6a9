package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName BySubCardGoodsService
 * @Description 次卡商品表
 * <AUTHOR>
 * @Date Thu Jul 25 10:13:01 CST 2019
 * @version1.0
 **/
public interface BySubCardGoodsService extends CommonService<BySubCardGoods>{

    /**
     * 次卡商品表查询
     * @param id
     * @return
     */
    default BySubCardGoods queryBySubCardGoodsById(Integer id){throw new NotImplementedException("queryBySubCardGoodsById");};

    /**
     * 次卡商品表添加
     * @param  bySubCardGoods
     * @return
     */
    default  void addBySubCardGoods(BySubCardGoods bySubCardGoods) throws IOException {throw new NotImplementedException("addBySubCardGoods");};


    /**
     * 次卡商品表删除
     * @param id
     * @return
     */
    default void removeBySubCardGoods(Object id){throw new NotImplementedException("removeBySubCardGoods");};


    /**
     * 次卡商品表修改
     * @param bySubCardGoods
     * @return
     */
    default void modifyBySubCardGoods(BySubCardGoods bySubCardGoods) throws IOException {throw new NotImplementedException("modifyBySubCardGoods");};

    /**
     * 次卡商品上下架
     * @param byGoodsInfo
     */
    default void updateShelf(BySubCardGoods bySubCardGoods){throw new NotImplementedException("updateShelf");};
}
