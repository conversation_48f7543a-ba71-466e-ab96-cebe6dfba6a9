
/*
* BaseNavigationConfMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 30 16:58:36 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseNavigationConfMapper extends Mapper<BaseNavigationConf> {
       /**List<BaseNavigationConf> select(BaseNavigationConf baseNavigationConf);

	BaseNavigationConf selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseNavigationConf baseNavigationConf);

	int updateByPrimaryKeySelective(BaseNavigationConf baseNavigationConf);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseNavigationConf baseNavigationConf);*/
	List<BaseNavigationConf> navigation();

	List<BaseNavigationConf> tcNavigation(Boolean isHome);
	
}