package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcLabel;
import com.wmeimob.fastboot.core.exception.CustomException;
import java.util.List;

/**
 * (TcLabel)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-20 14:08:58
 */


public interface TcLabelService {


    /**
     * 通过ID查询单条数据
     * @param id 主键
     * @return 实例对象
     */
    default  TcLabel queryById(Integer id){
        throw new CustomException("queryById");
    }


    /**
     * 新增数据
     *
     * @param tcLabel 实例对象
     * @return 实例对象
     */
   default Boolean insert(TcLabel tcLabel){
       throw new CustomException("insert");
   }

    /**
     * 条件查询
     */
   default List<TcLabel> queryAll(TcLabel tcLabel){

       throw new CustomException("queryAll");
   }

    /**
     * 根据标签名 查询 标签
     *
     */
    default TcLabel queryByName(String s){
        throw new CustomException("queryByName");
    }

}