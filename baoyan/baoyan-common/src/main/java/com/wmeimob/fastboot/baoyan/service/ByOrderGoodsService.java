package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByOrderGoodsService
 * @Description 普通订单商品表
 * <AUTHOR>
 * @Date Tue Aug 06 17:02:57 CST 2019
 * @version1.0
 **/
public interface ByOrderGoodsService extends CommonService<ByOrderGoods>{

    /**
     * 普通订单商品表查询
     * @param id
     * @return
     */
    default ByOrderGoods queryByOrderGoodsById(Object id){throw new NotImplementedException("queryByOrderGoodsById");};

    /**
     * 普通订单商品表添加
     * @param  byOrderGoods
     * @return
     */
    default  void addByOrderGoods(ByOrderGoods byOrderGoods){throw new NotImplementedException("addByOrderGoods");};


    /**
     * 普通订单商品表删除
     * @param id
     * @return
     */
    default void removeByOrderGoods(Object id){throw new NotImplementedException("removeByOrderGoods");};


    /**
     * 普通订单商品表修改
     * @param byOrderGoods
     * @return
     */
    default void modifyByOrderGoods(ByOrderGoods byOrderGoods){throw new NotImplementedException("modifyByOrderGoods");};
}
