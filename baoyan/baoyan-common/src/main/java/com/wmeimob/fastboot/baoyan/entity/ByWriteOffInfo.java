package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单商品信息
 * <AUTHOR>
 * @date 2021/7/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ByWriteOffInfo implements Serializable {

    /**
     * 订单详情，订单商品id
     */
    private Integer orderGoodsId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 核销总次数
     */
    private Integer writeOffTotal;

    /**
     * 核销剩余次数
     */
    private Integer writeOffSurplus;

    /**
     * 已核销次数
     */
    private Integer writeOffCount;


    /**
     * 是否显示退款类型和售后按钮
     * 0 申请售后
     * 1 已退款
     */
    private Integer refundType;

    /**
     * 核销开始时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date expiryDate;

    /**
     * 核销结束时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;

    /**
     * 最后一次核销时间
     */
    @JsonFormat(locale = "zh", pattern = "yyyy年MM月dd日 HH:mm:ss",timezone = "GMT+8")
    private Date lastWriteDate;

    /**
     * 核销状况
     * 1. 未付款 2.已付款 3.已完成 4.已过期
     */
    private Integer writeOffStatus;
}
