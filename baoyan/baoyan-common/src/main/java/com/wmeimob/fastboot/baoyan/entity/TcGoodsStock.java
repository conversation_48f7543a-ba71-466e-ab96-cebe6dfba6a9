package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.Date;
import java.io.Serializable;

/**
 * (TcGoodsStock)实体类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TcGoodsStock implements Serializable {
    private static final long serialVersionUID = 542550153294816547L;
    /**
    * 主键自增无意义
    */
    private Integer id;
    /**
    * 商品id
    */
    private Integer goodsId;
    /**
    * 库存数量
    */
    private Integer stock;
    /**
    * 预警数量
    */
    private Integer warningCount;
    /**
    * 预警消息
    */
    private String warningMsg;
    /**
    * 创建时间
    */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date gmtCreate;

}