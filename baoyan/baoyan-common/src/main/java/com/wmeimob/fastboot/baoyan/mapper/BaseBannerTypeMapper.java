
/*
* BaseBannerTypeMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 30 16:58:36 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseBannerType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseBannerTypeMapper extends Mapper<BaseBannerType> {
       /**List<BaseBannerType> select(BaseBannerType baseBannerType);

	BaseBannerType selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseBannerType baseBannerType);

	int updateByPrimaryKeySelective(BaseBannerType baseBannerType);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseBannerType baseBannerType);*/
	
}