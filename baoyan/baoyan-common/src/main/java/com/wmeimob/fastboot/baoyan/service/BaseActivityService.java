package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseActivity;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseActivityService
 * @Description 活动表
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
public interface BaseActivityService extends CommonService<BaseActivity>{

    /**
     * 活动表查询
     * @param id
     * @return
     */
    default BaseActivity queryBaseActivityById(Object id){throw new NotImplementedException("queryBaseActivityById");};

    /**
     * 活动表添加
     * @param  baseActivity
     * @return
     */
    default  void addBaseActivity(BaseActivity baseActivity){throw new NotImplementedException("addBaseActivity");};


    /**
     * 活动表删除
     * @param id
     * @return
     */
    default void removeBaseActivity(Object id){throw new NotImplementedException("removeBaseActivity");};


    /**
     * 活动表修改
     * @param baseActivity
     * @return
     */
    default void modifyBaseActivity(BaseActivity baseActivity){throw new NotImplementedException("modifyBaseActivity");};
}
