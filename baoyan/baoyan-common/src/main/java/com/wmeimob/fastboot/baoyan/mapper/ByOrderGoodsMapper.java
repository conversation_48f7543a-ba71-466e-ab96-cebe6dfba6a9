
/*
* ByOrderGoodsMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Aug 06 17:02:57 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

public interface ByOrderGoodsMapper extends Mapper<ByOrderGoods> {

    List<ByOrderGoods> selectByOrderId(@Param("orderId") Integer orderId);

    /**
     * 根据id更新参数集合中的订单商品实际支付价格
     * {@linkplain ByOrderGoods#payUnitPrice}
     * @param orderGoods
     * @return
     */
    int updatePayPrice(ByOrderGoods orderGoods);
}