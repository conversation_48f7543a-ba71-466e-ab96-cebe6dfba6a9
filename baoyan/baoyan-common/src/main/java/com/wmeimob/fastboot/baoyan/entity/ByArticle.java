/*
* ByArticle.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 09 16:59:24 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_article")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class ByArticle implements Serializable {
	
   private static final long serialVersionUID = 1L;
	
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 文章名称
     */
    @Column(name = "article_name")
    private String articleName;
    /**
     * 文章内容
     */
    @Column(name = "content")
    private Object content;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 数据类型 1 商品 2 次卡 3联票 4拼团
     */
    @Column(name = "data_type")
    private Integer type;
    /**
     * 商品id
     */
    @Column(name = "data_id")
    private Integer goodsId;
    /**
     * 商品名称
     */
    @Transient
    private String goodsName;
    /**
     * 参与滚动0否1是
     */
    @Column(name = "state")
    private Integer state;
    @Transient
    private String name;
    /**
     * 购物车数量
     */
    @Transient
    private Integer shoppingCount;

   public String getName() {
    return name;
   }

   public void setName(String name) {
    this.name = name;
   }

   public Integer getId() {
    return id;
   }

   public void setId(Integer id) {
    this.id = id;
   }

   public String getArticleName() {
    return articleName;
   }

   public void setArticleName(String articleName) {
    this.articleName = articleName;
   }

   public Object getContent() {
    return content;
   }

   public void setContent(Object content) {
    this.content = content;
   }

   public Date getGmtCreate() {
    return gmtCreate;
   }

   public void setGmtCreate(Date gmtCreate) {
    this.gmtCreate = gmtCreate;
   }

   public Date getGmtModified() {
    return gmtModified;
   }

   public void setGmtModified(Date gmtModified) {
    this.gmtModified = gmtModified;
   }
}