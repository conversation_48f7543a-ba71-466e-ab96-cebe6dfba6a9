<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeLogMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="writeOffId" column="write_off_id" jdbcType="INTEGER"/>
		<result property="storeId" column="store_id" jdbcType="INTEGER"/>
		<result property="staffId" column="staff_id" jdbcType="INTEGER"/>
		<result property="custUserId" column="cust_user_id" jdbcType="INTEGER"/>
		<result property="writeOffDate" column="writeOffDate" jdbcType="TIMESTAMP"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog" id="ListResultMap" extends="BaseResultMap">
		<result property="writeOffName" column="write_off_name" jdbcType="VARCHAR"/>
		<result property="channelSourceName" column="channelSourceName" jdbcType="VARCHAR"/>
		<result property="custUserName" column="custUserName" jdbcType="VARCHAR"/>
		<result property="actualAmount" column="actualAmount" jdbcType="VARCHAR"/>
		<result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>
		<result property="orderNo" column="orderNo" jdbcType="VARCHAR"/>
		<result property="orderType" column="order_type" jdbcType="VARCHAR"/>
		<result property="sourceGoodsId" column="source_goods_id" jdbcType="INTEGER"/>
		<result property="writeOffNum" column="writeOffNum" jdbcType="INTEGER"/>
		<result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
	</resultMap>
	<select id="findByCondition" resultMap="ListResultMap">
		SELECT
			woc.*,
			cu.nick_name as custUserName,
		    sta.staff_name as staffName,
		bo.actual_amount as actualAmount,
	    	st.`name` as  storeName,
		    woc.end_date as endDate,
		    log.write_off_date as writeOffDate,
		    log.write_off_num as writeOffNum,
			sdi.item_name as  channelSourceName,
		    woc.goods_name as goodsName,
		CONCAT(woc.order_no,IF(bo.current_user_id IS NOT NULL AND bo.current_user_id != '' AND bo.user_id != woc.cust_user_id,'【赠】','')) orderNo
		FROM
		 write_off_code_log log

		LEFT JOIN	write_off_code woc on log.write_off_id=woc.id
		LEFT JOIN by_orders bo ON bo.order_no = woc.order_no
		LEFT JOIN by_cust_user cu on cu.id=log.cust_user_id
		LEFT JOIN by_store_staff sta on sta.id= log.staff_id
		LEFT JOIN base_store st  on st.id=log.store_id
		left join by_channel_cust cc on bo.id = cc.order_id
		left join sys_dict_item sdi on bo.is_channel =1 and sdi.item_value = cc.channel_source and sdi.dict_id = 1
		<where>
			<if test="storeId !=null">
				AND log.store_id =#{storeId}
			</if>
			<if test="startTime !=null and startTime!=''">
				AND log.write_off_date&gt;=#{startTime}
			</if>
			<if test="endTime !=null and endTime!=''">
				AND log.write_off_date &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
			</if>
			<if test="searchName !=null and searchName !='' ">
				and ((woc.order_no like concat('%',#{searchName},'%')) or  (cu.mobile like concat('%',#{searchName},'%')))
			</if>
		</where>
  order by log.write_off_date desc
	</select>

</mapper>

