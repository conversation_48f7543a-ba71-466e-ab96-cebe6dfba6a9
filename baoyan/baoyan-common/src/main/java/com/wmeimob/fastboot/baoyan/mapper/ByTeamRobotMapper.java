
/*
* ByTeamRobotMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 11:18:31 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByTeamRobot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByTeamRobotMapper extends Mapper<ByTeamRobot> {
       /**List<ByTeamRobot> select(ByTeamRobot byTeamRobot);

	ByTeamRobot selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByTeamRobot byTeamRobot);

	int updateByPrimaryKeySelective(ByTeamRobot byTeamRobot);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByTeamRobot byTeamRobot);*/
	
}