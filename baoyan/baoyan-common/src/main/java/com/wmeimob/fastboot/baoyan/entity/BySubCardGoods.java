/*
 * BySubCardGoods.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 25 10:13:01 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_sub_card_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class BySubCardGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品名称
     */
    @Column(name = "sub_card_goods_name")
    private String subCardGoodsName;
    /**
     * 商品id
     */
//    @Column(name = "goods_id")
//    private Integer goodsId;
    /**
     * 次卡商品包含数量
     */
    @Column(name = "sub_card_goods_num")
    private Integer subCardGoodsNum;

    @Column(name = "goods_tag")
    private String goodsTag;

    /**
     * 出售价格
     */
    @Column(name = "sell_price")
    private BigDecimal sellPrice;
    /**
     * 市场价格
     */
    @Column(name = "market_price")
    private BigDecimal marketPrice;
    /**
     * 起始销量
     */
    @Column(name = "initial_sale_num")
    private Integer initialSaleNum;
    /**
     * 实际销量
     */
    @Column(name = "actual_sales_num")
    private Integer actualSalesNum;
    /**
     * 商品库存
     */
    @Column(name = "goods_stock")
    private Integer goodsStock;
    /**
     * 排序值
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 商品banner图片
     */
    @Column(name = "goods_banner")
    private String goodsBanner;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 上下架 0下架 1上架售卖 2上架隐藏 3定时上架
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否删除
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 富文本id
     */
    @Column(name = "rich_id")
    private Integer richId;

    /**
     * 限购数（null或0为不限购）
     */
    @Column(name = "limited")
    private Integer limited;
    /**
     * 能否使用优惠卷抵扣0否，1能
     */
    @Column(name = "is_coupon")
    private Integer isCoupon;
    /**
     * 能否使用积分抵扣0否，1能
     */
    @Column(name = "is_integral")
    private Integer isIntegral;
    /**
     * 购买须知富文本
     */
    @Column(name = "buy_notice")
    private String buyNotice;
    /**
     * 使用须知富文本
     */
    @Column(name = "use_notice")
    private String useNotice;
    /**
     * 亮点
     */
    @Column(name = "brights")
    private String brights;
    /**
     * 是否指定核销天数
     */
    @Column(name = "has_verification_day")
    private Boolean hasVerificationDay;
    /**
     * 指定天数内有效
     */
    @Column(name = "verification_day")
    private Integer verificationDay;
    /**
     * 定时下架状态 0:未开启 1:开启
     */
    @Column(name = "regular_down_status")
    private Boolean regularDownStatus;
    /**
     * 有效开始日期
     */
    @Column(name = "effective_start")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date effectiveStart;
    /**
     * 有效结束日期
     */
    @Column(name = "effective_end")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date effectiveEnd;
    /**
     *营销开始时间
     */
    @Column(name = "marketing_start")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date marketingStart;
    /**
     *营销结束时间
     */
    @Column(name = "marketing_end")
//    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date marketingEnd;

    /**
     * 组合油菜花预存款套餐，支持赠送本金和赠金
     */
    private Integer preDepositId;

    /**
     * 首次核销赠金
     */
    @Column(name = "first_verify_balance")
    private BigDecimal firstVerifyBalance;

    /**
     * 商品编号
     */
    @Column(name = "goods_no")
    private String goodsNo;


    /**
     *商品名称
     */
//    @Transient
//    private String subGoodsName;


    public String getGoodsName() {
        return subCardGoodsName;
    }

    @Transient
    private String richContent;
    @Transient
    private String searchName;

    @Transient
    private Integer goodsCount;
    /**
     * 分类名称
     */
    @Transient
    private String classifyName;
    /**
     * 分类id
     */
    @Transient
    private Integer classifyId;
    /**
     * 分类
     */
    @Transient
    private String classifyIds;
    @Transient
    private String stores;
    /**
     * 门店id
     */
    @Transient
    private Integer storeId;
    /**
     * 门店名称
     */
    @Transient
    private String storeName;

    public Integer getGoodsCount() {
        return initialSaleNum;
    }

    public void setGoodsCount(Integer goodsCount) {
        this.goodsCount = goodsCount;
    }
}