package com.wmeimob.fastboot.baoyan.utils.common;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectResult;
import com.wmeimob.fastboot.autoconfigure.oss.AliyunOssProperties;
import com.wmeimob.fastboot.util.StringUtils;

import java.io.InputStream;
import java.util.UUID;

/**
 * Created by Shinez on 2016/11/25.
 */
public class AliOOSUtil {

    public static String uploadFileByStream(AliyunOssProperties properties, InputStream file, String key) {
        OSSClient ossClient = new OSSClient(properties.getEndpoint(), properties.getAccessKeyId(), properties.getAccessKeySecret());
        if(StringUtils.isEmpty(key)){
            key = UUID.randomUUID().toString();
        }
        PutObjectResult result = ossClient.putObject(properties.getBucket(),key,file);
        ossClient.shutdown();
        return "";
    }
}
