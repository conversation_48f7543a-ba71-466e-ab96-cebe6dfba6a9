<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByStoreTableMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.vo.StoreTableVo" id="ListStoreTableMap">
        <result property="storeId" column="storeId" jdbcType="INTEGER"/>
        <result property="tableNo" column="tableNo" jdbcType="VARCHAR"/>
        <result property="tableNum" column="tableNum" jdbcType="INTEGER"/>
    </resultMap>

    <select id="queryStoreNoList" resultMap="ListStoreTableMap">
        select bs.id as storeId, group_concat(bst.table_no) as tableNo, count(bst.id) as tableNum
        from base_store as bs
        left join by_store_table as bst on bs.id = bst.store_id
        where bst.is_del = 0 and bs.store_type = 1
            <if test="storeId != null">
                and bs.id = #{storeId}
            </if>
            <if test="tableNo != null">
                    and bst.table_no like CONCAT('%',#{tableNo},'%')
            </if>
        group by bs.id
    </select>
</mapper>

