/*
 * 订单实体类
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_orders")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByOrders implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 商家支付时的订单号
     */
    @Column(name = "pay_order_no")
    private String payOrderNo;

    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private Integer goodsId;

    /**
     * 商品Tag
     */
    @Column(name = "goods_tag")
    private String goodsTag;

    /**
     * 积分抵扣金额
     */
    @Column(name = "integral_amount")
    private BigDecimal integralAmount;
    /**
     * 优惠券抵扣金额
     */
    @Column(name = "coupon_amount")
    private BigDecimal couponAmount;
    /**
     * 订单金额
     */
    @Column(name = "order_amount")
    private BigDecimal orderAmount;
    /**
     * 下单用户id
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 是否渠道订单
     */
    @Column(name = "is_channel")
    private Integer isChannel;

    /**
     * 渠道用户ID
     */
    @Column(name = "channel_id")
    private Integer channelId;

    @Column(name = "retired_amount")
    private Integer retiredAmount;

    /**
     * 被转赠用户id
     */
    @Column(name = "current_user_id")
    private Integer currentUserId;
    /**
     * 被转赠用户名称
     */
    @Transient
    private String currentUserName;
    /**
     * 下单时间
     */
    @Column(name = "order_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderTime;
    /**
     * 取消时间
     */
    @Column(name = "cancel_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;
    /**
     * 订单状态:-1订单取消1-待付款,2-已付款,3-已完成,4-已关闭
     */
    @Column(name = "order_status")
    private Integer orderStatus;

    /**
     * 订单状态名称
     */
    @Transient
    private String orderStatusName;


    /**
     * 实收金额
     */
    @Column(name = "actual_amount")
    private BigDecimal actualAmount;
    /**
     * 优惠券id
     */
    @Column(name = "coupon_id")
    private Integer couponId;
    /**
     * 商品类型，1-普通，2-拼团
     */
    @Column(name = "goods_type")
    private Integer goodsType;
    /**
     * 订单优惠前总计金额
     */
    @Column(name = "amount_before_discount")
    private BigDecimal amountBeforeDiscount;
    /**
     * 订单优惠后总计金额
     */
    @Column(name = "amount_after_discount")
    private BigDecimal amountAfterDiscount;
    /**
     * 订单优惠金额
     */
    @Column(name = "discount_amount")
    private BigDecimal discountAmount;
    /**
     * 退款状态 0-未申请退款订单,1-申请退款订单
     */
    @Column(name = "is_refund")
    private Boolean isRefund;
    /**
     * 订单是否删除（客户） 0未删除 1删除
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 是否评价 0未评价1已评价
     */
    @Column(name = "eval_type")
    private Boolean evalType;
    /**
     *  是否为售后;1:是,0:否
     */
    @Column(name = "is_after_sale")
    private Boolean isAfterSale;
    /**
     * 支付方式 1微信支付 2余额支付
     */
    @Column(name = "pay_type")
    private Integer payType;
    /**
     * 订单关闭类型 1.取消订单/订单超时 2.全部退款
     */
    @Column(name = "order_close_type")
    private Integer orderCloseType;
    /**
     * 支付時間
     */
    @Column(name = "pay_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;

    /**
     * 创建时间
     * */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 支付流水号
     */
    @Column(name = "pay_flow_no")
    private String payFlowNo;


    @Transient
    private Double maxMoney;
    @Transient
    private Double minMoney;

    /**
     *客户姓名
     */
    @Transient
    private String custUserName;
    /**
     *手机
     */
    @Transient
    private String mobile;
    /**
     * 查询名称
     */
    @Transient
    private String searchName;
    @Transient
    private String startTime;
    @Transient
    private String endTime;

    @Column(name = "remark")
    private String remark;
    /**
     * 门店id
     */
    @Transient
    private Integer storeId;
    /**
     * 分类
     */
    @Transient
    private Integer classifyId;

    @Column(name = "from_id")
    private String fromId;
    /**
     * 使用完成时间
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date useTime;

    /**
     * 商品名称
     */
    @Transient
    private String goodsName;

    /**
     * 商品单价
     */
    @Transient
    private BigDecimal goodsPrice;

    /**
     * 购买数量
     */
    @Transient
    private Integer goodsNum;
    /**
     * 优惠券金额
     */
    @Transient
    private BigDecimal couponPrice;
    /**
     * 积分金额
     */
    @Transient
    private BigDecimal integralPrice;
    @Transient
    private Integer teamType;
    @Transient
    private Integer returnStatus;
    @Transient
    private Integer writeStatus;
    /**
     * 是否是下单人/是否可赠送
     */
    @Transient
    private Integer isCurrent;
    /**
     * 1赠送人 0被赠送人
     */
    @Transient
    private Integer isPresenter;
    /**
     * 加密参数
     */
    @Transient
    private String random;
    /**
     * 规格名称
     */
    @Transient
    private String combinationName;




}