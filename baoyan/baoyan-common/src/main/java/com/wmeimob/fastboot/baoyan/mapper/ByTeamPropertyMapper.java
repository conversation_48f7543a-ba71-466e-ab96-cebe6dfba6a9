
/*
* ByTeamPropertyMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 15:59:56 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByTeamProperty;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByTeamPropertyMapper extends Mapper<ByTeamProperty> {
       /**List<ByTeamProperty> select(ByTeamProperty byTeamProperty);

	ByTeamProperty selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByTeamProperty byTeamProperty);

	int updateByPrimaryKeySelective(ByTeamProperty byTeamProperty);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByTeamProperty byTeamProperty);*/
	
}