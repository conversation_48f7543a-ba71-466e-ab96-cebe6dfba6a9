package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.baoyan.vo.TeamOrderVo;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTeamOrderService
 * @Description 拼团订单表
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
public interface ByTeamOrderService extends CommonService<ByTeamOrder>{

    /**
     * <AUTHOR> @date
     * @Description:查询拼团订单列表
     */
    default List<TeamOrderVo> queryTeamList(TeamOrderVo vo){throw new NotImplementedException("queryTeamList");};
    /**
     * 查询
     * @param id
     * @return
     */
    default TeamOrderVo queryTeamOrderById(Integer id){throw new NotImplementedException("queryTeamOrderById");};
    /**
     * 拼团管理 拼团订单
     */
    default  List<TeamOrderVo> getTeamOrderInfo(Integer id){throw new NotImplementedException("getTeamOrderInfo");};
}
