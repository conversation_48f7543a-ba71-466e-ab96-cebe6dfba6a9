package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByChannelCust;
import com.wmeimob.fastboot.baoyan.vo.ChannelCustWriteOffTotalVo;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.List;

/**
 * @Description 渠道会员表
 * <AUTHOR>
 * @Date 2024-06-22
 * @version 1.0
 **/
public interface ByChannelCustService extends CommonService<ByChannelCust>{
    /**
     * 授权用户查询
     * @param id
     * @return
     */
    default ByChannelCust queryById(Object id){
        throw new NotImplementedException("queryById");
    };

    default Boolean addByChannelCust(ByChannelCust byChannelCust){
        throw new NotImplementedException("addByChannelCust");
    }

    default Boolean updateByChannelCust(ByChannelCust byChannelCust){
        throw new NotImplementedException("updateByChannelCust");
    }

    default Boolean removeByChannelCust(Object id){
        throw new NotImplementedException("removeByChannelCust");
    }

    default Boolean deleteChannelAllInfo(Object id){
        throw new NotImplementedException("deleteChannelAllInfo");
    }

    default List<ChannelCustWriteOffTotalVo> exportChannelCustTotal(ByChannelCust byChannelCust){
        throw new NotImplementedException("exportChannelCustTotal");
    }
}
