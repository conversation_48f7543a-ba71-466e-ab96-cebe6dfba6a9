package com.wmeimob.fastboot.baoyan.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatPayProperties;
import com.wmeimob.fastboot.baoyan.constant.ExternalApiConstants;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.service.YchApiService;
import com.wmeimob.fastboot.baoyan.service.YchBusinessService;
import com.wmeimob.fastboot.baoyan.utils.UUIDOrder;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.utils.SignUtil;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class YchApiServiceImpl  implements YchApiService {
    @Resource
    private SignUtil signUtil;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private YchBusinessMapper ychBusinessMapper;

    @Resource
    private YchGoodsMapper ychGoodsMapper;

    @Resource
    private YchOrderMapper ychOrderMapper;

    @Resource
    private YchOrderItemMapper ychOrderItemMapper;

    @Resource
    private YchLeaguerMapper ychLeaguerMapper;

    @Resource
    private YchValueMapper ychValueMapper;

    @Resource
    private WxCustUserService userService;

    @Resource
    private WechatPayProperties wechatPayProperties;

    @Resource
    private YchBusinessService ychBusinessService;

    @Resource
    private ByCustUserMapper byCustUserMapper;

    @Resource
    private BaseStoreMapper baseStoreMapper;

    @Override
    public List<YchBusiness> getBusinessList() {
        String url = ExternalApiConstants.GET_BUSINESS_LIST;

        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("AppID", ExternalApiConstants.APP_ID);
            params.put("BussinessID", "");
            params.put("TS", String.valueOf(System.currentTimeMillis()));

            // 生成签名
            String sign = signUtil.generateSign(params);
            params.put("Sign", sign);

            // 发送POST请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            JSONObject responseEntity = restTemplate.postForEntity(
                    url, requestEntity, JSONObject.class).getBody();

            if (responseEntity != null && responseEntity.get("IsSuccess").equals(true) && responseEntity.get("list") != null) {
                List<YchBusiness> ychBusinessList =  convertToYchBusinessList(responseEntity.getJSONArray("list"));
                //批量保存到数据库中，如果存在则更新，不存在则新增
                batchInsertOrUpdate(ychBusinessList);
                return ychBusinessList;
            } else {
                log.error("获取商户列表失败. 响应: {}", responseEntity);
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("调用外部商户列表API时发生错误", e);
            return new ArrayList<>();
        }
    }

    private List<YchBusiness> convertToYchBusinessList(JSONArray list){
        List<YchBusiness> ychBusinessList = new ArrayList<>();
        list.forEach(obj -> {
            JSONObject jsonObject = (JSONObject) obj;
            YchBusiness ychBusiness = new YchBusiness();
            ychBusiness.setBusinessId(jsonObject.getStr("BussinessID"));
            ychBusiness.setBusinessName(jsonObject.getStr("BusinessName"));
            ychBusiness.setAddress(jsonObject.getStr("Address"));
            ychBusiness.setBusinessUrl(jsonObject.getStr("BussinessUrl"));
            ychBusiness.setMallCode(jsonObject.getStr("MallCode"));
            ychBusiness.setGmtCreate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ychBusinessList.add(ychBusiness);
        });
        return ychBusinessList;
    }

    /**
     * 批量添加和更新门店信息
     * @param ychBusinessList
     */
    private void batchInsertOrUpdate(List<YchBusiness> ychBusinessList) {
        if (ychBusinessList == null || ychBusinessList.isEmpty()) {
            return;
        }

        for (YchBusiness business : ychBusinessList) {
            // 根据业务主键查询是否存在
            Example example = new Example(YchBusiness.class);
            example.createCriteria()
                    .andEqualTo("businessId", business.getBusinessId());
            YchBusiness existingBusiness = ychBusinessMapper.selectOneByExample(example);
            if (existingBusiness != null) {
                // 更新已存在的记录
                business.setId(existingBusiness.getId());
                business.setGmtModified(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                ychBusinessMapper.updateByPrimaryKey(business);
            } else {
                // 插入新记录
                ychBusinessMapper.insertSelective(business);
            }
        }
    }

    /**
     * 获取商品列表 - 充值套餐
     */
    @Override
    public List<YchGoods> getGoodsList(Integer goodsType, Boolean isAll) {
        // 这里需要获取所有门店的商品列表
        log.info("获取商品列表 - 充值套餐");
        List<YchBusiness> ychBusinessList = ychBusinessMapper.selectAll();
        log.info("门店列表：{}", ychBusinessList);
        for (YchBusiness ychBusiness : ychBusinessList) {
            String businessId = ychBusiness.getBusinessId();
            String url = ychBusinessService.getFullApiUrl(businessId, ExternalApiConstants.GET_GOODS_LIST);
            if(StringUtils.isBlank(url)){
                continue;
            }
            log.info("请求地址：{}", url);
            try {
                // 准备请求参数
                Map<String, Object> params = new HashMap<>();
                params.put("AppID", ExternalApiConstants.APP_ID);
                params.put("BussinessID", businessId);
                params.put("TS", String.valueOf(System.currentTimeMillis()));

                params.put("LeaguerID", "");
                params.put("GoodsID", "");
                params.put("goodsType", goodsType);

                // 生成签名
                String sign = signUtil.generateSign(params);
                params.put("Sign", sign);

                // 发送POST请求
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
                log.info("请求参数：{}", params);
                HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
                JSONObject responseEntity = restTemplate.postForEntity(
                        url, requestEntity, JSONObject.class).getBody();
                log.info("获取预充值套餐结果：{}", responseEntity);
                if (responseEntity != null && responseEntity.get("IsSuccess").equals(true) && responseEntity.get("list") != null) {
                    List<YchGoods> ychGoodsList = convertToYchGoodsList(responseEntity.getJSONArray("list"));
                    for (YchGoods ychGoods : ychGoodsList) {
                        // 根据业务主键查询是否存在
                        Example example = new Example(YchGoods.class);
                        example.createCriteria()
                                .andEqualTo("businessId", ychGoods.getBusinessId())
                                .andEqualTo("goodsId", ychGoods.getGoodsId());
                        YchGoods existingGoods = ychGoodsMapper.selectOneByExample(example);
                        if (existingGoods != null) {
                            ychGoods.setId(existingGoods.getId());
                            ychGoodsMapper.updateByPrimaryKeySelective(ychGoods);
                        } else {
                            ychGoodsMapper.insertSelective(ychGoods);
                        }
                    }
                } else {
                    log.error("获取商品列表失败. 响应: {}", responseEntity);
                    return new ArrayList<>();
                }
            } catch (Exception e) {
                log.error("调用商品列表API时发生错误", e);
            }
        }
        Example example = new Example(YchGoods.class);
//        example.createCriteria()
//                .andEqualTo("businessId", businessId);
        if (!isAll) {
            example.createCriteria()
                    .andEqualTo("hasHideMini", 0);
        }
        return ychGoodsMapper.selectByExample(example);
    }

    /**
     * 获取商品列表 - 充值套餐
     */
    @Override
    public List<YchGoods> getGoodsListAll(Integer goodsType, Boolean isAll) {
        return ychGoodsMapper.selectAll();
    }

    @Override
    public YchGoods getGoodsDetail(YchGoods ychGoods){
        Example example = new Example(YchGoods.class);
        example.createCriteria()
                .andEqualTo("id", ychGoods.getId());
        return ychGoodsMapper.selectOneByExample(example);
    }

    private List<YchGoods> convertToYchGoodsList(JSONArray jsonArray) {
        List<YchGoods> ychGoodsList = new ArrayList<>();
        jsonArray.forEach(obj -> {
            JSONObject jsonObject = (JSONObject) obj;
            YchGoods ychGoods = new YchGoods();
            ychGoods.setBusinessId(jsonObject.getStr("BussinessID"));
            ychGoods.setGoodsName(jsonObject.getStr("GoodsName"));
            ychGoods.setGoodsId(jsonObject.getStr("GoodsID"));
            ychGoods.setAbstractRemark(jsonObject.getStr("Abstract"));
            ychGoods.setClassName(jsonObject.getStr("ClassName"));
            ychGoods.setGoodsPrice(jsonObject.getFloat("GoodsPrice"));
            ychGoods.setPromoPrice(jsonObject.getFloat("PromoPrice"));
            ychGoods.setPromoReason(jsonObject.getStr("PromoReason"));
            ychGoods.setGoodsType(jsonObject.getInt("GoodsType"));
            ychGoods.setGoodsCode(jsonObject.getStr("GoodsCode"));
            ychGoods.setStartTime(jsonObject.getStr("StartTime"));
            ychGoods.setEndTime(jsonObject.getStr("EndTime"));
            ychGoods.setSDateValidStr(jsonObject.getStr("SDateValidStr"));
            ychGoods.setEDateValidStr(jsonObject.getStr("EDateValidStr"));
            ychGoods.setSummery(jsonObject.getInt("Summery"));
            ychGoods.setGroupName(jsonObject.getInt("GroupName"));
            ychGoods.setPackageTicketSDateValidStr(jsonObject.getStr("PackageTicketSDateValidStr"));
            ychGoods.setPackageTicketEDateValidStr(jsonObject.getStr("PackageTicketEDateValidStr"));
            ychGoods.setPackageTicketWriteOffTimeDesc(jsonObject.getStr("PackageTicketWriteOffTimeDesc"));
            ychGoods.setGmtCreate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ychGoods.setGmtModified(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ychGoodsList.add(ychGoods);
        });
        return ychGoodsList;
    }

    /**
     * 通过手机号查询会员 ID
     * TODO 此处可能会有个缺陷，就是当会员在其他门店开卡时，在默认门店没有找到时，可能会在默认门店注册一个新的会员卡
     */
    @Override
    public YchLeaguer GetLeaguerByPhone(String phone) {
        String url = ychBusinessService.getFullApiUrl(ExternalApiConstants.DEFAULT_BUSINESS_ID, ExternalApiConstants.GET_LEAGUER_BY_PHONE);

        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("AppID", ExternalApiConstants.APP_ID);
            params.put("BussinessID", ExternalApiConstants.DEFAULT_BUSINESS_ID);
            params.put("TS", String.valueOf(System.currentTimeMillis()));

            params.put("Phone", phone);

            // 生成签名
            String sign = signUtil.generateSign(params);
            params.put("Sign", sign);

            // 发送POST请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
            log.info("请求地址：{}",  url);
            log.info("请求参数：{}", params);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            JSONObject responseEntity = restTemplate.postForEntity(
                    url, requestEntity, JSONObject.class).getBody();
            log.info("获取结果：{}", responseEntity);
            if (responseEntity != null && responseEntity.get("LeaguerID")!= null) {
                return getLeaguer(responseEntity.get("LeaguerID").toString());
            } else {
                //如果没有找到则通过手机号进行注册
                return registerLeaguer(phone);
            }
        } catch (Exception e) {
            log.error("调用会员 IDAPI时发生错误", e);
            return null;
        }
    }

    /**
     * 通过手机号注册会员
     */
    @Override
    public YchLeaguer registerLeaguer(String phone) {
        // 先获取会员表中 store_id 如果存在则使用此门店和 business表中匹配的 business_id 进行注册
        Example example = new Example(ByCustUser.class);
        example.createCriteria()
                .andEqualTo("mobile", phone);
        example.setOrderByClause("id desc limit 1");
        ByCustUser user = this.byCustUserMapper.selectOneByExample(example);
        String businessId = ExternalApiConstants.DEFAULT_BUSINESS_ID;
        if(ObjectUtil.isNotEmpty(user.getStoreId())){
            BaseStore baseStore = baseStoreMapper.selectByPrimaryKey(user.getStoreId());
            if(ObjectUtil.isNotEmpty(baseStore) && ObjectUtil.isNotEmpty(baseStore.getBusinessId())){
                businessId = baseStore.getBusinessId();
            }
        }

        String url = ychBusinessService.getFullApiUrl(businessId, ExternalApiConstants.LEAGUER_REGISTER_BY_LEVEL);

        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("AppID", ExternalApiConstants.APP_ID);
            params.put("BussinessID", businessId);
            params.put("TS", String.valueOf(System.currentTimeMillis()));
            params.put("Password", "123456");
            params.put("Phone", phone);
            params.put("OpenID", "");
            params.put("RealName", "");
            params.put("LevelName", "");

            // 生成签名
            String sign = signUtil.generateSign(params);
            params.put("Sign", sign);

            // 发送POST请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
            log.info("请求地址：{}", url);
            log.info("请求参数：{}", params);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            JSONObject responseEntity = restTemplate.postForEntity(
                    url, requestEntity, JSONObject.class).getBody();
            log.info("响应结果：{}", responseEntity);
            if (responseEntity != null && responseEntity.get("LeaguerID")!= null) {
                return getLeaguer(responseEntity.get("LeaguerID").toString());
            } else {
                log.error("获取会员ID失败. 响应: {}", responseEntity);
                return GetLeaguerByPhone(phone);
            }
        } catch (Exception e) {
            log.error("调用会员ID API时发生错误", e);
            return null;
        }
    }

    /**
     * 通过会员 ID 获取会员信息
     * @param leaguerId
     * @return
     */
    public YchLeaguer getLeaguer(String leaguerId) {

        //通过会员 ID 获取门店 ID
        Example example1 = new Example(YchLeaguer.class);
        example1.createCriteria().andEqualTo("leaguerId",leaguerId);
        YchLeaguer tmpLeaguer = ychLeaguerMapper.selectOneByExample(example1);
        
        String businessId = "";
        if(ObjectUtil.isEmpty(tmpLeaguer)){
            businessId = ExternalApiConstants.DEFAULT_BUSINESS_ID;
        }else{
            businessId = tmpLeaguer.getBusinessId();
        }
        String url = ychBusinessService.getFullApiUrl(businessId, ExternalApiConstants.GET_LEAGUER_INFO);


        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("AppID", ExternalApiConstants.APP_ID);
            params.put("BussinessID", businessId);
            params.put("TS", String.valueOf(System.currentTimeMillis()));

            params.put("LeaguerID", leaguerId.toLowerCase());
            params.put("Phone", "");

            // 生成签名
            String sign = signUtil.generateSign(params);
            params.put("Sign", sign);

            // 发送POST请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
            log.info("请求地址：{}", url);
            log.info("请求参数：{}", params);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            JSONObject responseEntity = restTemplate.postForEntity(
                    url, requestEntity, JSONObject.class).getBody();
            log.info("响应结果：{}", responseEntity);
            if (responseEntity != null && responseEntity.get("IsSuccess")!= null) {
                YchLeaguer ychLeaguer = convertToYchLeaguer(responseEntity,businessId);
                // 根据业务主键查询是否存在
                Example example = new Example(YchLeaguer.class);
                example.createCriteria()
                        .andEqualTo("leaguerId", leaguerId);
                YchLeaguer existingBusiness = ychLeaguerMapper.selectOneByExample(example);
                if(existingBusiness != null){
                    ychLeaguer.setId(existingBusiness.getId());
                    ychLeaguer.setLeaguerId(leaguerId);
                    ychLeaguerMapper.updateByPrimaryKey(ychLeaguer);
                }else{
                    ychLeaguer.setLeaguerId(leaguerId);
                    ychLeaguerMapper.insertSelective(ychLeaguer);
                }

                existingBusiness = ychLeaguerMapper.selectOneByExample(example);

                // 更新会员表
                log.info("更新会员表-会员卡数据");
                Example example2 = new Example(ByCustUser.class);
                example2.createCriteria()
                        .andEqualTo("mobile",existingBusiness.getPhone());
                example2.setOrderByClause("id desc limit 1");
                ByCustUser byCustUser = byCustUserMapper.selectOneByExample(example2);
                byCustUser.setMemberBusinessId(existingBusiness.getBusinessId());
                byCustUser.setMemberCardId(existingBusiness.getLeaguerId());
                byCustUser.setMemberCardCode(existingBusiness.getLeaguerCode());
                byCustUserMapper.updateByPrimaryKey(byCustUser);
                log.info("更新会员表-会员卡数据-完成");

                return ychLeaguer;
            } else {
                log.error("获取会员ID失败. 响应: {}", responseEntity);
                //如果没有找到则通过手机号进行注册
                return new YchLeaguer();
            }
        } catch (Exception e) {
            log.error("调用会员 IDAPI时发生错误", e);
            return new YchLeaguer();
        }
    }

    private YchLeaguer convertToYchLeaguer(JSONObject responseEntity,String businessId){
        YchLeaguer ychLeaguer = new YchLeaguer();
        ychLeaguer.setLeaguerId(responseEntity.getStr("LeaguerID"));
        ychLeaguer.setBusinessId(businessId);
        ychLeaguer.setIsTake(responseEntity.getStr("IsTake").equals("false")? "0":"1");
        ychLeaguer.setLeaguerCode(responseEntity.getStr("LeaguerCode"));
        ychLeaguer.setLeaguerLevelId(responseEntity.getStr("LeaguerLevelID"));
        ychLeaguer.setLeaguerLevelName(responseEntity.getStr("LeaguerLevelName"));
        ychLeaguer.setLeaguerStatus(responseEntity.getInt("LeaguerStatus"));
        ychLeaguer.setLeaguerStatusDesc(responseEntity.getStr("LeaguerStatusDesc"));
        ychLeaguer.setRealName(responseEntity.getStr("RealName"));
        if(responseEntity.getStr("VerifyTime") != null){
            ychLeaguer.setVerifyTime(LocalDateTime.parse(responseEntity.getStr("VerifyTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ychLeaguer.setOpenBusinessName(responseEntity.getStr("OpenBusinessName"));
        ychLeaguer.setSex(responseEntity.getInt("Sex"));
        ychLeaguer.setAddress(responseEntity.getStr("Address"));
        if(responseEntity.getStr("Birthday") != null){
            ychLeaguer.setBirthday(LocalDateTime.parse(responseEntity.getStr("Birthday"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ychLeaguer.setEmail(responseEntity.getStr("Email"));
        ychLeaguer.setIdCard(responseEntity.getStr("IDCard"));
        ychLeaguer.setChildName(responseEntity.getStr("ChildName"));
        if(responseEntity.getStr("ChildBirthday") != null){
            ychLeaguer.setChildBirthday(LocalDateTime.parse(responseEntity.getStr("ChildBirthday"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ychLeaguer.setChildSex(responseEntity.getInt("ChildSex"));
        ychLeaguer.setChild2Name(responseEntity.getStr("Child2Name"));
        if(responseEntity.getStr("Child2Birthday") != null){
            ychLeaguer.setChild2Birthday(LocalDateTime.parse(responseEntity.getStr("Child2Birthday"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        ychLeaguer.setChild2Sex(responseEntity.getInt("Child2Sex"));
        ychLeaguer.setPhone(responseEntity.getStr("Phone"));
        ychLeaguer.setRegTime(LocalDateTime.parse(responseEntity.getStr("RegTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ychLeaguer.setImno(responseEntity.getStr("IMNO"));
        ychLeaguer.setQq(responseEntity.getStr("QQ"));
        ychLeaguer.setPhotoUrl(responseEntity.getStr("PhotoUrl"));
        ychLeaguer.setGmtCreate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ychLeaguer.setGmtModified(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return ychLeaguer;
    }

    /**
     * 根据当前用户获取会员卡信息
     */
    @Override
    public YchValue GetLeaguerValues(String phone) {
        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("phone", phone);
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
        if(existingLeaguer == null) {
            existingLeaguer = registerLeaguer(phone);
        }

        String url = ychBusinessService.getFullApiUrl(existingLeaguer.getBusinessId(), ExternalApiConstants.GET_LEAGUER_VALUES);
        log.info("调用会员余额查询接口地址: {}", url);
        try {
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("AppID", ExternalApiConstants.APP_ID);
            params.put("BussinessID",existingLeaguer.getBusinessId());
            params.put("TS", String.valueOf(System.currentTimeMillis()));

            params.put("LeaguerID", existingLeaguer.getLeaguerId());
            params.put("ValueCode", "1");
            params.put("PayID", "");

            // 生成签名
            String sign = signUtil.generateSign(params);
            params.put("Sign", sign);

            // 发送POST请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
            log.info("调用会员余额查询接口参数: {}", params);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            JSONObject responseEntity = restTemplate.postForEntity(
                    url, requestEntity, JSONObject.class).getBody();
            log.info("查询结果：{}", responseEntity);
            if (responseEntity != null && responseEntity.get("rows")!= null) {
                List<YchValue> ychValueList = convertToYchValue(responseEntity.getJSONArray("rows"));
                log.info("会员卡余额查询转化结果：{}", responseEntity.getJSONArray("rows"));
                log.info("会员卡余额查询结果：{}", ychValueList);
                for (YchValue ychValue1 : ychValueList) {
                    // 根据业务主键查询是否存在
                    Example example2 = new Example(YchValue.class);
                    example2.createCriteria()
                            .andEqualTo("leaguerId", existingLeaguer.getLeaguerId())
                            .andEqualTo("businessId", existingLeaguer.getBusinessId());
                    YchValue existingValue = ychValueMapper.selectOneByExample(example2);
                    if (existingValue != null) {
                        ychValue1.setId(existingValue.getId());
                        ychValue1.setBusinessId(existingLeaguer.getBusinessId());
                        ychValue1.setLeaguerId(existingValue.getLeaguerId());
                        ychValue1.setMobile(phone);
                        ychValueMapper.updateByPrimaryKey(ychValue1);
                    } else {
                        ychValue1.setLeaguerId(existingLeaguer.getLeaguerId());
                        ychValue1.setBusinessId(existingLeaguer.getBusinessId());
                        ychValue1.setMobile(phone);
                        ychValueMapper.insertSelective(ychValue1);
                    }
                }
                Example example1 = new Example(YchValue.class);
                example1.createCriteria()
                        .andEqualTo("leaguerId", existingLeaguer.getLeaguerId())
                        .andEqualTo("businessId", existingLeaguer.getBusinessId());
                YchValue ychValue = ychValueMapper.selectOneByExample(example1);

                // 更新会员表
                log.info("更新会员表-余额数据");
                if(ObjectUtil.isNotEmpty(ychValue.getRemainAmount())){
                    Example example2 = new Example(ByCustUser.class);
                    example2.createCriteria()
                            .andEqualTo("mobile",phone);
                    example2.setOrderByClause("id desc limit 1");
                    ByCustUser byCustUser = byCustUserMapper.selectOneByExample(example2);
                    BigDecimal remainAmount = new BigDecimal(ychValue.getRemainAmount());
                    byCustUser.setMemberBalance(remainAmount);
                    byCustUser.setMemberBusinessId(existingLeaguer.getBusinessId());
                    byCustUser.setMemberCardId(ychValue.getLeaguerId());
                    byCustUser.setMemberCardCode(existingLeaguer.getLeaguerCode());
                    byCustUserMapper.updateByPrimaryKey(byCustUser);
                }else{
                    log.info("更新会员表-余额数据-未找到余额信息");
                }

                log.info("更新会员表-余额数据-完成");

                return ychValue;
            } else {
                return new YchValue();
            }
        } catch (Exception e) {
            log.error("会员余额查询异常", e);
            return null;
        }
    }

    /**
     * 获取会员余额记录
     */
    @Override
    public List<YchOrder> GetLeaguerValuesLog() {
        // 先根据当前用户，获取会员卡信息，然后再根据会员卡 ID 获取余额
        ByCustUser user = SecurityContext.getUser();
        ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("phone", byCustUser.getMobile());
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
        if(existingLeaguer == null) {
            existingLeaguer = registerLeaguer(byCustUser.getMobile());
        }

        Example example1 = new Example(YchOrder.class);
        example1.createCriteria()
                .andEqualTo("leaguerId", existingLeaguer.getLeaguerId())
                .andEqualTo("payState", 1);

        List<YchOrder> ychOrderList = ychOrderMapper.selectByExample(example1);
        ychOrderList.forEach(ychOrder -> {
            Example example2 = new Example(YchOrderItem.class);
            example2.createCriteria()
                    .andEqualTo("orderId", ychOrder.getId());
            ychOrder.setOrderItem(ychOrderItemMapper.selectByExample(example2));
        });
        return ychOrderList;

        // 可以直接根据会员卡 ID 直接查询充值订单，不需要在跑到油菜花查询一遍
//        ByCustUser user = SecurityContext.getUser();
//        ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
//        Example example = new Example(YchLeaguer.class);
//        example.createCriteria()
//                .andEqualTo("phone", byCustUser.getMobile());
//        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
//        if(existingLeaguer == null) {
//            existingLeaguer = registerLeaguer(user.getMobile());
//        }
//        String url = ExternalApiConstants.GET_LEAGUER_VALUES_LOG_BY_PAGE;
//
//        try {
//            // 准备请求参数
//            Map<String, Object> params = new HashMap<>();
//            params.put("AppID", ExternalApiConstants.APP_ID);
//            params.put("BussinessID", ExternalApiConstants.BUSINESS_ID);
//            params.put("TS", String.valueOf(System.currentTimeMillis()));
//
//            params.put("LeaguerID", existingLeaguer.getLeaguerId());
//            params.put("ValueCode", "1");
//            //开始时间=当前时间减一年
//            params.put("BeginTime", LocalDateTime.now().minusDays(90).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//            params.put("EndTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//            params.put("PageSize", "1");
//            params.put("PageIndex", "9999");
//
//            // 生成签名
//            String sign = signUtil.generateSign(params);
//            params.put("Sign", sign);
//
//            // 发送POST请求
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
//
//            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
//            JSONObject responseEntity = restTemplate.postForEntity(
//                    url, requestEntity, JSONObject.class).getBody();
//
//            if (responseEntity != null && responseEntity.get("rows")!= null) {
//                return convertToYchValueLog(responseEntity.getJSONArray("rows"));
//            } else {
//                return new ArrayList<YchValueLog>();
//            }
//        } catch (Exception e) {
//            return null;
//        }
    }

    @Override
    public YchOrder createOrder(YchGoods ychGoods,String OrderNo,String mobile,String remark) {
        YchOrder ychOrder = convertToYchOrder(ychGoods, OrderNo, mobile, remark);

        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("leaguerId", ychOrder.getLeaguerId());
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);

        String url = ychBusinessService.getFullApiUrl(existingLeaguer.getBusinessId(), ExternalApiConstants.CREATE_ORDER);
        // 准备请求参数
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("AppID", ExternalApiConstants.APP_ID);
        params.put("BussinessID", existingLeaguer.getBusinessId());
        params.put("TPOrderNo", ychOrder.getTPOrderNo());
        params.put("TS", String.valueOf(System.currentTimeMillis()));
        params.put("GoodsType", ychOrder.getGoodsType());
        params.put("LeaguerID", ychOrder.getLeaguerId());
        params.put("GuestName", ychOrder.getGuestName());
        params.put("GuestMobile", ychOrder.getGuestMobile());
        BigDecimal OrderMoney = BigDecimal.valueOf(ychOrder.getOrderMoney());
        params.put("OrderMoney", OrderMoney.setScale(2, RoundingMode.HALF_UP));
        params.put("SendAddress", ychOrder.getSendAddress());
        params.put("Summary", ychOrder.getSummary());
        params.put("CouponNumber", ychOrder.getCouponNumber());
        params.put("IsOutPrice", ychOrder.getIsOutPrice());
        params.put("OrderType", ychOrder.getOrderType());
        params.put("IsNotUseCoupon", ychOrder.getIsNotUseCoupon().equals(0) ? "False" : "True");

        YchOrderItem ychOrderItem = ychOrder.getOrderItem().get(0);
        LinkedHashMap<String, Object> params1 = new LinkedHashMap<>();
        params1.put("GoodsID", ychOrderItem.getGoodsId());
        params1.put("GoodsName", ychOrderItem.getGoodsName());
        BigDecimal goodsPrice = BigDecimal.valueOf(ychOrderItem.getGoodsPrice());
        params1.put("GoodsPrice", goodsPrice.setScale(2, RoundingMode.HALF_UP));
        params1.put("Amount", ychOrderItem.getAmount());
        params1.put("ExtendedContent", ychOrderItem.getExtendedContent());
        params1.put("Summary", ychOrderItem.getSummary());
        params.put("OrderItem", JSON.toJSONString(Collections.singletonList(params1)));

        // 生成签名
        String sign = signUtil.generateSign(params);
        params.put("Sign", sign);
        params.remove("OrderItem");
        params.put("OrderItem", Collections.singletonList(params1));

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        log.info("更新创建订单接口请求参数：{}", params);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        JSONObject responseEntity = restTemplate.postForEntity(
                url, requestEntity, JSONObject.class).getBody();
        log.info("更新创建订单接口返回参数：{}", responseEntity);
        if (responseEntity != null && responseEntity.get("OrderNo") != null) {
            ychOrder.setOrderNo(responseEntity.get("OrderNo").toString());
            ychOrder.setBusinessId(existingLeaguer.getBusinessId());
            ychOrder.setGmtCreate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ychOrderMapper.insert(ychOrder);
            Example example1 = new Example(YchOrder.class);
            example1.createCriteria()
                    .andEqualTo("TPOrderNo", ychOrder.getTPOrderNo());
            YchOrder ychOrder1 = ychOrderMapper.selectOneByExample(example1);
            if (ychOrder1.getId() == null) {
                throw new RuntimeException("插入订单失败");
            }
            ychOrder.getOrderItem().get(0).setOrderId(ychOrder1.getId().toString());
            ychOrderItemMapper.insert(ychOrder.getOrderItem().get(0));
            Example example2 = new Example(YchOrder.class);
            example2.createCriteria()
                    .andEqualTo("TPOrderNo", ychOrder.getTPOrderNo());
            return ychOrderMapper.selectOneByExample(example2);
        }
        return null;
    }

    /**
     * 创建订单
     */
    @Override
    public YchOrder createOrder(YchGoods ychGoods,String OrderNo) {
       createOrder(ychGoods,OrderNo,"","");
       return null;
    }

    /**
     * 更新订单状态
     */
    @Override
    public boolean updatePaid(String TPOrderNo, String transactionId) {

        //根据订单号查询油菜花充值订单
        YchOrder ychOrder = ychOrderMapper.findByPayOrderNo(TPOrderNo);

        //更新他的状态
        if (ObjectUtil.isNotEmpty(ychOrder.getPayState()) && ychOrder.getPayState() == 1) {
            log.error("订单号:{},目前的状态是:{},支付成功的回调在尝试改变订单状态", TPOrderNo, "已支付");
            return false;
        }

        //更新油菜花订单状态为完成
        String url = ychBusinessService.getFullApiUrl(ychOrder.getBusinessId(), ExternalApiConstants.ORDER_PAY_AND_COMPLETED);
        log.info("更新油菜花订单状态接口地址：{}", url);

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("AppID", ExternalApiConstants.APP_ID);
        params.put("BussinessID", ychOrder.getBusinessId());
        params.put("TS", String.valueOf(System.currentTimeMillis()));

        params.put("TPOrderNo", ychOrder.getTPOrderNo());
        params.put("PayName", "1");
        params.put("AccountNumber", wechatPayProperties.getMchNo());
        params.put("OnLineOrder", transactionId);

        // 生成签名
        String sign = signUtil.generateSign(params);
        params.put("Sign", sign);

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        log.info("更新油菜花订单状态接口参数：{}", params);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        JSONObject responseEntity = restTemplate.postForEntity(
                url, requestEntity, JSONObject.class).getBody();
        log.info("更新油菜花订单状态接口返回：{}", responseEntity);

        YchOrder upOrder = new YchOrder();
        upOrder.setId(ychOrder.getId());
        upOrder.setPayState(1);
        upOrder.setPayFlowNo(transactionId);
        upOrder.setPayTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        upOrder.setGmtModified(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ychOrderMapper.updateByPrimaryKeySelective(upOrder);

        return true;
    }

    /**
     * 余额支付-变更余额
     * @return
     */
    @Override
    public boolean LeaguerPrepaidChange(ByOrders byOrders){
        // 先根据当前用户，获取会员卡信息，然后再根据会员卡 ID 获取余额
        ByCustUser user = SecurityContext.getUser();
        ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("phone", byCustUser.getMobile());
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
        if(existingLeaguer == null) {
            existingLeaguer = registerLeaguer(byCustUser.getMobile());
        }
        //更新油菜花订单状态为完成
        String url = ychBusinessService.getFullApiUrl(existingLeaguer.getBusinessId(), ExternalApiConstants.LEAGUER_PREPAID_CHANGE);
        log.info("请求地址：{}",url);
        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("TransID", byOrders.getPayFlowNo());
        params.put("Summary", "");
        params.put("ChangeValue", String.valueOf(byOrders.getActualAmount().floatValue() * -1f));
        params.put("LeaguerPrepaidChangeType", "BonusMoney");
        params.put("TS", String.valueOf(System.currentTimeMillis()));
        params.put("LeaguerID", existingLeaguer.getLeaguerId());
        params.put("AppID", ExternalApiConstants.APP_ID);
        params.put("BussinessID", existingLeaguer.getBusinessId());

        // 生成签名
        String sign = signUtil.generateSign(params);
        params.put("Sign", sign);

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        log.info("请求参数：{}", params);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        JSONObject responseEntity = restTemplate.postForEntity(
                url, requestEntity, JSONObject.class).getBody();
        log.info("返回参数：{}", responseEntity);
        return true;
    }

    /**
     * 余额支付-变更余额
     * @return
     */
    @Override
    public boolean LeaguerPrepaidChange(float changeValue,String leaguerID,String leaguerPrepaidChangeType){
        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("leaguerId", leaguerID);
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
        //更新油菜花订单状态为完成
        String url = ychBusinessService.getFullApiUrl(existingLeaguer.getBusinessId(), ExternalApiConstants.LEAGUER_PREPAID_CHANGE);
        log.info("请求地址：{}",url);
        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("TransID", UUIDOrder.getUUID());
        params.put("Summary", "");
        params.put("ChangeValue", String.valueOf(changeValue));
        params.put("LeaguerPrepaidChangeType", "BonusMoney");
        params.put("TS", String.valueOf(System.currentTimeMillis()));
        params.put("LeaguerID", existingLeaguer.getLeaguerId());
        params.put("AppID", ExternalApiConstants.APP_ID);
        params.put("BussinessID", existingLeaguer.getBusinessId());

        // 生成签名
        String sign = signUtil.generateSign(params);
        params.put("Sign", sign);

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        log.info("请求参数：{}", params);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        JSONObject responseEntity = restTemplate.postForEntity(
                url, requestEntity, JSONObject.class).getBody();
        log.info("返回参数：{}", responseEntity);
        return true;
    }

    @Override
    public JSONObject orderReturn(String TPOrderNo) {
        Example example = new Example(YchOrder.class);
        example.createCriteria()
                .andEqualTo("TPOrderNo", TPOrderNo)
                .orEqualTo("orderNo", TPOrderNo);
        YchOrder ychOrder = ychOrderMapper.selectOneByExample(example);
        if(ObjectUtil.isEmpty(ychOrder)){
            return null;
        }
        //更新油菜花订单状态为退回
        String url = ychBusinessService.getFullApiUrl(ychOrder.getBusinessId(), ExternalApiConstants.ORDER_RETURN);
        log.info("请求地址：{}",url);
        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("TPOrderNo", ychOrder.getTPOrderNo());
        params.put("TS", String.valueOf(System.currentTimeMillis()));
        params.put("AppID", ExternalApiConstants.APP_ID);
        params.put("BussinessID", ychOrder.getBusinessId());

        // 生成签名
        String sign = signUtil.generateSign(params);
        params.put("Sign", sign);

        // 发送POST请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        log.info("请求参数：{}", params);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        JSONObject responseEntity = restTemplate.postForEntity(
                url, requestEntity, JSONObject.class).getBody();
        log.info("返回参数：{}", responseEntity);
        return responseEntity;
    }

    /**
     * 创建订单
     * @param ychGoods
     * @param mobile 如果为空，则取当前用户手机号
     * @return
     */
    private YchOrder convertToYchOrder(YchGoods ychGoods,String orderNo,String mobile,String remark) {
        ByCustUser byCustUser = null;
        if(StringUtils.isEmpty(mobile)){
            ByCustUser user = SecurityContext.getUser();
            byCustUser = userService.queryUserInfo(user.getWxOpenId());
            mobile = byCustUser.getMobile();
        }else{
            Example query = new Example(ByCustUser.class);
            query.createCriteria()
                    .andEqualTo("mobile", mobile);
            byCustUser = byCustUserMapper.selectOneByExample(query);
        }

        Example example = new Example(YchLeaguer.class);
        example.createCriteria()
                .andEqualTo("phone", byCustUser.getMobile());
        YchLeaguer existingLeaguer = ychLeaguerMapper.selectOneByExample(example);
        if(existingLeaguer == null) {
            existingLeaguer = registerLeaguer(mobile);
        }

        YchOrder ychOrder = new YchOrder();
        if(StringUtils.isEmpty(orderNo)){
            orderNo = "CZ" + UUIDOrder.getUUID();
        }
        ychOrder.setTPOrderNo(orderNo);
        ychOrder.setGoodsType(2);
        ychOrder.setLeaguerId(existingLeaguer.getLeaguerId());
        ychOrder.setGuestName(byCustUser.getNickName());
        ychOrder.setGuestMobile(byCustUser.getMobile());
        if(ychGoods.getPromoPrice() == null){
            ychOrder.setOrderMoney(ychGoods.getGoodsPrice());
        }else{
            ychOrder.setOrderMoney(ychGoods.getPromoPrice());
        }
        ychOrder.setSendAddress("");
        ychOrder.setSummary(remark);
        ychOrder.setCouponNumber("");
        ychOrder.setIsOutPrice("IN");
        ychOrder.setOrderType(1);
        ychOrder.setIsNotUseCoupon(0);
        YchOrderItem ychOrderItem = new YchOrderItem();
        ychOrderItem.setGoodsId(ychGoods.getGoodsId());
        ychOrderItem.setGoodsName(ychGoods.getGoodsName());
        ychOrderItem.setGoodsPrice(ychGoods.getGoodsPrice());
        ychOrderItem.setAmount(1f);
        ychOrderItem.setExtendedContent("");
        ychOrderItem.setSummary("");
        ychOrder.setOrderItem(Collections.singletonList(ychOrderItem));
        return ychOrder;
    }

    private List<YchValue> convertToYchValue(JSONArray jsonArray) {
        List<YchValue> ychValueList = new ArrayList<>();
        jsonArray.forEach(obj -> {
            JSONObject jsonObject = (JSONObject) obj;
            YchValue ychValue = new YchValue();
            ychValue.setValueId(jsonObject.getStr("ValueID"));
            ychValue.setBusinessName(jsonObject.getStr("BusinessName"));
            ychValue.setValueCode(jsonObject.getInt("ValueCode"));
            ychValue.setName(jsonObject.getStr("Name"));
            ychValue.setValue(jsonObject.getBigDecimal("Value", BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).toString());
            ychValue.setRemainAmount(jsonObject.getBigDecimal("RemainAmount", BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).toString());
            ychValue.setStatusDesc(jsonObject.getStr("StatusDesc"));
            ychValue.setStatus(jsonObject.getInt("Status"));
            ychValueList.add(ychValue);
        });
        return ychValueList;
    }

    private List<YchValueLog> convertToYchValueLog(JSONArray jsonArray){
        List<YchValueLog> ychValueLogList = new ArrayList<>();
        jsonArray.forEach(obj -> {
            JSONObject jsonObject = (JSONObject) obj;
            YchValueLog ychValueLog = new YchValueLog();
            ychValueLog.setLeaguerBaseId(jsonObject.getStr("LeaguerBaseID"));
            ychValueLog.setValueCode(jsonObject.getStr("ValueCode"));
            ychValueLog.setValueCodeName(jsonObject.getStr("ValueCodeName"));
            ychValueLog.setOrderTypeName(jsonObject.getStr("OrderTypeName"));
            ychValueLog.setLogTime(jsonObject.getStr("LogTime"));
            ychValueLog.setChangeAmount(jsonObject.getStr("ChangeAmount"));
            ychValueLog.setSummary(jsonObject.getStr("Summary"));
            ychValueLogList.add(ychValueLog);
        });
        return ychValueLogList;
    }
}
