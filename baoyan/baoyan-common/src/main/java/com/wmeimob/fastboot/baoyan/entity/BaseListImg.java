package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * 首页图片
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseListImg implements Serializable {
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 图片地址
     */
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 标题
     */
    @Column(name = "title")
    private String title;
    /**
     * 跳转类型id
     */
    @Column(name = "jump_type")
    private Integer jumpType;
    /**
     * 跳转content
     */
    @Column(name = "target")
    private String target;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 排版方式（1.横向排版2.二分之一排版）
     */
    @Column(name = "layout")
    private Integer layout;
    /**
     * 上下架 0下架 1上架
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;


}
