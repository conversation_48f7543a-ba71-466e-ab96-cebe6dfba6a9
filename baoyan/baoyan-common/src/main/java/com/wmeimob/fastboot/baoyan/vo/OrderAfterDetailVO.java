package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: OrderAfterDetailVO
 * @projectName baoyan
 * @description: 售后详情回显Detail
 * @date 2019/9/5 11:31
 */
public class OrderAfterDetailVO extends ByOrderAfter implements Serializable {


    public OrderAfterDetailVO() {
    }

    public OrderAfterDetailVO(ByOrderAfter orderAfter) {
        this.setId( orderAfter.getId() );
        this.setUserId( orderAfter.getUserId() );
        this.setOrderGoodsId( orderAfter.getOrderGoodsId() );
        this.setGoodsName( orderAfter.getGoodsName() );
        this.setGoodsImg( orderAfter.getGoodsImg() );
        this.setGoodsNum( orderAfter.getGoodsNum() );
        this.setGoodsPrice( orderAfter.getGoodsPrice() );
        this.setOrderNo( orderAfter.getOrderNo() );
        this.setAfterType( orderAfter.getAfterType() );
        this.setAfterAmount( orderAfter.getAfterAmount() );
        this.setAfterReason( orderAfter.getAfterReason() );
        this.setAfterImgs( orderAfter.getAfterImgs() );
        this.setAfterStatus( orderAfter.getAfterStatus() );
        this.setRefuseReason( orderAfter.getRefuseReason() );
        this.setAuditTime( orderAfter.getAuditTime() );
        this.setResouceType( orderAfter.getResouceType() );
        this.setGmtCreate( orderAfter.getGmtCreate() );
        this.setGmtUpdate( orderAfter.getGmtUpdate() );
        this.setRealRefund( orderAfter.getRealRefund() );
        this.setDetailId( orderAfter.getDetailId() );
        this.setReturnNum( orderAfter.getReturnNum() );
        this.setCustUserName( orderAfter.getCustUserName() );
        this.setMobile( orderAfter.getMobile() );
        this.setSearchName( orderAfter.getSearchName() );
        this.setStartTime( orderAfter.getStartTime() );
        this.setEndTime( orderAfter.getEndTime() );
        this.setReturnType( orderAfter.getReturnType() );
        this.setOrderId( orderAfter.getOrderId() );
        this.setProductType( orderAfter.getProductType() );
        this.setIsDel( orderAfter.getIsDel() );

    }
}
