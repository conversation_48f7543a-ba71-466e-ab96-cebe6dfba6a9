/*
 * ByOrderAfter.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Tue Jul 16 13:48:35 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_order_after")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByOrderAfter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 订单商品id
     */
    @Column(name = "order_goods_id")
    private Integer orderGoodsId;
    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 商品数量
     */
    @Column(name = "goods_num")
    private Integer goodsNum;
    /**
     * 商品单价
     */
    @Column(name = "goods_price")
    private BigDecimal goodsPrice;
    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 售后类型;0:仅退款
     */
    @Column(name = "after_type")
    private Integer afterType;
    /**
     * 退款金额
     */
    @Column(name = "after_amount")
    private BigDecimal afterAmount;
    /**
     * 售后原因
     */
    @Column(name = "after_reason")
    private String afterReason;
    /**
     * 售后图片
     */
    @Column(name = "after_imgs")
    private String afterImgs;
    /**
     * 售后状态;0:待审核,1:已通过,2:已拒绝
     */
    @Column(name = "after_status")
    private Integer afterStatus;
    /**
     * 拒绝原因
     */
    @Column(name = "refuse_reason")
    private String refuseReason;
    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;
    /**
     * 1:普通商品,2:拼团商品
     */
    @Column(name = "resouce_type")
    private Integer resouceType;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     *实际退款
     */
    @Column(name = "real_refund")
    private BigDecimal realRefund;

    @Column(name = "detail_id")
    public Integer detailId;

    /**
     * 用户申请退款的数量
     */
    @Transient
    private Integer returnNum;

    /**
     * 客户姓名
     */
    @Transient
    private String custUserName;
    /**
     * 手机
     */
    @Transient
    private String mobile;
    /**
     * 查询名称
     */
    @Transient
    private String searchName;
    @Transient
    private String startTime;
    @Transient
    private String endTime;
    /**
     * 默认 0重复购买
     */
    @Column(name = "return_type")
    private Integer returnType;
    @Transient
    private Integer orderId;
    /**
     * 商品类型 1 普通商品 3 次卡 2 联票 4 拼团
     */
    @Transient
    private Integer productType;
    @Column(name = "is_del")
    private Integer isDel;

}