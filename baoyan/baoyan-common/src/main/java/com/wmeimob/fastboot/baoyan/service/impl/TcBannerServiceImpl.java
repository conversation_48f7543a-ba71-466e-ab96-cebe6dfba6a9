package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.TcBannerMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.TcBannerService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import io.netty.util.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Service
public class TcBannerServiceImpl implements TcBannerService {
    @Resource
    private TcBannerMapper tcBannerMapper;
    @Resource
    private TcGoodsMapper tcGoodsMapper;
    @Override
    public List<TcBanner> findByCondition(TcBanner tcBanner) {
        Example example = new Example(TcBanner.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(tcBanner.getId())) {
            criteria.andEqualTo("id", tcBanner.getId());
        }
        if (!StringUtils.isEmpty(tcBanner.getImgUrl())) {
            criteria.andLike("imgUrl", StringUtils.fullFuzzy(tcBanner.getImgUrl()));
        }
        if (!StringUtils.isEmpty(tcBanner.getStatus())) {
            criteria.andEqualTo("status", tcBanner.getStatus());
        }
        if (!StringUtils.isEmpty(tcBanner.getJumpType())) {
            criteria.andEqualTo("jumpTypeId", tcBanner.getJumpType());
        }
        if (!StringUtils.isEmpty(tcBanner.getTarget())) {
            criteria.andLike("target", StringUtils.fullFuzzy(tcBanner.getTarget()));
        }
        if (!StringUtils.isEmpty(tcBanner.getSort())) {
            criteria.andEqualTo("sort", tcBanner.getSort());
        }
        if (!StringUtils.isEmpty(tcBanner.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", tcBanner.getGmtCreate());
        }
        if (!StringUtils.isEmpty(tcBanner.getGmtUpdate())) {
            criteria.andEqualTo("gmtUpdate", tcBanner.getGmtUpdate());
        }
        criteria.andEqualTo("isDel", 0);
        example.orderBy("id").desc();
        List<TcBanner> tcBanners = tcBannerMapper.selectByExample(example);
        return tcBanners;
    }
    @Override
    public TcBanner queryTcBannerById(Object id) {
        return tcBannerMapper.selectByPrimaryKey(id);
    }
    /**
     * 效验参数
     *
     * @param tcBanner
     */
    private void checkParam(TcBanner tcBanner) {
        if(null != tcBanner.getJumpType()){
            if (tcBanner.getJumpType().equals(1)){
                if (StringUtils.isEmpty(tcBanner.getTarget())){
                    throw new CustomException("选择商品ID");
                }
                //判断当前商品普通商品存在
                TcGoods tcGoods = tcGoodsMapper.queryById(Integer.parseInt(tcBanner.getTarget()));
                if (null==tcGoods){
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!tcGoods.getIsDeleted()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (tcBanner.getJumpType().equals(2)){
                if (StringUtils.isEmpty(tcBanner.getTarget())){
                    throw new CustomException("选择品类ID");
                }
            }
            if (tcBanner.getJumpType().equals(3)){
                if (StringUtils.isEmpty(tcBanner.getTarget())){
                    throw new CustomException("选择品牌ID");
                }
            }
            if (tcBanner.getJumpType().equals(4)){
                if (StringUtils.isEmpty(tcBanner.getTarget())){
                    throw new CustomException("选择标签ID");
                }
            }
            if (tcBanner.getImgUrl()==null||tcBanner.getImgUrl()==""){
                throw new CustomException("图片未上传");
            }
        }

    }
    @Override
    public void addTcBanner(TcBanner tcBanner) {
        tcBanner.setGmtCreate(new Date());
        checkParam(tcBanner);
        tcBannerMapper.insertSelective(tcBanner);
    }
    @Override
    public void removeTcBanner(Object id) {
        TcBanner tcBanner = new TcBanner();
        tcBanner.setId(Integer.parseInt(id.toString()));
        tcBanner.setIsDel(1);
        tcBannerMapper.updateByPrimaryKeySelective(tcBanner);
    }

    @Override
    public void modifyTcBanner(TcBanner tcBanner) {
        tcBanner.setGmtUpdate(new Date());
        checkParam(tcBanner);
        tcBannerMapper.updateByPrimaryKeySelective(tcBanner);
    }


}
