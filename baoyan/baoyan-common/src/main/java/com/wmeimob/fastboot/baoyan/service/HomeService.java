package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.vo.BaseAdvertisingVO;
import com.wmeimob.fastboot.baoyan.vo.ByArticlesVO;
import com.wmeimob.fastboot.baoyan.vo.ImgVO;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.rest.RestResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-06 16:15
 * @Version 1.0
 */
public interface HomeService {

    /**
     * 首页展示
     * @return
     */
    default Map<String, Object> showHome(){
        return null;
    };
    /**
    * @Description 首页活动
    * <AUTHOR>
    * @Date        2019-08-07 14:33
    * @Version    1.0
    */
    default List<BaseActivity> showActivity(){
        return null;
    };

    /**
    * @Description 文章详情
    * <AUTHOR>
    * @Date        2019-08-07 15:15
    * @Version    1.0
    */
    default ByArticle byarticle(Integer id){
        return null;
    };
    /**
    * @Description 分类
    * <AUTHOR>
    * @Date        2019-08-07 15:30
    * @Version    1.0
    */
    default List<BaseClassify> classification(){
        return null;
    };
    /**
     * @Description 联票商品
     * <AUTHOR>
     * @Date        2019-08-07 15:39
     * @Version    1.0
     */
    default List<ByTicketGoods> ticketGoods(){
        return null;
    };


    /**
    * @Description 此卡商品
    * <AUTHOR>
    * @Date        2019-08-07 16:07
    * @Version    1.0
    */
    default List<BySubCardGoods> cardGoods(){
        return null;
    };

    /**
     * 效验用户是否被禁用
     * @param user
     * @return
     */
    default  RestResult checkUserDisable(ByCustUser user,Integer type ,String ids){throw new NotImplementedException("checkUserDisable");};

    /**
     * 效验对应商品 上下架状态
     * @param jumpType
     * @param id
     * @return
     */
    default RestResult checkGoodsShelf(Integer jumpType, Integer id){throw new NotImplementedException("checkGoodsShelf");}

    /**
     * 广告-查询
     * @return
     */
    BaseAdvertisingVO advertising();

    /**
     * 头条文章-滚动列表
     * @return
     */
    List<ByArticlesVO> byArticles();

    /**
     * 首页-多图-查询
     * @return
     */
    List<ImgVO> imgs();

    String selectPrivacyAgreement();

    String selectPurchaseAgreement();

//    BaseBanner singlebanner(Object id);
}
