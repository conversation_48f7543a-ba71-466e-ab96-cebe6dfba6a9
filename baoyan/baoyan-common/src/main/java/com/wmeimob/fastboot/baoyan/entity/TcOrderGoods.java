package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 淘潮玩订单详情，订单商品表(TcOrderGoods)实体类
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TcOrderGoods implements Serializable {
    private static final long serialVersionUID = 392506367837297143L;
    /**
     * id自增，无意义
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**
     * 淘潮玩订单id
     */
    @Column(name = "tc_order_id")
    private Integer tcOrderId;
    /**
     * 淘潮玩订单号
     */
    private String tcOrderNo;
    /**
     * 淘潮玩商品id
     */
    private Integer tcGoodsId;
    /**
     * 淘潮玩商品名
     */
    private String tcGoodsName;
    /**
     * 淘潮玩商品图片
     */
    private String tcGoodsImg;
    /**
     * 商品数量
     */
    private Integer goodsCount;
    /**
     * 实付商品单价
     */
    private BigDecimal unitPrice;
    /**
     * 购买时的商品价格
     */
    private BigDecimal goodsPrice;
    /**
     * 订单状态:
     * -1订单超时自动取消 0-已关闭 1-待付款,
     * 2-已付款(待发货|待自提), 3-已发货, 4-已完成 5,已退款
     *
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 小程序是否能够申请售后
     * 未付款无法售后，售后过无法售后
     */
    private Boolean canAfter;
    /**
     * 退款数量，退款了多少个
     */
    private Integer refundCount;
    /**
     * 临时 实付单价，单位分
     */
    @Transient
    private Integer tempUnitPrice;


}