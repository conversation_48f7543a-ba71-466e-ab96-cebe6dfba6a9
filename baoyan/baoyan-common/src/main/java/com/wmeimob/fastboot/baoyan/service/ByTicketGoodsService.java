package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTicketGoods;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTicketGoodsService
 * @Description 联票商品表
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/
public interface ByTicketGoodsService extends CommonService<ByTicketGoods>{

    /**
     * 联票商品表查询
     * @param id
     * @return
     */
    default ByTicketGoods queryByTicketGoodsById(Integer id){throw new NotImplementedException("queryByTicketGoodsById");};

    /**
     * 联票商品表添加
     * @param  byTicketGoods
     * @return
     */
    default  void addByTicketGoods(ByTicketGoods byTicketGoods){throw new NotImplementedException("addByTicketGoods");};


    /**
     * 联票商品表删除
     * @param id
     * @return
     */
    default void removeByTicketGoods(Object id){throw new NotImplementedException("removeByTicketGoods");};


    /**
     * 联票商品表修改
     * @param byTicketGoods
     * @return
     */
    default void modifyByTicketGoods(ByTicketGoods byTicketGoods){throw new NotImplementedException("modifyByTicketGoods");};

    /**
     * 商品上下架
     * @param byTicketGoods
     */
    default void updateShelf(ByTicketGoods byTicketGoods){throw new NotImplementedException("updateShelf");};
}
