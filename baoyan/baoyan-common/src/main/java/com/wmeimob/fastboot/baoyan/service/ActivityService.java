package com.wmeimob.fastboot.baoyan.service;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.BaseClassify;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.core.rest.RestResult;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-16 17:35
 * @Version 1.0
 */
public interface ActivityService {
    default PageInfo showActivity(Integer pageIndex, Integer pageSize, String storeName,String str ,String str1,Set set){
        return null;
    };

    default PageInfo showProduct(Integer pageIndex, Integer pageSize, String productName,Integer id){
        return null;
    };

    default Map<String, Object> storeDetail(Integer id){
        return null;
    };

    default List<Map<String, Object>> showDate(Integer id, String date,String newDate){
        return null;
    };


    default RestResult insert(ByCustAppointment byCustAppointment){
        return null;
    };


    default PageInfo getClassList(Integer jumpType,Integer type,String goodsName,String longitude,String latitude,Integer pageIndex,Integer pageSize,String classId){
        return null;
    };

    default Set<Integer> showOrder(Integer orderId){
        return null;
    };
}
