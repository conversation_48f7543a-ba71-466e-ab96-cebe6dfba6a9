<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByTeamGoodsMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByTeamGoods" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
		<result property="sortNum" column="sort_num" jdbcType="INTEGER"/>
		<result property="stockNum" column="stock_num" jdbcType="INTEGER"/>
		<result property="teamPrice" column="team_price" jdbcType="DECIMAL"/>
		<result property="orgPrice" column="org_price" jdbcType="DECIMAL"/>
		<result property="teamPerson" column="team_person" jdbcType="INTEGER"/>
		<result property="teamNum" column="team_num" jdbcType="INTEGER"/>
		<result property="teamHour" column="team_hour" jdbcType="INTEGER"/>
		<result property="teamStatus" column="team_status" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="richId" column="rich_id" jdbcType="INTEGER"/>
		<result property="reason" column="reason" jdbcType="VARCHAR"/>
		<result property="autoHour" column="auto_hour" jdbcType="INTEGER"/>
		<result property="teamName" column="team_name" jdbcType="VARCHAR"/>
        <result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
        <result property="goodsBanner" column="goods_banner" jdbcType="VARCHAR"/>


    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo" id="VoResultMap" extends="BaseResultMap">
		<result property="firstName" column="firstName" jdbcType="VARCHAR"/>
		<result property="varietyId" column="variety_id" jdbcType="INTEGER"/>
		<result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsNumber" column="goods_number" jdbcType="VARCHAR"/>
		<result property="goodsBanner" column="goods_banner" jdbcType="VARCHAR"/>
		<result property="tagIds" column="tag_ids" jdbcType="VARCHAR"/>
		<result property="sales" column="sales" jdbcType="INTEGER"/>
		<result property="refund" column="refund" jdbcType="INTEGER"/>
		<result property="amount" column="amount" jdbcType="DECIMAL"/>
		<result property="teamId" column="teamId" jdbcType="INTEGER"/>
		<result property="saleNum" column="saleNum" jdbcType="INTEGER"/>
		<result property="id" column="id" jdbcType="INTEGER"/>
		<result property="teamName" column="team_name" jdbcType="VARCHAR"/>
	</resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo" id="GoodsResultMap" extends="BaseResultMap">
	</resultMap>

	<select id="forms" resultMap="VoResultMap">
		SELECT
			g.id,
			g.team_name ,
			i.goods_name goodsName,
			COUNT(o1.id) sales,
			SUM(o1.order_amount) amount,
		(SELECT COUNT(1) from by_order_after oa where oa.resouce_type=2 and oa.after_status=2 and oa.order_goods_id=g.id)as refund
		FROM
		by_team_goods g
		LEFT JOIN by_goods_info i ON i.id = g.goods_id
		LEFT JOIN by_team_order o1 ON g.id = o1.team_goods_id    AND o1.order_status in(2,3)
		WHERE
		1 = 1
		<if test="searchName != null and searchName != ''">
			AND ((g.team_name LIKE CONCAT('%',#{searchName},'%')) or(g.id LIKE CONCAT('%',#{searchName},'%')))
		</if>
		GROUP BY g.id
		ORDER BY g.id DESC
	</select>
    <select id="findByCondition" resultMap="VoResultMap">
		SELECT
			tg.*, rt.content AS richContent,
			c.saleNum as saleNum
		FROM
		by_team_goods tg
		LEFT JOIN by_rich_text rt ON rt.data_id = tg.id
		AND rt.data_type = 4
		LEFT JOIN (
		SELECT
			COUNT(tt.team_goods_id) saleNum,
			tt.team_goods_id
			FROM
			by_team_order tt
			WHERE
			tt.order_status in (2,3)
			GROUP BY
			tt.team_goods_id
		) c on c.team_goods_id =tg.id
    <where>
		tg.is_del=0
        <if test="searchName != null and searchName != ''">
            AND ((tg.team_name LIKE CONCAT('%',#{searchName},'%')) or (tg.id LIKE CONCAT('%',#{searchName},'%'))or (tg.goods_id LIKE CONCAT('%',#{searchName},'%')))
        </if>
    </where>
	</select>
	<select id="queryByTeamGoodsById" resultMap="VoResultMap">
		SELECT
		tg.*,
		rt.content as richContent
		FROM
		by_team_goods tg
		LEFT JOIN by_rich_text rt on rt.data_id=tg.id and rt.data_type=4
		<where>
			tg.is_del=0 AND  tg.id=#{id}

		</where>
	</select>

	<select id="selectList" resultMap="BaseResultMap">
		SELECT
	team.id,
	team.goods_id,
	team.sort_num,
	team.stock_num,
	team.team_price,
	team.org_price,
	team.team_person as team_num,
	team.team_num as team_person,
	team.team_hour,
	team.team_status,
	team.gmt_create,
	team.gmt_update,
	team.rich_id,
	team.is_del,
	team.reason,
	team.auto_hour,
	team.goods_img,
	team.goods_banner,
	team.team_name as team_name
FROM
	by_team_goods team
	left join by_goods_info info on info.id = team.goods_id
WHERE
	team.stock_num > 0
	AND team.is_del = 0
	AND team.team_status = 1
ORDER BY
	team.sort_num DESC
	</select>
	<update id="updateId" >
		update by_team_goods set stock_num = stock_num + 1 where id = #{id}
	</update>


	<select id="selectLists" resultType="com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo">
		SELECT
			team.id id,
			team.team_name goodsName,
			team.goods_img goodsImg,
			team.team_price sellPrice,
			team.org_price marketPrice
		FROM
			by_team_goods team
		left join by_goods_info info on info.id = team.goods_id
		WHERE
		team.stock_num > 0
		AND team.is_del = 0
		AND team.team_status = 1
		<if test="type == 1"> order by sort_num desc ,gmt_update DESC</if>
		<if test="type == 2"> order by team_price asc </if>
		<if test="type == 3"> order by team_price desc </if>
		<!--<if test="type == 4"> order by distance asc </if>-->
		<!--<if test="type == 5"> order by distance desc </if>-->
	</select>
</mapper>

