package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (ByClassifyRecommend)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:47
 */
public interface ByClassifyRecommendMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ByClassifyRecommend queryById(@Param("id")Integer id);



    /**
     * 通过实体作为筛选条件查询
     *
     * @param byClassifyRecommend 实例对象
     * @return 对象列表
     */
    List<ByClassifyRecommend> queryAll(ByClassifyRecommend byClassifyRecommend);

    List<ByClassifyRecommend> wxQueryAll();

    /**
     * 新增数据
     *
     * @param byClassifyRecommend 实例对象
     * @return 影响行数
     */
    int insert(ByClassifyRecommend byClassifyRecommend);

    /**
     * 修改数据
     *
     * @param byClassifyRecommend 实例对象
     * @return 影响行数
     */
    int update(ByClassifyRecommend byClassifyRecommend);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);
    int onAndOffShelves(@Param("id") Integer id , @Param("type") Boolean type);



}