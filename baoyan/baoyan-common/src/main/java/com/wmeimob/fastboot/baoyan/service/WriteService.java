package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-22 10:12
 * @Version 1.0
 */
public interface WriteService {
    default List<WriteOffCode> checkWrite(HttpServletResponse resp,String orderNo, Integer type){
        return null;
    };

    default Map<String, Object> check(Integer id, ByCustUser user){
        return null;
    };

    default WriteOffCodeLog writeGoods(Integer id, Integer count){
        return null;
    };
}
