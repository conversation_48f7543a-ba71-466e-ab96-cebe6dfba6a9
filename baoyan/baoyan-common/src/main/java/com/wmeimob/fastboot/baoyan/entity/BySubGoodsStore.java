package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "by_sub_goods_store")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BySubGoodsStore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品id
     */
    @Column(name = "sub_goods_id")
    private Integer subGoodsId;
    /**
     * 门店id
     */
    @Column(name = "store_id")
    private Integer storeId;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
}
