<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByOrderAfterMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrderAfter" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="userId" column="user_id" jdbcType="INTEGER"/>
		<result property="orderGoodsId" column="order_goods_id" jdbcType="INTEGER"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsNum" column="goods_num" jdbcType="INTEGER"/>
		<result property="goodsPrice" column="goods_price" jdbcType="DECIMAL"/>
		<result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
		<result property="afterType" column="after_type" jdbcType="INTEGER"/>
		<result property="afterAmount" column="after_amount" jdbcType="DECIMAL"/>
		<result property="afterReason" column="after_reason" jdbcType="VARCHAR"/>
		<result property="afterImgs" column="after_imgs" jdbcType="VARCHAR"/>
		<result property="afterStatus" column="after_status" jdbcType="INTEGER"/>
		<result property="refuseReason" column="refuse_reason" jdbcType="VARCHAR"/>
		<result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
		<result property="resouceType" column="resouce_type" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="realRefund" column="real_refund" jdbcType="DECIMAL"/>
		<result property="detailId" column="detail_id" jdbcType="INTEGER"/>
		<result property="returnType" column="return_type" jdbcType="INTEGER"/>
		<result property="isDel" column="is_del" jdbcType="INTEGER"/>
    </resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByOrderAfter" id="BaseResultMap2" extends="BaseResultMap">
		<result property="custUserName" column="cust_user_name" jdbcType="VARCHAR"/>
		<result property="mobile" column="mobile" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="queryAfterByCondition" resultMap="BaseResultMap2">
      	SELECT
			b.*,
			u.nick_name  as cust_user_name,
			u.mobile
		FROM
			by_order_after b
		LEFT JOIN by_cust_user u ON u.id = b.user_id
        <where>
			and b.is_del=0
            <if test="afterType !=null">
                and b.after_type = #{afterType}
            </if>
			<if test="userId !=null">
				and b.user_id = #{userId}
			</if>
            <if test="startTime != null and startTime != ''">
                AND DATE_FORMAT(b.gmt_create,'%Y-%m-%d')>= #{startTime}
            </if>
			<if test="afterStatus != null">
				AND b.after_status = #{afterStatus}
			</if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(b.gmt_create,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName !=null and searchName !='' ">
                and ((b.order_no like concat('%',#{searchName},'%')) or  (u.nick_name like concat('%',#{searchName},'%'))
                or (u.mobile like concat('%',#{searchName},'%')))
            </if>
        </where>
        order by b.id desc
	</select>

</mapper>

