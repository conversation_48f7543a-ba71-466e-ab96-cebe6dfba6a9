package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByGoodsClassifyService
 * @Description 商品类型关联表
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/
public interface ByGoodsClassifyService extends CommonService<ByGoodsClassify>{

    /**
     * 商品类型关联表查询
     * @param id
     * @return
     */
    default ByGoodsClassify queryByGoodsClassifyById(Object id){throw new NotImplementedException("queryByGoodsClassifyById");};

    /**
     * 商品类型关联表添加
     * @param  byGoodsClassify
     * @return
     */
    default  void addByGoodsClassify(ByGoodsClassify byGoodsClassify){throw new NotImplementedException("addByGoodsClassify");};


    /**
     * 商品类型关联表删除
     * @param id
     * @return
     */
    default void removeByGoodsClassify(Object id){throw new NotImplementedException("removeByGoodsClassify");};


    /**
     * 商品类型关联表修改
     * @param byGoodsClassify
     * @return
     */
    default void modifyByGoodsClassify(ByGoodsClassify byGoodsClassify){throw new NotImplementedException("modifyByGoodsClassify");};
}
