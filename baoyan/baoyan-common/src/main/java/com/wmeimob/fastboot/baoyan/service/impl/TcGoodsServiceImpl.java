package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.TcComtion;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import com.wmeimob.fastboot.baoyan.mapper.TcComtionMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsStockService;
import com.wmeimob.fastboot.baoyan.service.TcLabelService;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * (TcGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Service("tcGoodsService")
@Slf4j
public class TcGoodsServiceImpl implements TcGoodsService {
    @Resource
    private TcGoodsMapper tcGoodsDao;

    @Resource
    private TcLabelService tcLabelService;
//    ProgramQRCode

    @Resource
    private TcGoodsStockService tcGoodsStockService;

    @Resource
    private TcComtionMapper tcComtionMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcGoods queryById(Integer id) {
        if (null == id) return null;
        return this.tcGoodsDao.queryById(id);
    }

    @Resource
    private QrCodeService qrCodeService;

    // 小程序二维码地址

    private static final String wxUrl = "pages/caowan/detail/main";

    /**
     * 新增商品
     */
    @Override
    public Boolean insert(HttpServletResponse response, TcGoods tcGoods) {
        if (null == tcGoods || null == tcGoods.getGoodsName()) throw new CustomException("参数不对");
        tcGoods.setSellCount(0L);
        tcGoods.setGmtCreate(new Date());
        tcGoods.setIsCoupon(null == tcGoods.getIsCoupon() ? false : tcGoods.getIsCoupon());
        tcGoods.setIsIntegral(null == tcGoods.getIsIntegral() ? false : tcGoods.getIsIntegral());
        boolean insert = tcGoodsDao.insert(tcGoods)>0;
        HashMap<String, Object> stringObjectHashMap = new HashMap<>(2);
        stringObjectHashMap.put("id", tcGoods.getId());
        String codeUrl = qrCodeService.exportQrCode(response, stringObjectHashMap, wxUrl);
        tcGoods.setCodeImg(codeUrl);
        tcGoodsDao.update(tcGoods);
        List<String> storeList = Arrays.asList(tcGoods.getStores().split(","));
        if (storeList.size() > 0) {
            for (String str : storeList) {
                TcComtion tcComtion=new TcComtion();
                tcComtion.setTcGoodsId(tcGoods.getId());
                tcComtion.setTcShopId(Integer.valueOf(str));
                tcComtionMapper.insertSelective(tcComtion);
            }
        } else {
            throw new CustomException("请选择门店");
        }
        return insert;
    }




    /**
     * 修改数据
     *
     * @param tcGoods 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcGoods tcGoods) {
        if (null == tcGoods || null == queryById(tcGoods.getId())) throw new CustomException("参数不对 ");
        List<String> storeList = Arrays.asList(tcGoods.getStores().split(","));
        if (null != storeList && !tcGoods.getStores().equals("") && storeList.size() > 0) {
            //删除原有的门店 关联
            Example example = new Example(TcComtion.class);
            example.createCriteria().andEqualTo("tcGoodsId", tcGoods.getId());
            tcComtionMapper.deleteByExample(example);
            for (String str : storeList) {
                TcComtion tcComtion=new TcComtion();
                tcComtion.setTcGoodsId(tcGoods.getId());
                tcComtion.setTcShopId(Integer.valueOf(str));
                tcComtionMapper.insertSelective(tcComtion);
            }
        } else {
            throw new CustomException("请选择门店");
        }
        return tcGoodsDao.update(tcGoods) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        TcGoods tcGoods = queryById(id);
        if (null == tcGoods) {
            throw new CustomException("未找到指定的商品");
        } else if (null == tcGoods.getStatus()) {
            throw new CustomException("商品无效！");
        }
        tcGoods.setStatus(false);
        return this.tcGoodsDao.update(tcGoods) > 0;
    }

    @Override
    public List<TcGoods> queryPage(TcGoods queryObject) {
        return tcGoodsDao.queryAll(loadQueryObject(queryObject));
    }


    private TcGoods loadQueryObject(TcGoods queryObject) {
        if (null != queryObject) {
            queryObject.setMaxPrice(
                    queryObject.getMaxPrice() == null ?
                            Double.MAX_VALUE : queryObject.getMaxPrice());
            queryObject.setMinPrice(
                    queryObject.getMinPrice() == null ?
                            0 : queryObject.getMinPrice());

            if (null != queryObject.getGmtCreate()) {
                Date gmtCreate = queryObject.getGmtCreate();
                Calendar c = Calendar.getInstance();
                c.setTime(gmtCreate);
                c.add(Calendar.DAY_OF_MONTH, 1);
                queryObject.setGmtCreateEnd(c.getTime());
            }
        }
        return queryObject;
    }

    @Override
    public Long queryAllCount(TcGoods queryObject) {
        return tcGoodsDao.queryAllCount(loadQueryObject(queryObject));
    }

    @Override
    public Boolean synchronizeInventory(Integer tcGoodsId, Integer stock) {
        TcGoods tcGoods = queryById(tcGoodsId);
        if (null == tcGoods) throw new CustomException("未找到指定的商品");
        TcGoodsStock tcGoodsStock = tcGoodsStockService.queryByGoodsId(tcGoodsId);
        if (null != tcGoodsStock) {
            tcGoods.setStock(stock);
            tcGoodsStock.setStock(stock);
            return update(tcGoods) && tcGoodsStockService.update(tcGoodsStock);
        }
        return update(tcGoods);
    }

    @Override
    public String queryWxCodeImg(HttpServletResponse resp, Integer goodsId, Boolean flag) {
        TcGoods tcGoods = queryById(goodsId);
        if (null == tcGoods) throw new CustomException("参数不对");
        if (StringUtils.isNotBlank(tcGoods.getCodeImg()) && !flag) return tcGoods.getCodeImg();
        HashMap<String, Object> objectObjectHashMap = new HashMap<>(2);
        objectObjectHashMap.put("id", tcGoods.getId());
        String url = qrCodeService.exportQrCode(resp, objectObjectHashMap, wxUrl);
        if (null == url) throw new CustomException("小程序二维码生成失败");
        log.info(" code--img =================== > {}", url);
        tcGoods.setCodeImg(url);
        update(tcGoods);
        return url;
    }
}