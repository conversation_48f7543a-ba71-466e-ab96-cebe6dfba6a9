package com.wmeimob.fastboot.baoyan.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @title: WxCustUserServiceImpl
 * @projectName baoyan
 * @description: WxCustUserServiceImpl
 * @date 2019/8/1 17:08
 */
@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WxCustUserServiceImpl implements WxCustUserService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private ByCouponTempMapper byCouponTempMapper;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private ByCouponUserMapper byCouponUserMapper;
    @Resource
    private BaseClassifyMapper baseClassifyMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;
    @Resource
    private ByCouponMapper byCouponMapper;
    @Resource
    private MemberCardService memberCardService;


    @Override
    public RestResult registerUser(String mobile, Integer storeId) {
        //判断当前手机号是否注册过
        ByCustUser user = SecurityContext.getUser();

        Example example = new Example(ByCustUser.class);
        example.createCriteria().andEqualTo("mobile", mobile)
                .andEqualTo("wxOpenId", user.getWxOpenId());
        List<ByCustUser> userList = this.byCustUserMapper.selectByExample(example);
        if (null != userList && userList.size() > 0) {
            return RestResult.success();
        }

        user.setMobile(mobile);
        if(ObjectUtil.isEmpty(user.getStoreId())){
            user.setStoreId(storeId);
        }

        int num = byCustUserMapper.updateByPrimaryKeySelective(user);
        if (num <= 0) {
            log.error("注册用户失败,请稍后再试");
            return RestResult.fail("注册用户失败,请稍后再试");
        }
        log.info("==============开始查询是否有绑定员工处理===============");
        //查询用户是否在门店下有维护员工
        Example storeStaffExample = new Example(ByStoreStaff.class);
        storeStaffExample.createCriteria().andEqualTo("isDel", 0).andEqualTo("staffPhone", user.getMobile());
        List<ByStoreStaff> byStoreStaffs = byStoreStaffMapper.selectByExample(storeStaffExample);
        if (byStoreStaffs.size() > 0) {
            ByStoreStaff storeStaff = byStoreStaffs.get(0);
            storeStaff.setOpenId(user.getWxOpenId());
            byStoreStaffMapper.updateByPrimaryKeySelective(storeStaff);
        }

        memberCardService.registerMemberCard(mobile);
        return RestResult.success();
    }

    @Override
    public Map<String, Object> completeUser(ByCustUser user) {
        InputValidator.checkEmpty(user.getParentName(), "家长姓名");
        InputValidator.checkEmpty(user.getParentPhone(), "家长手机号");
        InputValidator.checkEmpty(user.getChildName(), "孩子姓名");
        InputValidator.checkEmpty(user.getBirthday(), "孩子生日");
        if (null == user.getId()) {
            throw new CustomException("当前用户不存在");
        }
        int num = byCustUserMapper.updateByPrimaryKeySelective(user);
        if (num <= 0) {
            throw new CustomException("完善信息失敗");
        }
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
        Map<String, Object> data = new HashMap<>(1);
        //判断是否配置了优惠券
        if (null != byPlatformSet.getPerfectInfoCouponId()) {
            //查询优惠券表
            // ByCoupon coupon = byCouponMapper.selectByPrimaryKey(byPlatformSet.getPerfectInfoCouponId());
            // ByCouponTemp byCouponTemp = this.byCouponTempMapper.selectByPrimaryKey(coupon.getTempId());
            ByCouponTemp byCouponTemp = this.byCouponTempMapper.selectByPrimaryKey(byPlatformSet.getPerfectInfoCouponId());
            // ByCouponUser byCouponTemp=this.byCouponUserMapper.selectByPrimaryKey(byPlatformSet.getPerfectInfoCouponId());
            /*发放优惠券*/
            ByCouponUser byCouponUser = new ByCouponUser();
            byCouponUser.setUserId(user.getId());
            //set tempId
            byCouponUser.setCouponId(byCouponTemp.getId());
            byCouponUser.setTargetId(byCouponTemp.getTargetId());
            byCouponUser.setName(byCouponTemp.getName());
            byCouponUser.setDiscount(byCouponTemp.getDiscount());
            byCouponUser.setFull(byCouponTemp.getFull());
            //效验当前优惠券类型是否是天数  若是则增加对应天数
            if (byCouponTemp.getEffectiveType().equals(BaoYanConstant.CONSTANT_TWO)) {
                //计算时间 选择天数 增加
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + byCouponTemp.getDayNum());//让日期加天数 
                byCouponUser.setStartDate(new Date());
                byCouponUser.setEndDate(calendar.getTime());
            } else {
                byCouponUser.setStartDate(byCouponTemp.getStartDate());
                byCouponUser.setEndDate(byCouponTemp.getEndDate());
            }
            byCouponUser.setIsUse(0);
            byCouponUser.setGetType(2);
            byCouponUser.setGmtCreate(new Date());
            byCouponUser.setAuditStatus(1);
            byCouponUser.setCouponType(byCouponTemp.getCouponType());
            byCouponUser.setAuditStatus(CommonFinal.ONE);//审核通过
            byCouponUser.setIsGive(1);//赠送优惠卷
            byCouponUser.setType(byCouponTemp.getType());
            byCouponUser.setSingleGoodsType(byCouponTemp.getSingleGoodsType());
            this.byCouponUserMapper.insertSelective(byCouponUser);
            if (byCouponTemp.getType() == 1) {
                BaseClassify baseClassify = baseClassifyMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                byCouponTemp.setGoodsName(baseClassify == null ? null : baseClassify.getClassifyTitle());
            }
            if (byCouponTemp.getType() == 2) {
                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                byCouponTemp.setGoodsName(byGoodsInfo == null ? null : byGoodsInfo.getGoodsName());
            }
            data.put("byCouponTemp", byCouponTemp);
        } else {
            data.put("byCouponTemp", new ByCouponTemp());
        }
        return data;
    }

    @Override
    public ByCustUser findUserById(ByCustUser wechatUser) {

        if (wechatUser == null) {
            return null;
        }
        //将用户信息存到缓存中
        ByCustUser user = (ByCustUser) JSONObject.parseObject((String) this.stringRedisTemplate.opsForValue().get("USER" + wechatUser.getId()), ByCustUser.class);
        if (user == null) {
            user = (ByCustUser) this.byCustUserMapper.selectByPrimaryKey(wechatUser.getId());
            this.stringRedisTemplate.opsForValue().set("USER" + wechatUser.getId(), JSONObject.toJSONString(user));
        } else {
            user = (ByCustUser) this.byCustUserMapper.selectByPrimaryKey(wechatUser.getId());
        }
        return user;

    }

    @Override
    public ByCustUser queryUserInfo(String openId) {
        Example example = new Example(ByCustUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("wxOpenId", openId);
        List<ByCustUser> userList = byCustUserMapper.selectByExample(example);
        if (null != userList && userList.size() > 0) {
            return userList.get(0);
        }
        return null;

    }

    /**
     * @Description 保存授权信息
     * <AUTHOR>
     * @Date 2019-08-06 15:27
     * @Version 1.0
     */
    @Override
    public ByCustUser saveByCustUser(ByCustUser byCustUser) {
        int i = this.byCustUserMapper.insertSelective(byCustUser);
        return byCustUser;
    }

    @Override
    public Boolean updateByCustUserInformation(ByCustUser byCustUser){
       return byCustUserMapper.updateByCustUserInformation(byCustUser)>0;
    }


}
