
/*
* ByArticleMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 09 16:59:24 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.ByArticlesVO;
import com.wmeimob.fastboot.baoyan.vo.GoodsVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByArticle;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByArticleMapper extends Mapper<ByArticle> {
	/**
	 * 文章列表 list
 	 * @param byArticle
	 * @return
	 */
	List<ByArticle> selectArticleList(ByArticle byArticle);

	/**
	 * 查询全部商品
	 * @param byArticle
	 * @return
	 */
    List<GoodsVo> selectAllForGoods(@Param("qo") ByArticle byArticle);

	/**
	 * 查询除了自己滚动状态为开起的条数
	 * @param id
	 * @return
	 */
	Long selectOtherState(@Param("id")Integer id);

	/**
	 * 查询滚动文章
	 * @return
	 */
	List<ByArticlesVO> selectByState();

	/**
	 * 全部不滚动
	 * @return
	 * */
	int updateState();

	/**List<ByArticle> select(ByArticle byArticle);

	ByArticle selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(ByArticle byArticle);

	int updateByPrimaryKeySelective(ByArticle byArticle);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(ByArticle byArticle);*/
	
}