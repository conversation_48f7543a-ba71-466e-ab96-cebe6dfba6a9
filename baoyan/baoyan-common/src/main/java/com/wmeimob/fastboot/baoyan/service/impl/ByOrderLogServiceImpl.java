package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
import com.wmeimob.fastboot.baoyan.mapper.ByOrderGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByOrderLogMapper;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/11
 */
@Service
public class ByOrderLogServiceImpl implements ByOrderLogService {

    @Resource
    private ByOrderLogMapper orderLogMapper;

    @Resource
    private ByOrderGoodsMapper orderGoodsMapper;

    /**
     * 异步添加日志
     *
     * @param byOrderLog
     */
    @Override
    @Async
    public void addOrderLogAsync(ByOrderLog byOrderLog) {
        this.addOrderLog(byOrderLog);
    }

    /**
     * 添加一条订单日志
     * @param byOrderLog
     * @return
     */
    @Override
    public Boolean addOrderLog(ByOrderLog byOrderLog) {
        byOrderLog.setGmtCreate( new Date() );
        //如果有订单商品id，就去查询商品名
        if (byOrderLog.getOrderGoodsId()!=null){
            ByOrderGoods orderGoods = orderGoodsMapper.selectByPrimaryKey(byOrderLog.getOrderGoodsId());
            byOrderLog.setGoodsName(orderGoods.getGoodsName());
            byOrderLog.setOrderNo(orderGoods.getOrderNo());
        }

        return orderLogMapper.insertLog(byOrderLog)>0;
    }

    /**
     * 根据订单号查询订单日志
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<ByOrderLog> queryOrderLog(String orderNo) {

        return orderLogMapper.queryByOrderNo(orderNo);
    }
}
