<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByRichTextMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByRichText" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<id column="dataId" property="data_id" jdbcType="INTEGER" />
		<id column="dataType" property="data_type" jdbcType="INTEGER" />
		<result property="content" column="content" jdbcType="LONGVARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>
	<update id="updateByDataIdAndType">
	   UPDATE by_rich_text
		<trim prefix="set" suffixOverrides=",">
		<if test="content != null">
		`content` = #{content},
	    </if>
		<if test="gmtModified != null">
			`gmt_modified` = #{gmtModified},
		</if>
		</trim>
	    WHERE data_id=${dataId} AND data_type=${dataType}
	</update>


</mapper>

