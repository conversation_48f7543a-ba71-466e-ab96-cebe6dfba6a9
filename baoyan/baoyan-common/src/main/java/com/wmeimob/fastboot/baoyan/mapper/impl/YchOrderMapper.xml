<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.YchOrderMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.YchOrder" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result property="ychOrderId" column="ych_order_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="TPOrderNo" column="tp_order_no" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="goodsType" column="goods_type" jdbcType="INTEGER"/>
        <result property="leaguerId" column="leaguer_id" jdbcType="VARCHAR"/>
        <result property="isOutPrice" column="is_out_price" jdbcType="VARCHAR"/>
        <result property="isNotUseCoupon" column="is_not_use_coupon" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="guestName" column="guest_name" jdbcType="VARCHAR"/>
        <result property="guestMobile" column="guest_mobile" jdbcType="VARCHAR"/>
        <result property="orderMoney" column="order_money" jdbcType="DECIMAL"/>
        <result property="sendAddress" column="send_address" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="couponNumber" column="coupon_number" jdbcType="VARCHAR"/>
        <result property="payFlowNo" column="pay_flow_no" jdbcType="VARCHAR"/>
        <result property="payState" column="pay_state" jdbcType="INTEGER"/>
        <result property="payTime" column="pay_time" jdbcType="DATE"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="DATE"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="DATE"/>
    </resultMap>
    <select id="findByPayOrderNo" resultMap="BaseResultMap">
        select *
        from ych_order
        where tp_order_no = #{orderNo}
    </select>
</mapper>