package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.BaseListImg;
import com.wmeimob.fastboot.baoyan.qo.BaseListImgQo;
import com.wmeimob.fastboot.core.service.CommonService;
/**
 * 多图片控制
 * <AUTHOR>
 */
public interface BaseManyImgService extends CommonService<BaseListImg> {
    /**
     * 新增/修改多图设置
     * @param qo
     */
    void insertOrUpdate(BaseListImgQo qo);

    void updateById(BaseListImgQo qo);

    /**
     * 删除
     * @param id
     */
    void deleteById(Integer id);

    /**
     * 编辑查看
     * @param id
     * @return
     */
    BaseListImg select(Integer id);


}
