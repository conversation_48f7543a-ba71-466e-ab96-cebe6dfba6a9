package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCouponTemp;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByCouponTempService
 * @Description 优惠券模板
 * <AUTHOR>
 * @Date Thu Jul 11 17:55:47 CST 2019
 * @version1.0
 **/
public interface ByCouponTempService extends CommonService<ByCouponTemp>{

    /**
     * 优惠券模板查询
     * @param id
     * @return
     */
    default ByCouponTemp queryByCouponTempById(Object id){throw new NotImplementedException("queryByCouponTempById");};

    /**
     * 优惠券模板添加
     * @param  byCouponTemp
     * @return
     */
    default void addByCouponTemp(ByCouponTemp byCouponTemp){throw new NotImplementedException("addByCouponTemp");};


    /**
     * 优惠券模板删除
     * @param id
     * @return
     */
    default void removeByCouponTemp(Object id){throw new NotImplementedException("removeByCouponTemp");};


    /**
     * 优惠券模板修改
     * @param byCouponTemp
     * @return
     */
    default void modifyByCouponTemp(ByCouponTemp byCouponTemp){throw new NotImplementedException("modifyByCouponTemp");};
}
