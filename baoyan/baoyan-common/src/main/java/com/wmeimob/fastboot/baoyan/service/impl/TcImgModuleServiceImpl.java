package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.baoyan.mapper.TcImgModuleMapper;
import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import com.wmeimob.fastboot.baoyan.service.TcImgModuleService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (TcImgModule)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-02 21:22:18
 */
@Service("tcImgModuleService")
@SuppressWarnings("all")
public class TcImgModuleServiceImpl implements TcImgModuleService {
    @Resource
    private TcImgModuleMapper tcImgModuleMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TcImgModule queryById(Integer id) {
        if (null == id) return null;
        return this.tcImgModuleMapper.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param tcImgModule 实例对象
     * @return 实例对象
     */

    @Override
    public Boolean insert(TcImgModule tcImgModule) {
        loadTcImgModule(tcImgModule);
        return this.tcImgModuleMapper.insert(tcImgModule) > 0;
    }


    private TcImgModule loadTcImgModule(TcImgModule tcImgModule) {
        if (null == tcImgModule)
            throw new CustomException("参数错误");
        if (StringUtils.isBlank(tcImgModule.getImgUrl()))
            throw new CustomException("请上传图片");
        if (null == tcImgModule.getJumpType() || (tcImgModule.getJumpType() < 1 && tcImgModule.getJumpType() < 5))
            throw new CustomException("跳转类型选择错误");
        if (null == tcImgModule.getSort())
            throw new CustomException("请输入排序值");

        Integer id = tcImgModule.getId();

        if (null != id && null == queryById(id))
            throw new CustomException("参数错误");
        tcImgModule.setStatus(null == tcImgModule.getStatus() ? Boolean.FALSE : tcImgModule.getStatus());
        tcImgModule.setLayout(null == tcImgModule.getLayout() ? Boolean.TRUE : tcImgModule.getLayout());
        return tcImgModule;
    }

    /**
     * 修改数据
     *
     * @param tcImgModule 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(TcImgModule tcImgModule) {
        loadTcImgModule(tcImgModule);
        return this.tcImgModuleMapper.update(tcImgModule) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        TcImgModule tcImgModule = queryById(id);
        if( null == tcImgModule || tcImgModule.getIsDel())
            throw new CustomException("未找到指定图片板块");
        return this.tcImgModuleMapper.deleteById(id) > 0;
    }

    @Override
    public List<TcImgModule> queryAll(TcImgModule tcImgModule) {
        return tcImgModuleMapper.queryAll(tcImgModule);
    }

    @Override
    public Boolean onAndOffShelves(TcImgModule object) {

        if (null == object || null == object.getId() || null == object.getStatus())
            throw new CustomException("参数错误");
        TcImgModule tcImgModule = queryById(object.getId());

        if (null == tcImgModule)
            throw new CustomException("参数错误");
        tcImgModule.setStatus(object.getStatus());

        return update(tcImgModule);
    }
}