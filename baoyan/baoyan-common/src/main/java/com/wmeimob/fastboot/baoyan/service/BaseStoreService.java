package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName BaseStoreService
 * @Description 门店
 * <AUTHOR>
 * @Date Wed Jul 10 11:37:09 CST 2019
 * @version1.0
 **/
public interface BaseStoreService extends CommonService<BaseStore>{

    /**
     * 门店查询
     * @param id
     * @return
     */
    default BaseStore queryBaseStoreById(Object id){throw new NotImplementedException("queryBaseStoreById");};

    /**
     * 门店添加
     * @param  baseStore
     * @return
     */
    default  void addBaseStore(BaseStore baseStore){throw new NotImplementedException("addBaseStore");};


    /**
     * 门店删除
     * @param id
     * @return
     */
    default void removeBaseStore(Object id){throw new NotImplementedException("removeBaseStore");};


    /**
     * 门店修改
     * @param baseStore
     * @return
     */
    default void modifyBaseStore(BaseStore baseStore){throw new NotImplementedException("modifyBaseStore");};

    /**
     *查看当前核销码 具体核销门店
     * @param writeOffId
     * @return
     */
    default List<BaseStore> show(Integer writeOffId){
        return null;
    };

    default List<ByStoreStaff> showUser(Integer id){
        return null;
    };


    List<BaseStore> findStoreByTcId(Integer tcId);

    String generateQrCode(Integer id);
}
