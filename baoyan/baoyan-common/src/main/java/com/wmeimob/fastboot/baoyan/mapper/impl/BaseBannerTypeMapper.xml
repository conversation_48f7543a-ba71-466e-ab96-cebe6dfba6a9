<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseBannerTypeMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseBannerType" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="jumpTypeName" column="jump_type_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseBannerType">
		SELECT 
			`id`,
			`jump_type_name`
		FROM  base_banner_type
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="jumpTypeName != '' and jumpTypeName != null">
			  	AND `jump_type_name` = #{jumpTypeName}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`jump_type_name`
		FROM base_banner_type
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseBannerType" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO base_banner_type
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="jumpTypeName != '' and jumpTypeName != null">
			`jump_type_name`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="jumpTypeName != '' and jumpTypeName != null">
			#{jumpTypeName},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseBannerType">
		UPDATE base_banner_type
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="jumpTypeName != '' and jumpTypeName != null">
				`jump_type_name` = #{jumpTypeName},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM base_banner_type WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseBannerType">
		DELETE FROM base_banner_type
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="jumpTypeName != '' and jumpTypeName != null">
			  	AND `jump_type_name` = #{jumpTypeName}
			</if>
		</where>
	</delete> -->
</mapper>

