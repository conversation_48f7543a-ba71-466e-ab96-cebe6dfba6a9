<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByGoodsInfoMapper">
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
		<result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
		<result property="specName" column="spec_name" jdbcType="VARCHAR"/>
		<result property="sellPrice" column="sell_price" jdbcType="DECIMAL"/>
		<result property="marketPrice" column="market_price" jdbcType="DECIMAL"/>
		<result property="initialSaleNum" column="initial_sale_num" jdbcType="INTEGER"/>
		<result property="actualSalesNum" column="actual_sales_num" jdbcType="INTEGER"/>
		<result property="goodsStock" column="goods_stock" jdbcType="INTEGER"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
		<result property="goodsImg" column="goods_img" jdbcType="VARCHAR"/>
		<result property="goodsBanner" column="goods_banner" jdbcType="VARCHAR"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
		<result property="status" column="status" jdbcType="TINYINT"/>
		<result property="hasVerificationDay" column="has_verification_day" jdbcType="TINYINT"/>
		<result property="verificationDay" column="verification_day" jdbcType="INTEGER"/>
		<result property="regularDownStatus" column="regular_down_status" jdbcType="TINYINT"/>
		<result property="verificationStart" column="verification_start" jdbcType="DATE"/>
		<result property="verificationEnd" column="verification_end" jdbcType="DATE"/>
		<result property="marketingStart" column="marketing_start" jdbcType="DATE"/>
		<result property="marketingEnd" column="marketing_end" jdbcType="DATE"/>
		<result property="preDepositId" column="pre_deposit_id" jdbcType="INTEGER"/>
		<result property="firstVerifyBalance" column="first_verify_balance" jdbcType="DECIMAL"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="richId" column="rich_id" jdbcType="INTEGER"/>
		<result property="tcount" column="tcount" jdbcType="INTEGER"/>
		<result property="limited" column="limited" jdbcType="INTEGER"/>
		<result property="isCoupon" column="is_coupon" jdbcType="INTEGER"/>
		<result property="isIntegral" column="is_integral" jdbcType="INTEGER"/>
		<result property="buyNotice" column="buy_notice" jdbcType="VARCHAR"/>
		<result property="useNotice" column="use_notice" jdbcType="VARCHAR"/>
		<result property="codeImg" column="code_img" jdbcType="VARCHAR"/>

	</resultMap>
	<resultMap type="com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo" id="ListResultMap" extends="BaseResultMap">
		<result property="storeName" column="storeName" jdbcType="VARCHAR"/>
		<result property="goodsCount" column="goodsCount" jdbcType="INTEGER"/>
	</resultMap>

	<resultMap type="com.wmeimob.fastboot.baoyan.vo.ByGoodsInfoVO" id="GoodDetailMap" extends="BaseResultMap">
		<result property="richContent" column="richContent" jdbcType="VARCHAR"/>
	</resultMap>

	<select id="findByCondition" resultMap="ListResultMap">
		SELECT
		g.*,
		rt.content  as richContent,
		c.`name` as storeName,
		t.classifyName as classifyName,
		c.storeIds,
		t.classifyIds
		FROM
		by_goods_info g
		LEFT JOIN by_rich_text rt on rt.id=g.rich_id and rt.data_type=1
		LEFT JOIN (SELECT
		gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) storeIds
		FROM
		by_goods_store gs
		left JOIN base_store bs on gs.store_id =bs.id  where  bs.delete_status=0 GROUP BY gs.goods_id ) c on c.goods_id=g.id
		left JOIN
		(
		SELECT
		gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
		GROUP_CONCAT(bc.id) as classifyIds
		FROM
		by_goods_classify gc
		LEFT JOIN base_classify bc on bc.id=gc.classify_id
		where bc.is_del=0
		GROUP BY gc.goods_id
		) t ON t.goods_id=g.id
		<where>
			and g.is_del=0
			<if test="id !=null">
				AND	g.id = #{id}
			</if>
			<if test="goodsName !=null">
				AND	g.goods_name LIKE CONCAT('%',#{goodsName},'%')
			</if>
			<if test="storeId !=null">
				AND	FIND_IN_SET(#{storeId},c.storeIds)
			</if>
			<if test="classifyId !=null">
				AND	FIND_IN_SET(#{classifyId}, t.classifyIds)
			</if>
			<if test="startTime !=null and startTime!='' ">
				AND g.gmt_create&gt;=#{startTime}
			</if>
			<if test="endTime !=null and endTime!=''">
				AND g.gmt_create &lt;=DATE_ADD(#{endTime}, INTERVAL 1 DAY)
			</if>
			<if test="searchName !=null and searchName!=''">
				AND ((g.goods_name LIKE CONCAT('%',#{searchName},'%')) OR
				(g.id =#{searchName}))
			</if>
		</where>
		ORDER BY g.id desc
	</select>

	<select id="queryGoodDetailInfoById" resultMap="GoodDetailMap">
		SELECT
		g.*,
	    g.goods_tag as goodsTag,
		rt.content  as richContent,
		t.classifyIds as classifyIds,
		c.stores as stores
		FROM
		by_goods_info g
		LEFT JOIN by_rich_text rt on rt.data_id=g.id and rt.data_type=1
		LEFT JOIN (SELECT
		gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) as stores
		FROM
		by_goods_store gs
		left JOIN base_store bs on gs.store_id =bs.id GROUP BY gs.goods_id ) c on c.goods_id=g.id
		left JOIN
		(
		SELECT
		gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,GROUP_CONCAT(gc.classify_id) as classifyIds
		FROM
		by_goods_classify gc
		LEFT JOIN base_classify bc on bc.id=gc.classify_id
		GROUP BY gc.goods_id
		) t ON t.goods_id=g.id
		where g.id=#{id}
	</select>

	<select id="selectGoods" resultMap="BaseResultMap">
		select *,count(b.id) as 'subCardGoodsNum' from by_goods_info b left join by_ticket_goods_mapping m on  b.id = m.goods_id
		where m.ticket_goods_id =  #{id} group by b.id
	</select>

	<select id="selectGoodsByCombination" resultMap="BaseResultMap">
		select b.*,count(b.id) as 'subCardGoodsNum' from by_goods_info b left join by_combination_goods_mapping m on  b.id = m.goods_id
		where m.combination_goods_id =  #{id} group by b.id
	</select>

	<select id="selectProdoct" resultMap="BaseResultMap">
		SELECT
		b.*
		FROM
		by_goods_info b
		LEFT JOIN by_goods_store s ON s.goods_id = b.id
		WHERE
		b.is_del = 0
		AND b.STATUS = 1
		<if test="id != null and id  != ''">
			and s.store_id = #{id}
		</if>
		<if test="productName != null and productName != ''">
			and b.goods_name like concat("%",#{productName},"%")
		</if>
	</select>
	<select id="selectDistance" resultMap="BaseResultMap">
		SELECT actual_sales_num+initial_sale_num as goodsCount,info.*,
		(select ifnull(
		ROUND(
		6378.138 * 2 * ASIN(
		SQRT(
		POW(
		SIN(
		(
		#{latitude} * PI() / 180 - s.latitude * PI() / 180
		) / 2
		),
		2
		) + COS(#{latitude} * PI() / 180) * COS(s.latitude * PI() / 180) * POW(
		SIN(
		(
		#{longitude} * PI() / 180 - s.longitude * PI() / 180
		) / 2
		),
		2
		)
		)
		) * 1,1
		),0) AS distance
		from base_store s left join by_goods_store st on s.id = st.store_id
		where st.goods_id = info.id order by distance asc limit 1) as distance
		from by_goods_info info
		LEFT JOIN (SELECT
		gs.goods_id ,GROUP_CONCAT(bs.`name`) `name`,GROUP_CONCAT(bs.id) storeIds
		FROM
		by_goods_store gs
		left JOIN base_store bs on gs.store_id =bs.id  where  bs.delete_status = 0 and bs.`status`=1 GROUP BY gs.goods_id ) c on c.goods_id=info.id
		left JOIN
		(
		SELECT
		gc.goods_id,GROUP_CONCAT(bc.classify_title) as classifyName,
		GROUP_CONCAT(bc.id) as classifyIds
		FROM
		by_goods_classify gc
		LEFT JOIN base_classify bc on bc.id=gc.classify_id
		where bc.is_del=0
		GROUP BY gc.goods_id
		) t ON t.goods_id=info.id
		where info.is_del = 0

		and ((info.status = 1 and info.regular_down_status = 0) ||
		(info.status = 1 and info.regular_down_status = 1 and info.marketing_end &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d')) ||
		(info.status = 3 and info.regular_down_status = 0 and info.marketing_end &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d')) ||
		(info.status = 3 and info.regular_down_status = 1 and info.marketing_start &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d') and info.marketing_end &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d')))

		<if test="goodsName != null and goodsName != ''"> and info.goods_name like concat("%",#{goodsName},"%")</if>
		<if test="classId != null and classId != ''"> and  	FIND_IN_SET(#{classId}, t.classifyIds) </if>
		<if test="type == 1"> order by info.sort desc ,info.gmt_modified DESC</if>
		<if test="type == 2"> order by info.sell_price asc </if>
		<if test="type == 3"> order by info.sell_price desc </if>
		<if test="type == 4"> order by distance asc </if>
		<if test="type == 5"> order by distance desc </if>
		<if test="type == 6"> order by goodsCount asc </if>
		<if test="type == 7"> order by goodsCount desc </if>

	</select>
	<select id="showStore" resultType="com.wmeimob.fastboot.baoyan.entity.BaseStore">
		select store.id,store.name,store.mobile,store.address,store.business_time businessTime,store.longitude,store.latitude from base_store store left join by_goods_store goods  on store.id = goods.store_id where goods.goods_id = #{id}
	</select>
	<update id="updateByAddStock">
		update by_goods_info
		set goods_stock = goods_stock + #{productCount}
		where id = #{productId}
	</update>
</mapper>

