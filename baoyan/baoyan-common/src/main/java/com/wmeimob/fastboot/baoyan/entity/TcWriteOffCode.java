package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 淘潮玩核销码表(TcWriteOffCode)实体类
 *
 * <AUTHOR>
 * @since 2021-08-11 15:25:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TcWriteOffCode implements Serializable {
    private static final long serialVersionUID = -80045412421844759L;

    @Id
    private Integer id;
    /**
     * 核销卡名称
     */
    private String writeOffName;
    /**
     * 商品图片
     */
    @Transient
    private String goodsImg;
    /**
     * 用户id
     */
    private Integer custUserId;
    /**
     * 来源商品ID
     */
    private Integer goodsId;
    /**
     * 来源订单编号
     */
    private String orderNo;
    /**
     * 剩余次数
     */
    private Integer surplusNum;
    /**
     * 总次数
     */
    private Integer totalNum;
    /**
     * 状态(0 待处理 1已使用 2已过期 3已退款)
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 订单详情id
     */
    private Integer detailId;
    /**
     * 核销码图片链接
     */
    private String code;
    /**
     * 对应订单 的状态
     */
    @Transient
    private Integer orderStatus;
    /**
     *
     */
    @Transient
    private Integer detailState;
    /**
     * 后台管理系统进行搜索
      */
    @Transient
    private String searchName;
    /**
     * 用户名
     */
    @Transient
    private String nickName;
    /**
     * 用户手机号
     */
    @Transient
    private String mobile;

    /**
     * 核销时核销店铺id
     */
    @Transient
    private Integer storeIds;

    /**
     * 核销时员工id
     */
    @Transient
    private Integer staffId;
}