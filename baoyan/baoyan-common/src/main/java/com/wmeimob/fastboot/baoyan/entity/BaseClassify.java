/*
 * BaseClassify.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Fri Jul 12 10:13:14 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "base_classify")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BaseClassify implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 分类名称
     */
    @Column(name = "classify_title")
    private String classifyTitle;
    /**
     * 图片地址
     */
    @Column(name = "classify_img")
    private String classifyImg;
    /**
     * 是否逻辑删除;1:删除,0:未删除.
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 开启状态
     */
    @Column(name = "state")
    private Integer state;

    @Transient
    private List<ByGoodsInfo> byGoodsInfoList;

}