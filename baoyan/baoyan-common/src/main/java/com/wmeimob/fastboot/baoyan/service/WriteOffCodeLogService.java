package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName WriteOffCodeLogService
 * @Description 核销码记录表
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/
public interface WriteOffCodeLogService extends CommonService<WriteOffCodeLog>{

    /**
     * 核销码记录表查询
     * @param id
     * @return
     */
    default WriteOffCodeLog queryWriteOffCodeLogById(Object id){throw new NotImplementedException("queryWriteOffCodeLogById");};

    /**
     * 核销码记录表添加
     * @param  writeOffCodeLog
     * @return
     */
    default  void addWriteOffCodeLog(WriteOffCodeLog writeOffCodeLog){throw new NotImplementedException("addWriteOffCodeLog");};


    /**
     * 核销码记录表删除
     * @param id
     * @return
     */
    default void removeWriteOffCodeLog(Object id){throw new NotImplementedException("removeWriteOffCodeLog");};


    /**
     * 核销码记录表修改
     * @param writeOffCodeLog
     * @return
     */
    default void modifyWriteOffCodeLog(WriteOffCodeLog writeOffCodeLog){throw new NotImplementedException("modifyWriteOffCodeLog");};
}
