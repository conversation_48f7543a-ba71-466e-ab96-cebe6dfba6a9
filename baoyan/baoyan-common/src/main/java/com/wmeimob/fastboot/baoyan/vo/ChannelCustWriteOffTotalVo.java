package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ChannelCustWriteOffTotalVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 来源渠道
     */
    private String channelSourceName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 有效日期
     */
    private String endDate;

    /**
     * 核销门店
     */
    private String writeOffStoreName;

    /**
     * 未核销次数
     */
    private Integer unWriteOffCount;

    /**
     * 已核销次数
     */
    private Integer writeOffCount;
}
