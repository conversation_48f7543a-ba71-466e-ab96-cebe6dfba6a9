package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods;
import com.wmeimob.fastboot.core.service.CommonService;

/**
 * 商品规格管理
 * <AUTHOR>
 */
public interface ByCombinationGoodsService extends CommonService<ByCombinationGoods> {
    /**
     * 编辑
     * @param id
     * @return
     */
    ByCombinationGoods select(Integer id);

    /**
     * 新增/修改
     * @param byCombinationGoods
     */
    void insertOne(ByCombinationGoods byCombinationGoods);

    /**
     * 修改
     * @param byCombinationGoods
     */
    void UpdateById(ByCombinationGoods byCombinationGoods);

    /**
     * 删除
     * @param id
     */
    void deleteById(Integer id);


}
