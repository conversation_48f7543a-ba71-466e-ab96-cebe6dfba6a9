<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByCouponUserMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCouponUser" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="couponId" column="coupon_id" jdbcType="INTEGER"/>
        <result property="targetId" column="target_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="discount" column="discount" jdbcType="DECIMAL"/>
        <result property="full" column="full" jdbcType="DECIMAL"/>
        <result property="startDate" column="start_date" jdbcType="DATE"/>
        <result property="endDate" column="end_date" jdbcType="DATE"/>
        <result property="isUse" column="is_use" jdbcType="TINYINT"/>
        <result property="inBatch" column="in_batch" jdbcType="VARCHAR"/>
        <result property="getType" column="get_type" jdbcType="TINYINT"/>
        <result property="useDate" column="use_date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
        <result property="issuerUserId" column="issuer_user_id" jdbcType="INTEGER"/>
        <result property="isGive" column="is_give" jdbcType="INTEGER"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="refuseReason" column="refuse_reason" jdbcType="VARCHAR"/>

        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="canUse" column="canUse" jdbcType="TINYINT"/>

        <result property="issuerUserName" column="issuer_user_name" jdbcType="VARCHAR"/>
        <result property="auditName" column="audit_name" jdbcType="VARCHAR"/>
        <result property="auditUserId" column="audit_user_id" jdbcType="INTEGER"/>

        <result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>
        <result property="couponType" column="coupon_type" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="singleGoodsType" column="single_goods_type" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.ByCouponUser" id="couponUserResultMap" extends="BaseResultMap">
        <result property="limitation" column="limitation" jdbcType="VARCHAR"/>
        <result property="goodsName" column="goodsName" jdbcType="VARCHAR"/>


    </resultMap>

    <select id="findUserCoupon" resultType="com.wmeimob.fastboot.baoyan.entity.ByCouponUser">
        select *
        from by_coupon_user
        where id = #{id}
          and user_id = #{userId}
          and is_use = 0
          and audit_status = 1
          and now() &lt;= end_date
    </select>

    <select id="getCouponGetList" resultMap="BaseResultMap">
        SELECT c.id,c.`name`,c.start_date,c.end_date,c.gmt_create,c.is_use,c.get_type,c.use_date
        ,u.nick_name ,u.mobile,c.user_id
        FROM by_coupon_user c
        LEFT JOIN by_cust_user u ON u.id=c.user_id
        LEFT JOIN by_coupon cp ON cp.id=c.coupon_id
        WHERE 1=1
        <if test="isUse != null">
            AND c.is_use=${isUse}
        </if>
        <if test="getType != null">
            AND c.get_type=${getType}
        </if>
        <if test="auditStatus != null">
            AND c.audit_status=${auditStatus}
        </if>
        <if test="mobile != null and mobile != ''">
            AND u.mobile =#{mobile}
        </if>
        <if test="name != null and name != ''">
            AND (c.`name` LIKE CONCAT('%',#{name},'%') OR u.nick_name LIKE CONCAT('%',#{name},'%'))
        </if>
        <if test="startTime != null and startTime != ''">
            AND DATE_FORMAT(c.gmt_create,'%Y-%m-%d')>= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE_FORMAT(c.gmt_create,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        ORDER BY c.id DESC
    </select>
    <select id="queryAuditListByCouponUser" resultMap="couponUserResultMap">
        SELECT
        c.`id`,
        c.`name`,
        c.start_date,
        c.end_date,
        c.gmt_create,
        c.is_use,
        c.get_type,
        c.use_date,
        u.nick_name ,
        u.mobile,
        c.audit_status,
        c.audit_user_id,
        c.audit_time,
        su.nickname as audit_name,
        su2.nickname as issuer_user_name,
        c.gmt_create,
        IFNULL(c.refuse_reason,'')refuse_reason,
        CASE c.type
        when 1 THEN cc.`classify_title`
        when 2 THEN(
        CASE c.single_goods_type
        when 1 THEN g.goods_name
        when 2 THEN sub.sub_card_goods_name
        when 3 THEN ticket.ticket_goods_name
        END
        )
        END as goodsName,
        c.discount,
        c.`full`
        FROM
        by_coupon_user c
        LEFT JOIN by_cust_user u ON u.id = c.user_id
        LEFT JOIN by_coupon cp ON cp.id = c.coupon_id
        LEFT JOIN sys_user su on su.id=c.audit_user_id
        LEFT JOIN sys_user su2 on su2.id=c.issuer_user_id
        LEFT JOIN base_classify cc ON cc.id=c.target_id
        LEFT JOIN by_goods_info g ON g.id=c.target_id
        LEFT JOIN by_ticket_goods ticket ON ticket.id=c.target_id
        LEFT JOIN by_sub_card_goods sub ON sub.id=c.target_id
        WHERE
        1 = 1
        <if test="name != null and name != ''">
            AND (c.`name` LIKE CONCAT('%',#{name},'%') OR u.nick_name LIKE CONCAT('%',#{name},'%'))
        </if>
        <if test="auditStatus != null">
            AND c.audit_status=${auditStatus}
        </if>
        order by c.gmt_create desc
    </select>
    <select id="getCouponUserList" resultMap="couponUserResultMap">
        SELECT
        cu.name,
        cu.`full`,
        cu.start_date,
        cu.end_date,
        cu.discount,
        cu.coupon_type,
        CASE ct.type
        when 1 THEN CONCAT(c.`classify_title`,'类')
        when 2 THEN(
        CASE ct.single_goods_type
        when 1 THEN g.goods_name
        when 2 THEN sub.sub_card_goods_name
        when 3 THEN t.ticket_goods_name
        END
        )
        when 5 then tcg.goods_name
        when 6 THEN '全部淘潮玩'
        when 7 THEN '全部商品'
        when 0 THEN '全部门票'
        END as limitation,
        cu.coupon_id,
        cu.is_give,
        cu.type
        FROM
        by_coupon_user cu
        LEFT JOIN by_coupon bc ON bc.id=cu.coupon_id
        LEFT JOIN by_coupon_temp ct on bc.temp_id =ct.id
        LEFT JOIN base_classify c ON c.id=ct.target_id
        LEFT JOIN by_goods_info g ON g.id=ct.target_id
        LEFT JOIN by_ticket_goods t ON t.id=ct.target_id
        LEFT JOIN by_sub_card_goods sub ON sub.id=ct.target_id
        LEFT JOIN tc_goods tcg ON tcg.id=ct.target_id
        where cu.audit_status = 1
        <if test="userId != null">
            AND cu.user_id= #{userId}
        </if>
        <if test="isUse != null">
            AND cu.is_use= #{isUse}
        </if>
        order by cu.gmt_create desc
    </select>

    <select id="selectByCouponUser" resultMap="couponUserResultMap">
        SELECT
        c.*,
        ( SELECT classify_title FROM base_classify WHERE is_del = 0 AND id = c.target_id ) AS goodsName,
        CASE
        temp.type
        WHEN 1 THEN
        CONCAT( cc.`classify_title`, '类' )
        WHEN 2 THEN
        <if test="singleGoodsType == 1 or singleGoodsType == 3">
            info.goods_name
        </if>
        <if test="singleGoodsType == 2">
            sub.sub_card_goods_name
        </if>
        WHEN 0 THEN
        '全部门票'
        END AS limitation
        FROM
        by_coupon_user c
        LEFT JOIN by_coupon_temp temp ON temp.id = c.coupon_id
        LEFT JOIN base_classify cc ON cc.id = c.target_id
        <if test="singleGoodsType == 1">
            LEFT JOIN by_goods_classify b ON c.target_id = b.classify_id
            left join by_goods_info info on info.id = b.goods_id
        </if>
        <if test="singleGoodsType == 2">
            LEFT JOIN by_sub_goods_classify b ON c.target_id = b.classify_id
            left join by_sub_card_goods sub on sub.id = b.sub_goods_id
        </if>
        <if test="singleGoodsType == 3">
            LEFT JOIN by_goods_classify b ON c.target_id = b.classify_id
            LEFT JOIN by_goods_info info ON info.id = b.goods_id
            LEFT JOIN by_ticket_goods_mapping g ON g.goods_id = info.id
        </if>

        WHERE
        c.user_id = #{id}
        <if test="singleGoodsType == 1">
            AND info.id = #{id1}
        </if>
        <if test="singleGoodsType == 2">
            AND sub.id = #{id1}
        </if>
        <if test="singleGoodsType == 3">
            AND g.ticket_goods_id = #{id1}
        </if>
        AND is_use = 0
        AND audit_status = 1
        AND c.type = 1
    </select>


    <select id="selectByCouponUser2" resultMap="couponUserResultMap">
        SELECT DISTINCT c.*, c.type ,
                        (SELECT classify_title FROM base_classify WHERE is_del = 0 AND id = c.target_id) AS goodsName,
                        CASE c.type
                            WHEN 1 THEN CONCAT(cc.`classify_title`, '类')
                            WHEN 2 THEN g.goods_name
                            WHEN 5 THEN tcg.goods_name
                            WHEN 6 THEN '全部淘潮玩'
                            WHEN 7 THEN '全部商品'
                            WHEN 0 THEN '全部门票'
                            END AS limitation
        FROM by_coupon_user c
                 LEFT JOIN by_goods_classify b
                           ON c.target_id = b.classify_id
                 LEFT JOIN by_coupon bc ON bc.id = c.coupon_id
                 LEFT JOIN by_coupon_temp temp ON temp.id = bc.temp_id
                 LEFT JOIN base_classify cc ON cc.id = temp.target_id
                 LEFT JOIN by_goods_info g ON g.id = temp.target_id
                 LEFT JOIN tc_goods tcg ON tcg.id = temp.target_id
        WHERE c.user_id = #{id}
          AND c.target_id = #{id1}
          AND is_use = 0
          AND audit_status = 1
          and c.type = 2
          and c.single_goods_type = #{singleGoodsType}
    </select>

    <select id="selectCouponByType" resultMap="couponUserResultMap">
        SELECT cou.*,
       CASE cou.type
           when 0 THEN '全部门票'
           when 6 THEN '全部淘潮玩'
           when 7 THEN '全部商品'
           END as limitation
        FROM by_coupon_user cou
        WHERE cou.`user_id` = #{userId}
          AND cou.is_use = 0
          AND cou.audit_status = 1
          AND cou.end_date >= #{now}
          AND cou.`type` = #{type}
    </select>

    <select id="selectTcCoupon" resultMap="couponUserResultMap">
        SELECT cou.*,
        tcg.goods_name limitation
        FROM by_coupon_user cou
        JOIN tc_goods tcg ON tcg.id=cou.target_id
        WHERE cou.`user_id` = #{userId}
        AND cou.is_use = 0
        AND cou.audit_status = 1
        AND cou.end_date &gt;= #{now}
        AND cou.`type` = 5
        AND cou.`target_id` IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>


    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        c.*,
        (select classify_title from base_classify where is_del = 0 and id = c.target_id) as goodsName,
        CASE temp.type
        when 1 THEN CONCAT(cc.`classify_title`,'类')
        when 2 THEN g.goods_name
        when 5 then tcg.goods_name
        when 6 THEN '全部淘潮玩'
        when 7 THEN '全部商品'
        when 0 THEN '全部门票'
        END as limitation

        FROM
        by_coupon_user c
        LEFT JOIN by_goods_classify b ON c.target_id = b.classify_id
        left join by_coupon_temp temp on temp.id = c.coupon_id
        LEFT JOIN base_classify cc ON cc.id=temp.target_id
        LEFT JOIN by_goods_info g ON g.id=temp.target_id
        LEFT JOIN tc_goods tcg ON tcg.id=temp.target_id
        WHERE
        c.user_id = #{id}
        AND is_use = 0
        AND audit_status = 1
        and c.type = 1
        and b.classify_id in
        <foreach collection="set" item="id" open="(" close=")" index="index" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="checkCouponUserExpire" resultMap="BaseResultMap">
        SELECT t.*
        from by_coupon_user t
        where t.end_date &lt; CURRENT_DATE
    </select>

    <select id="selectCountByParam" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        by_coupon_user t
        <where>
            <if test="userId != null">
                AND t.user_id= #{userId}
            </if>
            <if test="time != null">
                AND DATE_FORMAT(t.gmt_create,'%Y-%m-%d')= #{time}
            </if>
            <if test="couponId != null">
                AND t.coupon_id= #{couponId}
            </if>
        </where>
    </select>
</mapper>

