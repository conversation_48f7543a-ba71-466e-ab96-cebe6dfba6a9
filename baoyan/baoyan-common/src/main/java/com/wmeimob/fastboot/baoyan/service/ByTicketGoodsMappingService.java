package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTicketGoodsMapping;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTicketGoodsMappingService
 * @Description 联票商品mapping表
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/
public interface ByTicketGoodsMappingService extends CommonService<ByTicketGoodsMapping>{

    /**
     * 联票商品mapping表查询
     * @param id
     * @return
     */
    default ByTicketGoodsMapping queryByTicketGoodsMappingById(Object id){throw new NotImplementedException("queryByTicketGoodsMappingById");};

    /**
     * 联票商品mapping表添加
     * @param  byTicketGoodsMapping
     * @return
     */
    default  void addByTicketGoodsMapping(ByTicketGoodsMapping byTicketGoodsMapping){throw new NotImplementedException("addByTicketGoodsMapping");};


    /**
     * 联票商品mapping表删除
     * @param id
     * @return
     */
    default void removeByTicketGoodsMapping(Object id){throw new NotImplementedException("removeByTicketGoodsMapping");};


    /**
     * 联票商品mapping表修改
     * @param byTicketGoodsMapping
     * @return
     */
    default void modifyByTicketGoodsMapping(ByTicketGoodsMapping byTicketGoodsMapping){throw new NotImplementedException("modifyByTicketGoodsMapping");};
}
