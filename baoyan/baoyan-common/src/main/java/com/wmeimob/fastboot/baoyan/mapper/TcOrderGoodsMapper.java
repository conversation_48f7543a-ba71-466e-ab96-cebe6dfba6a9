package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcOrderGoods;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 淘潮玩订单详情，订单商品表(TcOrderGoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-24 16:27:30
 */
public interface TcOrderGoodsMapper extends Mapper<TcOrderGoods> {

    /**
     * 通过 订单id查询数据
     * @param orderId 主键
     * @return 实例对象
     */
    List<TcOrderGoods> queryByOrderId(Integer orderId);

    /**
     * 将一个订单下的全部 订单详情的状态更新为已支付状态
     * @param orderId
     * @return
     */
    int updateToPaid(Integer orderId);

    /**
     * 将一个订单下的全部 订单详情的状态更新为指定状态
     * @param orderId
     * @param status
     * @return
     */
    int updateStatus(@Param("orderId") Integer orderId, @Param("status") Integer status);
}