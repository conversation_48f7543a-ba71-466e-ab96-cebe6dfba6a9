package com.wmeimob.fastboot.baoyan.utils;

import com.wmeimob.fastboot.baoyan.constant.ExternalApiConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component
public class SignUtil {
    /**
     * 生成签名
     *
     * @param params 参数
     * @return 签名
     */
    public String generateSign(Map<String, Object> params) {
        // 移除sign参数
        params.remove("sign");
        
        // 将所有参数名转为小写并排序
        TreeMap<String, Object> sortedParams = new TreeMap<>();
        params.forEach((key, value) -> sortedParams.put(key.toLowerCase(), value));
        
        // 拼接参数 (fieldname+fieldvalue+fieldname+fieldvalue)
        StringBuilder stringA = new StringBuilder();
        sortedParams.forEach((key, value) -> {
            String valueStr = value == null ? "" : value.toString();
            stringA.append(key).append(valueStr);
        });
        
        // 在stringA前后加上key
        String signStr = ExternalApiConstants.PRIVATE_KEY + stringA.toString() + ExternalApiConstants.PRIVATE_KEY;
        
        // MD5加密并转大写
        return DigestUtils.md5DigestAsHex(signStr.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }
    
    /**
     * 验证签名
     *
     * @param params    参数
     * @param sign      签名
     * @param timestamp 时间戳
     */
    public void verifySign(Map<String, Object> params, String sign, String timestamp) {
        // 验证时间戳（10分钟内有效）
        long currentTime = System.currentTimeMillis();
        long requestTime = Long.parseLong(timestamp);
        long timeWindow = 10 * 60 * 1000; // 10分钟
        
//        if (Math.abs(currentTime - requestTime) > timeWindow) {
//            throw new BizException("500","请求已过期");
//        }
//
//        // 验证签名
//        String calculatedSign = generateSign(params);
//        if (!calculatedSign.equals(sign)) {
//            throw new BizException("500","签名验证失败");
//        }
    }
}
