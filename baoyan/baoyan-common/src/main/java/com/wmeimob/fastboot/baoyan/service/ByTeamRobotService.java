package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTeamRobot;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTeamRobotService
 * @Description 拼团机器人
 * <AUTHOR>
 * @Date Thu Jul 11 11:18:31 CST 2019
 * @version1.0
 **/
public interface ByTeamRobotService extends CommonService<ByTeamRobot>{

    /**
     * 拼团机器人查询
     * @param id
     * @return
     */
    default ByTeamRobot queryByTeamRobotById(Object id){throw new NotImplementedException("queryByTeamRobotById");};

    /**
     * 拼团机器人添加
     * @param  byTeamRobot
     * @return
     */
    default  void addByTeamRobot(ByTeamRobot byTeamRobot){throw new NotImplementedException("addByTeamRobot");};


    /**
     * 拼团机器人删除
     * @param id
     * @return
     */
    default void removeByTeamRobot(Object id){throw new NotImplementedException("removeByTeamRobot");};


    /**
     * 拼团机器人修改
     * @param byTeamRobot
     * @return
     */
    default void modifyByTeamRobot(ByTeamRobot byTeamRobot){throw new NotImplementedException("modifyByTeamRobot");};
}
