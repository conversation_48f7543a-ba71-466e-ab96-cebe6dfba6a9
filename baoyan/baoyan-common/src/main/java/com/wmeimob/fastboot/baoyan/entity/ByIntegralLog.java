/*
 * ByIntegralLog.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Fri Jul 05 15:35:59 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "by_integral_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByIntegralLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 客户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     *  '变更类型 1 增加2 减少',
     */
    @Column(name = "change_type")
    private Integer changeType;
    /**
     * 变更数量
     */
    @Column(name = "change_num")
    private Integer changeNum;
    /**
     * 变更原因
     */
    @Column(name = "change_reason")
    private String changeReason;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 变更前数量
     */
    @Column(name = "before_num")
    private Integer beforeNum;
    /**
     * 积分类型  1 评价 2购买商品 3积分抵扣 4.后台添加
     */
    @Column(name = "integral_type")
    private Integer integralType;
    @Transient
    private String integralTypeStr;
    @Transient
    private String mobile;
    @Transient
    private String userName;
    @Transient
    private String searchName;
    @Transient
    private String startTime;
    @Transient
    private String endTime;

}