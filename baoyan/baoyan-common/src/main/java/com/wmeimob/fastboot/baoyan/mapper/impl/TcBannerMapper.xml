<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcBannerMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcBanner" id="BaseResultMap">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="isDel" column="is_del" jdbcType="TINYINT"/>
    </resultMap>
</mapper>

