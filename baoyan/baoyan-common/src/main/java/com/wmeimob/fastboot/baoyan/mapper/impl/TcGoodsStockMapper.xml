<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcGoodsStockMapper">

    <resultMap id="BaseResultMap" type="com.wmeimob.fastboot.baoyan.entity.TcGoodsStock">
        <!--@Table tc_goods_stock-->
        <id property="id" column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="stock" column="stock" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="warningCount" column="warning_count" jdbcType="INTEGER" javaType="java.lang.Integer"/>
        <result property="warningMsg" column="warning_msg" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>
    <!--查询单个-->
    <select  id="queryById" resultMap="BaseResultMap">
        select
          id, goods_id, stock, warning_count, warning_msg, gmt_create
        from tc_goods_stock
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByGoodsId" resultMap="BaseResultMap">
        select
          id, goods_id, stock, warning_count, warning_msg, gmt_create
        from tc_goods_stock
       where tc_goods_stock.goods_id = #{id,jdbcType=INTEGER}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.wmeimob.fastboot.baoyan.entity.TcGoodsStock" resultMap="BaseResultMap">
        select
          id, goods_id, stock, warning_count, warning_msg, gmt_create
        from tc_goods_stock
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId}
            </if>
            <if test="stock != null">
                and stock = #{stock}
            </if>
            <if test="warningCount != null">
                and warning_count = #{warningCount}
            </if>
            <if test="warningMsg != null and warningMsg != ''">
                and warning_msg = #{warningMsg}
            </if>
            <if test="gmtCreate != null">
                and gmt_create = #{gmtCreate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">

        insert into tc_goods_stock(goods_id, stock, warning_count, warning_msg, gmt_create)
        values (#{goodsId}, #{stock}, #{warningCount}, #{warningMsg}, #{gmtCreate})


    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tc_goods_stock
        <set>
            <if test="goodsId != null">
                goods_id = #{goodsId},
            </if>
            <if test="stock != null">
                stock = #{stock},
            </if>
            <if test="warningCount != null">
                warning_count = #{warningCount},
            </if>
            <if test="warningMsg != null and warningMsg != ''">
                warning_msg = #{warningMsg},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tc_goods_stock where id = #{id}
    </delete>

</mapper>