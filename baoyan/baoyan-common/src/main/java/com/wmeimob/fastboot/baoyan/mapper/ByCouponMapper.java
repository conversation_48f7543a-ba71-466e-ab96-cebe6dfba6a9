
/*
* ByCouponMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 17:55:47 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.vo.CouponListByGoodsVo;
import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByCouponMapper extends Mapper<ByCoupon> {
	/**
	 *
	 * @param id 查询
	 * @return
	 */
	ByCoupon getCouponInfo(@Param("id") Integer id);
	/**
	 * 优惠券列表
	 * @param byCoupon
	 * @return
	 */
    List<ByCoupon> getCouponList(ByCoupon byCoupon);

	/**
	 * wechat 优惠券列表
	 * @param coupon
	 * @return
	 */
	List<ByCoupon> getWechatCouponList(ByCoupon coupon);

	/**
	 * 查询优优惠卷根据id
	 * @param target
	 * @return
	 */
    ByCoupon selectByState(@Param("id") String target);

	/**
	 * 商品适用优惠卷
	 * @param type
	 * @param goodsId
	 * @param classifyId
	 * @return
	 */
    List<CouponListByGoodsVo> couponListByGoods(@Param("type")Integer type,@Param("goodsId") Integer goodsId,@Param("list") List<Integer> classifyId);
}