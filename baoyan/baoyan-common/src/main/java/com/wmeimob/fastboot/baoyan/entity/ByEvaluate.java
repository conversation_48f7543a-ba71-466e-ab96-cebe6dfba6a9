/*
 * ByEvaluate.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Wed Jul 17 16:52:31 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "by_evaluate")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ByEvaluate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Integer orderId;
    /**
     * 商品ID
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 评分
     */
    @Column(name = "mark")
    private Integer mark;
    /**
     * 评价人
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * 订单号
     */
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 评价内容
     */
    @Column(name = "evaluate_text")
    private String evaluateText;
    /**
     * 图片
     */
    @Column(name = "img")
    private String img;
    /**
     * 状态：0：待回复，1：已回复
     */
    @Column(name = "is_used")
    private Integer isUsed;
    /**
     * 状态：0：隐藏，1：显示
     */
    @Column(name = "is_show")
    private Integer isShow;
    /**
     * 回复时间
     */
    @Column(name = "return_date")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date returnDate;
    /**
     * 回复人
     */
    @Column(name = "return_name")
    private String returnName;
    /**
     * 回复内容
     */
    @Column(name = "return_desc")
    private String returnDesc;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;
    /**
     * 是否匿名;1:是,0:否
     */
    @Column(name = "is_anonymous")
    private Boolean isAnonymous;
    /**
     * 是否删除;1:是,0:否
     */
    @Column(name = "is_del")
    private Boolean isDel;

    /**
     * 商品类型，1-普通，2-次卡 3-联票 4-拼团
     */
    @Column(name = "resouce_type")
    private Integer resouceType;
    /**
     * 昵称
     */
    @Transient
    private String  nickName;
    @Transient
    private String headImg;
    @Transient
    private String mobile;
    @Transient
    private Date startTime;
    @Transient
    private Date endTime;
    @Transient
    private String searchName;
    @Transient
    private Integer detailId;
}