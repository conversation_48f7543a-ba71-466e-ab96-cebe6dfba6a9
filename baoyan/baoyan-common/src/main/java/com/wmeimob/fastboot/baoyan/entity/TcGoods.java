package com.wmeimob.fastboot.baoyan.entity;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * (TcGoods)实体类
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TcGoods implements Serializable {
    private static final long serialVersionUID = 326455587216214148L;
    /**
    * 自增id，无意义
    */
    @Id
    private Integer id;
    /**
    * 商品名
    */
    private String goodsName;
    /**
    * 品牌id
    */
    private Integer brandId;
    /**
    * 品类id
    */
    private Integer cateId;
    /**
    * 标签,json字符串数组
    */
    private JSONArray title;

    /**
    * 销售区域
    */
    private String salesArea;
    /**
    * 销售价格
    */
    private BigDecimal salePrice;
    /**
    * 门市价格
    */
    private BigDecimal doorPrice;
    /**
    * 剩余库存
    */
    private Integer stock;
    /**
    * 小程序二维码图片
    */
    private String codeImg;
    /**
    * 封面图片
    */
    private String coverImg;
    /**
    * 轮播图片
    */
    private String bannerImg;
    /**
    * 商品详情，富文本
    */
    private String detail;
    /**
    * 服务介绍，富文本
    */
    private String introduction;
    /**
    * 是否上架
    */
    private Boolean isDeleted;

    /**
     * 能否使用优惠卷抵扣0否，1能
     */
    private Boolean isCoupon;
    /**
     * 能否使用积分抵扣0否，1能
     */
    private Boolean isIntegral;
    /**
    * 是否推荐首页
    */
    private Boolean isHome;
    /**
    * 创建时间
    */
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date gmtCreate;
    /**
    * 商品状态
    */
    private Boolean status;
    /**
    * 销售数量
    */
    private Long sellCount;
    /**
    * 商品排序，越小越前
    */
    private Integer orderBy;
    /**
     *商品是否支持邮寄
     */
    private Boolean isMailing;

    /**
     * 查询品牌对象
     */
    @Transient
    private TcBrand tcBrand;
    /**
     * 购物车数量
     */
    @Transient
    private int cartCount;
    /**
     * 查询品类对象
     */
    @Transient
    private TcCate tcCate;

    @Transient
    private TcGoodsStock tcGoodsStock;

    /**
     * 排序规则 1  销量降序 2 销量升序 3 销售价 降序 4 销售价 升序4 5 创建时间 降序
     * 为空 根据id 降序
     */
    @Transient
    private Integer sortSearch;
    /**
     *查询 商品名/编号
     */
    @Transient
    private String searchName;


    @Transient
    private Double maxPrice;
    @Transient
    private Double minPrice;

    /**
     *查询  计算结束时间
     */
    @Transient
    private Date gmtCreateEnd;
    /**
     * 上传门店id
     * **/
    @Transient
    private String stores;

    /**
     * 该商品可用的店铺
     */
    @Transient
    private List<BaseStore> baseStores;
    /**
     * 使用門店名
     * */
    @Transient
    private String storeName;
    /**
     * 门店id
     */
    @Transient
    private Integer storeId;
}