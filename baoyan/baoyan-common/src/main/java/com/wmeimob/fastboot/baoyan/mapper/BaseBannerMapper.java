
/*
* BaseBannerMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 30 16:58:36 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.BaseBanner;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseBannerMapper extends Mapper<BaseBanner> {
    List<BaseBanner> selectByExampleList();

    String selectPrivacyAgreement();

	String selectPurchaseAgreement();
	/**List<BaseBanner> select(BaseBanner baseBanner);

	BaseBanner selectByPrimaryKey(@Param("id") Object id);

	int insertSelective(BaseBanner baseBanner);

	int updateByPrimaryKeySelective(BaseBanner baseBanner);

	int deleteByPrimaryKey(@Param("id") Object id);

	int delete(BaseBanner baseBanner);*/
	
}