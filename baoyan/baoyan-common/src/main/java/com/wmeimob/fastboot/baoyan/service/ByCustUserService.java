package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

/**
 * @ClassName ByCustUserService
 * @Description 用户表
 * <AUTHOR>
 * @Date Fri Jul 05 13:51:52 CST 2019
 * @version 1.0
 **/
public interface ByCustUserService extends CommonService<ByCustUser>{



    /**
     * 用户表查询
     * @param id
     * @return
     */
    default ByCustUser queryByCustUserById(Object id){throw new NotImplementedException("queryByCustUserById");};

    /**
     * 用户表添加
     * @param  byCustUser
     * @return
     */
    default  void addByCustUser(ByCustUser byCustUser){throw new NotImplementedException("addByCustUser");};


    /**
     * 用户表删除
     * @param id
     * @return
     */
    default void removeByCustUser(Object id){throw new NotImplementedException("removeByCustUser");};


    /**
     * 用户表修改
     * @param byCustUser
     * @return
     */
    default void modifyByCustUser(ByCustUser byCustUser){throw new NotImplementedException("modifyByCustUser");};

    /**
     * 调整积分
     * @param sfUserInfo
     */
    default void updateIntegral(ByCustUser sfUserInfo){throw new NotImplementedException("updateIntegral");}

    default void updateRed(Integer id){
        return;
    };

    default Boolean updateByCustUserInformation (ByCustUser byCustUser){
        throw new NotImplementedException("modifyByCustUser");
    }
}
