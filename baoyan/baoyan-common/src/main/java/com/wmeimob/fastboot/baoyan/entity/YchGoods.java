package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

@Table(name = "ych_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchGoods implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private Integer id;

    /**
     * 商户ID
     */
    private String businessId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品ID
     */
    private String goodsId;

    /**
     * 套票简介
     */
    private String abstractRemark;

    /**
     * 商品分类
     */
    private String className;

    /**
     * 商品价格
     */
    private Float goodsPrice;

    /**
     * 商品促销价，空表示没有促销，如果有促销，应使用促销价售卖
     */
    private Float promoPrice;

    /**
     * 促销原因(促销方案名称)
     */
    private String promoReason;

    /**
     * 2：预存款，101代币，102 游乐套票，6 组合商品
     */
    private Integer goodsType;

    /**
     * '商品编码'
     */
    private String goodsCode;

    /**
     * 渠道商品上架开始时间
     */
    private String startTime;

    /**
     * 渠道商品上架结束时间
     */
    private String endTime;

    /**
     * 有效期启动时间（非空表示开启有效期，目前只支持代币）
     */
    private String  sDateValidStr;

    /**
     * 有效期结束时间（非空表示开启有效期，目前只支持代币）
     */
    private String eDateValidStr;

    private Integer summery;

    /**
     * '商品分组'
     */
    private Integer groupName;

    /**
     * '套票有效期开始(仅套票商品)'
     */
    private String packageTicketSDateValidStr;

    /**
     * '套票有效期结束(仅套票商品)'
     */
    private String packageTicketEDateValidStr;

    /**
     * '套票核销期描述(仅套票商品)'
     */
    private String packageTicketWriteOffTimeDesc;

    private String gmtCreate;
    private String gmtModified;

    /**
     * 是否在小程序上隐藏
     */
    private Integer hasHideMini;
}
