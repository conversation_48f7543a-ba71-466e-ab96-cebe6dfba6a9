package com.wmeimob.fastboot.baoyan.service.impl;

import com.wmeimob.fastboot.autoconfigure.wechat.WechatPayProperties;
import com.wmeimob.fastboot.baoyan.dto.WechatJsPayDTO;
import com.wmeimob.fastboot.starter.wechat.service.impl.WepayServiceImpl;
import com.wmeimob.fastboot.util.StringUtils;
import com.wmeimob.fastboot.util.web.IpAddressHelper;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.core.WepayBuilder;
import me.hao0.wepay.model.pay.JsPayRequest;
import me.hao0.wepay.model.pay.JsPayResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service("wepay")
public class WepayImpl implements com.wmeimob.fastboot.baoyan.service.Wepay {

    private static final Logger log = LoggerFactory.getLogger(WepayServiceImpl.class);
    private static final BigDecimal TEST_TOTAL_FEE = new BigDecimal("0.05");

    @Resource
    private WechatPayProperties wechatPayProperties;
    private volatile Map<String, Wepay> appidWepayMap = new HashMap();
    private volatile Map<String, Wepay> wepayMap = new HashMap();
    private ResourceLoader resourceLoader = new DefaultResourceLoader();


    public JsPayResponse prePay(WechatJsPayDTO wechatJsPayDTO) {
        if (wechatJsPayDTO.getIsTest() != null && wechatJsPayDTO.getIsTest()) {
            wechatJsPayDTO.setTotalFee(TEST_TOTAL_FEE);
        }

        me.hao0.wepay.core.Wepay wepay = WepayBuilder.newBuilder(wechatJsPayDTO.getAppid(), this.wechatPayProperties.getMchKey(), this.wechatPayProperties.getMchNo()).build();
        JsPayRequest jsPayRequest = new JsPayRequest();
        jsPayRequest.setBody(wechatJsPayDTO.getBody());
        jsPayRequest.setOutTradeNo(wechatJsPayDTO.getOrderNo());
        jsPayRequest.setTotalFee(wechatJsPayDTO.getTotalFee().multiply(new BigDecimal("100")).intValue());
        jsPayRequest.setOpenId(wechatJsPayDTO.getOpenid());
        jsPayRequest.setClientIp(IpAddressHelper.getRemoteHost(((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest()));
        jsPayRequest.setNotifyUrl(wechatJsPayDTO.getNotifyUrl());
        jsPayRequest.setTimeStart(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        jsPayRequest.setGoodsTag(wechatJsPayDTO.getGoodsTag());
        return wepay.pay().jsPay(jsPayRequest);
    }

    public WepayImpl() {
    }

    public void closeOrders(String appid, String ordersNo) {
        Wepay wepay = WepayBuilder.newBuilder(appid, this.wechatPayProperties.getMchKey(), this.wechatPayProperties.getMchNo()).build();
        boolean closeOrderResult = wepay.order().closeOrder(ordersNo);
        if (!closeOrderResult) {
            throw new RuntimeException(String.format("close order %s failure,cause it return null !", ordersNo));
        }
    }

    private Wepay buildFromProperties(String appid, WechatPayProperties wechatPayProperties) {
        WepayBuilder wepayBuilder = WepayBuilder.newBuilder(appid, wechatPayProperties.getMchKey(), wechatPayProperties.getMchNo());
        if (!StringUtils.isEmpty(wechatPayProperties.getCertPath())) {
            byte[] data = null;

            try {
                InputStream fis = this.resourceLoader.getResource(wechatPayProperties.getCertPath()).getInputStream();
                Throwable var6 = null;

                try {
                    ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
                    Throwable var8 = null;

                    try {
                        byte[] b = new byte[1000];

                        int n;
                        while((n = fis.read(b)) != -1) {
                            bos.write(b, 0, n);
                        }

                        data = bos.toByteArray();
                    } catch (Throwable var34) {
                        var8 = var34;
                        throw var34;
                    } finally {
                        if (bos != null) {
                            if (var8 != null) {
                                try {
                                    bos.close();
                                } catch (Throwable var33) {
                                    var8.addSuppressed(var33);
                                }
                            } else {
                                bos.close();
                            }
                        }

                    }
                } catch (Throwable var36) {
                    var6 = var36;
                    throw var36;
                } finally {
                    if (fis != null) {
                        if (var6 != null) {
                            try {
                                fis.close();
                            } catch (Throwable var32) {
                                var6.addSuppressed(var32);
                            }
                        } else {
                            fis.close();
                        }
                    }

                }
            } catch (IOException var38) {
                var38.printStackTrace();
            }

            wepayBuilder.certs(data).certPasswd(wechatPayProperties.getMchNo());
        }

        return wepayBuilder.build();
    }

    public Wepay getApiComponent(String appid) {
        if (this.appidWepayMap.get(appid) == null) {
            Class var2 = WepayServiceImpl.class;
            synchronized(WepayServiceImpl.class) {
                if (this.appidWepayMap.get(appid) == null) {
                    Wepay thisWepay = this.buildFromProperties(appid, this.wechatPayProperties);
                    this.appidWepayMap.put(appid, thisWepay);
                }
            }
        }

        return (Wepay)this.appidWepayMap.get(appid);
    }

    public Wepay getApiComponent(String appid, String mchNo) {
        if (StringUtils.isEmpty(mchNo)) {
            return this.getApiComponent(appid);
        } else {
            String mapKey = mchNo + "@" + appid;
            if (this.wepayMap.get(mapKey) == null) {
                Class var4 = WepayServiceImpl.class;
                synchronized(WepayServiceImpl.class) {
                    if (this.wepayMap.get(mapKey) == null) {
                        WechatPayProperties currentConfiguration = (WechatPayProperties)this.wechatPayProperties.getMultiples().get(mchNo);
                        Wepay currentWepay = this.buildFromProperties(appid, currentConfiguration);
                        this.wepayMap.put(mapKey, currentWepay);
                    }
                }
            }

            return (Wepay)this.wepayMap.get(mapKey);
        }
    }
}
