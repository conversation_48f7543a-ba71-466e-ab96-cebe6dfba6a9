package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * (TcImgModule)实体类
 *
 * <AUTHOR>
 * @since 2021-09-02 21:22:18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TcImgModule implements Serializable {
    private static final long serialVersionUID = -96275226943412192L;
    
    private Integer id;
    /**
    * 图片地址
    */
    private String imgUrl;
    /**
     * 布局方式
     * 1横向排版: 722px * 246px
     * 0 二分之一排版: 350px * 200px
     */
    private Boolean layout;
    /**
    * 跳转类型 (1.淘潮玩商品，2品牌，3品类，4.标签)
    */
    private Integer jumpType;
    /**
    * 跳转类容（跳转类型为2/3时 等于-1时 跳转全部品牌/品类显示）
    */
    private String target;
    /**
    * 1为删除，0为未删除
    */
    private Boolean isDel;
    /**
    * 1上架，0下架
    */
    private Boolean status;

    private Integer sort;




}