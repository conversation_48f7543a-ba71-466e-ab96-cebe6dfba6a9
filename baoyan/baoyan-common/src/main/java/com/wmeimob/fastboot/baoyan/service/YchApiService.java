package com.wmeimob.fastboot.baoyan.service;

import cn.hutool.json.JSONObject;
import com.wmeimob.fastboot.baoyan.entity.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 油菜花接口
 * @date 2025-01-08
 * @Version 1.0
 */
public interface YchApiService {
    List<YchBusiness> getBusinessList();

    List<YchGoods> getGoodsList(Integer goodsType, Boolean isAll);

    List<YchGoods> getGoodsListAll(Integer goodsType, Boolean isAll);

    YchGoods getGoodsDetail(YchGoods ychGoods);

    YchLeaguer GetLeaguerByPhone(String phone);

    YchLeaguer registerLeaguer(String phone);

    YchValue GetLeaguerValues(String phone);

    List<YchOrder> GetLeaguerValuesLog();

    YchOrder createOrder(YchGoods ychGoods,String OrderNo);

    YchOrder createOrder(YchGoods ychGoods,String OrderNo,String mobile,String remark);

    boolean updatePaid(String TPOrderNo, String transactionId);

    boolean LeaguerPrepaidChange(ByOrders byOrders);

    boolean LeaguerPrepaidChange(float changeValue,String leaguerID,String leaguerPrepaidChangeType);

    /**
     * 订单退回
     */
    JSONObject orderReturn(String TPOrderNo);
}
