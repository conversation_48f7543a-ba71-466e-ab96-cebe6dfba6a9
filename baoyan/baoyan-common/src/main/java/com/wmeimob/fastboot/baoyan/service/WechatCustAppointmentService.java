package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.core.service.CommonService;

import java.util.List;

/**
 * @ClassName ByCustAppointmentService
 * @Description 客户预约表
 * <AUTHOR>
 * @Date Tue Jul 09 14:34:33 CST 2019
 * @version1.0
 **/
public interface WechatCustAppointmentService extends CommonService<ByCustAppointment>{
    /**
     * 取消预约
     * @param id
     */
    default RestResult cancelAppointment(Integer id,String fromId){throw new NotImplementedException("cancelAppointment");};

    /**
     * 查询客户预约记录
     * @param userId
     * @return
     */
    default  List<ByCustAppointmentVO> custApponintByUserId(Integer userId,Integer apponintmentStatus){throw new NotImplementedException("custApponintByUserId");};

    /**
     * 删除预约记录
     * @param id
     * @return
     */
    default  RestResult removeForByAppointment(Integer id){throw new NotImplementedException("custApponintByUserId");};

    /**
     * 获取预约单详情
     * @param id
     * @return
     */
    default  ByCustAppointmentVO getDetailByAppointmentId(Integer id){throw new NotImplementedException("custApponintByUserId");};
}
