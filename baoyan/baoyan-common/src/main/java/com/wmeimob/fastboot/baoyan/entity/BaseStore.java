/*
* BaseStore.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Wed Jul 10 11:37:09 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.math.*;

@Table(name = "base_store")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BaseStore implements Serializable {
	
   private static final long serialVersionUID = 1L;
	
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 编号
     */
    @Column(name = "code")
    private String code;
    /**
     * 门店名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 门店类型
     */
    @Column(name = "store_type")
    private Integer storeType;

    /**
     * 所属门店
     */
    @Column(name = "store_id")
    private Integer storeId;

    /**
     * 油菜花门店 ID
     */
    @Column(name = "business_id")
    private String businessId;

    /**
     * 电话
     */
    @Column(name = "mobile")
    private String mobile;
    /**
     * 地址
     */
    @Column(name = "address")
    private String address;
    /**
     * 门店图片
     */
    @Column(name = "store_img")
    private String storeImg;

    /**
     * 渠道（门店）二维码
     */
    @Column(name="store_qrcode")
    private String storeQrcode;

    /**
     * 经纬度
     */
    @Column(name = "point")
    private String point;
    /**
     * 预约人数
     */
    @Column(name = "appointment_num")
    private Integer appointmentNum;
    /**
     * 即将满员人数
     */
    @Column(name = "full_num")
    private Integer fullNum;
    /**
     * 工作日状态  0:禁用  1:启用
     */
    @Column(name = "status")
    private Boolean status;

    /**
     * 周末状态  0:禁用  1:启用
     */
    @Column(name = "weekend_status")
    private Boolean  weekendStatus;

    /**
     * 支付密码
     */
    @Column(name = "pay_pwd")
    private String payPwd;
    /**
     * 0-可用,1-删除
     */
    @Column(name = "delete_status")
    private Boolean deleteStatus;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_modified")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;


    /**
     * 营业时间描述
     */
    @Column(name = "business_time")
    private String businessTime;

    @Transient
    private String distance;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "longitude")
    private String longitude;

}