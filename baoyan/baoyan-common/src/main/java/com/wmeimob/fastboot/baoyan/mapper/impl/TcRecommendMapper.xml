<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 <mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcRecommendMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcRecommend" id="TcRecommendMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="templateId" column="template_id" jdbcType="INTEGER"/>
        <result property="text" column="text" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="coverImg" column="cover_img" jdbcType="VARCHAR"/>
        <result property="cateId" column="cate_id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="INTEGER"/>
        <result property="isShelves" column="is_shelves" jdbcType="BOOLEAN"/>
        <result property="isDel" column="is_del" jdbcType="BOOLEAN"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
        <result property="labelId" column="label_id" jdbcType="VARCHAR"/>
        <result property="tcFlag" jdbcType="INTEGER" column="tc_flag"/>
    </resultMap>

    <resultMap id="queryAll" type="com.wmeimob.fastboot.baoyan.entity.TcRecommend" extends="TcRecommendMap">
        <result property="tcTemplate.id" column="tid" jdbcType="INTEGER"/>
        <result property="tcTemplate.name" column="tname" jdbcType="VARCHAR"/>
        <result property="tcTemplate.isShelves" column="tshelves" jdbcType="TINYINT"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TcRecommendMap">
        select
          tc_recommend.*
          from tc_recommend
        where id = #{id}
    </select>

    <select id="wxQueryById" resultMap="queryAll">
        select
        tc_recommend.*,
        tc_template.id as tid, tc_template.name as tname,tc_template.is_shelves as tshelves

        from tc_recommend LEFT JOIN tc_template ON tc_recommend.template_id= tc_template.id
        where  tc_recommend.is_del = 1
            and tc_recommend.is_shelves = 1
            and tc_template.is_del = 0
            and tc_recommend.id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="queryAll">
        select
        tc_recommend.*,
        tc_template.id as tid, tc_template.name as tname,tc_template.is_shelves as tshelves

        from tc_recommend LEFT JOIN tc_template ON tc_recommend.template_id= tc_template.id
        where tc_recommend.is_del = 1
            <if test="templateId != null">
                and tc_recommend.template_id = #{templateId}
            </if>
            <if test="text != null and text != ''">
                and tc_recommend.text like concat('%',#{text,jdbcType=VARCHAR},'%')
            </if>
            <if test="jumpType != null">
                and tc_recommend.jump_type = #{jumpType}
            </if>
            <if test="isHome!= null">
                and tc_template.is_home = #{isHome}
            </if>
        order by tc_recommend.is_shelves desc,tc_recommend.id desc
    </select>

    <select id="wxQueryAll" resultMap="queryAll">
        select
        tc_recommend.*,
        tc_template.id as tid, tc_template.name as tname,tc_template.is_shelves as tshelves

        from tc_recommend LEFT JOIN tc_template ON tc_recommend.template_id= tc_template.id
        where
        tc_recommend.is_del = 1
        and tc_recommend.is_shelves = 1
        and tc_template.is_del = 0
        <if test="templateId != null">
            and tc_recommend.template_id = #{templateId}
        </if>
<!--        <if test="cateId != null">-->
<!--            and tc_recommend.cate_id = #{cateId}-->
<!--        </if>-->
<!--        <if test="brandId != null">-->
<!--            and tc_recommend.brand_id = #{brandId}-->
<!--        </if>-->
        order by tc_recommend.sort asc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tc_recommend(template_id, text, jump_type, target, sort, goods_id, cover_img, cate_id, brand_id, gmt_create, gmt_update,label_id,tc_flag)
        values (#{templateId}, #{text}, #{jumpType}, #{target}, #{sort}, #{goodsId}, #{coverImg},
                #{cateId}, #{brandId}, #{gmtCreate}, #{gmtUpdate},#{labelId},#{tcFlag})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tc_recommend(template_id, text, jump_type, target, sort, goods_id, cover_img, cate_id,
        brand_id, is_shelves, is_del, gmt_create, gmt_update,label_id,tc_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.templateId}, #{entity.text}, #{entity.jumpType}, #{entity.target}, #{entity.sort},
            #{entity.goodsId}, #{entity.coverImg}, #{entity.cateId}, #{entity.brandId}, #{entity.isShelves},
            #{entity.isDel}, #{entity.gmtCreate}, #{entity.gmtUpdate},#{labelId},#{tcFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tc_recommend(template_id, text, jump_type, target, sort, goods_id, cover_img, cate_id,
        brand_id, is_shelves, is_del, gmt_create, gmt_update,label_id,tc_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.templateId}, #{entity.text}, #{entity.jumpType}, #{entity.target}, #{entity.sort},
            #{entity.goodsId}, #{entity.coverImg}, #{entity.cateId}, #{entity.brandId}, #{entity.isShelves},
            #{entity.isDel}, #{entity.gmtCreate}, #{entity.gmtUpdate},#{labelId},#{tcFlag})
        </foreach>
        on duplicate key update
        template_id = values(template_id) , text = values(text) , jump_type = values(jump_type) , target =
        values(target) , sort = values(sort) , goods_id = values(goods_id) , cover_img = values(coverImg) , cate_id =
        values(cate_id) , brand_id = values(brand_id) , is_shelves = values(is_shelves) , is_del = values(is_del) ,
        gmt_create = values(gmt_create) , gmt_update =
        values(gmt_update),label_id=values(label_id),tc_flag=values(tc_flag)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tc_recommend
        <set>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType},
            </if>
            <if test="tcFlag == null ">
                tc_flag = null,
            </if>
<!--            <if test="tcFlag == 1 and goodsId == null">-->
<!--                target = #{target},-->
<!--            </if>-->
            <if test="target != null and target != '' and tcFlag != 2 ">
                target = #{target},
            </if>
            <if test="goodsId != null and target == null">
                target = #{target},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="coverImg != null">
                cover_img = #{coverImg},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate},
            </if>
            <if test="tcFlag != null">
                tc_flag = #{tcFlag},
                target = #{target},
            </if>
            <if test="isShelves != null">
                is_shelves = #{isShelves},
            </if>
        </set>
        ,goods_id = #{goodsId},
        cate_id = #{cateId},
        label_id = #{labelId},
        brand_id = #{brandId}
        where is_del=1 and id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
         update tc_recommend set tc_recommend.is_del = 0 where is_del=1 and  id =#{id,jdbcType=INTEGER}
    </update>
    <update id="onAndOffShelves">
         update tc_recommend set tc_recommend.is_shelves = #{type,jdbcType=BOOLEAN}
          where is_del=1 and  id =#{id,jdbcType=INTEGER}
    </update>
</mapper>

