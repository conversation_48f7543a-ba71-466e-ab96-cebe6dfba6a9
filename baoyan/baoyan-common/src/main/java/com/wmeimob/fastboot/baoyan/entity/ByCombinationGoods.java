package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品规格表
 * <AUTHOR>
 */
@Table(name = "by_combination_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class ByCombinationGoods implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 套餐名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 商品库存
     */
    @Column(name = "goods_stock")
    private Integer goodsStock;
    /**
     * 实际销量
     */
    @Column(name = "actual_sales_num")
    private Integer actualSalesNum;
    /**
     * 套餐价格
     */
    @Column(name = "price")
    private BigDecimal price;
    /**
     * 核销开始时间
     */
    @Column(name = "verification_start")
    private Date verificationStart;
    /**
     * 核销结束时间
     */
    @Column(name = "verification_end")
    private Date verificationEnd;
    /**
     * 限购数（null或0为不限购）
     */
    @Column(name = "limited")
    private Integer limited;
    /**
     * 能否使用优惠卷抵扣0否，1能
     */
    @Column(name = "is_coupon")
    private Integer isCoupon;
    /**
     * 能否使用积分抵扣0否，1能
     */
    @Column(name = "is_integral")
    private Integer isIntegral;
    /**
     * 上下架 0下架 1上架售卖 2上架隐藏 3定时上架
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否指定核销天数
     */
    @Column(name = "has_verification_day")
    private Boolean hasVerificationDay;
    /**
     * 指定天数内有效
     */
    @Column(name = "verification_day")
    private Integer verificationDay;
    /**
     * 定时下架状态 0:未开启 1:开启
     */
    @Column(name = "regular_down_status")
    private Boolean regularDownStatus;

    /**
     * 营销开始时间
     */
    @Column(name = "marketing_start")
    private Date marketingStart;

    /**
     * 营销结束时间
     */
    @Column(name = "marketing_end")
    private Date marketingEnd;

    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;

    /**
     * 组合油菜花预存款套餐，支持赠送本金和赠金
     */
    private Integer preDepositId;

    /**
     * 首次核销赠金
     */
    @Column(name = "first_verify_balance")
    private BigDecimal firstVerifyBalance;

    /**
     * 是否删除
     */
    @Column(name = "is_del")
    private Integer isDel;
    /**
     * 商品名称
     */
    @Transient
    private String goodsNames;
    /**
     * 规格对应商品图片
     */
    @Transient
    private String goodsImg;


}
