package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcBanner;
import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.entity.TcComtion;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.service.CommonService;

/**
 * 淘潮商品关联表(TcComtion)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-12 21:16:14
 */
public interface TcComtionService extends CommonService<TcComtion>{
    /**
     * Banner查询
     * @param id
     * @return
     */
    default TcComtion queryTcComtionById(Object id){throw new NotImplementedException("queryTcComtionById");};

    /**
     * Banner添加
     * @param  tcComtion
     * @return
     */
    default  void addTcComtion(TcComtion tcComtion){throw new NotImplementedException("addTcComtion");};


    /**
     * Banner删除
     * @param id
     * @return
     */
    default void removeTcComtion(Object id){throw new NotImplementedException("removeTcComtion");};


    /**
     * Banner修改
     * @param tcComtion
     * @return
     */
    default void modifyTcComtion(TcComtion tcComtion){throw new NotImplementedException("modifyTcComtion");}
}
