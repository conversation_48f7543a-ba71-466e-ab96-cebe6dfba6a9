package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 首页-多图返回
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ImgVO implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 图片地址
     */
    private String imgUrl;
    /**
     * 标题
     */
    private String title;
    /**
     * 跳转类型id
     */
    private Integer jumpType;
    /**
     * 跳转content
     */
    private String target;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 排版方式（1.横向排版2.二分之一排版）
     */
    private Integer layout;
    /**
     * 分类url
     */
    private String targetImgUrl;
    /**
     * 分类名称
     */
    private String classifyTitle;
}
