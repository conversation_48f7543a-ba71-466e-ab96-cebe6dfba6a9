package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.core.exception.CustomException;

import java.util.List;

/**
 * (TcTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-20 19:01:01
 */
public interface TcTemplateService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
   default TcTemplate queryById(Integer id){
       throw   new CustomException("TcTemplateService");
   }


    /**
     * 新增数据
     *
     * @param tcTemplate 实例对象
     * @return 实例对象
     */
  default  Boolean insert(TcTemplate tcTemplate){
      throw  new CustomException("TcTemplateService");
  }

    /**
     * 修改数据
     *
     * @param tcTemplate 实例对象
     * @return 实例对象
     */
    default Boolean update(TcTemplate tcTemplate){
        throw  new CustomException("TcTemplateService");
    }

   default Boolean onAndOffShelves(TcTemplate tcTemplate){
       throw  new CustomException("TcTemplateService");
   }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
   default Boolean deleteById(Integer id){
       throw  new CustomException("TcTemplateService");
   }

   default List<TcTemplate> queryPage(TcTemplate queryObject){
       throw  new CustomException("TcTemplateService");
   }
}
