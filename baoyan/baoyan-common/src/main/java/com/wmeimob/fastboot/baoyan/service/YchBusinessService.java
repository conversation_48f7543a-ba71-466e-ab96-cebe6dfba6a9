package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.YchBusiness;
import com.wmeimob.fastboot.baoyan.mapper.YchBusinessMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

public interface YchBusinessService {
    /**
     * 根据门店ID获取门店URL
     * @param businessId
     * @param apiPath
     * @return
     */
    String getBusinessUrl(String businessId);

    /**
     * 获取完整的API调用地址
     * @param businessId
     * @param apiPath
     * @return
     */
    String getFullApiUrl(String businessId, String apiPath);
} 