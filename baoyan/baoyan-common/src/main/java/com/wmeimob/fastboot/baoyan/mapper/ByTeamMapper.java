
/*
* ByTeamMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Jul 16 15:59:56 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.core.orm.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ByTeamMapper extends Mapper<ByTeam> {
	/**
	 * 拼团管理分页查询
	 * @param byTeam
	 * @return
	 */
    List<ByTeam> findByCondition(ByTeam byTeam);
	/**
	 * <AUTHOR>
	 * @date 14:40 2019/5/7
	 * @Description:查询拼团信息
	 */
	ByTeam queryById(@Param("id") Integer id);
	/**
	 * 拼团详情
	 * @param teamId
	 * @return
	 */
    ByTeam teamDetailByOrder(Integer teamId);

	List<ByTeam> selectList(@Param("id") Integer id);

    List<ByTeam> presonTeam(@Param("id")Integer id);
    ByTeam userTeam(@Param("id")Integer id);

	List<ByTeam> selectByExampleLIst();
}