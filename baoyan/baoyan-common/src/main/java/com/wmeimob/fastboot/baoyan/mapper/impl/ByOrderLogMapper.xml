<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.ByOrderLogMapper">

    <resultMap id="ByOrderLogMap" type="com.wmeimob.fastboot.baoyan.entity.ByOrderLog">
        <id column="id" property="id"></id>
        <result column="goods_id" property="goodsId"></result>
        <result column="goods_name" property="goodsName"></result>
        <result column="gmt_create" property="gmtCreate"></result>
        <result column="logs" property="logs"></result>
        <result column="log_type" property="logType" typeHandler="com.wmeimob.fastboot.baoyan.handler.OrderTypeHandler"></result>
        <result column="operator_type" property="operatorType"></result>
        <result column="operator_count" property="operatorCount"></result>
    </resultMap>

    <select id="findAll" resultMap="ByOrderLogMap">
        select * from by_order_log
    </select>

    <update id="insertLog">
        insert into
        by_order_log (order_no,order_goods_id, goods_id, goods_name, logs, log_type, operator_type, operator_count, gmt_create)
        value (#{orderNo},#{orderGoodsId}, #{goodsId}, #{goodsName}, #{logs},
         #{logType, typeHandler=com.wmeimob.fastboot.baoyan.handler.OrderTypeHandler},
        #{operatorType}, #{operatorCount}, #{gmtCreate})
    </update>

    <select id="queryByOrderNo" resultMap="ByOrderLogMap">
        select * from by_order_log
        where order_no = #{orderNo}
        order by gmt_create desc
    </select>

</mapper>

