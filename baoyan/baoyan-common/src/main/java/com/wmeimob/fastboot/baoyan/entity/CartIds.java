package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 删除购物车时传递的对象
 * <AUTHOR>
 * @date 2021/7/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CartIds {

    /**
     * 需要删除的门票id
     */
    private List<Integer> ticketIds;

    /**
     * 需要删除的淘潮玩id
     */
    private List<Integer> tcIds;

}
