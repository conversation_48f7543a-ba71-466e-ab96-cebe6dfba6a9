package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TcRecommend)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-20 18:53:29
 */
public interface TcRecommendMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcRecommend queryById(Integer id);



    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcRecommend 实例对象
     * @return 对象列表
     */
    List<TcRecommend> queryAll(TcRecommend tcRecommend);

    /**
     * 新增数据
     *
     * @param tcRecommend 实例对象
     * @return 影响行数
     */
    int insert(TcRecommend tcRecommend);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TcRecommend> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TcRecommend> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TcRecommend> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<TcRecommend> entities);

    /**
     * 修改数据
     *
     * @param tcRecommend 实例对象
     * @return 影响行数
     */
    int update(TcRecommend tcRecommend);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    int onAndOffShelves(@Param("id") Integer id,@Param("type") Boolean type);

    List<TcRecommend> wxQueryAll(TcRecommend tcRecommend);

    TcRecommend wxQueryById(Integer id);

}

