/*
* ByGoodsShoping.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Tue Aug 13 13:32:55 CST 2019 Created
*/ 
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Table(name = "by_goods_shoping")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class ByGoodsShoping implements Serializable {
	
   private static final long serialVersionUID = 1L;
	
    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品id
     */
    @Column(name = "goods_id")
    private Integer goodsId;
    /**
     * 商品类型 1  普通 2 次卡 3  联票 5规格
     */
    @Column(name = "goods_type")
    private Integer goodsType;
    /**
     * 商品数量
     */
    @Column(name = "goods_count")
    private Integer goodsCount;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Integer userId;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale="zh", pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

    @Transient
    private Integer type;

    @Transient
    private String marketPrice;
    @Transient
    private String sellPrice;
    @Transient
   private String goodsName;

    @Transient
    private String goodsStock;
    @Transient
   private BigDecimal amount;
    @Column(name = "is_del")
     private Integer isDel;
    @Transient
    private BigDecimal inAmount;
   /**
    *商品限购数
    */
    @Transient
    private Integer limited;

}