
/*
* ByCouponUserMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Thu Jul 11 17:28:52 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.core.orm.Mapper;
import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ByCouponUserMapper extends Mapper<ByCouponUser> {

	/**
	 * 查询用户是否有对应id的有效优惠券
	 * @param id
	 * @param userId
	 * @return
	 */
	ByCouponUser findUserCoupon(@Param("id") Integer id, @Param("userId") Integer userId);

	/**
	 * 优惠券领取列表
	 * @param byCouponUser
	 * @return
	 */
    List<ByCouponUser> getCouponGetList(ByCouponUser byCouponUser);

	/**
	 * 查询优惠券审核列表
	 * @param byCouponUser
	 * @return
	 */
	List<ByCouponUser> queryAuditListByCouponUser(ByCouponUser byCouponUser);


	/**
	 * 领取列表
	 * @param byCouponUser
	 * @return
	 */
    List<ByCouponUser> getCouponUserList(ByCouponUser byCouponUser);

    List<ByCouponUser> selectByCouponUser(@Param("id") Integer id,@Param("id1") Integer id1,@Param("singleGoodsType")Integer singleGoodsType);

	List<ByCouponUser> selectByCouponUser2(@Param("id") Integer id,@Param("id1") Integer id1,@Param("singleGoodsType")Integer singleGoodsType);

	/**
	 * 查询用户指定类型的优惠券，目前支持全部 淘潮玩优惠券的查询
	 * @param userId
	 * @param type
	 * @param now
	 * @return
	 */
	List<ByCouponUser> selectCouponByType(@Param("userId") Integer userId,
										  @Param("type") Integer type,
										  @Param("now") Date now);

	/**
	 * 查询指定淘潮玩商品 id的优惠券
	 * @return
	 */
	List<ByCouponUser> selectTcCoupon(@Param("userId") Integer userId, @Param("ids") List<Integer> ids, @Param("now") Date now);

	List<ByCouponUser> selectList(@Param("set") Set<Integer> set,@Param("id") Integer id);

	/**
	 * 查询用户过期的优惠券
	 * @return
	 */
	List<ByCouponUser> checkCouponUserExpire();

	/**
	 * 查询当日领取 是否超过
 	 * @param param
	 * @return
	 */
    Integer selectCountByParam(Map<String, Object> param);
}