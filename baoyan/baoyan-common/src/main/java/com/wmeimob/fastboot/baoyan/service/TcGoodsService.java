package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import com.wmeimob.fastboot.core.exception.CustomException;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * (TcGoods)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcGoodsService {


    /**
     * 扣减库存，
     * @param id 商品id
     * @param change 改变的数量
     * @return
     */
    default boolean reduceStock(Integer id, Integer change){
        throw new CustomException("reduceStock");
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    default TcGoods queryById(Integer id) {
        throw new CustomException("TcGoodsService");
    }


    /**
     * 新增数据
     *
     * @param tcGoods 实例对象
     * @return 实例对象
     */


    default Boolean insert(HttpServletResponse response, TcGoods tcGoods) {
        throw new CustomException("TcGoodsService");
    }

    /**
     * 修改数据
     *
     * @param tcGoods 实例对象
     * @return 实例对象
     */
    default Boolean update(TcGoods tcGoods) {
        throw new CustomException("TcGoodsService");
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    default boolean deleteById(Integer id) {
        throw new CustomException("TcGoodsService");
    }

    /**
     * 根据条件查询 进行分页
     *
     * @param queryObject
     * @return
     */
    default List<TcGoods> queryPage(TcGoods queryObject) {
        throw new CustomException("TcGoodsService");
    }

    default Long queryAllCount(TcGoods tcGoods) {
        throw new CustomException("TcGoodsService");
    }

    /**
     * 同步库存
     *
     * @param id
     * @return
     */
    default Boolean synchronizeInventory(Integer id, Integer stock) {
        throw new CustomException("TcGoodsService");
    }

    default String queryWxCodeImg(HttpServletResponse resp, Integer goodsId, Boolean falg) {
        throw new CustomException("TcGoodsService");
    }
}