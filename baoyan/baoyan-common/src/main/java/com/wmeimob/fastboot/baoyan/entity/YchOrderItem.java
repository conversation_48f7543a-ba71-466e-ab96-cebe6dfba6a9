package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchOrderItem  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    private String goodsId;

    private String orderId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品价格
     */
    private Float goodsPrice;

    /**
     * 商品数量
     */
    private Float amount;

    /**
     * 扩展信息可空
     */
    private String extendedContent;

    /**
     * 备注可空
     */
    private String summary;
}
