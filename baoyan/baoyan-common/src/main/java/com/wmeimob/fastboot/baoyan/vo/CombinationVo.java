package com.wmeimob.fastboot.baoyan.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * wx-商品规格返回
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CombinationVo implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 规格id
     */
    private Integer id;
    /**
     * 规格名称
     */
    private String name;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 限购数（null或0为不限购）
     */
    private Integer limited;
    /**
     * 能否使用优惠卷抵扣0否，1能
     */
    private Integer isCoupon;
    /**
     * 能否使用积分抵扣0否，1能
     */
    private Integer isIntegral;
    /**
     * 是否指定核销天数
     */
    private Boolean hasVerificationDay;
    /**
     * 指定天数内有效
     */
    private Integer verificationDay;
    /**
     * 核销开始时间
     */
    private Date verificationStart;
    /**
     * 核销结束时间
     */
    private Date verificationEnd;
    /**
     * 库存
     */
    private Integer goodsStock;
}
