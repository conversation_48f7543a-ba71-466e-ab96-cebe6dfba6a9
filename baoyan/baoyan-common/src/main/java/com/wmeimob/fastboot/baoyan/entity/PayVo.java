package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import me.hao0.wepay.model.pay.JsPayResponse;

import java.math.BigDecimal;

/**
 * 帮助前端唤醒支付的类
 * <AUTHOR>
 * @date 2021/7/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayVo {
    /**
     * 支付时使用
     * 微信支付相关信息
     */
    private JsPayResponse pay;
    /**
     * 支付时使用
     * 订单id
     */
    private String orderId;
    /**
     * 支付时使用
     * 订单号
     */
    private String orderNo;
    /**
     * 含义不清楚，以前支付的map中有
     */
    private Integer type;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
}
