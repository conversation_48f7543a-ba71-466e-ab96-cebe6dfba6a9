package com.wmeimob.fastboot.baoyan.vo;

import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/16
 */
@Data
public class BatchWrite implements Serializable {
    private List<TcWriteOffCode> writeOffCodes;
    /**
     * 核销时核销店铺id
     */
    @Transient
    private Integer storeIds;

    /**
     * 核销时员工id
     */
    @Transient
    private Integer staffId;
}
