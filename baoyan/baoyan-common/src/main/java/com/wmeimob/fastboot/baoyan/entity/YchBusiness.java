package com.wmeimob.fastboot.baoyan.entity;

import java.io.Serializable;
import java.time.LocalDate;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Table(name = "ych_business")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchBusiness implements Serializable {

    private static final long serialVersionUID = 1L;

     /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 门店ID
     */
    private String businessId;

    /**
     * 名称
     */
    private String businessName;

    /**
     * 地址
     */
    private String address;

    /**
     * URL
     */
    private String businessUrl;

    /**
     * 门店
     */
    private String mallCode;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
    
}
