package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByStoreTable;
import com.wmeimob.fastboot.baoyan.qo.StoreTableQo;
import com.wmeimob.fastboot.baoyan.vo.StoreTableVo;
import com.wmeimob.fastboot.core.orm.Mapper;

import java.util.List;

public interface ByStoreTableMapper  extends Mapper<ByStoreTable> {
    List<StoreTableVo> queryStoreNoList(StoreTableQo storeTableQo);
}
