package com.wmeimob.fastboot.baoyan.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: OrderStatisticsVO
 * @projectName baoyan
 * @description:财务统计vo
 * @date 2019/8/20 14:19
 */
@Data
public class FinanceStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @Transient
    private Integer writeOffId;
    /**
     * 核销商品名称
     */
    @Transient
    private String writeOffName;
    /**
     * 下单人
     */
    @Transient
    private String custUserName;
    /**
     * 手机人
     */
    @Transient
    private String mobile;
    /**
     * 下单时间
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderTime;
    /**
     * 支付时间
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;
    /**
     * 订单号
     */
    @Transient
    private String orderNo;
    /**
     * 商品名称
     */
    @Transient
    private String goodsName;
    /**
     * 订单状态
     */
    @Transient
    private Integer orderStatus;

    /**
     * 单价
     */
    @Transient
    private BigDecimal goodsPrice;
    /**
     * 出售数量
     */
    @Transient
    private Integer salesNum;
    /**
     * 退款数量
     */
    @Transient
    private Integer refundNum;
    /**
     * 应收金额
     */
    @Transient
    private BigDecimal amount;
    /**
     * 优惠券抵扣
     */
    @Transient
    private BigDecimal couponAmount;
    /**
     * 积分抵扣
     */
    @Transient
    private BigDecimal integralAmount;
    /**
     * 实收金额
     */
    @Transient
    private BigDecimal actualAmount;
    /**
     * 退款状态
     */
    @Transient
    private Integer refundStatus;
    /**
     * 退款时间
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date refundTime;
    /**
     * 核销状态
     */
    @Transient
    private Integer writeOffStatus;
    /**
     * 核销员工
     */
    @Transient
    private String writeOffStaff;
    /**
     * 核销门店
     */
    @Transient
    private String writeOffStore;
    /**
     * 核销员工
     */
    @Transient
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date writeOffTime;
    /**
     * 来源商品编号
     */
    @Transient
    private String sourceGoodsNo;
    /**
     *核销商品编号
     */
    @Transient
    private String writeOffGoodsNo;
    /**
     * 订单类型 0普通订单 1拼团订单
     */
    @Transient
    private Integer orderType;
    /**
     * detailId
     */
    @Transient
    private Integer detailId;
    /**
     * orderId
     */
    @Transient
    private Integer orderId;
    /**
     * storeIds  核销门店
     */
    @Transient
    private String storeIds;
}
