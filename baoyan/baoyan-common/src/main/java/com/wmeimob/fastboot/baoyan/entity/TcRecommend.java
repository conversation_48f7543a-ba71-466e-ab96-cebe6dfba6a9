package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


import java.io.Serializable;

/**
 * (TcRecommend)表实体类
 *
 * <AUTHOR>
 * @since 2021-07-20 18:49:26
 */
@SuppressWarnings("all")
@Table(name = "tc_recommend")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TcRecommend implements Serializable {
    //id自增,无意义
    private Integer id;
    //模板id
    private Integer templateId;
    //推荐标题
    private String text;

    /**
     * 跳转类型(
     * 1.商品分类2.联票列表3.次卡列表
     * 4.拼团列表5.文章6.普通商品7.联票商品
     * 8.次卡商品9.拼团商品10.优惠券11.淘潮玩
     * )
     */
    private Integer jumpType;
    //跳转content
    private String target;
    //排序值
    private Integer sort;
    //商品id
    private Integer goodsId;
    //封面图
    private String coverImg;
    //品类id
    private Integer cateId;
    //品牌id
    private Integer brandId;
    //1上架 0下架 默认上架
    private Boolean isShelves;
    //0删除1未删除
    private Boolean isDel;
    //创建时间
    private Date gmtCreate;
    //修改时间
    private Date gmtUpdate;

//    标签id json 数组
    private String labelId;

//    标识符 标记 淘潮玩推荐的类别
    private Integer tcFlag;

    @Transient
    private  TcTemplate tcTemplate;

    @Transient
    private Integer searchTemplateId;
    //1 商城首页 0 淘潮玩主页 默认 商城首页
    @Transient
    private Boolean isHome;
}
