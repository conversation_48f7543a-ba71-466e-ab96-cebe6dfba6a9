package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/11
 */
public interface ByOrderLogService {

    /**
     * 异步添加日志
     * @param byOrderLog
     */
    void addOrderLogAsync(ByOrderLog byOrderLog);

    /**
     * 添加一条订单日志
     * @return
     */
    Boolean addOrderLog(ByOrderLog byOrderLog);

    /**
     * 根据订单号查询订单日志
     * @param orderNo
     * @return
     */
    List<ByOrderLog> queryOrderLog(String orderNo);
}
