package com.wmeimob.fastboot.baoyan.entity;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

@Table(name = "ych_order")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YchOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private Integer id;

    /**
     * 第三方订单号：必须唯一，建议使用GUID,UUID
     */
    @Column(name = "tp_order_no")
    private String TPOrderNo;

    /**
     * 油菜花订单ID
     */
    private String ychOrderId;

    /**
     * 油菜花订单号
     */
    private String orderNo;

    /**
     * 商品类型 2：预存款，101代币，102 游乐套票，6 组合商品
     */
    private Integer goodsType;

    /**
     * 会员Id
     */
    private String leaguerId;

    /**
     * 门店ID
     */
    private String businessId;

    /**
     * 订购人
     */
    private String guestName;

    /**
     * GuestMobile
     */
    private String guestMobile;

    /**
     * 订单金额，验签请加.0
     */
    private Float orderMoney;

    /**
     * 传空字符串""
     */
    private String sendAddress;

    /**
     * 备注
     */
    private String summary;

    /**
     * 优惠券号
     */
    private String couponNumber;

    /**
     * 客户端计价。默认值“IN” 服务端计价
     */
    private String IsOutPrice;

    /**
     * 订单类型（0无，1小程序，2社群）
     */
    private Integer orderType;

    /**
     * 是否不适用优惠券
     */
    private Integer isNotUseCoupon;

    /**
     * 支付流水号
     */
    private String payFlowNo;

    /**
     * 支付状态（0未支付，1已支付，2已退款）
     */
    private Integer payState;

    private String payTime;

    private List<YchOrderItem> orderItem;

    private String gmtCreate;

    private String gmtModified;
}
