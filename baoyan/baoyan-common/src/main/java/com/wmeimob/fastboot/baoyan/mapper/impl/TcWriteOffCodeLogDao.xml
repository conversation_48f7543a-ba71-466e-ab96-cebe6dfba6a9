<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeLogMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcWriteOffCodeLog" id="TcWriteOffCodeLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="writeOffId" column="write_off_id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="INTEGER"/>
        <result property="staffId" column="staff_id" jdbcType="INTEGER"/>
        <result property="writeOffDate" column="write_off_date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="custUserId" column="cust_user_id" jdbcType="INTEGER"/>
        <result property="writeOffNum" column="write_off_num" jdbcType="INTEGER"/>
    </resultMap>


    <select id="findByCondition" resultMap="TcWriteOffCodeLogMap">
        SELECT log.id id, code.`write_off_name` writeOffName,
        code.`order_no` orderNo, log.`cust_user_id`, user.`nick_name` nickName,
        log.`write_off_date`, store.name storeName, staff.staff_name staffName
        FROM tc_write_off_code_log `log`
        LEFT JOIN tc_write_off_code `code`
        ON log.`write_off_id` = code.`id`
        LEFT JOIN by_cust_user `user`
        ON log.`cust_user_id` = user.`id`
        left join by_store_staff staff
        on staff.id = log.staff_id
        left join base_store store
        on staff.store_id = store.id
        <where>
            <if test="startTime!=null and startTime!=''">
                and DATE_FORMAT(log.write_off_date,'%Y-%m-%d') >= #{startTime}
            </if>
            <if test="endTime!=null and endTime!=''">
                and DATE_FORMAT(log.write_off_date,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="searchName!=null and searchName!=''">
                and
                (
                code.order_no like concat('%',#{searchName},'%')
                or
                user.mobile like concat('%',#{searchName},'%')
                )
            </if>
        </where>
        ORDER BY log.id DESC
    </select>
</mapper>