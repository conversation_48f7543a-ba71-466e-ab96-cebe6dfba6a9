<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.BaseClassifyMapper">
    <resultMap type="com.wmeimob.fastboot.baoyan.entity.BaseClassify" id="BaseResultMap">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result property="classifyTitle" column="classify_title" jdbcType="VARCHAR"/>
		<result property="classifyImg" column="classify_img" jdbcType="VARCHAR"/>
		<result property="isDel" column="is_del" jdbcType="TINYINT"/>
		<result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
		<result property="gmtUpdate" column="gmt_update" jdbcType="TIMESTAMP"/>
		<result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <!--<select id="select" resultMap="BaseResultMap" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseClassify">
		SELECT 
			`id`,
			`classify_title`,
			`classify_img`,
			`is_del`,
			`gmt_create`,
			`gmt_update`
		FROM  base_classify
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="classifyTitle != '' and classifyTitle != null">
			  	AND `classify_title` = #{classifyTitle}
			</if>
			<if test="classifyImg != '' and classifyImg != null">
			  	AND `classify_img` = #{classifyImg}
			</if>
			<if test="isDel != null">
			  	AND `is_del` = #{isDel}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtUpdate != null">
			  	AND `gmt_update` = #{gmtUpdate}
			</if>
		</where>
		ORDER  BY id DESC
    </select>


    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		SELECT 
			`id`,
			`classify_title`,
			`classify_img`,
			`is_del`,
			`gmt_create`,
			`gmt_update`
		FROM base_classify
		WHERE id = #{id}
	</select>

    <insert id="insertSelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseClassify" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO base_classify
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			`id`,
		</if>
		<if test="classifyTitle != '' and classifyTitle != null">
			`classify_title`,
		</if>
		<if test="classifyImg != '' and classifyImg != null">
			`classify_img`,
		</if>
		<if test="isDel != null">
			`is_del`,
		</if>
		<if test="gmtCreate != null">
			`gmt_create`,
		</if>
		<if test="gmtUpdate != null">
			`gmt_update`,
		</if>
		</trim>
		) VALUES 
		<trim prefix="(" suffixOverrides=","> 
		<if test="id != null">
			#{id},	
		</if>
		<if test="classifyTitle != '' and classifyTitle != null">
			#{classifyTitle},	
		</if>
		<if test="classifyImg != '' and classifyImg != null">
			#{classifyImg},	
		</if>
		<if test="isDel != null">
			#{isDel},	
		</if>
		<if test="gmtCreate != null">
			#{gmtCreate},	
		</if>
		<if test="gmtUpdate != null">
			#{gmtUpdate},	
		</if>
		</trim>
		)
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseClassify">
		UPDATE base_classify
		<trim prefix="set" suffixOverrides=","> 
			<if test="id != null">
				`id` = #{id},
			</if>
			<if test="classifyTitle != '' and classifyTitle != null">
				`classify_title` = #{classifyTitle},
			</if>
			<if test="classifyImg != '' and classifyImg != null">
				`classify_img` = #{classifyImg},
			</if>
			<if test="isDel != null">
				`is_del` = #{isDel},
			</if>
			<if test="gmtCreate != null">
				`gmt_create` = #{gmtCreate},
			</if>
			<if test="gmtUpdate != null">
				`gmt_update` = #{gmtUpdate},
			</if>
		</trim>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteByPrimaryKey" parameterType="map">
		DELETE FROM base_classify WHERE id = #{id}
	</delete>

	<delete id="delete" parameterType="com.wmeimob.fastboot.baoyan.entity.BaseClassify">
		DELETE FROM base_classify
		<where>
			1 = 1
			<if test="id != null">
			  	AND `id` = #{id}
			</if>
			<if test="classifyTitle != '' and classifyTitle != null">
			  	AND `classify_title` = #{classifyTitle}
			</if>
			<if test="classifyImg != '' and classifyImg != null">
			  	AND `classify_img` = #{classifyImg}
			</if>
			<if test="isDel != null">
			  	AND `is_del` = #{isDel}
			</if>
			<if test="gmtCreate != null">
			  	AND `gmt_create` = #{gmtCreate}
			</if>
			<if test="gmtUpdate != null">
			  	AND `gmt_update` = #{gmtUpdate}
			</if>
		</where>
	</delete> -->
</mapper>

