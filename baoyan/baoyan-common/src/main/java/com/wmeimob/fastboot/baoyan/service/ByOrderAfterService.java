package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName ByOrderAfterService
 * @Description 订单售后
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/
public interface ByOrderAfterService extends CommonService<ByOrderAfter>{

    /**
     * 根据id查询售后
     * @param id
     * @return
     */
    @Override
    ByOrderAfter findById(Integer id);

    /**
     * 订单售后查询
     * @param id
     * @return
     */
    default ByOrderAfter queryByOrderAfterById(Object id){throw new NotImplementedException("queryByOrderAfterById");};

    /**
     * 审核售后订单
     * @param byOrderAfter
     */
    default void auditAfterOrder(ByOrderAfter byOrderAfter){throw new NotImplementedException("auditAfterOrder");};


    /**
     * 查询该订单可以退多少件，退多少钱
     * @param byOrderAfter
     * @return
     */
    OrderAfterDetailVO queryAfterByOrderId(ByOrderAfter byOrderAfter);

    /**
     * 退款逻辑
     * @param byOrderAfter
     * @return
     */
    boolean returnSingeOrder(ByOrderAfter byOrderAfter);

    boolean isAllRefund(Integer detailId);

    /**
     * 退款的方法
     * @param orderNo 商家订单号
     * @param refundNo 退款记录号
     * @param totalFee 支付的总金额
     * @param refundFee 退款金额
     */
    void refundPay(String orderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee);

    /**
     * 根据核销卡进行退款
     * @param ids
     * @return
     */
    boolean returnByWrite(List<Integer> ids, Integer resouceType);
}
