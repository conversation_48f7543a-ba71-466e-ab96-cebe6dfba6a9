package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.core.service.CommonService;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import java.util.List;

/**
 * @ClassName ByTeamService
 * @Description 拼团管理
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
public interface ByTeamService extends CommonService<ByTeam>{

    /**
     * 拼团管理查询
     * @param id
     * @return
     */
    default ByTeam queryByTeamById(Object id){throw new NotImplementedException("queryByTeamById");};

    /**
     * 拼团管理添加
     * @param  byTeam
     * @return
     */
    default  void addByTeam(ByTeam byTeam){throw new NotImplementedException("addByTeam");};


    /**
     * 拼团管理删除
     * @param id
     * @return
     */
    default void removeByTeam(Object id){throw new NotImplementedException("removeByTeam");};


    /**
     * 拼团管理修改
     * @param byTeam
     * @return
     */
    default void modifyByTeam(ByTeam byTeam){throw new NotImplementedException("modifyByTeam");};
}
