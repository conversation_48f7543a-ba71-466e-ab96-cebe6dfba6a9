
/*
* ByPlatformSetMapper.java
* http://www.wmeimob.com
* Copyright © 2018 wmeimob All Rights Reserved,version1.0
* 作者：xbing
* Mon Jul 22 17:18:53 CST 2019 Created
* 
*/ 
package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.ByPlatformSet;
import com.wmeimob.fastboot.core.orm.Mapper;

public interface ByPlatformSetMapper extends Mapper<ByPlatformSet> {

    /**
     * 更新 完善信息优惠券
     * @param byPlatformSet
     */
    void updatePerfectCoupon(ByPlatformSet byPlatformSet);
    /**
     * 更新 生日优惠券
     * @param byPlatformSet
     */
    void updateBirthdayCoupon(ByPlatformSet byPlatformSet);
}