package com.wmeimob.fastboot.baoyan.mapper;

import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 淘潮玩品牌表(TcBrand)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
public interface TcBrandMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TcBrand queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<TcBrand> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tcBrand 实例对象
     * @return 对象列表
     */
    List<TcBrand> queryAll(TcBrand tcBrand);

    /**
     * 新增数据
     *
     * @param tcBrand 实例对象
     * @return 影响行数
     */
    int insert(TcBrand tcBrand);

    /**
     * 修改数据
     *
     * @param tcBrand 实例对象
     * @return 影响行数
     */
    int update(TcBrand tcBrand);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}