package com.wmeimob.fastboot.baoyan.service;


import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.core.service.CommonService;

/**
 * @ClassName ByOrderAfterService
 * @Description 订单售后
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/
public interface WechatOrderAfterService extends CommonService<ByOrderAfter>{
    /**
     * 订单售后查询
     * @param id
     * @return
     */
    default ByOrderAfter queryByOrderAfterById(Integer id){throw new NotImplementedException("queryByOrderAfterById");};

    /**
     * 查看售后详情 回显
     * @param byOrderAfter
     * @return
     */
    default OrderAfterDetailVO queryAfterByOrderIdAndGoodsId(ByOrderAfter byOrderAfter){throw new NotImplementedException("queryAfterByOrderIdAndGoodsId");};

    /**
     * 删除售后订单
     * @param byOrderAfter
     * @return
     */
    default RestResult deleteOrderAfter(ByOrderAfter byOrderAfter){throw new NotImplementedException("deleteOrderAfter");};

}
