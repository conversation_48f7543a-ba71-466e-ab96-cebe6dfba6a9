<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcOrderGoods" id="TcOrderGoodsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tcOrderId" column="tc_order_id" jdbcType="INTEGER"/>
        <result property="tcGoodsId" column="tc_goods_id" jdbcType="INTEGER"/>
        <result property="tcGoodsName" column="tc_goods_name" jdbcType="VARCHAR"/>
        <result property="tcGoodsImg" column="tc_goods_img" jdbcType="VARCHAR"/>
        <result property="goodsCount" column="goods_count" jdbcType="INTEGER"/>
        <result property="goodsPrice" column="goods_price" jdbcType="NUMERIC"/>
        <result property="unitPrice" column="unit_price" jdbcType="NUMERIC"/>
        <result property="refundCount" column="refund_count" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryByOrderId" resultMap="TcOrderGoodsMap">
        select
          id, tc_order_id, tc_order_no, tc_goods_id, tc_goods_name,
          tc_goods_img, goods_count, goods_price, unit_price, status, refund_count
        from tc_order_goods
        where tc_order_id = #{orderId}
    </select>

    <update id="updateToPaid">
        update tc_order_goods
        set status = 2,
        can_after = 1
        where tc_order_id = #{orderId}
    </update>

    <update id="updateStatus">
        update tc_order_goods
        set status = #{status}
        where tc_order_id = #{orderId}
    </update>

</mapper>