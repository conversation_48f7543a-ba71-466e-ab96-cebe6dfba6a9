package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.io.Serializable;

/**
 * (ByClassifyRecommend)实体类
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:47
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class ByClassifyRecommend implements Serializable {
    private static final long serialVersionUID = -91836634338616650L;
    /**
    * id自增,无意义
    */
    private Integer id;
    /**
    * 推荐标题
    */
    private String text;
    /**
    * 跳转类型(1.商品分类2.联票列表3.次卡列表 4.拼团列表5.文章6.普通商品
     * 7.联票商品 8.次卡商品9.拼团商品10.优惠券 11 淘潮玩);
    */
    private Integer jumpType;
    /**
    * 跳转content
    */
    private String target;
    /**
    * 排序值
    */
    private Integer sort;
    /**
    * 商品id
    */
    private Integer goodsId;
    /**
    * 封面图
    */
    private String coverImg;
    /**
    * 品类id
    */
    private Integer cateId;
    /**
    * 品牌id
    */
    private Integer brandId;
    /**
    * 0下架 1上架 默认上架
    */
    private Boolean isShelves;
    /**
    * 0删除 1 未删除 默认1
    */
    private Boolean isDel;
    /**
    * 创建时间
    */
    private Date gmtCreate;
    /**
    * 修改时间
    */
    private Date gmtUpdate;
    /**
    * 标签id
    */
    private String labelId;

    /**
    * 标识符 标记 淘潮玩推荐的类别
    */
    private Integer tcFlag;




}