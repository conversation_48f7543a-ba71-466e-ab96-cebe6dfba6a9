/*
 * ByTicketGoods.java
 * http://www.wmeimob.com
 * Copyright © 2018 wmeimob All Rights Reserved,version1.0
 * 作者：xbing
 * Thu Jul 25 13:46:23 CST 2019 Created
 */
package com.wmeimob.fastboot.baoyan.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Table(name = "by_ticket_goods")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Accessors(chain = true)
public class ByTicketGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商品名称
     */
    @Column(name = "ticket_goods_name")
    private String ticketGoodsName;
    /**
     * 出售价格
     */
    @Column(name = "sell_price")
    private BigDecimal sellPrice;
    /**
     * 市场价格
     */
    @Column(name = "market_price")
    private BigDecimal marketPrice;
    /**
     * 起始销量
     */
    @Column(name = "initial_sale_num")
    private Integer initialSaleNum;
    /**
     * 实际销量
     */
    @Column(name = "actual_sales_num")
    private Integer actualSalesNum;

    @Transient
    private Integer goodsCount;


    public Integer getGoodsCount() {
        return actualSalesNum;
    }

    public void setGoodsCount(Integer goodsCount) {
        this.goodsCount = goodsCount;
    }

    /**
     /**
     * 商品库存
     */
    @Column(name = "goods_stock")
    private Integer goodsStock;
    /**
     * 排序值
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 商品图片
     */
    @Column(name = "goods_img")
    private String goodsImg;
    /**
     * 商品banner图片
     */
    @Column(name = "goods_banner")
    private String goodsBanner;
    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @Column(name = "gmt_update")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtUpdate;
    /**
     * 有效开始日期
     */
    @Column(name = "effective_start")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date effectiveStart;
    /**
     * 有效结束日期
     */
    @Column(name = "effective_end")
    @JsonFormat(locale = "zh", pattern = "yyyy-MM-dd")
    private Date effectiveEnd;

    /**
     * 上下架 0下架 1上架售卖 2上架隐藏 3定时上架
     */
    @Column(name = "status")
    private Boolean status;

    /**
     * 是否指定核销天数
     */
    @Column(name = "has_verification_day")
    private Boolean hasVerificationDay;
    /**
     * 指定天数内有效
     */
    @Column(name = "verification_day")
    private Integer verificationDay;
    /**
     * 定时下架状态 0:未开启 1:开启
     */
    @Column(name = "regular_down_status")
    private Boolean regularDownStatus;

    /**
     * 组合油菜花预存款套餐，支持赠送本金和赠金
     */
    @Column(name = "pre_deposit_id")
    private Integer preDepositId;

    /**
     * 首次核销金额
     */
    @Column(name = "first_verify_balance")
    private BigDecimal firstVerifyBalance;

    /**
     * 是否删除
     */
    @Column(name = "is_del")
    private Boolean isDel;
    /**
     * 查询名称
     */
    @Transient
    private String searchName;
    /**
     * 富文本内容
     */
    @Transient
    private String richContent;
    /**
     * 商品名称
     */
    @Transient
    private String goodsName;

    public String getGoodsName() {
        return ticketGoodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    @Transient
    private String showGoodsName;
    /**
     * 商品id （1,2,）
     */
    @Transient
    private String  goodsIds;
    /**
     * 联票选择商品逗号分隔
     */
    @Transient
    private String ticketGoodsId;
    private List<String>ticketGoodList;
}