<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wmeimob.fastboot.baoyan.mapper.TcOrderParentMapper">

    <resultMap type="com.wmeimob.fastboot.baoyan.entity.TcOrderParent" id="tcOrderParentMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderParentNo" column="order_parent_no" jdbcType="VARCHAR"/>
        <result property="orderAmount" column="order_amount" jdbcType="NUMERIC"/>
        <result property="payAmount" column="pay_amount" jdbcType="NUMERIC"/>
        <result property="couponAmount" column="coupon_amount" jdbcType="NUMERIC"/>
        <result property="integralAmount" column="integral_amount" jdbcType="NUMERIC"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findByOrderParentNo" resultMap="tcOrderParentMap">
        select *
        from tc_order_parent
        where order_parent_no = #{no}
    </select>

</mapper>