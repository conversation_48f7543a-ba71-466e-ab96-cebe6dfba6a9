package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsShoping;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.util.InputValidator;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 购物车
 * @date 2019-08-13 13:45
 * @Version 1.0
 */
public interface ByGoodsShoppingService {

    /**
     * 统计用户的淘潮玩和门票购物车数量总和
     * @return
     */
    default int cartCount(Integer userId){
        throw new CustomException("cartCount");
    }

    /**
    * @Description 我的购物车
    * <AUTHOR>
    * @Date        2019-08-13 13:50
    * @Version    1.0
    */
    default List<ByGoodsShoping> show(){
        return null;
    };


    /**
    * @Description 添加购物车
    * <AUTHOR>
    * @Date        2019-08-13 13:50
    * @Version    1.0
    */

    default RestResult insert(ByGoodsShoping byGoodsShoping){
        return null;
    };

    /**
     * @Description 删除购物车
     * <AUTHOR>
     * @Date        2019-08-13 16:52
     * @Version    1.0
     */
    default RestResult delete(String ids){
        return null;
    };

    default RestResult delete(List<Integer> ids) {
        return null;
    }

    /**
     * @Description 修改购物车
     * <AUTHOR>
     * @Date        2019-08-13 17:01
     * @Version    1.0
     */
    default RestResult update(ByGoodsShoping byGoodsShoping){
        return null;
    };

    default Map<String, Object> showAll(List<ByGoodsShoping> byGoodsShoping){
        return null;
    };
}
