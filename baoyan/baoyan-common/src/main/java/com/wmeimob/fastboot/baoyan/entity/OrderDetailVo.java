package com.wmeimob.fastboot.baoyan.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import me.hao0.wepay.model.pay.JsPayResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付前响应给前端的详情
 * <AUTHOR>
 * @date 2021/7/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailVo {

    /**
     * 可用优惠券
     */
    private List<ByCouponUser> coupon;
    /**
     * 是否可用优惠券
     */
    private boolean couponType;

    /**
     * 总金额，没有抵扣的价格
     */
    private BigDecimal goodsAmount;

    /**
     * 可使用{{fraction}}积分抵扣 ¥{{count}}
     */
    private int count;

    private List<ByCouponUser> couponCount;

    /**
     * 淘潮商品信息
     */
    private List<TcGoodsShoping> tcGoods;

    /**
     * 门票
     */
    private List<ByGoodsInfo> goods;

    /**
     * 商品数量
     */
    private int goodsCount;

    /**
     * 是否可用积分
     */
    private boolean type;

    /**
     * 可使用{{fraction}}积分抵扣 ¥{{count}}
     */
    private int fraction;
    /**
     * 用户的积分数量
     */
    private Integer userPoint;

}
