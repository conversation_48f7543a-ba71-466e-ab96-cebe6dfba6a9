/*
Navicat MySQL Data Transfer

Source Server         : 宝燕-prd
Source Server Version : 80015
Source Host           : localhost:3306
Source Database       : baoyan

Target Server Type    : MYSQL
Target Server Version : 80015
File Encoding         : 65001

Date: 2020-09-14 15:51:25
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for base_activity
-- ----------------------------
DROP TABLE IF EXISTS `base_activity`;
CREATE TABLE `base_activity` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `activity_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动名称',
  `img_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片地址',
  `jump_type` int(11) DEFAULT NULL COMMENT '跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品）',
  `target` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '跳转content',
  `rank` int(11) DEFAULT NULL COMMENT '排序',
  `status` tinyint(1) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='活动表';

-- ----------------------------
-- Table structure for base_banner
-- ----------------------------
DROP TABLE IF EXISTS `base_banner`;
CREATE TABLE `base_banner` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
  `status` int(11) DEFAULT NULL COMMENT '是否上架 0 不上架 1上架',
  `jump_type` int(11) DEFAULT NULL COMMENT '跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品）',
  `target` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '跳转content',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_pl_0900_ai_ci COMMENT='Banner';

-- ----------------------------
-- Table structure for base_base_region
-- ----------------------------
DROP TABLE IF EXISTS `base_base_region`;
CREATE TABLE `base_base_region` (
  `id` int(6) NOT NULL COMMENT '主键ID',
  `name` varchar(15) NOT NULL DEFAULT '' COMMENT '简称',
  `full_name` varchar(30) NOT NULL DEFAULT '' COMMENT '全称',
  `pinyin` varchar(30) NOT NULL DEFAULT '' COMMENT '简称拼音（空格间隔）',
  `lat` decimal(8,5) NOT NULL DEFAULT '38.65180' COMMENT '纬度',
  `lng` decimal(8,5) NOT NULL DEFAULT '104.07642' COMMENT '经度',
  `pid` int(6) NOT NULL DEFAULT '0' COMMENT '父ID（额外加的）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='微信文档中获取的地区信息';

-- ----------------------------
-- Table structure for base_classify
-- ----------------------------
DROP TABLE IF EXISTS `base_classify`;
CREATE TABLE `base_classify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `classify_title` varchar(50) NOT NULL COMMENT '分类名称',
  `classify_img` varchar(255) DEFAULT NULL COMMENT '图片地址',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除;1:删除,0:未删除.',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `sort` int(100) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类表';

-- ----------------------------
-- Table structure for base_navigation_conf
-- ----------------------------
DROP TABLE IF EXISTS `base_navigation_conf`;
CREATE TABLE `base_navigation_conf` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `img_url` varchar(255) DEFAULT NULL COMMENT 'logo图片地址',
  `jump_type` int(11) DEFAULT NULL COMMENT '跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品）',
  `status` tinyint(1) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `target` text COMMENT '跳转content',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `sort` int(100) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导航设置';

-- ----------------------------
-- Table structure for base_region
-- ----------------------------
DROP TABLE IF EXISTS `base_region`;
CREATE TABLE `base_region` (
  `id` int(6) NOT NULL COMMENT '主键ID',
  `name` varchar(15) NOT NULL DEFAULT '' COMMENT '简称',
  `full_name` varchar(30) NOT NULL DEFAULT '' COMMENT '全称',
  `pinyin` varchar(30) NOT NULL DEFAULT '' COMMENT '简称拼音（空格间隔）',
  `lat` decimal(8,5) NOT NULL DEFAULT '38.65180' COMMENT '纬度',
  `lng` decimal(8,5) NOT NULL DEFAULT '104.07642' COMMENT '经度',
  `pid` int(6) NOT NULL DEFAULT '0' COMMENT '父ID（额外加的）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='微信文档中获取的地区信息';

-- ----------------------------
-- Table structure for base_store
-- ----------------------------
DROP TABLE IF EXISTS `base_store`;
CREATE TABLE `base_store` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) DEFAULT NULL COMMENT '编号',
  `name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `mobile` varchar(50) DEFAULT NULL COMMENT '电话',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `store_img` varchar(255) DEFAULT NULL COMMENT '门店图片',
  `point` varchar(100) DEFAULT NULL COMMENT '经纬度',
  `appointment_num` int(11) DEFAULT NULL COMMENT '预约人数',
  `full_num` int(11) DEFAULT NULL COMMENT '即将满员人数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态  0:禁用  1:启用',
  `pay_pwd` varchar(100) DEFAULT NULL COMMENT '支付密码',
  `delete_status` tinyint(255) DEFAULT '0' COMMENT '0-可用,1-删除',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `longitude` varchar(255) DEFAULT NULL COMMENT '经度',
  `latitude` varchar(255) DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店';

-- ----------------------------
-- Table structure for by_article
-- ----------------------------
DROP TABLE IF EXISTS `by_article`;
CREATE TABLE `by_article` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `article_name` varchar(50) DEFAULT NULL COMMENT '文章名称',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '文章内容',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文章表';

-- ----------------------------
-- Table structure for by_coupon
-- ----------------------------
DROP TABLE IF EXISTS `by_coupon`;
CREATE TABLE `by_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `temp_id` int(11) DEFAULT NULL,
  `total_limit` int(11) DEFAULT NULL COMMENT '每人？次',
  `day_limit` int(11) DEFAULT NULL COMMENT '每人每天？次',
  `total` int(11) DEFAULT NULL COMMENT '优惠券总数',
  `taked` int(11) DEFAULT '0' COMMENT '已领取数',
  `sort` int(11) DEFAULT NULL,
  `state` tinyint(1) DEFAULT NULL COMMENT '0下架1上架',
  `is_del` tinyint(1) DEFAULT NULL COMMENT '0否1是',
  `target_id` int(11) DEFAULT NULL,
  `gmt_create` datetime DEFAULT NULL,
  `gmt_update` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='优惠券';

-- ----------------------------
-- Table structure for by_coupon_temp
-- ----------------------------
DROP TABLE IF EXISTS `by_coupon_temp`;
CREATE TABLE `by_coupon_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) DEFAULT NULL COMMENT '优惠券名称',
  `target_id` int(11) DEFAULT '0' COMMENT '0是全部商品，大于0 是分类ID或者商品ID',
  `discount` decimal(10,2) DEFAULT NULL,
  `full` decimal(10,2) DEFAULT NULL COMMENT '满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）',
  `start_date` date DEFAULT NULL COMMENT '有效期',
  `end_date` date DEFAULT NULL COMMENT '有效期',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '0否1是',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_update` datetime DEFAULT NULL,
  `type` int(1) DEFAULT NULL COMMENT '类型 0 全部商品 1 分类商品 2单个商品',
  `coupon_type` int(11) DEFAULT NULL COMMENT '1优惠券（抵扣卷） 2折扣卷',
  `effective_type` int(11) DEFAULT NULL COMMENT '有效类型 1 日期范围 2天数',
  `day_num` int(11) DEFAULT NULL COMMENT '天數',
  `single_goods_type` int(1) DEFAULT NULL COMMENT '单个商品类型 1 普通商品 2 次卡 3 联票',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='优惠券模板';

-- ----------------------------
-- Table structure for by_coupon_user
-- ----------------------------
DROP TABLE IF EXISTS `by_coupon_user`;
CREATE TABLE `by_coupon_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL COMMENT '优惠券ID(若是赠送优惠券 此处值为tempId 反之couponId)',
  `target_id` int(11) DEFAULT '0' COMMENT '0是全部商品，大于0 是分类ID或者商品ID',
  `name` varchar(20) DEFAULT NULL COMMENT '优惠券名',
  `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣金额',
  `full` decimal(10,2) DEFAULT '0.00' COMMENT '满多少减（不填或为0即不限制，此时类型为抵扣券，反之为优惠券）',
  `start_date` date DEFAULT NULL COMMENT '有效期',
  `end_date` date DEFAULT NULL COMMENT '有效期',
  `is_use` tinyint(1) DEFAULT NULL COMMENT '0未使用，1使用，2过期',
  `in_batch` varchar(20) DEFAULT NULL COMMENT '导入记录',
  `is_give` int(1) DEFAULT '0' COMMENT '是否赠送优惠券 0 否 1是',
  `get_type` tinyint(1) DEFAULT NULL COMMENT '领取方式，1后台发放，2前端领取',
  `use_date` datetime DEFAULT NULL COMMENT '使用时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `audit_status` int(1) DEFAULT '0' COMMENT '审核状态 0 待审核 1已通过 2已拒绝',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人id',
  `audit_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '审核时间',
  `issuer_user_id` int(11) DEFAULT NULL COMMENT '发放人',
  `refuse_reason` varchar(255) DEFAULT NULL COMMENT '拒绝理由',
  `coupon_type` int(11) DEFAULT NULL COMMENT '1优惠券（抵扣卷） 2折扣卷',
  `type` int(255) DEFAULT NULL COMMENT '类型 0 全部商品 1 分类商品 2单个商品',
  `single_goods_type` int(1) DEFAULT NULL COMMENT '单个商品类型 1 普通商品 2 次卡 3 联票',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_issuer_user_id` (`issuer_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='优惠券领取';

-- ----------------------------
-- Table structure for by_cust_appointment
-- ----------------------------
DROP TABLE IF EXISTS `by_cust_appointment`;
CREATE TABLE `by_cust_appointment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `apponintment_type` int(11) DEFAULT NULL COMMENT '0 已预约 1.已取消 2.已过期',
  `apponintment_date` date DEFAULT NULL COMMENT '预约日期',
  `apponintment_num` int(11) DEFAULT NULL COMMENT '预约数量',
  `ticket_channel` int(1) DEFAULT NULL COMMENT '购票渠道(0 乐园微商城 1大众点评/美团 2其他平台)',
  `cust_user_id` int(11) DEFAULT NULL COMMENT '预约用户id',
  `address` varchar(255) DEFAULT NULL COMMENT '预约门店地址',
  `store_id` int(11) DEFAULT NULL COMMENT '门店id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除 0 未删除 1删除',
  `apponintment_status` int(1) DEFAULT NULL COMMENT '预约状态 0 已预约 1已取消 2已结束',
  `tel` varchar(50) DEFAULT NULL COMMENT '预约手机号',
  `parent_name` varchar(255) DEFAULT NULL COMMENT '家长姓名',
  `child_name` varchar(255) DEFAULT NULL COMMENT '小孩姓名',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `child_sex` varchar(255) DEFAULT NULL COMMENT '性别小朋友性别 0男 1女',
  `product_id` int(11) DEFAULT NULL COMMENT '产品id',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `from_id` varchar(255) DEFAULT NULL COMMENT '模板id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_cust_user_id` (`cust_user_id`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1747 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='客户预约表';

-- ----------------------------
-- Table structure for by_cust_user
-- ----------------------------
DROP TABLE IF EXISTS `by_cust_user`;
CREATE TABLE `by_cust_user` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
  `head_img` varchar(150) DEFAULT NULL COMMENT '头像',
  `mobile` varchar(24) DEFAULT NULL COMMENT '手机号',
  `password` varchar(64) DEFAULT NULL COMMENT '登录密码',
  `parent_name` varchar(24) DEFAULT NULL COMMENT '家长姓名',
  `parent_phone` varchar(24) DEFAULT NULL COMMENT '家长联系方式',
  `child_name` varchar(24) DEFAULT NULL COMMENT '小朋友姓名',
  `child_age` int(3) DEFAULT NULL COMMENT '小朋友年龄',
  `child_sex` tinyint(1) DEFAULT NULL COMMENT '小朋友性别 0男 1女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `is_disable` tinyint(1) DEFAULT '0' COMMENT '禁用状态 默认正常0',
  `delete_status` tinyint(1) DEFAULT '0' COMMENT '是否删除;0:否,1:是',
  `wx_open_id` varchar(64) DEFAULT NULL COMMENT '微信授权登录唯一标识',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `now_point` int(11) DEFAULT '0' COMMENT '当前可用积分',
  `history_point` int(11) DEFAULT '0' COMMENT ' 历史总获得积分（累积积分）',
  `be_point` int(11) DEFAULT '0' COMMENT '即将获得积分（待到账积分）',
  `year_coupon_flag` tinyint(1) DEFAULT '0' COMMENT '本年是否领取过优惠券',
  `show_red_count` int(255) DEFAULT '0' COMMENT '是否显示小红点',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37551 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- ----------------------------
-- Table structure for by_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `by_evaluate`;
CREATE TABLE `by_evaluate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL COMMENT '订单id',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `resouce_type` int(1) DEFAULT NULL COMMENT '商品类型，1-普通，2-次卡 3-联票 4-拼团',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `mark` int(11) DEFAULT NULL COMMENT '评分',
  `user_name` varchar(50) DEFAULT NULL COMMENT '评价人',
  `order_no` varchar(255) DEFAULT NULL COMMENT '订单号',
  `evaluate_text` varchar(1024) DEFAULT NULL COMMENT '评价内容',
  `img` varchar(1024) DEFAULT NULL COMMENT '图片',
  `is_used` int(11) DEFAULT '0' COMMENT '状态：0：待回复，1：已回复',
  `is_show` int(11) DEFAULT '1' COMMENT '状态：0：隐藏，1：显示',
  `return_date` datetime DEFAULT NULL COMMENT '回复时间',
  `return_name` varchar(255) DEFAULT NULL COMMENT '回复人',
  `return_desc` varchar(255) DEFAULT NULL COMMENT '回复内容',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `is_anonymous` tinyint(1) DEFAULT NULL COMMENT '是否匿名;1:是,0:否',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除;1:是,0:否',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评价表';

-- ----------------------------
-- Table structure for by_goods_car
-- ----------------------------
DROP TABLE IF EXISTS `by_goods_car`;
CREATE TABLE `by_goods_car` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `num` int(11) DEFAULT NULL COMMENT '购买数量',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='购物车';

-- ----------------------------
-- Table structure for by_goods_classify
-- ----------------------------
DROP TABLE IF EXISTS `by_goods_classify`;
CREATE TABLE `by_goods_classify` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `classify_id` int(11) DEFAULT NULL COMMENT '类型id',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1832 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品类型关联表';

-- ----------------------------
-- Table structure for by_goods_info
-- ----------------------------
DROP TABLE IF EXISTS `by_goods_info`;
CREATE TABLE `by_goods_info` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `goods_no` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `sell_price` decimal(10,2) DEFAULT NULL COMMENT '出售价格',
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
  `initial_sale_num` int(11) DEFAULT '0' COMMENT '起始销量',
  `actual_sales_num` int(11) DEFAULT '0' COMMENT '实际销量',
  `goods_stock` int(11) DEFAULT '0' COMMENT '商品库存',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_banner` text COMMENT '商品banner图片',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint(1) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `verification_start` date DEFAULT NULL COMMENT '核销开始时间',
  `verification_end` date DEFAULT NULL COMMENT '核销结束时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否逻辑删除;1:删除,0:未删除.',
  `rich_id` int(10) DEFAULT NULL COMMENT '富文本内容外键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='商品信息表';

-- ----------------------------
-- Table structure for by_goods_shoping
-- ----------------------------
DROP TABLE IF EXISTS `by_goods_shoping`;
CREATE TABLE `by_goods_shoping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(255) DEFAULT NULL COMMENT '商品id',
  `goods_type` int(255) DEFAULT NULL COMMENT '商品类型 1  普通 2 次卡 3  联票',
  `goods_count` int(255) DEFAULT NULL COMMENT '商品数量',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `is_del` int(255) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7428 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for by_goods_store
-- ----------------------------
DROP TABLE IF EXISTS `by_goods_store`;
CREATE TABLE `by_goods_store` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `store_id` int(11) DEFAULT NULL COMMENT '门店id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1651 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='商品门店关系表';

-- ----------------------------
-- Table structure for by_integral_log
-- ----------------------------
DROP TABLE IF EXISTS `by_integral_log`;
CREATE TABLE `by_integral_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '客户id',
  `change_type` tinyint(1) DEFAULT NULL COMMENT '变更类型 1 增加2 减少',
  `change_num` int(11) DEFAULT NULL COMMENT '变更数量',
  `change_reason` varchar(150) DEFAULT NULL COMMENT '变更原因',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `before_num` int(11) DEFAULT NULL COMMENT '变更前数量',
  `integral_type` tinyint(1) DEFAULT NULL COMMENT '积分类型 1 评价 2购买商品 3积分抵扣',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=55199 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='积分明细记录表';

-- ----------------------------
-- Table structure for by_order_after
-- ----------------------------
DROP TABLE IF EXISTS `by_order_after`;
CREATE TABLE `by_order_after` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `order_goods_id` int(11) DEFAULT NULL COMMENT '订单商品id',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_num` int(11) DEFAULT NULL COMMENT '商品数量',
  `goods_price` decimal(10,2) DEFAULT NULL COMMENT '商品单价',
  `order_no` varchar(64) DEFAULT NULL COMMENT '订单编号',
  `after_type` int(11) DEFAULT NULL COMMENT '售后类型;''0重复购买'',''1买多了，买错了'',''2商家关店、装修'',''3预约不上'',''4门店活动更优惠'',''5活动计划有变、无时间消费''',
  `after_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `after_reason` varchar(255) DEFAULT NULL COMMENT '售后原因',
  `after_imgs` text COMMENT '售后图片',
  `after_status` int(4) DEFAULT '0' COMMENT '售后状态;0:待审核,1:已通过,2:已拒绝',
  `refuse_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `resouce_type` tinyint(1) DEFAULT NULL COMMENT '1:普通商品,2:拼团商品',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `real_refund` decimal(10,2) DEFAULT NULL COMMENT '实际退款',
  `return_type` int(1) DEFAULT NULL COMMENT '退款类型0 申请售后 1 已退款',
  `detail_id` int(11) DEFAULT NULL COMMENT '订单详情id',
  `is_del` int(1) DEFAULT '0' COMMENT '是否逻辑删除;1:删除,0:未删除.',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_goods_id` (`order_goods_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1462 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='订单售后';

-- ----------------------------
-- Table structure for by_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `by_order_goods`;
CREATE TABLE `by_order_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL COMMENT '主订单id',
  `order_no` varchar(64) DEFAULT NULL COMMENT '主订单编号',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `goods_no` varchar(20) DEFAULT NULL COMMENT '商品编号',
  `first_class` int(11) DEFAULT NULL COMMENT '一级分类id',
  `goods_price` decimal(10,2) DEFAULT NULL COMMENT '商品单价',
  `goods_num` int(11) DEFAULT NULL COMMENT '购买数量',
  `coupon_price` decimal(10,2) DEFAULT NULL COMMENT '优惠券抵扣金额',
  `is_after_sale` tinyint(1) DEFAULT '1' COMMENT '是否售后;1:是,0:否',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `order_status` tinyint(1) DEFAULT NULL COMMENT '订单状态:-1订单取消1-待付款,2-已付款,3-已完成,4-已关闭',
  `integral_price` decimal(10,2) DEFAULT '0.00' COMMENT '积分抵扣金额',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `product_id` varchar(255) DEFAULT NULL COMMENT '商品id',
  `product_type` varchar(255) DEFAULT NULL COMMENT '\r商品类型 1 普通商品 2 次卡 3 联票',
  `product_count` varchar(255) DEFAULT NULL COMMENT '关联产品购买数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=36390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='普通订单商品表';

-- ----------------------------
-- Table structure for by_orders
-- ----------------------------
DROP TABLE IF EXISTS `by_orders`;
CREATE TABLE `by_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单编号',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `coupon_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠券抵扣金额',
  `integral_amount` decimal(10,2) DEFAULT '0.00' COMMENT '积分抵扣金额',
  `order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单金额',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `order_time` datetime DEFAULT NULL COMMENT '下单时间',
  `order_status` tinyint(6) DEFAULT NULL COMMENT '订单状态:-1订单取消1-待付款,2-已付款,3-已完成,4-已关闭',
  `actual_amount` decimal(10,2) DEFAULT '0.00' COMMENT '实收金额',
  `coupon_id` int(11) DEFAULT NULL COMMENT '优惠券id',
  `goods_type` int(11) DEFAULT NULL COMMENT '商品类型，1-普通，2-拼团',
  `amount_before_discount` decimal(10,2) DEFAULT NULL COMMENT '订单优惠前总计金额',
  `amount_after_discount` decimal(10,2) DEFAULT NULL COMMENT '订单优惠后总计金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单优惠金额',
  `is_refund` tinyint(1) DEFAULT '0' COMMENT '退款状态 0-未申请退款订单,1-申请退款订单',
  `is_del` tinyint(1) DEFAULT NULL COMMENT '订单是否删除（客户） 0未删除 1删除',
  `pay_type` int(11) DEFAULT '1' COMMENT '支付方式 1微信支付',
  `pay_time` datetime DEFAULT NULL COMMENT '支付時間',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `is_after_sale` tinyint(1) DEFAULT NULL COMMENT ' 是否为售后;1:是,0:否',
  `eval_type` tinyint(1) DEFAULT '0' COMMENT '是否评价 0未评价1已评价',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `from_id` varchar(255) DEFAULT NULL,
  `pay_flow_no` varchar(255) DEFAULT NULL COMMENT '支付流水号',
  `order_close_type` int(1) DEFAULT NULL COMMENT '订单关闭类型 1.取消订单/订单超时 2.全部退款',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=34849 DEFAULT CHARSET=utf8 COMMENT='商品订单表';

-- ----------------------------
-- Table structure for by_pay_log
-- ----------------------------
DROP TABLE IF EXISTS `by_pay_log`;
CREATE TABLE `by_pay_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) DEFAULT NULL COMMENT '1普通订单,2拼团',
  `pay` tinyint(1) DEFAULT NULL COMMENT '1微信',
  `order_no` varchar(50) DEFAULT NULL,
  `code` varchar(20) DEFAULT NULL,
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='支付日志';

-- ----------------------------
-- Table structure for by_platform_set
-- ----------------------------
DROP TABLE IF EXISTS `by_platform_set`;
CREATE TABLE `by_platform_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `pay_time` int(11) DEFAULT '0' COMMENT '支付时间(小时)',
  `integral_return` decimal(10,2) DEFAULT NULL COMMENT '积分返赠',
  `comment_integral` int(11) DEFAULT NULL COMMENT '评论获得积分',
  `integration_deduction` int(11) DEFAULT NULL COMMENT '积分抵扣',
  `team_integral_is_enable` tinyint(1) DEFAULT '0' COMMENT '拼团商品是否可用积分 默认 0不使用',
  `ticket_integral_is_enable` tinyint(1) DEFAULT '0' COMMENT '联票商品是否可用积分 默认 0不使用',
  `sub_card_integral_is_enable` tinyint(1) DEFAULT '0' COMMENT '次卡商品是否可用积分 默认 0不使用',
  `general_integral_is_enable` tinyint(1) DEFAULT '0' COMMENT '普通商品是否可用积分 默认 0不使用',
  `team_coupon_is_enable` tinyint(1) DEFAULT '0' COMMENT '拼团商品是否可用优惠券 默认 0不使用',
  `ticket_coupon_is_enable` tinyint(1) DEFAULT '0' COMMENT '联票商品是否可用优惠券 默认 0不使用',
  `general_coupon_is_enable` tinyint(1) DEFAULT '0' COMMENT '普通商品是否可用优惠券 默认 0不使用',
  `sub_card_coupon_is_enable` tinyint(1) DEFAULT '0' COMMENT '次卡商品是否可用优惠券 默认 0不使用',
  `coupon_invalid` int(11) DEFAULT NULL COMMENT '优惠券失效前x天提醒用户/天',
  `birthday_coupon_id` int(11) DEFAULT NULL COMMENT '生日礼遇 赠品优惠券',
  `perfect_info_coupon_id` int(11) DEFAULT NULL COMMENT '完善有礼 赠品优惠券',
  `gmt_modified` date DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='平台设置表';

-- ----------------------------
-- Table structure for by_rich_text
-- ----------------------------
DROP TABLE IF EXISTS `by_rich_text`;
CREATE TABLE `by_rich_text` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `data_id` int(10) DEFAULT '0' COMMENT '数据引用ID',
  `data_type` int(10) DEFAULT '0' COMMENT '数据类型 1 商品 2 次卡 3联票  4拼团',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '内容',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='富文本';

-- ----------------------------
-- Table structure for by_store_staff
-- ----------------------------
DROP TABLE IF EXISTS `by_store_staff`;
CREATE TABLE `by_store_staff` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键 无符号 自动递增',
  `staff_name` varchar(20) NOT NULL COMMENT '员工姓名',
  `staff_phone` char(11) DEFAULT NULL COMMENT '员工手机号',
  `store_id` int(11) NOT NULL COMMENT '员工所属门店id',
  `open_id` varchar(255) DEFAULT NULL COMMENT '微信openid，同时用于是否绑定',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否逻辑删除;1:删除,0:未删除.',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店员工';

-- ----------------------------
-- Table structure for by_sub_card_goods
-- ----------------------------
DROP TABLE IF EXISTS `by_sub_card_goods`;
CREATE TABLE `by_sub_card_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `sub_card_goods_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `sub_card_goods_num` int(11) DEFAULT '0' COMMENT '次卡商品包含数量',
  `sell_price` decimal(10,2) DEFAULT NULL COMMENT '出售价格',
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
  `initial_sale_num` int(11) DEFAULT '0' COMMENT '起始销量',
  `actual_sales_num` int(11) DEFAULT '0' COMMENT '实际销量',
  `goods_stock` int(11) DEFAULT '0' COMMENT '商品库存',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_banner` text COMMENT '商品banner图片',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint(1) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `rich_id` int(11) DEFAULT NULL COMMENT '富文本id',
  `effective_start` datetime DEFAULT NULL COMMENT '有效开始日期',
  `effective_end` datetime DEFAULT NULL COMMENT '有效结束日期',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100006 DEFAULT CHARSET=utf8 COMMENT='次卡商品表';

-- ----------------------------
-- Table structure for by_team
-- ----------------------------
DROP TABLE IF EXISTS `by_team`;
CREATE TABLE `by_team` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `team_goods_id` int(11) DEFAULT NULL COMMENT '拼团商品id',
  `user_id` int(11) DEFAULT NULL COMMENT '团长用户id',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `goods_no` varchar(64) DEFAULT NULL COMMENT '商品编号',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `team_price` decimal(10,2) DEFAULT NULL COMMENT '拼团价',
  `team_num` int(10) DEFAULT NULL COMMENT '成团人数',
  `order_status` int(4) DEFAULT NULL COMMENT '拼团状态;-1:未支付(作废),0:拼团中,1:拼团成功,2:拼团失败.',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `end_date` datetime DEFAULT NULL COMMENT '拼团结束时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) DEFAULT NULL COMMENT '是否删除;1:是,0:否.',
  `team_name` varchar(255) DEFAULT NULL COMMENT '拼团名称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_team_goods_id` (`team_goods_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='拼团管理';

-- ----------------------------
-- Table structure for by_team_goods
-- ----------------------------
DROP TABLE IF EXISTS `by_team_goods`;
CREATE TABLE `by_team_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(10) DEFAULT NULL COMMENT '商品id',
  `sort_num` int(11) DEFAULT NULL COMMENT '排序值',
  `stock_num` int(11) DEFAULT NULL COMMENT '库存',
  `team_price` decimal(10,2) DEFAULT NULL COMMENT '拼团价',
  `org_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `team_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `team_person` int(11) DEFAULT NULL COMMENT '拼团人数',
  `team_num` int(11) DEFAULT NULL COMMENT '拼团次数',
  `team_hour` int(11) DEFAULT NULL COMMENT '拼团时间',
  `team_status` tinyint(1) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除;1:是,0:否',
  `rich_id` int(10) DEFAULT NULL COMMENT '富文本id',
  `reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `auto_hour` int(11) DEFAULT '0' COMMENT '自动拼团成功时间/小时',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_banner` text COMMENT '商品banner图片',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `write_count` varchar(255) DEFAULT NULL COMMENT '剩余核销次数',
  `code` varchar(255) DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `user_img` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='拼团商品表';

-- ----------------------------
-- Table structure for by_team_order
-- ----------------------------
DROP TABLE IF EXISTS `by_team_order`;
CREATE TABLE `by_team_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int(11) DEFAULT NULL COMMENT '拼团id',
  `team_goods_id` int(11) DEFAULT NULL COMMENT '拼团商品id',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `order_no` varchar(64) DEFAULT NULL COMMENT '订单编号',
  `pay_type` int(4) DEFAULT NULL COMMENT '支付方式;1:微信',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '拼团金额',
  `pay_amount` decimal(10,2) DEFAULT NULL COMMENT '实付金额',
  `num` int(11) DEFAULT NULL COMMENT '数量',
  `order_remark` varchar(255) DEFAULT NULL COMMENT '订单备注',
  `order_status` int(4) DEFAULT NULL COMMENT '订单状态;-2取消,-1:定单未支付(作废) 1-待付款,2-已付款,3-已完成,4-已关闭',
  `is_team` tinyint(1) DEFAULT '0' COMMENT '是否是团长;1:是,0:否',
  `is_after` tinyint(1) DEFAULT '0' COMMENT '是否为售后订单;1:是,0:否',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除;1:是,0:否',
  `point` int(11) DEFAULT NULL COMMENT '积分',
  `integral_amount` decimal(10,2) DEFAULT NULL COMMENT '积分抵扣金额',
  `coupon_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠券抵扣金额',
  `order_time` datetime DEFAULT NULL COMMENT '下单时间',
  `order_count` int(255) DEFAULT NULL COMMENT '拼团剩余人数 0 平团 完成',
  `order_state` tinyint(2) DEFAULT '0' COMMENT '是否为 机器人  0 否 1 是',
  `user_name` varchar(255) DEFAULT NULL COMMENT '支付流水号',
  `user_img` varchar(255) DEFAULT NULL,
  `form_id` varchar(255) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL COMMENT '优惠券id',
  `pay_flow_no` varchar(255) DEFAULT NULL COMMENT '支付流水号',
  `order_close_type` int(1) DEFAULT NULL COMMENT '订单关闭类型 1.取消订单/订单超时 2.全部退款',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_team_id` (`team_id`),
  KEY `idx_team_goods_id` (`team_goods_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_coupon_id` (`coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='拼团订单表';

-- ----------------------------
-- Table structure for by_team_property
-- ----------------------------
DROP TABLE IF EXISTS `by_team_property`;
CREATE TABLE `by_team_property` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int(11) NOT NULL COMMENT '拼团商品id',
  `property_id` int(11) DEFAULT NULL COMMENT '属性id',
  `property_val` varchar(255) DEFAULT NULL COMMENT '商品属性值',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='预售商品属性';

-- ----------------------------
-- Table structure for by_team_robot
-- ----------------------------
DROP TABLE IF EXISTS `by_team_robot`;
CREATE TABLE `by_team_robot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `obot_img` varchar(255) DEFAULT NULL COMMENT '机器人头像图片',
  `obot_nick_name` varchar(255) DEFAULT NULL COMMENT '机器人昵称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='拼团机器人';

-- ----------------------------
-- Table structure for by_ticket_goods
-- ----------------------------
DROP TABLE IF EXISTS `by_ticket_goods`;
CREATE TABLE `by_ticket_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_goods_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `sell_price` decimal(10,2) DEFAULT NULL COMMENT '出售价格',
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
  `initial_sale_num` int(11) DEFAULT '0' COMMENT '起始销量',
  `actual_sales_num` int(11) DEFAULT '0' COMMENT '实际销量',
  `goods_stock` int(11) DEFAULT '0' COMMENT '商品库存',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  `goods_img` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `goods_banner` text COMMENT '商品banner图片',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint(4) DEFAULT '0' COMMENT '上下架 0下架 1上架',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `effective_start` datetime DEFAULT NULL COMMENT '有效开始日期',
  `effective_end` datetime DEFAULT NULL COMMENT '有效结束日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=200001 DEFAULT CHARSET=utf8 COMMENT='联票商品表';

-- ----------------------------
-- Table structure for by_ticket_goods_mapping
-- ----------------------------
DROP TABLE IF EXISTS `by_ticket_goods_mapping`;
CREATE TABLE `by_ticket_goods_mapping` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_goods_id` int(11) DEFAULT NULL COMMENT '联票商品id',
  `goods_id` int(11) DEFAULT NULL COMMENT '商品id',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_update` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ticket_goods_id` (`ticket_goods_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='联票商品mapping表';

-- ----------------------------
-- Table structure for simple_config
-- ----------------------------
DROP TABLE IF EXISTS `simple_config`;
CREATE TABLE `simple_config` (
  `id` int(9) NOT NULL AUTO_INCREMENT,
  `config_name` varchar(255) DEFAULT NULL COMMENT '系統設置名稱',
  `config_value` varchar(255) DEFAULT NULL COMMENT '系統設置數值',
  `config_type` int(3) DEFAULT NULL COMMENT '系統設置 類型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='系統配置';

-- ----------------------------
-- Table structure for sys_application
-- ----------------------------
DROP TABLE IF EXISTS `sys_application`;
CREATE TABLE `sys_application` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `app_name` varchar(255) NOT NULL DEFAULT '' COMMENT '应用名称',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `instance_count` int(11) NOT NULL DEFAULT '0' COMMENT '实例数量',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用表';

-- ----------------------------
-- Table structure for sys_application_instance
-- ----------------------------
DROP TABLE IF EXISTS `sys_application_instance`;
CREATE TABLE `sys_application_instance` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `instance_name` varchar(255) NOT NULL DEFAULT '' COMMENT '应用的实例名称',
  `created_username` varchar(64) NOT NULL DEFAULT '' COMMENT '创建者账号',
  `instance_info` json NOT NULL COMMENT '实例信息',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用实例表';

-- ----------------------------
-- Table structure for sys_beetle_linked_list
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_linked_list`;
CREATE TABLE `sys_beetle_linked_list` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名',
  `table_content` varchar(255) DEFAULT NULL COMMENT '表备注',
  `field_name` varchar(255) DEFAULT NULL COMMENT '字段名',
  `field_conent` varchar(255) DEFAULT NULL COMMENT '字段内容',
  `field_type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 COMMENT='多表连接';

-- ----------------------------
-- Table structure for sys_beetle_params
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_params`;
CREATE TABLE `sys_beetle_params` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '后台管理用户id',
  `alias` varchar(20) DEFAULT NULL COMMENT '别名',
  `author` varchar(20) NOT NULL COMMENT '作者',
  `location_path` varchar(250) NOT NULL COMMENT '本地存放路径',
  `unite_package` varchar(200) NOT NULL COMMENT '统一package,com.wmeimob.fastboot',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称，接在 unite_package 后面 . 例子： linde ，项目中目录结构 ： com.wmeimob.fastboot.linde',
  `entity_package` varchar(20) NOT NULL COMMENT 'entity层, package名称,例子： com.wmeimob.fastboot.linde.entity',
  `mapper_package` varchar(20) NOT NULL COMMENT 'mapper层，package名称,例子： com.wmeimob.fastboot.linde.mapper，程序中实现 com.wmeimob.fastboot.linde.mapper.impl',
  `service_package` varchar(20) NOT NULL COMMENT 'service层，package名称,例子：com.wmeimob.fastboot.linde.service,程序中实现： com.wmeimob.fastboot.linde.service.impl',
  `controller_package` varchar(20) NOT NULL COMMENT 'controller层,package名称,例子：com.wmeimob.fastboot.linde.controller',
  `html_path` varchar(255) NOT NULL COMMENT '页面路径',
  `copyright` varchar(2000) DEFAULT NULL COMMENT '版权信息',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='生成参数';

-- ----------------------------
-- Table structure for sys_beetle_table_field
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_table_field`;
CREATE TABLE `sys_beetle_table_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '后台管理用户id',
  `table_id` int(10) DEFAULT NULL COMMENT '关联表ID',
  `field_name` varchar(100) NOT NULL COMMENT '列名',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `content` varchar(200) DEFAULT NULL COMMENT '字段内容',
  `field_type` varchar(20) NOT NULL COMMENT '列名类型',
  `field_length` varchar(255) DEFAULT NULL COMMENT '字段可包容数据长度',
  `jdbc_type` varchar(255) DEFAULT NULL,
  `type` varchar(20) NOT NULL COMMENT '类型',
  `is_key` varchar(2) DEFAULT NULL COMMENT '是否主键',
  `is_null` varchar(5) DEFAULT NULL COMMENT '是否可以为空',
  `is_show_add` int(1) DEFAULT '1' COMMENT '是否显示新增',
  `is_show_edit` int(1) DEFAULT '1' COMMENT '是否显示编辑',
  `is_show_detail` int(1) DEFAULT '1' COMMENT '是否显示详情',
  `is_show_list` int(1) DEFAULT '2' COMMENT '是否列表显示',
  `is_import` int(1) DEFAULT '1' COMMENT '是否Excel导入',
  `is_export` int(1) DEFAULT '1' COMMENT '是否导出Excel',
  `is_query` int(1) DEFAULT '2' COMMENT '是否查询',
  `query_mode` varchar(10) DEFAULT NULL COMMENT '查询类型',
  `show_type` varchar(10) DEFAULT NULL COMMENT '显示类型',
  `order_num` int(11) DEFAULT NULL COMMENT '排序',
  `dict_name` varchar(100) DEFAULT NULL COMMENT '字段名称',
  `dict_type` int(1) DEFAULT NULL COMMENT '字段类型 1 枚举 2 字段 3 列表',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25884 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='字段对应关系';

-- ----------------------------
-- Table structure for sys_beetle_table_head
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_table_head`;
CREATE TABLE `sys_beetle_table_head` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '后台管理用户id',
  `table_name` varchar(50) NOT NULL COMMENT '表名',
  `class_name` varchar(50) DEFAULT NULL COMMENT '类名',
  `initial_letter_name` varchar(255) DEFAULT NULL COMMENT '首字母小写类名',
  `lower_class_name` varchar(255) DEFAULT NULL COMMENT '全小写类名',
  `content` varchar(200) DEFAULT NULL COMMENT '表备注',
  `is_import` tinyint(1) DEFAULT '1' COMMENT '是否导入Excel,1：是，0:否',
  `is_export` tinyint(1) DEFAULT '1' COMMENT '是否导出Excel,1：是，0:否',
  `is_pagination` tinyint(1) DEFAULT '1' COMMENT '是否分页,1：是，0:否',
  `is_log` tinyint(1) DEFAULT '1' COMMENT '是否添加日志',
  `is_protocol` tinyint(1) DEFAULT '1' COMMENT '是否添加协议',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2484 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='表单管理';

-- ----------------------------
-- Table structure for sys_beetle_template
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_template`;
CREATE TABLE `sys_beetle_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) DEFAULT NULL COMMENT '模板名称',
  `user_id` int(10) DEFAULT NULL COMMENT '用户id',
  `type` int(1) DEFAULT NULL COMMENT '1:Controller.2:service.3:service.impl.4:mapper.5:mapper.impl,6:entity,7.html',
  `file_name` varchar(100) DEFAULT NULL COMMENT '文件名称，例子：%sController.java、%sService.java等',
  `file_content` text COMMENT '文件内容',
  `group_id` int(10) unsigned DEFAULT NULL COMMENT '组id',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8 COMMENT='beetle模板';

-- ----------------------------
-- Table structure for sys_beetle_template_group
-- ----------------------------
DROP TABLE IF EXISTS `sys_beetle_template_group`;
CREATE TABLE `sys_beetle_template_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(200) NOT NULL COMMENT '组名称',
  `group_description` varchar(255) DEFAULT NULL COMMENT '详细描述',
  `user_id` int(10) DEFAULT NULL COMMENT '用户id',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='模板分组';

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` int(4) NOT NULL AUTO_INCREMENT,
  `pid` int(4) NOT NULL DEFAULT '0' COMMENT '父菜单',
  `apps_id` json NOT NULL COMMENT '应用ID',
  `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '图标',
  `menu_type` enum('FOLDER','PAGE') NOT NULL DEFAULT 'PAGE' COMMENT '菜单类型 目录/页面',
  `menu_code` varchar(64) NOT NULL DEFAULT '' COMMENT '菜单编号',
  `menu_name` varchar(32) NOT NULL DEFAULT '' COMMENT '菜单名称',
  `first_name` varchar(255) NOT NULL DEFAULT '' COMMENT '简称',
  `second_name` varchar(255) NOT NULL DEFAULT '' COMMENT '完整名称后缀',
  `route` varchar(255) NOT NULL DEFAULT '' COMMENT '路由地址',
  `score` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=332 DEFAULT CHARSET=utf8 COMMENT='系统-菜单表';

-- ----------------------------
-- Table structure for sys_policy
-- ----------------------------
DROP TABLE IF EXISTS `sys_policy`;
CREATE TABLE `sys_policy` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `apps_id` json NOT NULL COMMENT '应用ID',
  `instance_id` int(11) NOT NULL DEFAULT '0' COMMENT '实例ID',
  `policy_name` varchar(255) NOT NULL DEFAULT '' COMMENT '授权策略名称',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `policy_type` enum('SYSTEM','CUSTOM') NOT NULL DEFAULT 'CUSTOM' COMMENT '策略类型',
  `menus_id` json NOT NULL,
  `permissions` json NOT NULL COMMENT '权限集',
  `created_username` varchar(255) NOT NULL DEFAULT '' COMMENT '创建用户',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略表';

-- ----------------------------
-- Table structure for sys_resource
-- ----------------------------
DROP TABLE IF EXISTS `sys_resource`;
CREATE TABLE `sys_resource` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `apps_id` json NOT NULL COMMENT '应用ID',
  `resource_code` varchar(255) NOT NULL DEFAULT '' COMMENT '资源编码',
  `resource_name` varchar(255) NOT NULL DEFAULT '' COMMENT '资源名称',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '资源路径',
  `params` varchar(255) NOT NULL DEFAULT '' COMMENT '查询参数',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资源表';

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` int(9) NOT NULL AUTO_INCREMENT,
  `apps_id` json NOT NULL COMMENT '应用ID',
  `instance_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用实例',
  `authority` varchar(32) NOT NULL DEFAULT '' COMMENT '角色权限标识',
  `role_name` varchar(32) NOT NULL DEFAULT '' COMMENT '角色名',
  `role_type` enum('SYSTEM','NORMAL','DEVELOPER') NOT NULL DEFAULT 'NORMAL',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_username` varchar(11) NOT NULL DEFAULT '0' COMMENT '创建的用户',
  `policies_id` json NOT NULL COMMENT '策略数组',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_roleCode` (`authority`) USING BTREE,
  KEY `fk_sys_role_created_user` (`created_username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8 COMMENT='系统-角色表';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` int(9) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `instance_id` int(11) NOT NULL COMMENT '应用实例',
  `username` varchar(32) NOT NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(32) NOT NULL DEFAULT '' COMMENT '显示名',
  `password` varchar(256) NOT NULL DEFAULT '' COMMENT '密码',
  `roles_id` json NOT NULL COMMENT '绑定的角色ID',
  `salt` varchar(32) NOT NULL DEFAULT '' COMMENT '盐值',
  `mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '电话',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `is_locked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定',
  `created_username` varchar(11) NOT NULL DEFAULT '' COMMENT '创建者',
  `data_rules` json NOT NULL COMMENT '数据权限',
  `gmt_create` datetime DEFAULT NULL,
  `gmt_modified` datetime DEFAULT NULL,
  `last_login_date` datetime DEFAULT NULL,
  `last_login_ip` varchar(255) NOT NULL DEFAULT '',
  `user_type` enum('SYSTEM','CUSTOM') NOT NULL DEFAULT 'CUSTOM',
  `store_id` int(11) DEFAULT NULL COMMENT '门店id',
  `is_disable` tinyint(1) DEFAULT NULL COMMENT '是否禁用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_userame` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8 COMMENT='管理员';

-- ----------------------------
-- Table structure for write_off_code
-- ----------------------------
DROP TABLE IF EXISTS `write_off_code`;
CREATE TABLE `write_off_code` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `write_off_name` varchar(100) DEFAULT NULL COMMENT '核销卡名称',
  `cust_user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `store_ids` text COMMENT '适用门店',
  `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `source_goods_id` int(11) DEFAULT NULL COMMENT '来源商品ID',
  `order_type` int(11) DEFAULT '0' COMMENT '订单类型 0普通订单 1拼团订单',
  `order_no` varchar(100) DEFAULT NULL COMMENT '来源订单编号',
  `surplus_num` int(11) DEFAULT NULL COMMENT '剩余次数',
  `total_num` int(11) DEFAULT NULL COMMENT '总次数',
  `expiry_date` date DEFAULT NULL COMMENT '有效期开始',
  `status` int(1) DEFAULT '0' COMMENT '状态(0 待处理 1已使用 2已过期)',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `end_date` datetime DEFAULT NULL COMMENT '核销有效结束时间',
  `detail_id` int(11) DEFAULT NULL COMMENT '订单详情id',
  `code` varchar(255) DEFAULT NULL,
  `order_state` int(1) DEFAULT '1' COMMENT '订单状态类型 为0正常 1订单取消当前适用核销业务 为了处理订单取消情况字段',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_cust_user_id` (`cust_user_id`) USING BTREE,
  KEY `idx_source_goods_id` (`source_goods_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_detail_id` (`detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40917 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='核销码表';

-- ----------------------------
-- Table structure for write_off_code_log
-- ----------------------------
DROP TABLE IF EXISTS `write_off_code_log`;
CREATE TABLE `write_off_code_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `write_off_id` int(11) DEFAULT NULL COMMENT '核销卡id',
  `store_id` int(11) DEFAULT NULL COMMENT '核销门店',
  `staff_id` int(11) DEFAULT NULL COMMENT '核销员工',
  `write_off_date` datetime DEFAULT NULL COMMENT '核销时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `cust_user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_write_off_id` (`write_off_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=36807 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='核销码记录表';
SET FOREIGN_KEY_CHECKS=1;
