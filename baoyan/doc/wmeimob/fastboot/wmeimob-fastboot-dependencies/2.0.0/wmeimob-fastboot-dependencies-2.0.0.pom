<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <properties>
        <java.version>1.8</java.version>
        <spring-boot.version>2.1.1.RELEASE</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wmeimob.fastboot</groupId>
    <artifactId>wmeimob-fastboot-dependencies</artifactId>
    <version>2.0.0</version>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>


            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.9</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.50</version>
            </dependency>


            <dependency>
                <groupId>commons-dbcp</groupId>
                <artifactId>commons-dbcp</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>

            <dependency>
                <groupId>me.hao0</groupId>
                <artifactId>wechat</artifactId>
                <version>1.10.4</version>
            </dependency>

            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>persistence-api</artifactId>
                <version>1.0</version>
            </dependency>

            <dependency>
                <groupId>com.mzlion</groupId>
                <artifactId>easy-okhttp</artifactId>
                <version>1.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.1.10</version>
            </dependency>

            <dependency>
                <groupId>me.hao0</groupId>
                <artifactId>wepay</artifactId>
                <version>1.3.4.WMEIMOB</version>
            </dependency>

            <dependency>
                <groupId>me.hao0</groupId>
                <artifactId>alipay-core</artifactId>
                <version>1.3.0</version>
            </dependency>


            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.9</version>
            </dependency>

            <dependency>
                <groupId>net.sf.barcode4j</groupId>
                <artifactId>barcode4j</artifactId>
                <version>2.1</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.3</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.3.3</version>
            </dependency>

            <dependency>
                <groupId>avalon-framework</groupId>
                <artifactId>avalon-framework</artifactId>
                <version>4.1.5</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>4.3.2</version>
            </dependency>


            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.20</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-release-Eso7dC/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-snapshot-HDWA0j/</url>
        </snapshotRepository>
    </distributionManagement>
</project>