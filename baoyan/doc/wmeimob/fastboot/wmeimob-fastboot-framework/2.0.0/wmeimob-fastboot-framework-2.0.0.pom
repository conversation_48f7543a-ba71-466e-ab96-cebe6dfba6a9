<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wmeimob-fastboot-dependencies</artifactId>
        <groupId>com.wmeimob.fastboot</groupId>
        <version>2.0.0</version>
        <relativePath />
    </parent>

    <version>2.0.0</version>

    <properties>
        <wmeimob-fastboot-common.version>2.0.0</wmeimob-fastboot-common.version>
    </properties>


    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>wmeimob-starter</module>
        <module>wmeimob-fastboot-framework-security</module>

        <module>wmeimob-fastboot-framework-wechat</module>
        <module>wmeimob-fastboot-framework-wechat-common</module>

        <module>wmeimob-fastboot-framework-common</module>

        <module>wmeimob-management-common</module>
        <module>wmeimob-management-dev</module>
        <module>wmeimob-management-standard</module>
        <module>wmeimob-fastboot-run-test</module>
    </modules>

    <artifactId>wmeimob-fastboot-framework</artifactId>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-common</artifactId>
                <version>${wmeimob-fastboot-common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-starter</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework-security</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework-wechat</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework-wechat-common</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-management-common</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-management-standard</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-management-dev</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework-common</artifactId>
                <version>2.0.0</version>
            </dependency>


        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-release-Eso7dC/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-snapshot-HDWA0j/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- Source -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--<plugin>-->
            <!--<groupId>org.apache.maven.plugins</groupId>-->
            <!--<artifactId>maven-source-plugin</artifactId>-->
            <!--<version>3.0.1</version>-->
            <!--<executions>-->
            <!--<execution>-->
            <!--<phase>package</phase>-->
            <!--<goals>-->
            <!--<goal>jar-no-fork</goal>-->
            <!--</goals>-->
            <!--</execution>-->
            <!--</executions>-->
            <!--</plugin>-->

        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

</project>