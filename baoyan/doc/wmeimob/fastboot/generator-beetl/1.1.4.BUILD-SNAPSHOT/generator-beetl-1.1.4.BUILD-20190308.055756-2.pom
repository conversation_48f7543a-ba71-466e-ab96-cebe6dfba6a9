<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>generator-beetl</artifactId>
    <version>1.1.4.BUILD-SNAPSHOT</version>


    <parent>
        <artifactId>wmeimob-fastboot-dependencies</artifactId>
        <groupId>com.wmeimob.fastboot</groupId>
        <version>2.0.0</version>
    </parent>


    <repositories>
        <repository>
            <id>rdc-releases</id>
            <url>http://192.168.110.227/mvn-release/</url>
        </repository>
        <repository>
            <id>rdc-snapshots</id>
            <url>http://192.168.110.227/mvn-snapshot/</url>
        </repository>
    </repositories>

    <properties>
        <beetl.version>2.7.15</beetl.version>
        <freemark.version>2.3.23</freemark.version>
        <commons.io.version>2.5</commons.io.version>
        <commons.lang.version>2.5</commons.lang.version>
        <wmeimob-fastboot-common.version>2.1.BUILD-SNAPSHOT</wmeimob-fastboot-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-common</artifactId>
                <version>${wmeimob-fastboot-common.version}</version>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <dependencies>
        <dependency>
            <groupId>com.wmeimob.fastboot</groupId>
            <artifactId>wmeimob-fastboot-core</artifactId>
            <version>${wmeimob-fastboot-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ibeetl</groupId>
            <artifactId>beetl</artifactId>
            <version>${beetl.version}</version>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemark.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons.lang.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>


</project>