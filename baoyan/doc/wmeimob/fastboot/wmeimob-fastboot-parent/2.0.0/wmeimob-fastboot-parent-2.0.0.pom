<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wmeimob-fastboot-dependencies</artifactId>
        <groupId>com.wmeimob.fastboot</groupId>
        <version>2.0.0</version>
        <relativePath />
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>2.0.0</version>

    <packaging>pom</packaging>
    <artifactId>wmeimob-fastboot-parent</artifactId>
    <properties>
        <wmeimob-fastboot-framework.version>2.0.0</wmeimob-fastboot-framework.version>
        <wmeimob-fastboot-common.version>2.0.0</wmeimob-fastboot-common.version>

        <wmeimob-fastboot-excel-lite.version>1.1.4.RELEASE</wmeimob-fastboot-excel-lite.version>
        <wechat3rdplatform.version>1.0.3</wechat3rdplatform.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-common</artifactId>
                <version>${wmeimob-fastboot-common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework</artifactId>
                <version>${wmeimob-fastboot-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-excel-lite</artifactId>
                <version>${wmeimob-fastboot-excel-lite.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wechat-3rd-platform</artifactId>
                <version>${wechat3rdplatform.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-release-Eso7dC/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://repo.rdc.aliyun.com/repository/25107-snapshot-HDWA0j/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.0.0.RELEASE</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                            <configuration>
                                <classifier>exec</classifier>
                                <mainClass>com.wmeimob.WmeimobFastbootApplication</mainClass>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>

</project>