<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wmeimob-fastboot-framework</artifactId>
        <groupId>com.wmeimob.fastboot</groupId>
        <version>2.0.0</version>
        <relativePath>../</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wmeimob-fastboot-framework-wechat-common</artifactId>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>me.hao0</groupId>
            <artifactId>wechat</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wmeimob.fastboot</groupId>
            <artifactId>wmeimob-fastboot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wmeimob.fastboot</groupId>
            <artifactId>wmeimob-fastboot-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wmeimob.fastboot</groupId>
            <artifactId>wmeimob-fastboot-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


        <dependency>
            <groupId>me.hao0</groupId>
            <artifactId>wepay</artifactId>
        </dependency>
    </dependencies>


</project>