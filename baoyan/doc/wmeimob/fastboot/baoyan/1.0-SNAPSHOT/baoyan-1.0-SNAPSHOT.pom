<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>baoyan</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>baoyan-admin</module>
        <module>baoyan-common</module>
        <module>baoyan-wechat</module>
    </modules>


    <parent>
        <groupId>com.wmeimob.fastboot</groupId>
        <artifactId>wmeimob-fastboot-parent</artifactId>
        <version>2.0.0</version>
    </parent>


<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>rdc-releases</id>-->
<!--            <url>http://172.18.58.137/mvn-release/</url>-->
<!--        </repository>-->
<!--        <repository>-->
<!--            <id>rdc-snapshots</id>-->
<!--            <url>http://172.18.58.137/mvn-snapshot/</url>-->
<!--        </repository>-->
<!--    </repositories>-->

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-framework</artifactId>
                <type>pom</type>
                <scope>import</scope>
                <version>${wmeimob-fastboot-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>baoyan-common</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

</project>