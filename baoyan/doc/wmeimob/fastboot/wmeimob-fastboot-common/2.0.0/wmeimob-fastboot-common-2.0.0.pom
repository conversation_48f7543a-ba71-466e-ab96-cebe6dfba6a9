<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wmeimob-fastboot-dependencies</artifactId>
        <groupId>com.wmeimob.fastboot</groupId>
        <version>2.0.0</version>
        <relativePath />
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>wmeimob-fastboot-common</artifactId>
    <version>2.0.0</version>
    <modules>
        <module>wmeimob-fastboot-autoconfigure</module>
        <module>wmeimob-fastboot-utils</module>
        <module>wmeimob-fastboot-core</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-utils</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-core</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.wmeimob.fastboot</groupId>
                <artifactId>wmeimob-fastboot-autoconfigure</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>7.2.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>

            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>3.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>1.1.0</version>
            </dependency>


            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>2.8.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <!-- Source -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--<plugin>-->
                <!--<groupId>org.apache.maven.plugins</groupId>-->
                <!--<artifactId>maven-source-plugin</artifactId>-->
                <!--<version>3.0.1</version>-->
                <!--<executions>-->
                    <!--<execution>-->
                        <!--<phase>package</phase>-->
                        <!--<goals>-->
                            <!--<goal>jar-no-fork</goal>-->
                        <!--</goals>-->
                    <!--</execution>-->
                <!--</executions>-->
            <!--</plugin>-->

        </plugins>
    </build>

</project>