<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.wmeimob.fastboot</groupId>
  <artifactId>wmeimob-fastboot-excel-lite</artifactId>
  <version>1.1.2.RELEASE</version>
  <properties>
    <spring-boot.version>2.0.1.RELEASE</spring-boot.version>
    <java.version>1.8</java.version>
    <revision>1.0.4</revision>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <wmeimob.common.version>1.0.6</wmeimob.common.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>3.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>3.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>3.1.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
