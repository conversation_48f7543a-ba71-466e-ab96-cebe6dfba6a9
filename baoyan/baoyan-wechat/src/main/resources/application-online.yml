spring:
  datasource:
    #测试环境
#    url: jdbc:mysql://**************:3306/baoyan?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#    username: root
#    password: 15ce21a718c7def1
    url: **************************************************************************************************************************************************************************
    username: root
    password: Weimob@baoyan123

  redis:
    database: 10
    host: 127.0.0.1
    port: 6379
    password: Weimob@baoyan123

  main:
    allow-bean-definition-overriding: true

fastboot:
  ## 本地上传配置 ##
  upload:
    configs:
      #  场景值：
      #    配置选项1:
      #    配置选项2:
      #    ……
      images:
        #上传文件目录（以 “/” 结尾）
        dir: /statics/images/
        #回显域名
        domain: shinez01.frp.dev.wmeimob.com
        #访问前缀（非 “/” 结尾）
        visit-prefix: /statics/images

  #微信配置
  wechat:
    #正式服
    appid: wx7ed8a8419f68e07a
    secret: 0f504082c7706c7b72975d6e0c1f3fbf
    #企业号corpid
    corpid:
    #是否是小程序
    is-miniprogram: true
    #授权作用域
    auth-scope:
    #第三方平台APPID
    component-appid:
    #公众号原始ID
    user-name:
    #消息加解密token
    token: 1
    #消息加解密key
    encoding-aes-key: 1
    #公众号名称
    nick-name: 依视路预约验光
    #二维码地址
    qrcode-url:
    #企业应用
    apps:
      myApp1:
        agentid:
        corpsecret:


    #多公众号配置
    multiples:
      myApp1:
        appid:
        secret:

  #微信支付配置
  wechat-pay:
    server-mch-no: 1702733131
    mch-no: 1702733131
    mch-key: dlqQ26kstXWDteci2SPsSosS9BlFBplE
    pay-notify-url: https://boboland.byloft.net/v2/api/pay/updateOrder
    js-pay-notify-url: https://boboland.byloft.net/v2/api/pay/updateOrder
    #证书位置
    cert-path: application_cert.p12