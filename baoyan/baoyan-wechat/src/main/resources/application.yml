server:
  port: 10086
  servlet:
    context-path: /api


spring:
  application:
    name: 应用名称
  servlet:
    multipart:
      #最大上传文件大小
      max-file-size: 5MB
  cache:
    type: redis
  jackson:
    default-property-inclusion: NON_NULL
    serialization.write-dates-as-timestamps: true
    time-zone: GMT+8
  datasource:
    #测试环境
#    url: jdbc:mysql://**************:3306/baoyan?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#    username: root
#    password: 15ce21a718c7def1
#    正式环境
    url: ******************************************************************************************************************************************************************************************************
    username: root
    password: <PERSON><PERSON><PERSON>@baoyan123
    druid:
      initial-size: 10
      max-active: 20
      test-on-borrow: true
      validation-query: SELECT 1 FROM DUAL
      validation-query-timeout: 300
      stat-view-servlet:
        login-username: project_druid_user
        login-password: <EMAIL>
      filter:
        stat:
          enabled: true
          log-slow-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true
  redis:
    database: 10
    host: 127.0.0.1
#    password: Weimob@baoyan123
    port: 6379
    lettuce.pool:
      max-idle: 8
      min-idle: 0
      max-active: 8
      max-wait: -1ms

  session:
    store-type: redis
  profiles:
    active: online
#    active: dev



## 阿里云oss上传配置 ##
aliyun:
  oss:
    access-key-id: LTAI6CHvq0bGQ3PU
    access-key-secret: EJTO1P01d23d3M5HMHHi1tppDsV35t
    bucket: boboland6
    endpoint: oss-cn-shanghai.aliyuncs.com
    dir: img/
    #凭证有效期
    expire: 7200
    #https开启 默认不开启
    secure: true
    #上传文件大小限制单位 MB
    max-size: 10


  #阿里云短信配置
  sms:
    access-key-id: LTAI6CHvq0bGQ3PU
    access-key-secret: EJTO1P01d23d3M5HMHHi1tppDsV35t
    configs:
      ###场景值（reg为注册场景值固定值）
      reg:
        sign: 豹豹乐园
        template-code: SMS_478545107
        timeout: 60
        length: 5



#框架配置
fastboot:
  ## 本地上传配置 ##
  upload:
    configs:
      #  场景值：
      #    配置选项1:
      #    配置选项2:
      #    ……
      images:
        #上传文件目录（以 “/” 结尾）
        dir: /statics/images/
        #回显域名
        domain: shinez01.frp.dev.wmeimob.com
        #访问前缀（非 “/” 结尾）
        visit-prefix:

  #微信配置
  wechat:
    #公众号APPID
#    appid: wx07a42f09798787bb
#    secret: 7ce07eca7380a0a3862ca63c79086a0a
    #企业号corpid
    corpid:
    #是否是小程序
    is-miniprogram: true
    #授权作用域
    auth-scope:
    #第三方平台APPID
    component-appid:
    #公众号原始ID
    user-name:
    #消息加解密token
    token: 1
    #消息加解密key
    encoding-aes-key: 1
    #公众号名称
    nick-name: 依视路预约验光
    #二维码地址
    qrcode-url:

    #企业应用
    apps:
      myApp1:
        agentid:
        corpsecret:


    #多公众号配置
    multiples:
      myApp1:
        appid:
        secret:

  #微信支付配置
  wechat-pay:
    #服务商微信商户号
    server-mch-no: 1702733131
    mch-no: 1702733131
    #微信支付key
    mch-key: dlqQ26kstXWDteci2SPsSosS9BlFBplE
    #证书位置
    cert-path: application.cert.p12
    #全局支付回调
    #穿透
    pay-notify-url: https://boboland.byloft.net/v2/api/pay/updateOrder
    #测试服
#    pay-notify-url: https://api.baoyan.llons.com/api/pay/updateOrder
    #正式服
#    pay-notify-url: https://wx.byloft.net/v2/api/pay/updateOrder
    #退款回调
    refund-notify-url:
    #js支付回调
    js-pay-notify-url: https://boboland.byloft.net/v2/api/pay/updateOrder
#    js-pay-notify-url: https://api.baoyan.llons.com/api/pay/updateOrder
#    js-pay-notify-url: https://wx.byloft.net/v2/api/pay/updateOrder
    #支付body
    pay-body:
    #多商户号
    multiples:
      myPay1:
        mch-no:
        mch-key:




  management:
    #开发端用户名
    dev-login-name: sys

  #安全配置
  security:
    jwt:
      #token有效期（秒）
      expiration: 604800
      #请求头部名
      header: Authorization
      #验签密钥
      secret: 12312421
      #放行接口列表
      permission-urls:
      - /druid/**
      - /login
      - /dev/login
      - /shop/showShop
      - /store/**
      - /**/**
      #未定义的资源是否放行（默认true）
      undefined-resource-access: true
      #未定义的权限是否放行（默认true）
      undefined-policy-access: false
      #允许相同登录用户同时在线（默认true）
      allow-same-login: true



#微信第三方平台配置
wechat3rd:
  component-appid:
  miniprogram:
    request-domain:
    - https://shine.frp.dev.wmeimob.com
    - https://www.uatmall.test.bizvane.cn
    - https://www.sitmall.test.bizvane.cn
    socket-domain:
    - wss://shine.frp.dev.wmeimob.com
    - wss://www.uatmall.test.bizvane.cn
    - wss://www.sitmall.test.bizvane.cn
    upload-domain:
    - https://up.qbox.me
    - https://up-z0.qiniup.com
    download-domain:
    - https://resource.uat.bizvane.cn
    - https://qi.test.bizvane.cn
    webview-domain:
    - https://shine.frp.dev.wmeimob.com
    - https://www.uatmall.test.bizvane.cn
    - https://www.sitmall.test.bizvane.cn

#代码生成器配置
beetle:
  source:
    dbName: baoyan
    dbType: mysql
  generate:
    shielding-start: sys_,rich,simple,regio       #屏蔽以 sys 开头 《一键初始化》，如果有需要添加屏蔽的信息,以逗号分隔
    gen-local: true    #是否允许在本地生成代码
    auto-check: true   #是否检测文件变化,开发适用于true,线上适用于false
