spring:
  datasource:
    url: ***************************************************************************************************************************************************************
    username: root
    password: root

  redis:
    host: 127.0.0.1
    port: 6379
    password: Weimob@baoyan123

  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册

fastboot:
  ## 本地上传配置 ##
  upload:
    configs:
      #  场景值：
      #    配置选项1:
      #    配置选项2:
      #    ……
      images:
        #上传文件目录（以 “/” 结尾）
        dir: /statics/images/
        #回显域名
        domain: shinez01.frp.dev.wmeimob.com
        #访问前缀（非 “/” 结尾）
        visit-prefix: /statics/images

  #微信配置
  wechat:
#    appid: wx86c65b8c31535078
#    secret: 3f1dfd81ac939ae4c50ceaa0573ba36a
    #正式服
    appid: wx07a42f09798787bb
    secret: 7ce07eca7380a0a3862ca63c79086a0a
    #企业号corpid
    corpid:
    #是否是小程序
    is-miniprogram: true
    #授权作用域
    auth-scope:
    #第三方平台APPID
    component-appid:
    #公众号原始ID
    user-name:
    #消息加解密token
    token: 1
    #消息加解密key
    encoding-aes-key: 1
    #公众号名称
    nick-name: 依视路预约验光
    #二维码地址
    qrcode-url:
    #企业应用
    apps:
      myApp1:
        agentid:
        corpsecret:


    #多公众号配置
    multiples:
      myApp1:
        appid:
        secret:

  #微信支付配置
  wechat-pay:
    server-mch-no: 1602975518
    mch-no: 1602975518
    mch-key: 7B0E1D17568540FD7482DDD1FAE098A7
#    pay-notify-url: http://lqn.mynatapp.cc/api/pay/updateOrder
    pay-notify-url: https://api.baoyan.llons.com/api/pay/updateOrder
    #pay-notify-url: https://wx.byloft.net/v2/api/pay/updateOrder
#server:
#  ssl:
#    key-store: classpath:5875396_wx.gmcoai.com.pfx
#    key-store-password: jF7sju9N
