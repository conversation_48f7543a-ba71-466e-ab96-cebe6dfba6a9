package com.wmeimob.fastboot.baoyan.controller.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import com.wmeimob.fastboot.baoyan.service.UserService;
import com.wmeimob.fastboot.baoyan.service.WechatOrdersService;
import com.wmeimob.fastboot.baoyan.vo.OrderInfoVo;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: WechatOrderController
 * @projectName baoyan
 * @description: 我的订单
 * @date 2019/8/6 16:53
 */
@RestController
@RequestMapping("order")
@Slf4j
public class WechatOrderController {
    @Resource
    private WechatOrdersService wechatOrdersService;

    @Resource
    private UserService userService;

    @Resource
    private ByOrderLogService orderLogService;

    /**
     * 我的订单 查询
     *
     * @param request
     * @param orderStatus
     * @return
     */
    @GetMapping("/list")
    public PageInfo queryForByOrderAfter(HttpServletRequest request, Integer orderStatus,Integer payType) {
        PageHelper.startPage(PageContext.pageIndex(request), 10);
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        return new PageInfo<OrderInfoVo>(wechatOrdersService.userOrderList(user, orderStatus,payType));
    }

    /**
     * 获取用户订单详情
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/userOrderDetail/{id}")
    public Map<String, Object> userOrderDetail(HttpServletRequest request, @PathVariable("id") Integer id) {
        return wechatOrdersService.userOrderDetail(id);
    }

    /**
     * 取消订单
     *
     * @param request
     * @param id
     */
    @PostMapping("/cancelOrder/{id}")
    public void cancelOrder(HttpServletRequest request, @PathVariable("id") Integer id) {
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        log.info("取消订单 cancelOrder() id: {},登录人userId:{}", id, user.getId());
        wechatOrdersService.cancelUserOrder(user, id);
    }

    /**
     * <AUTHOR>
     * @date 19:05 2019/4/26
     * @Description: 提交申请售后
     */
    @PostMapping("/applySale")
    public void applySale(HttpServletRequest request, @RequestBody ByOrderAfter byOrderAfter) throws IOException {
        ByCustUser user =  SecurityContext.getUser();
        log.info("提交申请售后 applySale() byOrderAfter: {},登录人userId:{}", byOrderAfter, user.getId());
        //默认仅退款类型
        if (null == byOrderAfter.getAfterType()) {
            byOrderAfter.setAfterType(0);
        }
        byOrderAfter.setUserId(user.getId());
        //断言商品类型不能为null
        InputValidator.checkEmpty(byOrderAfter.getAfterType(), "请选择商品类型");
        //modify 2019年12月18日15:49:39 客户暂时不需要这种退款理由类型
//       InputValidator.checkEmpty(byOrderAfter.getResouceType(), "退款理由类型")
        //断言售后原因不能为null
        InputValidator.checkEmpty(byOrderAfter.getAfterReason(), "售后原因");
        if (byOrderAfter.getResouceType() == 1) {
            //退普通商品
            wechatOrdersService.applySale(user, byOrderAfter);

            //记录订单操作日志
            orderLogService.addOrderLogAsync(
                    ByOrderLog.builder()
                            .orderNo(byOrderAfter.getOrderNo())
                            .orderGoodsId(byOrderAfter.getDetailId())
                            .operatorType(1)
                            .logType(OrderLogType.APPLY_AFTER)
                            .operatorCount(byOrderAfter.getReturnNum())
                            .build()
            );
        } else {
            //秒杀商品
            wechatOrdersService.applyTeam(user, byOrderAfter);
        }
        userService.addRed();
    }

    /**
     * <AUTHOR>
     * @date 19:05 2019/4/26
     * @Description: 订单评价
     */
    @PostMapping("/evalOrder")
    public Map<String, Object> evalOrder(HttpServletRequest request, @RequestBody Map<String, List> evaluateInfoList) {
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        log.info("订单评价 evalOrder() evaluateInfoList: {},登录人userId:{}", evaluateInfoList, user.getId());
        String listTxt = JSONArray.toJSONString(evaluateInfoList.get("evaluateInfoList"));
        List<ByEvaluate> list = JSON.parseArray(listTxt, ByEvaluate.class);
        return wechatOrdersService.evalOrder(user, list);
    }

    /**
     * 会员中心相关统计数据
     */
    @GetMapping("/num")
    public Map<String, Object> orderNum() {
        Integer userId = 0;
        try {
            ByCustUser user = (ByCustUser) SecurityContext.getUser();
            userId = user.getId();
        } catch (Exception e) {
            userId = 0;
        }
        return wechatOrdersService.getUserOrderNum(userId);
    }

    /**
     * <AUTHOR>
     * @date 19:05 2019/4/26
     * @Description: 删除订单
     */
    @DeleteMapping("/delOrder/{id}")
    public void delOrder(HttpServletRequest request, @PathVariable("id") Integer id) {
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        log.info("删除订单 delOrder() id: {},登录人userId:{}", id, user.getId());
        wechatOrdersService.delOrder(user, id);
    }

    /**
     * 生成渠道订单
     * @param request
     * @param channelCust
     * @return
     */
    @PostMapping("/generateChannelOrder")
    public R generateChannelOrder(HttpServletRequest request, @RequestBody ByChannelCust channelCust){
        return R.data(wechatOrdersService.generateChannelOrder(channelCust));
    }
}
