package com.wmeimob.fastboot.baoyan.controller.tc;

import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import com.wmeimob.fastboot.baoyan.service.ByClassifyRecommendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/8/8
 * packageName:com.wmeimob.fastboot.baoyan.controller.tc
 */
@RestController
@RequestMapping("/byClassifyRecommend")
@Slf4j
public class WxByClassifyRecommendController {
    @Resource(name = "wxByClassifyRecommendService")
    private ByClassifyRecommendService byClassifyRecommendService;

    @GetMapping("/")
    public List<ByClassifyRecommend> queryList(){
        List<ByClassifyRecommend> byClassifyRecommends = byClassifyRecommendService.queryAll(null);
        log.info("wx === get=> queryList [出参]============={}",byClassifyRecommends);
        return byClassifyRecommends;

    }
}
