package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.CartIds;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsShopingMapper;
import com.wmeimob.fastboot.baoyan.service.TcGoodsShopingService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/21
 */
@Service
public class TcGoodsShopingServiceImpl implements TcGoodsShopingService {

    @Resource
    private TcGoodsShopingMapper goodsShopingMapper;

    @Resource
    private ByGoodsShoppingService goodsShoppingService;

    @Resource
    private TcGoodsMapper tcGoodsMapper;

    /**
     * 添加到购物车
     *
     * @return
     */
    @Override
    public boolean addCart(TcGoodsShoping tcShoping) {
        //查询该淘潮玩商品是否是 邮寄，邮寄才可以添加购物车
        TcGoods tcGoods = tcGoodsMapper.selectByPrimaryKey(tcShoping.getGoodsId());
        if (!tcGoods.getIsMailing()){
            throw new CustomException("不能邮寄的商品不可以添加购物车");
        }


        ByCustUser user = SecurityContext.getUser();
        //查询用户有没有添加过这个商品
        List<TcGoodsShoping> dbShoping = goodsShopingMapper.findGoodsShoping(user.getId(), tcShoping.getGoodsId());

        if (CollectionUtils.isEmpty(dbShoping)){
            //用户没有添加过这个商品
            tcShoping.setUserId(user.getId());
            tcShoping.setGmtCreate(new Date());
            //保存到数据库
            goodsShopingMapper.insertSelective(tcShoping);
        }else{
            //用户添加过这个商品了
            //增加数量
            goodsShopingMapper.addCartCount(dbShoping.get(0).getId(), tcShoping.getGoodsCount());
        }

        return true;
    }

    /**
     * 展示用户的淘潮玩购物车
     *
     * @return
     */
    @Override
    public List<TcGoodsShoping> show() {
        //获得登录的用户
        ByCustUser user = SecurityContext.getUser();

        return goodsShopingMapper.findByUserId(user.getId());
    }

    /**
     * 根据id删除购物车
     * @param cartDelete
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(CartIds cartDelete) {
        //删除淘潮玩
        if (!CollectionUtils.isEmpty(cartDelete.getTcIds())){
        ByCustUser user = SecurityContext.getUser();
            goodsShopingMapper.deleteByIds(user.getId(), cartDelete.getTcIds());
        }

        //删除门票
        if (!CollectionUtils.isEmpty(cartDelete.getTicketIds())){

            goodsShoppingService.delete(cartDelete.getTicketIds());
        }

        return true;
    }

    /**
     * 根据购物车id查询商品
     * @param tcGoods
     * @return
     */
    @Override
    public List<TcGoodsShoping> findGoodsByCart(Integer userId, List<TcGoodsShoping> tcGoods, boolean justAloneTc) {
        if (justAloneTc){
            //商品详情页进行购买前的查询
            Integer goodsId = tcGoods.get(0).getGoodsId();
            Integer goodsCount = tcGoods.get(0).getGoodsCount();
            TcGoods dbGoods = tcGoodsMapper.queryById(goodsId);

            TcGoodsShoping tempCart = TcGoodsShoping
                    .builder()
                    .tcGoods(dbGoods)
                    .goodsId(dbGoods.getId())
                    .goodsCount(goodsCount)
                    .build();

            return Collections.singletonList(tempCart);
        }else{
            //购物车页面进行购买前的查询
            List<Integer> ids = tcGoods.stream().map(tcCart -> tcCart.getId()).collect(Collectors.toList());
            return goodsShopingMapper.findGoodsByIds(userId, ids);
        }
    }
}
