package com.wmeimob.fastboot.baoyan.controller.store;

import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.mapper.BaseStoreMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("store")
@Slf4j
public class StoreController {
    @Resource
    private BaseStoreMapper baseStoreMapper;

    @RequestMapping("list")
    public List<BaseStore> list()
    {
        Example example = new Example(BaseStore.class);
        example.createCriteria().andEqualTo("storeType", 0)
                .andEqualTo("deleteStatus", 0)
                .andEqualTo("status", 1);
        example.orderBy("id").desc();
        return baseStoreMapper.selectByExample(example);
    }
}
