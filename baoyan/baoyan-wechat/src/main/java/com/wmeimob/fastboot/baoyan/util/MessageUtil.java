package com.wmeimob.fastboot.baoyan.util;

import com.alibaba.fastjson.JSONObject;
import com.mzlion.easyokhttp.HttpClient;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import lombok.extern.slf4j.Slf4j;
import me.hao0.common.util.Preconditions;
import me.hao0.wechat.core.Wechat;
import me.hao0.wechat.core.WechatBuilder;
import me.hao0.wechat.model.base.WechatMp;
import me.hao0.wechat.model.message.send.TemplateField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 模板推送
 * @date 2019-08-22 09:10
 * @Version 1.0
 */
@Slf4j
@Service
public class MessageUtil {


    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WechatService wechatService;
    /**
     * <AUTHOR>
     * @date 2018-01-22 15:59
     * @Description: 发送模板消息
     */
    public JSONObject sendWxTemplateMsg(Map<String, String> msgMap, String openId, String templateId, String formId, String url) {
        JSONObject result = new JSONObject();
        List<TemplateField> templateFieldList = new ArrayList<>();
        for (Map.Entry<String, String> entry : msgMap.entrySet()) {
            TemplateField tf = new TemplateField();
            tf.setName(entry.getKey());
            tf.setValue(entry.getValue());
            templateFieldList.add(tf);
        }
        Wechat apiComponent = this.wechatService.getApiComponent();
        Wechat wechat = WechatBuilder.newBuilder(apiComponent.getAppId(), apiComponent.getAppSecret()).build();
        String accessToken = getAccessToken(wechat);
        Preconditions.checkNotNullAndEmpty(accessToken, "accessToken");
        Preconditions.checkNotNullAndEmpty(openId, "openId");
        Preconditions.checkNotNullAndEmpty(templateId, "templateId");
        String tempUrl = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send?access_token=" + accessToken;
        JSONObject params = buildTemplateParams(openId, templateId, formId, url, templateFieldList);
        String results = HttpClient.textBody(tempUrl).json(params).asString();
        if (Strings.isNullOrEmpty(results)) {
            result.put("errcode", -1);
            result.put("errmsg", "调用发送模板接口失败");
            log.error("\n#############Calling the send template interface failed：" + msgMap);
            return result;
        }
        log.info("\n#############" + openId + ":::send template result" + results);
        return JSONObject.parseObject(results);
    }

    public String getAccessToken(Wechat wechat) {
//        String accessToken = this.stringRedisTemplate.opsForValue().get("mps:" + wechat.getAppId() + "access_token");
        String accessToken = this.stringRedisTemplate.opsForValue().get("mps:" + wechat.getAppId() + "access_token");
       /* System.out.println(accessToken);
        if (accessToken == null || "".equals(accessToken) || StringUtils.isEmpty(accessToken)) {*/
        String result = HttpClient.get("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET".replace("APPID", wechat.getAppId()).replace("APPSECRET", wechat.getAppSecret())).asString();
        JSONObject jsonObject = JSONObject.parseObject(result);
        accessToken = jsonObject.getString("access_token");
        if (accessToken == null) {
            return "";
        }
       /*     stringRedisTemplate.opsForValue().set("mps:" + wechat.getAppId() + "access_token", accessToken, (long) (jsonObject.getInteger("expires_in") - 1800) * 1000L);
        }*/
        return accessToken;
    }
    /**
     * <AUTHOR>
     * @date 2018-08-23 13:40
     * @Description: 初始化参数
     */
    private JSONObject buildTemplateParams(String openId, String templateId, String formId, String page, List<TemplateField> fields) {
        JSONObject params = new JSONObject();
        params.put("touser", openId);
        params.put("template_id", templateId);
        params.put("form_id", formId);
        if (!Strings.isNullOrEmpty(page)) {
            params.put("page", page);
        }
        if (fields != null && !fields.isEmpty()) {
            JSONObject data = new JSONObject();
            Iterator var8 = fields.iterator();
            while (var8.hasNext()) {
                TemplateField field = (TemplateField) var8.next();
                JSONObject dataItem = new JSONObject();
                dataItem.put("value", field.getValue());
                data.put(field.getName(), dataItem);
            }
            params.put("data", data);
        }
        return params;
    }
}
