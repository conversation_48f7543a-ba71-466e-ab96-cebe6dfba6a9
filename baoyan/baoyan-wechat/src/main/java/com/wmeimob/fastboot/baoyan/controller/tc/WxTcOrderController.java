package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021/8/2
 */
@RequestMapping("/tc/order")
@RestController
public class WxTcOrderController {

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderService tcOrderService;

    /**
     * 取消订单
     * @param id
     * @return
     */
    @PutMapping("/cancelOrder/{id}")
    public boolean cancelOrder(@PathVariable Integer id){
        return tcOrderService.userCancelOrder(id);
    }

    /**
     * 用户收货
     * @param id
     * @return
     */
    @PutMapping("/receiving/{id}")
    public boolean receiving(@PathVariable Integer id){
        ByCustUser user = SecurityContext.getUser();
//        ByCustUser user = ByCustUser.builder().id(100754).build();
        TcOrder dbOrder = tcOrderMapper.selectByPrimaryKey(id);
        Assert.notEq(user.getId(), dbOrder.getUserId(), "这不是你的订单");

        return tcOrderService.receiving(id);
    }

    /**
     * 查询登录用户的订单列表
     * @param tcOrder
     * @return
     */
    @GetMapping
    public PageInfo<TcOrder> list(TcOrder tcOrder){
        PageContext.startPage();
        ByCustUser user = SecurityContext.getUser();
//        ByCustUser user = ByCustUser.builder().id(100755).build();
        tcOrder.setUserId( user.getId() );

        return new PageInfo<TcOrder>(tcOrderMapper.selectUserOrder(tcOrder));
    }

    /**
     * 根据id查看订单详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public TcOrder findOne(@PathVariable Integer id){
        TcOrder order = tcOrderMapper.findById(id);
        ByCustUser user = SecurityContext.getUser();

        Assert.notEq(user.getId(), order.getUserId(), "这不是您的订单");

        return order;
    }

}
