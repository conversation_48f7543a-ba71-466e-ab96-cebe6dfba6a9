package com.wmeimob.fastboot.baoyan.config;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.starter.security.interfaces.JsonWebTokenDecoder;
import io.jsonwebtoken.Claims;
import org.springframework.security.core.userdetails.UserDetails;

public class UserInfoJsonWebTokenDecoder implements JsonWebTokenDecoder {


    @Override
    public UserDetails decode(Claims claims) {

        String openid = (String) claims.get("openid");
        Integer id = (Integer) claims.get("id");
        String nickname = (String) claims.get("nickname");
        String headimgurl = (String) claims.get("headimgurl");
        String mobile = (String) claims.get("mobile");

        //返回给spring security
        ByCustUser userInfo=new ByCustUser();
        userInfo.setId(id);
        userInfo.setWxOpenId(openid);
        userInfo.setNickName(nickname);
        userInfo.setHeadImg(headimgurl);
        userInfo.setMobile(mobile);
        return  userInfo;
    }

}
