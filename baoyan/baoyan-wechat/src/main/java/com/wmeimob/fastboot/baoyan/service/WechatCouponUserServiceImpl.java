package com.wmeimob.fastboot.baoyan.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.GoodsType;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.utils.DecimalUtil;
import com.wmeimob.fastboot.baoyan.vo.CouponListByGoodsVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @ClassName ByCouponServiceImpl
 * @Description 优惠券 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 17:55:47 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WechatCouponUserServiceImpl implements WechatCouponUserService {

    @Resource
    private ByCouponUserMapper byCouponUserMapper;

    @Resource
    private ByCouponMapper byCouponMapper;
    @Resource
    private ByCouponTempMapper byCouponTempMapper;
    @Resource
    private BaseClassifyMapper baseClassifyMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Resource
    private ByGoodsClassifyMapper byGoodsClassifyMapper;
    @Resource
    private BySubGoodsClassifyMapper bySubCardGoodsClassifyMapper;
    @Override
    public List<ByCouponUser> findByCondition(ByCouponUser byCouponUser) {
        if (null == byCouponUser.getIsUse()) {
            //默認查询未使用
            byCouponUser.setIsUse(0);
        }
        List<ByCouponUser> list = byCouponUserMapper.getCouponUserList(byCouponUser);
        if (null != list && list.size() > 0) {
            for (ByCouponUser user : list) {
                //处理赠送优惠卷情况 赠送优惠卷
                if (null != user.getIsGive() && user.getIsGive().equals(1)) {
                    ByCouponTemp byCouponTemp = byCouponTempMapper.selectByPrimaryKey(user.getCouponId());
                    switch (byCouponTemp.getType()) {
                        case 1:
                            BaseClassify classify = baseClassifyMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                            user.setLimitation(classify.getClassifyTitle()+"类");
                            break;
                        case 2:
                            ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                            user.setLimitation(byGoodsInfo.getGoodsName());
                            break;

                        case 0:
                            user.setLimitation("全部商品");
                            break;
                        default:
                    }
                }
                //处理折扣字段  db中是 97 % 折换成 9.7折
                //折扣卷类型
                if (user.getCouponType().equals(2)) {
                    //前端展示 例 97/10 9.7
                    user.setDiscount(user.getDiscount().divide(new BigDecimal(10)));
                }


            }
        }
        return list;
    }

    @Override
    public void consumerCoupon(Integer couponId, Integer userId, Consumer<ByCouponUser> consumer1, Consumer<ByCouponUser> consumer2) {
        ByCouponUser coupon = byCouponUserMapper.findUserCoupon(couponId, userId);
        Assert.isNull(coupon,"该优惠券不存在，请重新下单选择");

        Integer type = coupon.getType();
        if ( type==0 || type==1 || type==2 ){
            consumer1.accept(coupon);
        }else if ( type == 3 || type ==4 || type ==5 || type ==6 ){
            consumer2.accept(coupon);
        }else {
            throw new CustomException("优惠券类型有误，请重新下单选择");
        }
    }

    /**
     * 找到一批淘潮玩商品可以使用的优惠券
     * @param userId
     * @param tcCart
     * @return
     */
    @Override
    public List<ByCouponUser> findUserTcCoupon(Integer userId, List<TcGoodsShoping> tcCart, BigDecimal goodsAmount) {
        //购物车中的全部淘潮玩商品id，排除了不可用优惠券的商品
        List<Integer> ids = tcCart
                .stream()
                .filter(tc -> tc.getTcGoods().getIsCoupon())
                .map(tc -> tc.getTcGoods().getId())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)){
            return null;
        }

        Date now = new Date();
        //查询单个淘潮玩商品 可用优惠券
        List<ByCouponUser> tcCoupon = byCouponUserMapper.selectTcCoupon(userId, ids, now);
        //查询 全部淘潮玩商品可用优惠券
        List<ByCouponUser> allTcCoupon = byCouponUserMapper.selectCouponByType(userId, 6, now);

        tcCoupon.addAll(allTcCoupon);


        tcCoupon = tcCoupon.stream()
                .filter( couponUser -> couponUser.getDiscount().compareTo( goodsAmount )<0 )
                .collect(Collectors.toList());


        return tcCoupon;
    }

    @Override
    public void addCouponUser(ByCouponUser byCouponUser) {
        ByCoupon coupon = byCouponMapper.getCouponInfo(byCouponUser.getCouponId());
        if (coupon == null) {
            throw new CustomException("优惠券错误");
        }
        if (coupon.getTaked() >= coupon.getTotal()) {
            throw new CustomException("优惠券已领完了");
        }
        Example example = new Example(ByCouponUser.class);
        if (coupon.getTotalLimit() > 0) {
            example.createCriteria().andEqualTo("userId", byCouponUser.getUserId()).andEqualTo("couponId", byCouponUser.getCouponId());
            if (byCouponUserMapper.selectCountByExample(example) >= coupon.getTotalLimit()) {
                throw new CustomException("领取次数已达上限");
            }
        }
        if (coupon.getDayLimit() >= 1) {
            example.clear();
            example.createCriteria().andEqualTo("userId", byCouponUser.getUserId()).andEqualTo("couponId", byCouponUser.getCouponId()).andEqualTo("gmtCreate", new Date());
            //优惠券 id 用户id  当日时间
            Map<String, Object> param = Maps.newHashMap();
            param.put("userId", byCouponUser.getUserId());
            param.put("time", DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            param.put("couponId", byCouponUser.getCouponId());
            if (byCouponUserMapper.selectCountByParam(param) >= coupon.getDayLimit()) {
                throw new CustomException("今日领取次数已达上限");
            }
        }
        coupon.setTaked(coupon.getTaked() + 1);
        byCouponMapper.updateByPrimaryKeySelective(coupon);

        byCouponUser.setDiscount(coupon.getDiscount());
        byCouponUser.setFull(coupon.getFull());
        byCouponUser.setName(coupon.getName());

        //效验当前优惠券类型是否是天数  若是则增加对应天数
        if (coupon.getEffectiveType().equals(BaoYanConstant.CONSTANT_TWO)) {
            //计算时间 选择天数 增加
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + coupon.getDayNum());//让日期加天数 
            byCouponUser.setStartDate(new Date());
            byCouponUser.setEndDate(calendar.getTime());
        }else{
            byCouponUser.setStartDate(coupon.getStartDate());
            byCouponUser.setEndDate(coupon.getEndDate());
        }

        byCouponUser.setGmtCreate(new Date());
        byCouponUser.setTargetId(coupon.getTargetId());
        byCouponUser.setIsUse(0);
        byCouponUser.setGetType(BaoYanConstant.GET_TYPE_2);
        byCouponUser.setCouponType(coupon.getCouponType());
        byCouponUser.setAuditStatus(CommonFinal.ONE);//审核通过
        byCouponUser.setType(coupon.getType());
        byCouponUser.setSingleGoodsType(coupon.getSingleGoodsType());
        byCouponUserMapper.insertSelective(byCouponUser);
    }

    @Override
    public List<ByCoupon> couponList(ByCoupon coupon) {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = SecurityContext.getUser();
            coupon.setUserId(user.getId());
            coupon.setGmtCreate(new Date());
        }
        List<ByCoupon> list = byCouponMapper.getWechatCouponList(coupon);
        List<ByCoupon> newCoupon = new ArrayList<>();
        List<ByCoupon> oldCoupon = new ArrayList<>();
        //处理折扣字段  db中是 97 % 折换成 9.7折
        if (null != list && list.size() > 0) {
            for (ByCoupon cu : list) {
                if (cu.getRegisterType() != null && cu.getRegisterType() < 0 || cu.getTaked() >= cu.getTotal() ){
                    cu.setRegisterType(0);
                }
                //折扣卷类型
                if (cu.getCouponType().equals(2)) {
                    //前端展示 例 97/10 9.7
                    cu.setDiscount(cu.getDiscount().divide(new BigDecimal(10)));
                }
                // 处理优惠券是否被领取的问
                if (cu.getRegisterType() != null && cu.getRegisterType() == 0){
                    oldCoupon.add(cu);
                }else {
                    newCoupon.add(cu);
                }
            }
        }
        newCoupon.addAll(oldCoupon);
        return newCoupon;
    }

    @Override
    public List<CouponListByGoodsVo> couponListByGoods(Integer type, Integer goodsId) {
        List<CouponListByGoodsVo> list = new ArrayList<>();
        //查看全局优惠卷设置
        ByPlatformSet byPlatformSet = null;
        //商品优惠卷启用状态
        Integer isCoupon = null;
        //分类id
        List<Integer> classifyId = null;
        if (GoodsType.Goods.getCode().equals(type)){
            byPlatformSet = new ByPlatformSet();
            byPlatformSet.setGeneralCouponIsEnable(Status.on.getCode());
            ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goodsId);
            //普通商品查询分类id
            classifyId = byGoodsClassifyMapper.selectClassifyIdByGoods(goodsId);
            isCoupon = byGoodsInfo.getIsCoupon();
        }else if (GoodsType.SubCardGoods.getCode().equals(type)){
            byPlatformSet = new ByPlatformSet();
            byPlatformSet.setSubCardCouponIsEnable(Status.on.getCode());
            BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(goodsId);
            //次卡商品查询分类id
            classifyId = bySubCardGoodsClassifyMapper.selectClassifyIdByGoods(goodsId);
            isCoupon = bySubCardGoods.getIsCoupon();
        }else if (GoodsType.ticketGoods.getCode().equals(type)){
            byPlatformSet = new ByPlatformSet();
            byPlatformSet.setTicketCouponIsEnable(Status.on.getCode());
        }else if (GoodsType.combinationGoods.getCode().equals(type)){
            ByCombinationGoods byCombinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(goodsId);
            isCoupon = byCombinationGoods.getIsCoupon();
            type = 1;goodsId = byCombinationGoods.getGoodsId();
            classifyId = byGoodsClassifyMapper.selectClassifyIdByGoods(goodsId);
        }else {
            return list;
        }
        if (byPlatformSet != null && byPlatformSetMapper.selectOne(byPlatformSet) == null){
            //全局设置
            return list;
        }else if (isCoupon != null && isCoupon.compareTo(Status.off.getCode())==0){
            //商品设置
            return list;
        }
        //返回商品优惠卷-适用商品（全部商品/属于分类商品/单个商品）-如果是有效期（包含当前日期）-没有删除-上架状态-已领取数<总数
        return byCouponMapper.couponListByGoods(type,goodsId,classifyId);
    }


}
