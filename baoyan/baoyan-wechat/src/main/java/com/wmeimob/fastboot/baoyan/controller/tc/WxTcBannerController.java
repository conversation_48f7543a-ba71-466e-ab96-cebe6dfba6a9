package com.wmeimob.fastboot.baoyan.controller.tc;

import com.wmeimob.fastboot.baoyan.entity.TcBanner;
import com.wmeimob.fastboot.baoyan.service.TcBannerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("tc/tcBanner")
@Slf4j
public class WxTcBannerController {

    @Resource(name = "wxTcBannerServiceImpl")
    private TcBannerService tcBannerService;

    @GetMapping("/")
    public List<? extends TcBanner>  queryAll(){
        List<? extends TcBanner> tcBanners = tcBannerService.wxQueryAll();
        log.info("wx == get === > [出参]  {}",tcBanners);
        return tcBanners;
    }

}
