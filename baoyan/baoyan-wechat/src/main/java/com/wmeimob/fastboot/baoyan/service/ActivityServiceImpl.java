package com.wmeimob.fastboot.baoyan.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.DateUtil;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.common.xml.XmlWriters;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-16 17:36
 * @Version 1.0
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ActivityServiceImpl implements ActivityService {

    @Autowired
    private BaseStoreMapper baseStoreMapper;

    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private ByCustUserMapper byCustUserMapper;
    @Autowired
    private ByCustAppointmentMapper byCustAppointmentMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private ByCombinationGoodsMapper byCombinationGoodsMapper;

    @Override
    public PageInfo showActivity(Integer pageIndex, Integer pageSize, String storeName, String longitude, String latitude, Set set) {
        List<BaseStore> baseStores = this.baseStoreMapper.selectList(storeName, longitude, latitude, set);
        return new PageInfo<>(baseStores);
    }

    @Override
    public PageInfo showProduct(Integer pageIndex, Integer pageSize, String productName, Integer id) {
        if (pageIndex == null) {
            pageIndex = 1;
        }
        if (id == null) {
            throw new CustomException("参数有误");
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<ByGoodsInfo> byGoodsInfos = this.byGoodsInfoMapper.selectProdoct(productName, id);
        return new PageInfo<>(byGoodsInfos);
    }


    @Override
    public Map<String, Object> storeDetail(Integer id) {
        BaseStore baseStore = this.baseStoreMapper.selectByPrimaryKey(id);
        if (baseStore == null || baseStore.getDeleteStatus() || baseStore.getStatus() == false) {
            throw new CustomException("门店不存在");
        }
        ByCustUser user = SecurityContext.getUser();
        ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("store", baseStore);
        map.put("user", byCustUser);
        return map;
    }

    @Override
    public List<Map<String, Object>> showDate(Integer id, String date, String newDate) {
        BaseStore baseStore = this.baseStoreMapper.selectByPrimaryKey(id);
        List<Map<String, Object>> maps = this.baseStoreMapper.showDate(id, date);
        String str = "";
        if (maps.size() != 0) {
            for (int i = 0; i < maps.size(); i++) {
                Map<String, Object> map = maps.get(i);
                BigDecimal apponintmentNum = (BigDecimal) map.get("apponintmentNum");
                //门店即将满员数量 》当日 预约数量 可预约
                if (new BigDecimal(baseStore.getFullNum()).compareTo(apponintmentNum) == 1) {
                    map.put("type", 1);
                }
                //门店即将满员数量 》 或者 《 当日 预约数量 可预约 && 门店预约数量》当日预约数量
                if (new BigDecimal(baseStore.getFullNum()).compareTo(apponintmentNum) != 1 && new BigDecimal(baseStore.getAppointmentNum()).compareTo(apponintmentNum) == 1) {
                    map.put("type", 2);
                }
                // 门店预约数量  》 或者 《当日预约数量
                if (new BigDecimal(baseStore.getAppointmentNum()).compareTo(apponintmentNum) != 1) {
                    map.put("type", 3);
                }
                //当前他的原有逻辑是 把小于当前时间 日期全部剔除掉
                String time = (String) map.get("time");
                Date date1 = DateUtils.parseDate(time, "yyyy-MM-dd");
                if (new Date().getTime() > date1.getTime()) {
                    maps.remove(i);
                    i--;
                    continue;
                } else {
                    str += "#" + time + "#";
                }

            }
            Calendar calendar = Calendar.getInstance();
            Date date1 = DateUtils.parseDate(date, "yyyy-MM-dd");
            calendar.setTime(date1);
            for (int i = 1; i < 31; i++) {
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                if (!DateUtils.formatDate(calendar.getTime(), "yyyy-MM").equals(DateUtils.formatDate(date1, "yyyy-MM"))) {
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    continue;
                }
                if (!str.contains("#" + DateUtils.formatDate(calendar.getTime(), "yyyy-MM-dd") + "#")) {
                    Map<String, Object> data = new HashMap<>();
                    if (calendar.getTime().getTime() < new Date().getTime()) {
                        if (DateUtils.formatDate(calendar.getTime(), "yyyy-MM-dd").equals(DateUtils.formatDate(new Date(), "yyyy-MM-dd"))) {
                            data.put("apponintmentNum", 0);
                            data.put("time", DateUtils.formatDate(calendar.getTime(), "yyyy-MM-dd"));
                            data.put("type", 1);
                        }
                    } else {
                        data.put("apponintmentNum", 0);
                        data.put("time", DateUtils.formatDate(calendar.getTime(), "yyyy-MM-dd"));
                        data.put("type", 1);
                    }
                    maps.add(data);
                }
                ;
            }

        }

        return maps;
    }

    @Override
    public RestResult insert(ByCustAppointment byCustAppointment) {
        InputValidator.checkEmpty(byCustAppointment.getStoreId(), "门店不能能为空");
        InputValidator.checkEmpty(byCustAppointment.getProductId(), "请选择产品");
        InputValidator.checkEmpty(byCustAppointment.getApponintmentDate(), "选择预约日期");
        InputValidator.checkEmpty(byCustAppointment.getApponintmentNum(), "选择预约数量");
        InputValidator.checkEmpty(byCustAppointment.getParentName(), "家长姓名");
        InputValidator.checkEmpty(byCustAppointment.getTel(), "家长手机号");
        InputValidator.checkEmpty(byCustAppointment.getChildName(), "小孩姓名");
        InputValidator.checkEmpty(byCustAppointment.getBirthday(), "小孩生日");
        InputValidator.checkEmpty(byCustAppointment.getTicketChannel(), "选择预约方式");
        BaseStore baseStore = this.baseStoreMapper.selectByPrimaryKey(byCustAppointment.getStoreId());
        if (baseStore == null || baseStore.getDeleteStatus() || baseStore.getStatus() == false) {
            throw new CustomException("门店不存在");
        }
        //判断门店是否是禁用状态
        if (!baseStore.getStatus()) {
            throw new CustomException("该门店已禁用");
        }
        ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byCustAppointment.getProductId());
        if (byGoodsInfo == null || byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
            throw new CustomException("产品已下架");
        }
        ByCustUser user = SecurityContext.getUser();
        byCustAppointment.setCustUserId(user.getId());
        byCustAppointment.setApponintmentStatus(0);
        byCustAppointment.setAddress(baseStore.getAddress());
        byCustAppointment.setGmtCreate(new Date());
        byCustAppointment.setIsDel(Boolean.FALSE);
        byCustAppointment.setApponintmentType(0);
        Integer count = this.baseStoreMapper.selectCountList(byCustAppointment.getApponintmentDate(), baseStore.getId());
        if (count + byCustAppointment.getApponintmentNum() > baseStore.getAppointmentNum()) {
            return RestResult.fail("最多可预约人数" + (baseStore.getAppointmentNum() - count));
        }
        int i = this.byCustAppointmentMapper.insertSelective(byCustAppointment);
        if (i > 0) {
            String s = stringRedisTemplate.opsForValue().get(user.getWxOpenId() + "user");
            if (StringUtil.isEmpty(s)) {
                stringRedisTemplate.opsForValue().set(user.getWxOpenId() + "user", "1");
            } else {
                int i1 = Integer.parseInt(s);
                stringRedisTemplate.opsForValue().set(user.getWxOpenId() + "user", String.valueOf(i1 + 1));
            }
        }
        return i == 0 ? RestResult.fail("预约失败") : RestResult.success();
    }

    /**
     * @Description 分类
     * <AUTHOR>
     * @Date 2019-08-19 17:30
     * @Version 1.0
     */


    @Override
    public PageInfo getClassList(Integer jumpType, Integer type, String goodsName, String longitude, String latitude, Integer pageIndex, Integer pageSize, String classId) {
        if (StringUtil.isEmpty(latitude) || StringUtil.isEmpty(latitude)) throw new CustomException("经纬度不能为空");
        if (pageIndex == null) {
            pageIndex = 1;
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<ByGoodsInfo> baseClassifies = new ArrayList<>();
        if (jumpType ==1){
            baseClassifies = this.byGoodsInfoMapper.selectDistance(type, goodsName, longitude, latitude, !StringUtils.isEmpty(classId) && NumberUtils.isDigits(classId) ? Integer.parseInt(classId) : null);
            for (ByGoodsInfo goodsInfo : baseClassifies) {
                List<ByCombinationGoods> combination = byCombinationGoodsMapper.select(new ByCombinationGoods().setGoodsId(goodsInfo.getId()).setIsDel(0).setStatus(1));
                if (combination != null && !combination.isEmpty()){
                    goodsInfo.setIsCombination(1);
                    List<BigDecimal> price = new ArrayList<>();
                    price.add(goodsInfo.getSellPrice());
                    combination.stream().forEach((p)->{
                        price.add(p.getPrice());
                    });
                    BigDecimal minPrice = Collections.min(price);
                    goodsInfo.setLowPrice(minPrice);
                }else {
                    goodsInfo.setIsCombination(0);
                }
            }
        }else if (jumpType == 2){
            //联票列表
            baseClassifies = byTicketGoodsMapper.selectList(type);
        }else if (jumpType == 3){
            //次卡列表
            baseClassifies = bySubCardGoodsMapper.selectList(type);
        }else if (jumpType == 4){
            //拼团列表
            baseClassifies = byTeamGoodsMapper.selectLists(type);
        }
        return new PageInfo<>(baseClassifies);
    }

    /**
     * @Description
     * <AUTHOR>
     * @Date 2019-12-17 17:48
     * @Version 1.0
     */
    @Override
    public Set<Integer> showOrder(Integer orderId) {
        Set<Integer> set = new HashSet<>();
        Example example1 = new Example(ByOrderGoods.class);
        example1.createCriteria().andEqualTo("orderId", orderId);
        List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
        // 获取商品类型
        byOrderGoods.forEach(goods -> {
            Example example11 = new Example(WriteOffCode.class);
            example11.createCriteria().andEqualTo("detailId", goods.getId()).andEqualTo("orderType", 0);
            WriteOffCode writeOffCode = this.writeOffCodeMapper.selectOneByExample(example11);
            String[] split = writeOffCode.getStoreIds().split(",");
            for (String s : split) {
                set.add(Integer.parseInt(s));
            }
        });
        return set;
    }
}
