package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

/**
 * 渠道客户服务
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ByChannelCustServiceImpl implements ByChannelCustService {
    @Resource
    private ByChannelCustMapper byChannelCustMapper;
    @Override
    public List<ByChannelCust> findByCondition(ByChannelCust byChannelCust) {
        Example example = new Example(ByChannelCust.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byChannelCust.getMobile())) {
            criteria.andLike("mobile", StringUtils.fullFuzzy(byChannelCust.getMobile()));
        }
        if(!StringUtils.isEmpty(byChannelCust.getStoreId())){
            criteria.andEqualTo("storeId",byChannelCust.getStoreId());
        }
        if(!StringUtils.isEmpty(byChannelCust.getGoodsId())){
            criteria.andEqualTo("goodsId",byChannelCust.getGoodsId());
        }
        if(!StringUtils.isEmpty(byChannelCust.getChannelSource())){
            criteria.andEqualTo("channelSource",byChannelCust.getChannelSource());
        }
        if(!StringUtils.isEmpty(byChannelCust.getIsGenerateOrder())){
            criteria.andEqualTo("isGenerateOrder",byChannelCust.getIsGenerateOrder());
        }
        criteria.andEqualTo("deleteStatus",false);
        example.orderBy("id").desc();
        return byChannelCustMapper.selectByExample(example);
    }
}
