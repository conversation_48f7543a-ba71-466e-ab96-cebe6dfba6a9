package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.TcOrderAfter;
import com.wmeimob.fastboot.baoyan.entity.TcOrderGoods;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderAfterMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderAfterService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@RestController
@RequestMapping("/tc/orderAfter")
@Slf4j
public class WxTcOrderAfterController {

    @Resource
    private TcOrderAfterService tcOrderAfterService;

    @Resource
    private TcOrderAfterMapper tcOrderAfterMapper;

    /**
     * 退款退货的售后填写订单
     * @param tcOrderAfter
     * @return
     */
    @PutMapping("/fillExpress")
    public boolean fillExpress(@RequestBody TcOrderAfter tcOrderAfter){
        ByCustUser user = SecurityContext.getUser();

        TcOrderAfter dbAfter = tcOrderAfterMapper.selectByPrimaryKey(tcOrderAfter.getId());
        Assert.notEq(dbAfter.getUserId(), user.getId(), "这不是你的售后申请");
        Assert.notEq(dbAfter.getAfterType(),2,"售后不是退款退货");
        Assert.notEq(dbAfter.getStatus(),2,"退款退货还没有同意");
        Assert.isNull("物流信息不完整",tcOrderAfter.getLogisticsNo(),tcOrderAfter.getRefundExpress());
        //物流单号
        TcOrderAfter newAfter = TcOrderAfter.builder()
                .id(dbAfter.getId())
                .logisticsNo(tcOrderAfter.getLogisticsNo())
                .refundExpress(tcOrderAfter.getRefundExpress())
                .build();

        int update = tcOrderAfterMapper.updateByPrimaryKeySelective(newAfter);
        return update>0;
    }

    /**
     * 查看售后单详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public TcOrderAfter findOne(@PathVariable Integer id){
        ByCustUser user = SecurityContext.getUser();


        TcOrderAfter detail = tcOrderAfterMapper.findDetailById(id);

        Assert.isNull(detail,"没有这个售后单");
        Assert.notEq(detail.getUserId(), user.getId(),"这不是你的售后单");

        return detail;
    }

    /**
     * 查看用户的售后单
     * @return
     */
    @GetMapping
    public PageInfo<TcOrderAfter> findAll(){
        ByCustUser user = SecurityContext.getUser();
//        ByCustUser user = ByCustUser.builder().id(100754).build();

        TcOrderAfter tcOrderAfter = TcOrderAfter.builder().userId(user.getId()).build();
        PageContext.startPage();
        return new PageInfo<TcOrderAfter>(tcOrderAfterMapper.findDetail(tcOrderAfter));
    }


    /**
     * 用户申请售后
     * @param tcOrderAfter
     * @return
     */
    @PostMapping
    public boolean applyAfter(@RequestBody TcOrderAfter tcOrderAfter){
        //获得登录的用户
        Assert.isNull(tcOrderAfter.getReason(), "请填写售后理由");

        ByCustUser user = SecurityContext.getUser();

        TcOrderAfter after = TcOrderAfter.builder()
                .userId( user.getId() )
                .detailId( tcOrderAfter.getDetailId() )
                .afterType( tcOrderAfter.getAfterType() )
                .reason( tcOrderAfter.getReason() )
                .build();

        return tcOrderAfterService.applyAfter(after);
    }



}
