package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.baoyan.service.WeChatTeamOrderService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ByTeamController
 * @projectName baoyan
 * @description: 拼团 Controller
 * @date 2019/8/12 11:09
 */
@RestController
@RequestMapping("team")
@Slf4j
public class ByTeamController {
    @Resource
    private WeChatTeamOrderService weChatTeamOrderService;

    /**
     * <AUTHOR>
     * @date 13:22 2019/5/7
     * @Description:个人拼团列表
     */
    @GetMapping("/myTeam")
    public PageInfo myTeam() {
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        return new PageInfo<ByTeamOrder>(weChatTeamOrderService.myTeam(user));
    }

    /**
     * 拼团详情
     * @param id
     * @return
     */
    @GetMapping("/teamOrderDetail/{id}")
    public Map<String,Object> teamOrderDetail(@PathVariable("id")Integer id){
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        return weChatTeamOrderService.teamOrderDetail(user,id);
    }


    /**
     * <AUTHOR>
     * @date 19:16 2019/5/14
     * @Description:拼团订单评价
     */
    @PostMapping("/evalTeam")
    public void evalTeam(@RequestBody ByEvaluate evaluateInfo){
        if(StringUtils.isEmpty(evaluateInfo.getOrderId())){
            throw new CustomException("订单有误");
        }
        verify(evaluateInfo);
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        log.info("拼团订单评价 evalTeam() evaluateInfo: {},登录人userId:{}", evaluateInfo,user.getId());
        evaluateInfo.setUserId(user.getId());
        weChatTeamOrderService.evalTeam(evaluateInfo);
    }

    /**
     * 效验参数
     * @param evaluateInfo
     */
    private void verify(ByEvaluate evaluateInfo){
        if(StringUtils.isEmpty(evaluateInfo.getMark())){
            throw new CustomException("请选择评价等级");
        }
        if(StringUtils.isEmpty(evaluateInfo.getEvaluateText())){
            throw new CustomException("请输入评论内容");
        }
        if(evaluateInfo.getIsAnonymous() == null){
            evaluateInfo.setIsAnonymous(false);
        }
    }


}
