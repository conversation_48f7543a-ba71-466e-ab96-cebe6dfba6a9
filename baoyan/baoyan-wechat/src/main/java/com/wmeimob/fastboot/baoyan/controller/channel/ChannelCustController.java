package com.wmeimob.fastboot.baoyan.controller.channel;

import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.ByChannelCust;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.service.ByChannelCustService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("channelCust")
@Slf4j
public class ChannelCustController {
    @Resource
    private ByChannelCustService byChannelCustService;

    @Resource
    private WxCustUserService userService;

    @Resource
    private ByCustUserMapper byCustUserMapper;

    @RequestMapping("/")
    public R queryById(String mobile, Integer storeId) {
        ByChannelCust byChannelCust = new ByChannelCust();
        byChannelCust.setMobile(mobile);
        byChannelCust.setIsGenerateOrder(false);
        List<ByChannelCust> byChannelCusts = byChannelCustService.findByCondition(byChannelCust);

        // 查询当前用户是否已经有手机号，如果没有的情况下更新下手机号
        ByCustUser byCustUser = SecurityContext.getUser();
        byCustUser = userService.queryUserInfo(byCustUser.getWxOpenId());
        if(ObjectUtil.isNotEmpty(byCustUser) && ObjectUtil.isEmpty(byCustUser.getMobile())){
            byCustUser.setMobile(mobile);
            if(ObjectUtil.isEmpty(byCustUser.getStoreId())){
                byCustUser.setStoreId(storeId);
            }
            byCustUserMapper.updateByPrimaryKey(byCustUser);
        }

        if(ObjectUtil.isNotEmpty(byChannelCusts) && byChannelCusts.size()>0){
            return R.data(byChannelCusts.get(0));
        }else{
            return R.fail("未找到授权的会员信息，请和工作人员确认后重新认证");
        }
    }
}
