package com.wmeimob.fastboot.baoyan.controller;

import com.alibaba.fastjson.JSONObject;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.HomeService;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.utils.HttpUtils;
import com.wmeimob.fastboot.baoyan.utils.R;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.common.sms.SmsSendHandler;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: WxbyCustUserController
 * @projectName baoyan
 * @description: 用户 Controller
 * @date 2019/8/1 15:53
 */
@RestController
@RequestMapping("user")
@Slf4j
public class WxByCustUserController {

    @Resource
    private WxCustUserService wxCustUserService;

    @Resource
    private SmsSendHandler regSmsSendHandler;

    @Resource
    private HomeService homeService;

    @Resource
    private MemberCardService memberCardService;


    @PostMapping("/updateByCustUser")
    public boolean updateByCustUser(@RequestBody ByCustUser byCustUser){
        ByCustUser user2  = SecurityContext.getUser();
        log.info("[修改客户信息] completeUser()============> user: {},userId:{}",byCustUser,user2.getId());
        byCustUser.setId(user2.getId());
        Boolean aBoolean = wxCustUserService.updateByCustUserInformation(byCustUser);
        log.info("[响应结果]==========>\t{}",aBoolean);
        return aBoolean;
    }

    /**
     * 注册
     * @param mobile
     * @param verifyCode
     * @return
     */
    @GetMapping("register")
    public RestResult register(String mobile, String verifyCode,Integer storeId) {
        log.info("注册 register() mobile: {},verifyCode:{},storeId:{}",mobile,verifyCode,storeId);
        boolean telCodeValidResult = regSmsSendHandler.valid(mobile, verifyCode, "reg");
        if (!telCodeValidResult) {
            throw new CustomException("验证码不正确");
        }
        return wxCustUserService.registerUser(mobile, storeId);
    }

    @GetMapping("login")
    public RestResult login(String mobile,Integer storeId) {
        log.info("注册 register() mobile: {},storeId:{}",mobile,storeId);
        return wxCustUserService.registerUser(mobile, storeId);
    }

    /**
     * 完善客户信息
     * @param user
     * @return
     */
    @PostMapping("/completeUser")
    public Map<String, Object> completeUser(@RequestBody ByCustUser user) {
        ByCustUser user2  = SecurityContext.getUser();
        log.info("完善客户信息 completeUser() user: {},userId:{}",user,user2.getId());
        user.setId(user2.getId());
        return wxCustUserService.completeUser(user);
    }

    /**
     * 效验用户是否被禁用
     *
     * @param request
     * @return
     */
    @PostMapping("checkUserDisable")
    public RestResult checkUserDisable(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        if (StringUtils.isEmpty(jsonObject.get("type"))){
            throw new CustomException("商品已下架");
        }
        Integer type = Integer.parseInt(jsonObject.get("type").toString());
        String ids = (String)jsonObject.get("ids");
        ByCustUser user = SecurityContext.getUser();
        return homeService.checkUserDisable(user,type,ids);
    }

    @GetMapping("balance")
    public R getMemberBalance() {
        return R.ok(memberCardService.getMemberBalance());
    }

}
