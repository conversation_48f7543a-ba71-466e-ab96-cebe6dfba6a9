package com.wmeimob.fastboot.baoyan.filter;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-05-05 12:22
 */
@Configuration
public class WechatWebMvcConfigurerAdapter extends WebMvcConfigurationSupport {

    @Resource
    RegisterInterceptor registerInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(registerInterceptor)
                .excludePathPatterns("/core/wx-token","/order/js-pay-notify","/core/register","/core/reg-sms","/wx/**","/pay/updateOrder",
                        "/common/upload","/file/**","/pay/updateOrder","/pay/updateTicketOrder","/home/<USER>","/goods/**",
                        "/activity/**","/couponUser/**", "/collect/ticket","/collect/ticketDetail","/collect/team","/webSocket/**",
                        "/tc/**","/navigation/home","/store/**");
    }

}
