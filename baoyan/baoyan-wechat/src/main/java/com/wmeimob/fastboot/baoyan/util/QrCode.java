package com.wmeimob.fastboot.baoyan.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectRequest;
import com.wmeimob.fastboot.autoconfigure.oss.AliyunOssProperties;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import me.hao0.wechat.core.Wechat;
import me.hao0.wechat.model.qrcode.WXAQRCodeRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.UUID;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 生成二维码
 * @date 2019-08-22 11:16
 * @Version 1.0
 */
@Service
public class QrCode {

    @Autowired
    private WechatService wechatService;

    @Resource
    private AliyunOssProperties aliyunOssProperties;

    @Resource
    private QrCodeService qrCodeService;


    public String getCode(HttpServletResponse resp, Integer id){
        String str = "pages/writeoff/main";
        return qrCodeService.exportQrCode(resp,id,str);
//        //TODO 生成二维码
//        File image = new File("/tmp/"+ UUID.randomUUID().toString());
////            FileInputStream fileInputStream = null;
//        try {
//
//            Wechat wechat = wechatService.getApiComponent();
//            WXAQRCodeRequest wxaqrCodeRequest = new WXAQRCodeRequest();
//            wxaqrCodeRequest.setPath("/pages/main/writemask/main?id="+id);
//            byte[] bytes = wechat.qr().getWXACodeUnlimit(wxaqrCodeRequest);
//            InputStream fileInputStream = new ByteArrayInputStream(bytes);
//            String url = uploadImageToOSS(aliyunOssProperties, UUID.randomUUID().toString()+".jpg", fileInputStream);
//            return url;
//        }catch (Exception e) {
//            e.printStackTrace();
//        }finally {
//        }
//        return null;
    }
    /**
     * 上传图片
     *
     * @param fileName 文件名
     * @param inputStream 流
     */
    public static String uploadImageToOSS(AliyunOssProperties aliOss, String fileName, InputStream inputStream) {
        /**
         * 创建OSS客户端
         */
        OSSClient ossClient = new OSSClient(aliOss.getEndpoint(), aliOss.getAccessKeyId(), aliOss.getAccessKeySecret());
        try {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String[] names = fileName.split("[.]");
            String name = uuid + "." + names[names.length - 1];
            ossClient.putObject(new PutObjectRequest(aliOss.getBucket(), aliOss.getDir() + name, inputStream));
            String host = (aliOss.isSecure() ? "https" : "http") + "://" +aliOss.getBucket() + "." + aliOss.getEndpoint();
            return host +"/" + aliOss.getDir() + name;
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println(e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return null;
    }
}
