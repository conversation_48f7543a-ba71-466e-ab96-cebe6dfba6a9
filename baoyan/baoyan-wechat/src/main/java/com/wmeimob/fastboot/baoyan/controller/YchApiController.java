package com.wmeimob.fastboot.baoyan.controller;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;
import com.wmeimob.fastboot.baoyan.service.YchApiService;
import com.wmeimob.fastboot.baoyan.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 油菜花接口
 */
@RestController
@RequestMapping("ych")
@Slf4j
public class YchApiController {
    @Autowired
    private YchApiService ychApiService;

    @Autowired
    private MemberCardService memberCardService;

    @RequestMapping("/getBusinessList")
    public R getBusinessList()
    {
        return R.ok(ychApiService.getBusinessList());
    }

    /**
     * 获取充值套餐
     * @return
     */
    @GetMapping("/GetGoodsList")
    public R getGoodsList(@RequestParam Integer goodsType) {
        return R.ok(ychApiService.getGoodsList(goodsType,false));
    }

    @PostMapping("/GetLeaguerByPhone")
    public R GetLeaguerByPhone(
            @RequestParam String Phone) {
        return R.ok(ychApiService.GetLeaguerByPhone(Phone));
    }

    /**
     * 获取余额
     * @return
     */
    @GetMapping("/leaguer_value")
    public R GetLeaguerValues(String phone) {
        try {
            // 优先使用本地数据
            ByCustUser custUser = memberCardService.getMemberBalance();
            return R.ok(custUser);
        } catch (Exception e) {
            log.error("获取余额失败", e);
            // 降级：尝试直接调用油菜花接口
            return R.ok(ychApiService.GetLeaguerValues(phone));
        }
    }

    /**
     * 会员预存款变更V3
     * @param changeValue
     * @param leaguerID
     * @param leaguerPrepaidChangeType
     * @return
     */
    @GetMapping("/LeaguerPrepaidChange")
    public R LeaguerPrepaidChange(float changeValue,String leaguerID,String leaguerPrepaidChangeType){
        return R.ok(ychApiService.LeaguerPrepaidChange(changeValue,leaguerID,leaguerPrepaidChangeType));
    }

    /**
     * 注册手机号
     * @param phone
     * @return
     */
    @GetMapping("/registerLeaguer")
    public R registerLeaguer(String  phone) {
        try {
            // 在本地创建会员卡，并异步同步到油菜花
            memberCardService.registerMemberCard(phone);
            
            // 查询本地会员卡信息
            ByCustUser cardInfo = memberCardService.getMemberCardInfo(phone);
            return R.ok(cardInfo);
        } catch (Exception e) {
            log.error("注册会员失败", e);
            // 降级：尝试直接调用油菜花接口
            return R.ok(ychApiService.registerLeaguer(phone));
        }
    }

    /**
     * 获取储值变更记录
     */
    @GetMapping("/leaguer_value_log")
    public R GetLeaguerValuesLog() {
        return R.ok(ychApiService.GetLeaguerValuesLog());
    }

    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    public R createOrder(@RequestBody YchGoods ychGoods){
        return R.ok(ychApiService.createOrder(ychGoods,""));
    }
}
