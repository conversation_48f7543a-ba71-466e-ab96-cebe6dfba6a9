package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import com.wmeimob.fastboot.baoyan.service.TcImgModuleService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wangShun
 * @systemName: king
 * @CreationDate: 2021/9/3
 * @packageName: com.wmeimob.fastboot.baoyan.controller.tc
 */

@RestController
@RequestMapping("tc/tcImgModule")
public class WxTcImgModuleController {
    @Resource(name ="wxTcImgModuleServiceImpl" )
    private TcImgModuleService tcImgModuleService;

    @GetMapping("/")
    public PageInfo<TcImgModule> selectAll() {
        return new PageInfo<>(this.tcImgModuleService.queryAll(null));
    }
}
