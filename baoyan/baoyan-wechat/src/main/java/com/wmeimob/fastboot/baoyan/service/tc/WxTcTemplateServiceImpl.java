package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.BaseClassify;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.mapper.BaseClassifyMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcTemplateMapper;
import com.wmeimob.fastboot.baoyan.service.TcTemplateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/8/2
 * packageName:com.wmeimob.fastboot.baoyan.service.tc
 */
@Service("wxTcTemplateServiceImpl")
public class WxTcTemplateServiceImpl implements TcTemplateService {
    @Resource
    private TcTemplateMapper tcTemplateMapper;

    @Resource
    private BaseClassifyMapper baseClassifyMapper;

    @Override
    public List<TcTemplate> queryPage(TcTemplate queryObject){
        List<TcTemplate> templates = tcTemplateMapper.wxQueryAll(queryObject);

        for (TcTemplate template : templates) {
            if (template.getJumpType()==1){
                String target = template.getTarget();
                BaseClassify classify = baseClassifyMapper.selectByPrimaryKey(target);
                template.setBaseBanner(classify.getClassifyImg());
            }
        }

        return templates;
    }
}
