package com.wmeimob.fastboot.baoyan.filter;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wechat.model.base.WechatUser;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2018-05-05 12:10
 * @Description: 注册拦截器
 */
@Component
@Slf4j
public class RegisterInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private WxCustUserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            ByCustUser user = SecurityContext.getUser();
            ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
            //*401 授权*//
           if (byCustUser == null){
               response.setStatus(HttpStatus.UNAUTHORIZED.value());
               return false;
           }
        } catch (RuntimeException e) {
            log.error("出错的控制器{}",request.getRequestURI());
            log.error(e.getMessage(), e);
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            return false;
        }
        return true;
    }
}
