package com.wmeimob.fastboot.baoyan.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 购物车
 * @date 2019-08-13 13:46
 * @Version 1.0
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ByGoodsShoppingServiceImp implements ByGoodsShoppingService{

    @Autowired
    private ByGoodsShopingMapper  byGoodsShopingMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Autowired
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    private ByCombinationGoodsMapper byCombinationGoodsMapper;

    @Resource
    private TcGoodsShopingMapper tcGoodsShopingMapper;

    /**
     * 统计用户的淘潮玩和门票购物车数量总和
     * @return
     */
    @Override
    public int cartCount(Integer userId) {
        Integer ticketCartCount = this.byGoodsShopingMapper.selectShopCount(userId);
        ticketCartCount = ticketCartCount==null ? 0 : ticketCartCount ;
        Integer tcCartCount = tcGoodsShopingMapper.selectshopCount(userId);
        tcCartCount = tcCartCount==null ? 0 : tcCartCount ;

        return ticketCartCount+tcCartCount;
    }

    /**
     * @Description 我的购物车
     * <AUTHOR>
     * @Date        2019-08-13 13:47
     * @Version    1.0
     */
    @Override
    public List<ByGoodsShoping> show() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal.equals("anonymousUser")){
            return new ArrayList<>();
        }
        ByCustUser user = SecurityContext.getUser();
        List<ByGoodsShoping> show = this.byGoodsShopingMapper.show(user.getId());
        for (ByGoodsShoping shoping : show){
            if (shoping.getGoodsType() == 2){
                shoping.setGoodsType(3);
                continue;
            }
            if (shoping.getGoodsType() == 3){
                shoping.setGoodsType(2);
                continue;
            }
            if (shoping.getGoodsType() == 5){
                shoping.setGoodsType(10);
                continue;
            }
            if (Integer.parseInt(shoping.getGoodsStock()) < shoping.getGoodsCount() ){
                shoping.setType(0);
            }
        }
        return show;
    }

    /**
    * @Description 添加购物车
    * <AUTHOR>
    * @Date        2019-08-13 13:51
    * @Version    1.0
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult insert(ByGoodsShoping byGoodsShoping) {
        ByCustUser user = SecurityContext.getUser();
        log.info("添加购物车 byGoodsShoping: {} 当前登录人userId：{}", byGoodsShoping,user.getId());
        InputValidator.checkEmpty(byGoodsShoping.getGoodsId(),"请选择商品");
        InputValidator.checkEmpty(byGoodsShoping.getGoodsCount(),"选择购买数量");
        InputValidator.checkEmpty(byGoodsShoping.getGoodsType(),"请选择商品");
        byGoodsShoping.setUserId(user.getId());
        Integer limited = null;
        if (byGoodsShoping.getGoodsType() == 1){
            ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
            limited = byGoodsInfo.getLimited();
            if (byGoodsInfo == null || byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)){
                return RestResult.fail("商品已下架");
            }else if (byGoodsInfo.getLimited() != null && byGoodsInfo.getLimited() != 0 && byGoodsShoping.getGoodsCount()>byGoodsInfo.getLimited()){
                return RestResult.fail("商品限购"+byGoodsInfo.getLimited());
            }
        }
        if (byGoodsShoping.getGoodsType() == 2){
            BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
            limited = bySubCardGoods.getLimited();
            if (null == bySubCardGoods || bySubCardGoods.getIsDel() || bySubCardGoods.getStatus().equals(0)){
                return RestResult.fail("商品已下架");
            }else if (bySubCardGoods.getLimited() != null && bySubCardGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount()>bySubCardGoods.getLimited()){
                return RestResult.fail("商品限购"+bySubCardGoods.getLimited());
            }
        }
        if (byGoodsShoping.getGoodsType() == 3){
            ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
            if (null == byTicketGoods || byTicketGoods.getIsDel() || byTicketGoods.getStatus() == false){
                return RestResult.fail("商品已下架");
            }
        }
        if (byGoodsShoping.getGoodsType() == 5){
            ByCombinationGoods byCombinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
            limited = byCombinationGoods.getLimited();
            if (null == byCombinationGoods || byCombinationGoods.getIsDel().compareTo(Status.on.getCode())==0 || byCombinationGoods.getStatus().compareTo(Status.off.getCode())==0){
                return RestResult.fail("商品已下架");
            }else if (byCombinationGoods.getLimited() != null && byCombinationGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount() > byCombinationGoods.getLimited()){
                return RestResult.fail("商品限购"+byCombinationGoods.getLimited());
            }
        }


        Example example = new Example(ByGoodsShoping.class);
        example.createCriteria()
                .andEqualTo("goodsId",byGoodsShoping.getGoodsId())
                .andEqualTo("userId",user.getId())
                .andEqualTo("isDel",0)
                .andEqualTo("goodsType",byGoodsShoping.getGoodsType());
        List<ByGoodsShoping> byGoodsShopings = this.byGoodsShopingMapper.selectByExample(example);
        if (byGoodsShopings.size() != 0){
            ByGoodsShoping byGoodsShoping1 = byGoodsShopings.get(0);
            Integer i = byGoodsShoping.getGoodsCount() + byGoodsShoping1.getGoodsCount();
            if (limited != null && limited != 0 && i > limited){
                return RestResult.fail("商品限购"+limited);
            }
            byGoodsShoping1.setGoodsCount(i);
            int i1 = byGoodsShopingMapper.updateByPrimaryKeySelective(byGoodsShoping1);
            return RestResult.success();
        }

        int i = byGoodsShopingMapper.insertSelective(byGoodsShoping.setGmtCreate(new Date()));
        return i == 0 ?RestResult.fail("添加失败"):RestResult.success();
    }
    /**
     * @Description 删除购物车
     * <AUTHOR>
     * @Date        2019-08-13 16:52
     * @Version    1.0
     */
    @Override
    public RestResult delete(String ids) {
        log.info("删除购物车 ids: {}", ids);
        InputValidator.checkEmpty(ids,"选择商品");
        String[] split = ids.split(",");
        for (int i = 0 ;i < split.length; i++){
            int j = this.byGoodsShopingMapper.deleteByPrimaryKey(split[i]);
            if (j == 0) throw new CustomException("删除失败");
        }
        return RestResult.success();
    }

    @Override
    public RestResult delete(List<Integer> ids) {
        log.info("删除购物车 ids: {}", ids);
        InputValidator.checkEmpty(ids,"选择商品");
        for (Integer id : ids) {
            int j = this.byGoodsShopingMapper.deleteByPrimaryKey(id);
            if (j == 0) throw new CustomException("删除失败");
        }
        return RestResult.success();
    }



    /**
     * @Description 修改购物车
     * <AUTHOR>
     * @Date        2019-08-13 17:01
     * @Version    1.0
     */
    @Override
    public RestResult update(ByGoodsShoping byGoodsShoping) {
        log.info("修改购物车 byGoodsShoping: {}", byGoodsShoping);
        InputValidator.checkEmpty(byGoodsShoping.getId(),"参数有误");
        InputValidator.checkEmpty(byGoodsShoping.getGoodsCount(),"参数有误");
        ByGoodsShoping shoping = byGoodsShopingMapper.selectByPrimaryKey(byGoodsShoping.getId());
        if (shoping != null && shoping.getGoodsType() != null && shoping.getGoodsId() != null){
            if (shoping.getGoodsType() == 1){
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(shoping.getGoodsId());
                if (byGoodsInfo != null && byGoodsInfo.getLimited() != null && byGoodsShoping.getGoodsCount() > byGoodsInfo.getLimited()){
                    return RestResult.fail("商品限购"+byGoodsInfo.getLimited());
                }
            }else if (shoping.getGoodsType() == 2){
                BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
                if (bySubCardGoods != null && bySubCardGoods.getLimited() != null && byGoodsShoping.getGoodsCount() > bySubCardGoods.getLimited()){
                    return RestResult.fail("商品限购"+bySubCardGoods.getLimited());
                }
            }else if (shoping.getGoodsType() == 5){
                ByCombinationGoods byCombinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
                if (byCombinationGoods != null && byCombinationGoods.getLimited() != null && byGoodsShoping.getGoodsCount() > byCombinationGoods.getLimited()){
                    return RestResult.fail("商品限购"+byCombinationGoods.getLimited());
                }
            }
        }
        int i = this.byGoodsShopingMapper.updateByPrimaryKeySelective(byGoodsShoping);
        return i == 0 ?RestResult.fail("参数有误"):RestResult.success();
    }

    @Override
    public Map<String, Object> showAll(List<ByGoodsShoping> byGoodsShoping) {
        Map<String,Object> map = new HashMap<>(2);

        if (byGoodsShoping.size() == 0){
            map.put("amount",0);
            return map;
        }
        BigDecimal bigDecimal = new BigDecimal(0);
        for (ByGoodsShoping e : byGoodsShoping){
            BigDecimal multiply = new BigDecimal(e.getSellPrice()).multiply(new BigDecimal(e.getGoodsCount()));
            bigDecimal = multiply.add(bigDecimal);
        };
        map.put("amount",bigDecimal);
        return map;
    }
}
