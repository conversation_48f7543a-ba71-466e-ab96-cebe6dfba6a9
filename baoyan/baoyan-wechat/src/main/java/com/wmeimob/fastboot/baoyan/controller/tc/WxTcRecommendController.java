package com.wmeimob.fastboot.baoyan.controller.tc;


import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.service.TcRecommendService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * (TcRecommend)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-20 19:02:47
 */
@RestController
@RequestMapping("/tc/recommend")
@Slf4j
public class WxTcRecommendController {
    /**
     * 服务对象
     */
    @Resource(name = "wxTcRecommendServiceImpl")
    private TcRecommendService tcRecommendService;

    /**
     * 通过主键查询单条数据
     *
     */
    @GetMapping("/{id}")
    public TcRecommend selectOne(@PathVariable("id") Integer id) {
        log.info("get  => selectOne [入参]============={}",id);
        return this.tcRecommendService.queryById(id);
    }

    /**
     * 根据模版 id 查询 推荐系列商品信息
     * @param id
     * @return
     */

    @GetMapping("/template/{id}")
    public List<? extends TcRecommend> queryAllByTemplateId(@PathVariable("id") Integer id) {
        log.info("get  => queryAllByTemplateId [入参]============={}",id);
        TcRecommend tcRecommend =new TcRecommend();
        tcRecommend.setTemplateId(id);
        return this.tcRecommendService.queryAll(tcRecommend);
    }


}
