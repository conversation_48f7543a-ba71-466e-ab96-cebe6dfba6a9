package com.wmeimob.fastboot.baoyan.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.vo.CombinationVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.common.entity.RichText;
import com.wmeimob.fastboot.starter.common.mapper.RichTextMapper;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-07 17:47
 * @Version 1.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GoodsServiceImpl implements GoodsService {

    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private ByRichTextMapper richTextMapper;
    @Autowired
    private ByEvaluateMapper evaluateMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Autowired
     private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    private ByGoodsShopingMapper byGoodsShopingMapper;
    @Autowired
    private ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Resource
    private ByGoodsShoppingService byGoodsShoppingService;

    @Override
    public void checkGoodsStock(Integer goodsId, Integer stock) {
        ByGoodsInfo dbGoods = byGoodsInfoMapper.selectByPrimaryKey(goodsId);
        if (dbGoods.getGoodsStock() < stock){
            throw new CustomException("库存不足");
        }
    }

    @Override
    public Map<String,Object> combinationGoods(Integer id) {
        ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(id);
        return detail(combinationGoods.getGoodsId());
    }
    /**
    * @Description 普通商品
    * <AUTHOR>
    * @Date        2019-08-07 17:49
    * @Version    1.0
    */
    @Override
    public Map<String,Object> detail(Integer id) {
        ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(id);
        if (byGoodsInfo != null && byGoodsInfo.getIsDel() == false){
            ByRichText richText = getRich(id,1);
            byGoodsInfo.setRichContent(richText == null? null :String.valueOf(richText.getContent()));
        }

        Map<String, Object> dataMap = new HashMap<>(3);
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = SecurityContext.getUser();
            int shoppingCount = byGoodsShoppingService.cartCount(user.getId());
            dataMap.put("shoppingCount", shoppingCount );
        }
        dataMap.put("store",this.byGoodsInfoMapper.showStore(id));
        dataMap.put("byGoodsInfo",byGoodsInfo);
        dataMap.put("evaluate",getEvaluate(1,1,5,id));
        dataMap.put("combination", getCombination(id));
        return dataMap;
    }


    /**
     * @Description 次卡商品
     * <AUTHOR>
     * @Date        2019-08-07 17:59
     * @Version    1.0
     */
    @Override
    public Map<String,Object> cardGoods(Integer id) {
        BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(id);
        Map<String, Object> dataMap = new HashMap<>(4);
        if (bySubCardGoods != null && bySubCardGoods.getIsDel() == false){
            ByRichText richText = getRich(id,2);
            bySubCardGoods.setRichContent(richText == null ? null:String.valueOf(richText.getContent()));
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = SecurityContext.getUser();
            dataMap.put("shoppingCount",getShoppingCount(user.getId()));
        }
        dataMap.put("bySubCardGoods",bySubCardGoods);
        dataMap.put("evaluate",getEvaluate(3,1,5,id));
        dataMap.put("store",this.bySubCardGoodsMapper.showStore(id));
        return dataMap;
    }

    /**
    * @Description 联票商品
    * <AUTHOR>
    * @Date        2019-08-09 17:07
    * @Version    1.0
    */
    @Override
    public Map<String, Object> votingGoods(Integer id) {
        ByTicketGoods byTicketGoods = byTicketGoodsMapper.selectByPrimaryKey(id);
        byTicketGoods.setGoodsName(byTicketGoods.getTicketGoodsName());
        Map<String, Object> dataMap = new HashMap<>(4);
        if (byTicketGoods != null && byTicketGoods.getIsDel() == false){
            ByRichText richText = getRich(id,3);
            byTicketGoods.setRichContent(richText == null ? null:String.valueOf(richText.getContent()));
            List<ByGoodsInfo> byGoodsInfo1 = byGoodsInfoMapper.selectGoods(id);
            dataMap.put("goods",byGoodsInfo1);
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = SecurityContext.getUser();
            dataMap.put("shoppingCount",getShoppingCount(user.getId()));
        }
        dataMap.put("byTicketGoods",byTicketGoods);
        dataMap.put("evaluate",getEvaluate(2,1,5,id));
        return dataMap;
    }

    /**更多评价*/
    @Override
    public PageInfo evaluate(Integer id, Integer type, Integer pageIndex, Integer pageSize) {
    //        跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品）
        if (type == 7) type = 2;
        if (type == 8) type = 3;
        if (type == 6) type = 1;
        List<ByEvaluate> evaluate = getEvaluate(type, pageIndex, pageSize, id);
        return new PageInfo<>(evaluate);
    }

    private List<ByEvaluate> getEvaluate(Integer type,Integer pageIndex,Integer pageSize,Integer id){
        PageHelper.startPage(pageIndex,pageSize);
        return this.evaluateMapper.getEvaluate(type,id);
    }


    /**
     * 商品规格-上架-未删除-库存>0 now()<核销结束时间
     * @return
     */
    public List<CombinationVo> getCombination(Integer id){
        return byCombinationGoodsMapper.getCombination(id);
    }

    private ByRichText getRich(Integer id,Integer type){
        Example example = new Example(ByRichText.class);
        example.createCriteria()
                .andEqualTo("dataId",id)
                .andEqualTo("dataType",type);
        List<ByRichText> byRichTexts = this.richTextMapper.selectByExample(example);
        ByRichText byRichText = new ByRichText();
        return byRichTexts.size() == 0?byRichText:byRichTexts.get(0);
    }

    private Integer getShoppingCount(Integer id){
        return this.byGoodsShopingMapper.selectShopCount(id);
    }
}
