package com.wmeimob.fastboot.baoyan.controller.order;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import com.wmeimob.fastboot.baoyan.service.ByOrderAfterService;
import com.wmeimob.fastboot.baoyan.service.WechatOrderAfterService;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName WechatOrderAfterController
 * @Description 我的售后
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("orderafter")
@Slf4j
public class WechatOrderAfterController {

    @Resource
    private WechatOrderAfterService byOrderAfterService;

    /**
     * 我的售后 查询
     * @param request
     * @param byOrderAfter
     * @return
     */
    @GetMapping("/list")
    public PageInfo queryForByOrderAfter(HttpServletRequest request, ByOrderAfter byOrderAfter){
        PageContext.startPage();
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        byOrderAfter.setUserId(user.getId());
        return new PageInfo<ByOrderAfter>(byOrderAfterService.findByCondition(byOrderAfter));
    }

    /**
     * <通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByOrderAfter queryForByOrderAfterById(HttpServletRequest request, @PathVariable("id") Integer id){
        return  byOrderAfterService.queryByOrderAfterById(id);
    }

    /**
     * 查看售后详情 回显
     * @param request
     * @param byOrderAfter
     * @return
     */
    @GetMapping("/getAfterDetail")
    public OrderAfterDetailVO queryAfterByOrderIdAndGoodsId(HttpServletRequest request, ByOrderAfter byOrderAfter){
        return  byOrderAfterService.queryAfterByOrderIdAndGoodsId(byOrderAfter);
    }

    /**
     * 删除售后订单
     * @param request
     * @param byOrderAfter
     * @return
     */
    @PostMapping("/deleteOrderAfter")
    public RestResult deleteOrderAfter(HttpServletRequest request,@RequestBody ByOrderAfter byOrderAfter){
        log.info("删除售后订单 byOrderAfter: {}", byOrderAfter);
        return  byOrderAfterService.deleteOrderAfter(byOrderAfter);
    }


}
