package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByTeamOrderServiceImpl
 * @Description  拼团订单表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class WeChatTeamOrderServiceImpl implements WeChatTeamOrderService {

    @Resource
    private ByTeamOrderMapper byTeamOrderMapper;
    @Resource
    private ByTeamMapper byTeamMapper;
	@Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
	@Resource
	private ByEvaluateMapper byEvaluateMapper;
	@Resource
	private ByPlatformSetMapper byPlatformSetMapper;
	@Resource
	private ByCustUserMapper byCustUserMapper;

	@Override
	public List<ByTeamOrder> myTeam(ByCustUser user) {
		ByTeamOrder sfTeamOrder = new ByTeamOrder();
		sfTeamOrder.setUserId(user.getId());
		return byTeamOrderMapper.myTeam(sfTeamOrder);
	}
	@Override
	public Map<String,Object> teamOrderDetail(ByCustUser user, Integer id) {
		ByTeamOrder sfTeamOrder = byTeamOrderMapper.selectByPrimaryKey(id);
		if(null == sfTeamOrder){
			throw new CustomException("订单信息不存在");
		}
		//当订单状态为已完成 取值更新字段 为使用完成时间
		if (sfTeamOrder.getOrderStatus().equals(3)) {
			sfTeamOrder.setUseTime(sfTeamOrder.getGmtUpdate());
		}

		ByTeam sfTeam = byTeamMapper.teamDetailByOrder(sfTeamOrder.getTeamId());
		Map<String, Object> map = new HashMap<>(3);
		map.put("byTeamOrder",sfTeamOrder);
		map.put("byTeam",sfTeam);
		return map;
	}

	@Override
	public void evalTeam(ByEvaluate evaluateInfo) {
		ByTeamOrder sfTeamOrder = byTeamOrderMapper.selectByPrimaryKey(evaluateInfo.getOrderId());
		if(null == sfTeamOrder){
			throw new CustomException("拼团订单信息异常");
		}
		if(!CommonFinal.THREE.equals(sfTeamOrder.getOrderStatus())){
			throw new CustomException("订单状态不允许评价");
		}
		ByTeamGoods sfTeamGoods = byTeamGoodsMapper.selectByPrimaryKey(sfTeamOrder.getTeamGoodsId());
		if(null ==sfTeamGoods){
			throw new CustomException("订单商品信息异常");
		}
		ByCustUser user = (ByCustUser) SecurityContext.getUser();
		evaluateInfo.setGoodsName(sfTeamGoods.getTeamName());
		evaluateInfo.setGoodsId(sfTeamGoods.getGoodsId());
		evaluateInfo.setOrderNo(sfTeamOrder.getOrderNo());
		evaluateInfo.setUserName(user.getUsername());
		evaluateInfo.setImg(evaluateInfo.getImg());
		evaluateInfo.setResouceType(4);
		evaluateInfo.setIsDel(Boolean.FALSE);
		evaluateInfo.setGmtCreate(new Date());
		evaluateInfo.setOrderId(sfTeamOrder.getId());
		int i = byEvaluateMapper.insertSelective(evaluateInfo);
		if(i<1){
			throw new CustomException("评价失败");
		}
		sfTeamOrder.setOrderStatus(3);
		sfTeamOrder.setGmtUpdate(new Date());
		int i1 = byTeamOrderMapper.updateByPrimaryKeySelective(sfTeamOrder);
		if(i1<1){
			throw new CustomException("评价失败");
		}
		//新增积分
		addIntegral(evaluateInfo.getUserId());
	}

	/**
	 * 新增积分
	 * @param userId
	 */
	private void addIntegral(Integer userId) {
		//查询评价获取积分数量
		ByPlatformSet set=new ByPlatformSet();
		ByPlatformSet resultSet = byPlatformSetMapper.selectOne(set);
		if (null!=resultSet && null!=resultSet.getCommentIntegral()) {
			//当前积分+加评价积分
			ByCustUser user = byCustUserMapper.selectByPrimaryKey(userId);
			user.setNowPoint(user.getNowPoint()+resultSet.getCommentIntegral());
			byCustUserMapper.updateByPrimaryKey(user);
		}
		log.info("ByPlatformSet 平台设置info ",resultSet);
	}


}
