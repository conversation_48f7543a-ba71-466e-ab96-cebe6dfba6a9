package com.wmeimob.fastboot.baoyan.service;

import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.util.QrCode;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.ws.WebSocketServer;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 核销
 * @date 2019-08-22 10:12
 * @Version 1.0
 */
@Service
@Slf4j
public class WriteServiceImpl implements WriteService {

    @Autowired
    private WriteOffCodeMapper writeOffCodeMapper;
    @Autowired
    private QrCode qrCode;
    @Autowired
    private ByCustUserMapper byCustUserMapper;
    @Autowired
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Autowired
    private ByStoreStaffMapper byStoreStaffMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private ByOrderAfterMapper byOrderAfterMapper;
    @Autowired
    private ByTeamOrderMapper byTeamOrderMapper;

    @Autowired
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    @Autowired
    private ByPlatformSetMapper byPlatformSetMapper;
    @Autowired
    private ByIntegralLogMapper byIntegralLogMapper;
    @Autowired
    private BaseStoreMapper baseStoreMapper;
    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Autowired
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    private ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Resource
    private ByOrderLogService orderLogService;
    @Autowired
    private WebSocketServer socketServer;
    @Resource
    private ByChannelCustMapper byChannelCustMapper;
    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @Autowired
    private YchApiService ychApiService;

    @Resource
    private VerifyService verifyService;


    /**查看核销码*/

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<WriteOffCode> checkWrite(HttpServletResponse resp,String orderNo, Integer type) {
        ByCustUser user = SecurityContext.getUser();
        Example example = new Example(WriteOffCode.class);
        example.createCriteria().andEqualTo("orderNo", orderNo).andEqualTo("orderType",type)
                .andEqualTo("orderState",0).andEqualTo("custUserId",user.getId());
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
        if (writeOffCodes.size() != 0) {
            for (WriteOffCode writeOffCode : writeOffCodes) {
                if (StringUtil.isEmpty(writeOffCode.getCode()) || "生成失败".equals(writeOffCode.getCode())) {
                    writeOffCode.setCode( getCode(resp,writeOffCode) );
                    this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
                }
                if (writeOffCode.getType() != null && writeOffCode.getType() == 5){
                    ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(writeOffCode.getSourceGoodsId());
                    ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                    writeOffCode.setGoodsName(goodsInfo.getGoodsName()+"（"+writeOffCode.getGoodsName()+"）");
                }else {
                    ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(writeOffCode.getDetailId());
                    if ("1".equals(byOrderGoods.getProductType())){
                        ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods.getGoodsId());
                        if (!StringUtils.isEmpty(goodsInfo.getSpecName())){
                            writeOffCode.setGoodsName(goodsInfo.getGoodsName()+"（"+goodsInfo.getSpecName()+"）");
                        }
                    }
                }
            }
        }
        return writeOffCodes;
    }

    private String  getCode(HttpServletResponse resp,WriteOffCode writeOffCode){
        String code = qrCode.getCode(resp,writeOffCode.getId());
        if ("生成失败".equals(code)){
            return getCode(resp,writeOffCode);
        }
        return code;
    }
    /**
    * @Description 核销详情
    * @Date        2024-07-03
    * @Version    1.0
    */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> check(Integer id,ByCustUser user) {
        ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
        WriteOffCode writeOffCode = this.writeOffCodeMapper.selectByPrimaryKey(id);
        if (byCustUser == null || writeOffCode == null){
            throw new CustomException("您不是管理员，无权操作");
        }
        Example example = new Example(ByStoreStaff.class);
        example.createCriteria().andEqualTo("openId",byCustUser.getWxOpenId()).andEqualTo("isDel",Boolean.FALSE);
        List<ByStoreStaff> byStoreStaffs = this.byStoreStaffMapper.selectByExample(example);
        if (byStoreStaffs.size() == 0) throw new CustomException("无权操作");
        /**核销*/
        if (StringUtil.isNotEmpty(writeOffCode.getStoreIds())){
            String[] split = writeOffCode.getStoreIds().split(",");
            int j = 0;
            for (int i = 0 ; i < split.length ; i++){
                for (ByStoreStaff byStoreStaff : byStoreStaffs){
                    if (byStoreStaff.getStoreId().equals(Integer.parseInt(split[i]))){
                        j = 1;
                        break;
                    }
                }
            }
            if (j == 0){
                throw new CustomException("您不是该门店下的员工不能操作");
            }
        }
        try {
            //判断核销 开始时间 是否 大于等于 当天 &&  结束时间小于等于今天
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = format.parse(format.format(new Date()));
            Date startDate = format.parse(format.format(writeOffCode.getExpiryDate()));
            Date endDate = format.parse(format.format(writeOffCode.getEndDate()));
            //当天 大于等于核销开始时间
            if (currentDate.compareTo(startDate) < 0) {
                throw new CustomException("暂未到核销开始时间");
            }
            //当天 >结束时间 过期
            if (currentDate.compareTo(endDate) == 1) {
                throw new CustomException("已核销或已过期");
            }

        } catch (ParseException e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }

        Map<String,Object> map = new HashMap<>();
        map.put("id",writeOffCode.getId());

        String goodsName = "";
        String goodsImg = "";
        if (writeOffCode.getOrderType() == 1){
            ByTeamOrder byTeamOrder = this.byTeamOrderMapper.selectByPrimaryKey(writeOffCode.getDetailId());
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
            goodsName = byTeamGoods.getTeamName();
            goodsImg = byTeamGoods.getGoodsImg();
        }else {
            ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(writeOffCode.getDetailId());
            if (byOrderGoods.getProductType().equals("1")){
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                goodsName = byGoodsInfo.getGoodsName();
                if (!StringUtils.isEmpty(byGoodsInfo.getSpecName())){
                    goodsName = byGoodsInfo.getGoodsName()+"（"+byGoodsInfo.getSpecName()+"）";
                }
                goodsImg = byGoodsInfo.getGoodsImg();
            }
            if (byOrderGoods.getProductType().equals("2")){
                BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                goodsName = bySubCardGoods.getGoodsName();
                goodsImg = bySubCardGoods.getGoodsImg();
            }
            if (byOrderGoods.getProductType().equals("3")){
                ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                goodsName = byTicketGoods.getGoodsName();
                goodsImg = byTicketGoods.getGoodsImg();
            }
            if (byOrderGoods.getProductType().equals("5")){
                ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                goodsName = byGoodsInfo.getGoodsName()+"（"+combinationGoods.getName()+"）";
                goodsImg = byGoodsInfo.getGoodsImg();
            }
            if(ObjectUtil.isNotEmpty(byOrderGoods.getOrderId())){
                ByOrders byOrders = this.byOrdersMapper.selectByPrimaryKey(byOrderGoods.getOrderId());
                if(ObjectUtil.isNotEmpty(byOrders.getIsChannel()) && byOrders.getIsChannel() == 1){
                    ByChannelCust byChannelCust = byChannelCustMapper.selectByPrimaryKey(byOrders.getChannelId());
                    Example exampleDictItem = new Example(SysDictItem.class);
                    exampleDictItem.createCriteria().andEqualTo("dictId",1)
                            .andEqualTo("itemValue",byChannelCust.getChannelSource());
                    List<SysDictItem> sysDictItemList = sysDictItemMapper.selectByExample(exampleDictItem);
                    if(sysDictItemList.size() >0){
                        map.put("channelSourceName",sysDictItemList.get(0).getItemName());
                    }
                }
            }

        }

        ByCustUser custUser = this.byCustUserMapper.selectByPrimaryKey(writeOffCode.getCustUserId());
        Example example1 = new Example(ByOrderAfter.class);
        example1.createCriteria().andEqualTo("orderNo",writeOffCode.getOrderNo()).andEqualTo("afterStatus",1);
        List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example1);
        if (byOrderAfters.size() == 0){
            map.put("refuse",0);
        }else {
            map.put("refuse",1);
        }
        map.put("storeId",byStoreStaffs.get(0).getStoreId().toString());
        map.put("goodsImg",goodsImg);
        map.put("goodsName",goodsName);
        map.put("userName",custUser.getNickName());
        map.put("mobile",custUser.getMobile());
        map.put("total",writeOffCode.getTotalNum().toString());
        map.put("surplus",writeOffCode.getSurplusNum().toString());
        map.put("endDate", DateUtils.formatDate(writeOffCode.getEndDate(),"yyyy-MM-dd"));
        if (byOrderAfters == null || writeOffCode.getSurplusNum() == 0){
            map.put("isUser","0");
        }else {
            map.put("isUser","1");
        }
        return map;
    }
    private static final String LIMIT_CHECK_KEY = "check-limit:%s";
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ByOrdersMapper byOrdersMapper;

    @Autowired
    private YchOrderMapper ychOrderMapper;

    /**
     * 核销门票
     * @param id
     * @param countProduct
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public WriteOffCodeLog writeGoods(Integer id,Integer countProduct) {

            long start = System.currentTimeMillis();
            String limitCheckKey = String.format(LIMIT_CHECK_KEY, id);
            boolean limitCheckResult = stringRedisTemplate.opsForValue().setIfAbsent(limitCheckKey, id.toString(), 3, TimeUnit.SECONDS);
            if (!limitCheckResult) {
                throw new CustomException("您的操作过于频繁");
            }
        try {
            ByCustUser user = SecurityContext.getUser();
            int i = 0;

            //判断 前端 数量是否正常 countProduct
            if (countProduct.intValue() <= 0) {
                throw new CustomException("请重新核对核销数量,不能小于1次");
            }
            //根据核销码idcheck
            Map<String, Object> check = check(id, user);
            WriteOffCode queryCOde = this.writeOffCodeMapper.selectByPrimaryKey(id);
            //判断剩余数量 是否 符合 核销数量  剩余数量 是否小于 核销数量
            if (queryCOde.getSurplusNum().intValue() < countProduct.intValue()) {
                throw new CustomException("当前核销数量不足");
            }
            if ("0".equals(check.get("surplus"))) {
                throw new CustomException("不能核销");
            }

            //根据订单号查询售后单
            Example example = new Example(ByOrderAfter.class);
            example.createCriteria().andEqualTo("orderNo", queryCOde.getOrderNo()).andEqualTo("afterStatus", 0);
            List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example);
            if (!byOrderAfters.isEmpty()) {
                throw new CustomException("该订单申请售后中，请取消申请售后再进行核销；");
            }

            WriteOffCode writeOffCode = this.writeOffCodeMapper.selectByPrimaryKey(id);
            writeOffCode.setSurplusNum(writeOffCode.getSurplusNum() - countProduct);
            if (writeOffCode.getSurplusNum() == 0) {
                writeOffCode.setStatus(1);
            }
            writeOffCode.setGmtModified(new Date());
            i = this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);


            if (writeOffCode.getSurplusNum() == 0) {
             /*   Example example = new Example(WriteOffCode.class);
                example.createCriteria().andEqualTo("orderNo",writeOffCode.getOrderNo()).andNotEqualTo("surplusNum",0).andEqualTo("status",0);*/
                List<WriteOffCode> writeOffCodes = writeOffCodeMapper.queryNotWriteCodeList(writeOffCode.getOrderNo());
                // List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
                if (writeOffCode.getOrderType() == 0) {
                    Example example1 = new Example(ByOrders.class);
                    example1.createCriteria().andEqualTo("orderNo", writeOffCode.getOrderNo());
                    List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example1);
                    ByOrders byOrders1 = byOrders.get(0);
                    if (writeOffCodes.size() == 0) {
                        byOrders1.setGmtUpdate(new Date());
                        byOrders1.setOrderStatus(3);
                        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                        if (byOrders1.getActualAmount().compareTo(new BigDecimal(0)) != 0) {
                            Example example2 = new Example(ByOrderGoods.class);
                            example2.createCriteria().andEqualTo("orderId", byOrders1.getId());
                            List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example2);

                            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
                            if (byOrderGoods.size() != 0 && byPlatformSet != null && byPlatformSet.getIntegralReturn() != null && byPlatformSet.getIntegralReturn().compareTo(new BigDecimal(0)) != 0 && byOrders1.getActualAmount().compareTo(new BigDecimal(0)) != 0) {
                                BigDecimal bigDecimal = new BigDecimal(0);
                                for (ByOrderGoods byOrderGood : byOrderGoods) {
                                    BigDecimal multiply = byOrderGood.getGoodsPrice().multiply(new BigDecimal(byOrderGood.getGoodsNum()));
                                    BigDecimal subtract = multiply.subtract(byOrderGood.getIntegralPrice().add(byOrderGood.getCouponPrice()));
                                    bigDecimal = bigDecimal.add(subtract);
                                }
                                if (bigDecimal.compareTo(new BigDecimal(0)) > 0) {
                                    ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(byOrders1.getUserId());
                                    ByIntegralLog byIntegralLog = new ByIntegralLog();
                                    byIntegralLog.setUserId(byCustUser.getId());
                                    byIntegralLog.setChangeType(1);
                                    //Integer count = byPlatformSet.getIntegralReturn().multiply(byOrders1.getActualAmount()).intValue();
                                    //配置（返增比例/100） * 商品实付金额
                                    Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(bigDecimal).intValue();
                                    byIntegralLog.setChangeNum(coun);
                                    byIntegralLog.setChangeReason("商品购买赠送");
                                    byIntegralLog.setIntegralType(2);
                                    byIntegralLog.setChangeType(1);
                                    byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                                    byIntegralLog.setGmtCreate(new Date());
                                    this.byCustUserMapper.updateByPrimaryKeySelective(byCustUser);
                                    if (byIntegralLog.getChangeNum() > 0) {
                                        byIntegralLogMapper.insertSelective(byIntegralLog);
                                        int i1 = this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(), byIntegralLog.getChangeNum().intValue());
                                        if (i1 < 1) {
                                            throw new CustomException("核销失败");
                                        }
                                    }
                                }

                            }
//
                        }
                    }
                } else {
                    Example example1 = new Example(ByTeamOrder.class);
                    example1.createCriteria().andEqualTo("orderNo", writeOffCode.getOrderNo());
                    List<ByTeamOrder> byOrders = this.byTeamOrderMapper.selectByExample(example1);
                    ByTeamOrder byOrders1 = byOrders.get(0);
                    if (writeOffCodes.size() == 0) {
                        byOrders1.setGmtUpdate(new Date());
                        byOrders1.setOrderStatus(3);
                        this.byTeamOrderMapper.updateByPrimaryKeySelective(byOrders1);
                        if (byOrders1.getIntegralAmount() != null && byOrders1.getIntegralAmount().compareTo(new BigDecimal(0)) > 0) {
                            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
                            if (byOrders1.getIntegralAmount() != null && byPlatformSet.getCommentIntegral() != null && byPlatformSet.getCommentIntegral().equals(0) == false) {
                                Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(byOrders1.getPayAmount()).intValue();
                                ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(byOrders1.getUserId());
                                ByIntegralLog byIntegralLog = new ByIntegralLog();
                                byIntegralLog.setUserId(byOrders1.getUserId());
                                byIntegralLog.setChangeType(1);
                                byIntegralLog.setChangeNum(Integer.parseInt(coun.toString()));
                                byIntegralLog.setChangeReason("拼团");
                                byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                                byIntegralLog.setGmtCreate(new Date());
                                byIntegralLog.setIntegralType(1);
                                if (byIntegralLog.getChangeNum() > 0) {
                                    byIntegralLogMapper.insertSelective(byIntegralLog);
                                }
                                this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(), byIntegralLog.getChangeNum().intValue());
                                if (byIntegralLog.getChangeNum() > 0) {
                                    byIntegralLogMapper.insertSelective(byIntegralLog);
                                }
                            }
                        }
                    }
                }

            }
            if (i != 0) {
                Example example1 = new Example(ByStoreStaff.class);
                ByCustUser byCustUser1 = this.byCustUserMapper.selectByPrimaryKey(user.getId());
                example1.createCriteria().andEqualTo("staffPhone", byCustUser1.getMobile()).andEqualTo("isDel", Boolean.FALSE);
                List<ByStoreStaff> byStoreStaffs = this.byStoreStaffMapper.selectByExample(example1);
                if (byStoreStaffs.size() <= 0) {
                    throw new CustomException("无权核销");
                }
                WriteOffCodeLog writeOffCodeLog = new WriteOffCodeLog();
                writeOffCodeLog.setWriteOffId(id);
                writeOffCodeLog.setStoreId(byStoreStaffs.get(0).getStoreId());
                writeOffCodeLog.setCustUserId(writeOffCode.getCustUserId());
                writeOffCodeLog.setGmtCreate(new Date());
                writeOffCodeLog.setWriteOffDate(new Date());
                writeOffCodeLog.setStaffId(Integer.valueOf(byStoreStaffs.get(BaoYanConstant.CONSTANT_ZERO).getId().toString()));
                writeOffCodeLog.setWriteOffNum(countProduct);
                writeOffCodeLogMapper.insertSelective(writeOffCodeLog);
                BaseStore baseStore = baseStoreMapper.selectByPrimaryKey(byStoreStaffs.get(0).getStoreId());
                writeOffCodeLog.setStoreName(baseStore.getName());
                writeOffCodeLog.setGoodsName(check.get("goodsName").toString());
                writeOffCodeLog.setUserName(byStoreStaffs.get(0).getStaffName());
                writeOffCodeLog.setExpiryDate(new Date());
                writeOffCodeLog.setSurplusNum(countProduct);


                //记录日志
                orderLogService.addOrderLogAsync(
                        ByOrderLog.builder()
                                .orderNo(queryCOde.getOrderNo())
                                .logType(OrderLogType.WRITE_OFF)
                                .build()
                );

                //检查当前订单详情的核销码 如果全部退款或者全部核销，订单状态就是 已完成
                List<WriteOffCode> write1 = writeOffCodeMapper.select(WriteOffCode.builder().detailId(queryCOde.getDetailId()).build());

                boolean allMatch1 = write1.stream().allMatch(code -> code.getStatus() == 1 || code.getOrderState() == 1);
                if (allMatch1) {
                    byOrderGoodsMapper.updateByPrimaryKeySelective(
                            ByOrderGoods.builder()
                                    .id(queryCOde.getDetailId())
                                    .orderStatus(3)
                                    .build()
                    );
                }

                //检查当前订单的核销码 如果全部退款或者全部核销，订单状态就是 已完成
                List<WriteOffCode> write2 = writeOffCodeMapper.select(WriteOffCode.builder().orderNo(queryCOde.getOrderNo()).build());
                boolean allMatch2 = write2.stream().allMatch(code -> code.getStatus() == 1 || code.getOrderState() == 1);
                if (allMatch2) {
                    byOrdersMapper.updateStatusByOrderNo(
                            ByOrders.builder()
                                    .orderNo(queryCOde.getOrderNo())
                                    .orderStatus(3)
                                    .build()
                    );
                }
                log.info("开始处理核销后首次赠金");
                verifyService.handleFirstVerifyBalance(writeOffCode.getCustUserId(), id, writeOffCode.getSourceGoodsId(), writeOffCode.getType(), queryCOde.getOrderNo());
                log.info("处理核销后首次赠金完成");
                // YchOrder ychOrder = ychOrderMapper.findByPayOrderNo(writeOffCode.getOrderNo());
                // if (ObjectUtil.isNotEmpty(ychOrder)) {
                //     log.info("更新赠金支付状态：{}", writeOffCode.getOrderNo());
                //     Example queryOrder = new Example(ByOrders.class);
                //     queryOrder.createCriteria().andEqualTo("orderNo", writeOffCode.getOrderNo());
                //     ByOrders byOrders = byOrdersMapper.selectOneByExample(queryOrder);
                //     ychApiService.updatePaid(writeOffCode.getOrderNo(), byOrders.getPayFlowNo());
                // }
                try {
                    socketServer.sendInfo(String.valueOf(writeOffCode.getCustUserId()), String.valueOf(id));
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                return writeOffCodeLog;
            }
            long end = System.currentTimeMillis();
            log.info("time_consuming:" + (end - start) + "ms");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new CustomException(e.getMessage());
        } finally {
            stringRedisTemplate.delete(limitCheckKey);
        }
        throw new CustomException("核销失败");
    }

}
