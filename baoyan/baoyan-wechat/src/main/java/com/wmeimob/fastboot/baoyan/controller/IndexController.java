package com.wmeimob.fastboot.baoyan.controller;

import cn.hutool.core.math.MathUtil;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.constant.OrderGoodsConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.ByCouponUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.ByCustUserService;
import com.wmeimob.fastboot.baoyan.service.ByTeamGoodsService;
import com.wmeimob.fastboot.baoyan.service.PayService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.utils.DecimalUtil;
import com.wmeimob.fastboot.baoyan.vo.Goods;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IndexController
 * @projectName baoyan
 * @description: index
 * @date 2019/8/1 15:51
 */
@RestController
@RequestMapping("collect")
@Slf4j
public class IndexController {


    @Autowired
    private ByTeamGoodsService byTeamGoodsService;
    @Autowired
    private PayService payService;
    @Resource
    private WxCustUserService userService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ByCouponUserMapper couponUserMapper;

    private static final String LIMIT_CHECK_KEY = "check-limit:%s";

    /**
    * @Description 拼团列表
    * <AUTHOR>
    * @Date        2019-08-20 16:16
    * @Version    1.0
    */
    @GetMapping("ticket")
    public PageInfo ticketList(Integer pageIndex,Integer pageSize){
        return this.byTeamGoodsService.ticketList(pageIndex,pageSize);
    }

    /**
    * @Description 拼团详情
    * <AUTHOR>
    * @Date        2019-08-20 17:13
    * @Version    1.0
    */
    @GetMapping("ticketDetail")
    public ByTeamGoods ticketDetail(Integer id){
        return this.byTeamGoodsService.ticket(id);
    }
    @GetMapping("team")
    public List<ByTeam> getTeam(Integer id){
        return this.byTeamGoodsService.getTeam(id);
    }

    /**
    * @Description 拼团详情
    * <AUTHOR>
    * @Date        2019-08-21 14:14
    * @Version    1.0
    */
    @GetMapping("teamDetail")
    public Map<String,Object> teamDetail(Integer id){
        return this.byTeamGoodsService.teamDetail(id);
    }

    /**
    * @Description 订单详情
    * <AUTHOR>
    * @Date        2019-08-21 14:59
    * @Version    1.0
    */
    @GetMapping("teamGoodsDetail")
    public Map<String,Object> teamGoodsDetail(Integer id,Integer type){
        return this.byTeamGoodsService.teamGoodsDetail(id,type);
    }



    /**
     * 订单计算优惠
     * @param allOrder 订单金额必须传递
     * @return
     */
    @GetMapping("/calculationAll")
    public Map<String,Object> calculationAll(PayAllOrder allOrder){


        //订单金额不能为空
        if (allOrder==null || allOrder.getOrderAmount()==null){
            throw new CustomException("参数不合法");
        }

        //抵扣的积分
        BigDecimal scoreAmount = allOrder.getScoreAmount();

        //优惠券和积分不能一起用
        if ( allOrder.getCouponId()!=null && DecimalUtil.notZeroNull(scoreAmount) ){
            throw new CustomException("积分和优惠券不可以一起使用");
        }

        BigDecimal orderAmount = allOrder.getOrderAmount();

        BigDecimal discount = BigDecimal.valueOf(0);
        //优惠券优惠金额不能 大于订单金额
        if (allOrder.getCouponId()!=null){
            ByCouponUser byCoupon = couponUserMapper.selectByPrimaryKey(allOrder.getCouponId());
            discount  = byCoupon.getDiscount();
            if ( discount.compareTo(allOrder.getOrderAmount())>0 ){
                throw new CustomException("优惠券金额不能大于订单金额");
            }else{
                orderAmount = orderAmount.subtract( discount );
            }
        }

        HashMap<String, Object> returnMap = new HashMap<>(3);

        //优惠券抵扣金额
        returnMap.put("discountAmount",discount);

        //验证积分
        if ( DecimalUtil.notZeroNull(scoreAmount) ){
            //type 是否可使用积分
            returnMap.put("type",Boolean.TRUE);
            //积分抵扣金额不能大于订单金额
            if (scoreAmount.compareTo(orderAmount)>0){
                throw new CustomException("积分金额不能大于订单金额");
            }else{
                orderAmount =  orderAmount.subtract(scoreAmount);
            }
        }else{
            returnMap.put("type",Boolean.FALSE);
        }


        returnMap.put("orderAmount",orderAmount);

        return returnMap;
    }

    /**
    * @Description 拼团计算优惠券
    * <AUTHOR>
    * @Date        2019-08-29 9:33
    * @Version    1.0
    */
    @GetMapping("calculation")
    public Map<String,Object> calculation(BigDecimal orderAmount,Integer couponId,BigDecimal scoreAmount,Integer goodsCount){
        log.info("拼团计算优惠券 calculation() orderAmount: {},couponId:{},scoreAmount:{},goodsCount:{}",orderAmount,couponId, scoreAmount, goodsCount);
        if (orderAmount.compareTo(new BigDecimal(0)) == 0){
            throw new CustomException("不可使用优惠卷");
        }
        Goods goods = new Goods();
        goods.setOrderAmount(orderAmount);
        goods.setType(1);
        goods.setCouponId(couponId);
        goods.setScoreAmount(scoreAmount);
//        goods.setGoodsCount(goodsCount);
        //计算抵扣积分和优惠价后的价格
        BigDecimal calculation = this.payService.calculation(goods);
        //至少0.01
        log.info(calculation.toString());
        log.info((calculation.intValue()==0)+"");
        if ( calculation.doubleValue()==0 ){
            calculation = BigDecimal.valueOf( OrderGoodsConstant.LOW_PRICE_GOODS  );
        }
        Map<String,Object> map = new HashMap<>(2);
        if (goods.getScoreAmount() != null && goods.getScoreAmount().compareTo(new BigDecimal(0)) != 0){
            map.put("type",Boolean.TRUE);
        }else {
            map.put("type",Boolean.FALSE);
        }
        if (couponId != null){
            goods.setScoreAmount(new BigDecimal(0));
            BigDecimal calculation1 = this.payService.calculation(goods);
            if (calculation.compareTo(new BigDecimal(0)) == -1){
                calculation = new BigDecimal(0);
            }
            map.put("orderAmount",calculation);
            map.put("discountAmount",orderAmount.subtract(calculation1));
        }else {
            if (calculation.compareTo(new BigDecimal(0)) == -1){
                calculation = new BigDecimal(0);
            }
            map.put("orderAmount",calculation);
            map.put("discountAmount",0);
        }
        return map;
    }


    /**
     * 拼团支付
     * */
    @GetMapping("pay")
    public Map<String,Object> pay(Integer id, Integer type, String remark, BigDecimal payAmount,String fromId,Integer couponId,
                                  BigDecimal couponAmount,BigDecimal scoreAmount,BigDecimal orderAmount){

        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ByCustUser user = (ByCustUser)principal;
        ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
        if (byCustUser.getIsDisable()) throw new CustomException("你已被禁用，请联系管理员");
        Map<String, Object> pay = this.byTeamGoodsService.pay(id, type, remark, payAmount, fromId, couponId, couponAmount, scoreAmount, orderAmount);
        return pay;
    }

    /**
    * @Description 我的拼团
    * <AUTHOR>
    * @Date        2019-08-26 15:34
    * @Version    1.0
    */
    @GetMapping("presonTeam")
    public PageInfo personTeam(Integer pageIndex,Integer pageSize){
        return this.byTeamGoodsService.presonTeam(pageIndex,pageSize);
    }

    /**
    * @Description 详情
    * <AUTHOR>
    * @Date        2019-08-26 15:54
    * @Version    1.0
    */
    @GetMapping("detail")
    public Map<String,Object> detail(Integer id){
        return this.byTeamGoodsService.teamDetails(id);
    }

}
