package com.wmeimob.fastboot.baoyan.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatPayProperties;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.UUIDOrder;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.vo.Goods;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.starter.wechat.dto.WechatJsPayDTO;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import me.hao0.wechat.core.Wechat;
import me.hao0.wepay.model.pay.JsPayResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-20 16:40
 * @Version 1.0
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ByTeamGoodsServiceImpl implements ByTeamGoodsService {

    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Autowired
    private ByTeamOrderMapper byTeamOrderMapper;
    @Autowired
    private ByRichTextMapper byRichTextMapper;
    @Autowired
    private ByEvaluateMapper byEvaluateMapper;
    @Autowired
    private ByTeamMapper byTeamMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private WechatService wechatService;
    @Resource
    private WepayService wepayService;
    @Autowired
    private PayService payService;
    @Autowired
    private ByPlatformSetMapper byPlatformSetMapper;
    @Autowired
    private ByGoodsStoreMapper byGoodsStoreMapper;
    @Autowired
    private WriteOffCodeMapper writeOffCodeMapper;
    @Autowired
    private ByOrderAfterMapper byOrderAfterMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WechatPayProperties wechatPayProperties;

    @Override
    public PageInfo ticketList(Integer pageIndex, Integer pageSize) {
        PageHelper.startPage(pageIndex, pageSize);
        List<ByTeamGoods> byTeamGoods = this.byTeamGoodsMapper.selectList();
        return new PageInfo<>(byTeamGoods);
    }

    @Override
    public ByTeamGoods ticket(Integer id) {
        ByTeamGoods byTeamGoods = byTeamGoodsMapper.selectByPrimaryKey(id);
        if (byTeamGoods == null || !byTeamGoods.getTeamStatus() || byTeamGoods.getIsDel()) {
            throw new CustomException("拼团活动已结束");
        }
        Example example = new Example(ByRichText.class);
        example.createCriteria().andEqualTo("dataId", id).andEqualTo("dataType", 4);
        List<ByRichText> byRichTexts = this.byRichTextMapper.selectByExample(example);
        byTeamGoods.setRichContent((String) byRichTexts.get(0).getContent());
        List<ByTeam> list = getList(id);
        byTeamGoods.setByTeamOrders(list);
        byTeamGoods.setByEvaluates(getEvaluate(id));
        byTeamGoods.setTickCount(byTeamMapper.selectList(id).size());
        byTeamGoods.setByEvaluatesCount(byEvaluateMapper.selectList(id).size());
        return byTeamGoods;
    }

    @Override
    public List<ByTeam> getTeam(Integer id) {
        List<ByTeam> team = byTeamMapper.selectList(id);
        return team;
    }

    /**
     * @Description 拼团详情
     * <AUTHOR>
     * @Date 2019-08-21 14:14
     * @Version 1.0
     */
    @Override
    public Map<String, Object> teamDetail(Integer id) {
        ByTeam byTeam = this.byTeamMapper.selectByPrimaryKey(id);
        if (byTeam == null || byTeam.getOrderStatus() < 0) {
            throw new CustomException("拼团已结束");
        }
        ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeam.getTeamGoodsId());
        if (byTeamGoods == null || byTeamGoods.getIsDel()) {
            throw new CustomException("拼团已结束");
        }
        ByCustUser user = SecurityContext.getUser();
        Example example = new Example(ByTeamOrder.class);
        example.createCriteria().andEqualTo("teamId", id).andEqualTo("userId", user.getId()).andNotEqualTo("orderStatus", -1);
        List<ByTeamOrder> byTeamOrders1 = this.byTeamOrderMapper.selectByExample(example);
        ByTeamOrder byTeamOrder = new ByTeamOrder();
        if (byTeamOrders1.size() != 0) {
            byTeamOrder = byTeamOrders1.get(0);
        }
        Map<String, Object> map = new HashMap<>();
        ByOrders byOrders = new ByOrders();
        byOrders.setRemark(byTeamOrder.getOrderRemark());
        byOrders.setPayTime(byTeamOrder.getPayTime());
        byOrders.setActualAmount(byTeamOrder.getPayAmount());
        byOrders.setGoodsId(byTeamOrder.getTeamGoodsId());
        byOrders.setOrderTime(byTeamOrder.getGmtCreate());
        byOrders.setPayFlowNo(byTeamOrder.getPayFlowNo());
        byOrders.setOrderStatus(byTeamOrder.getOrderStatus());
        byOrders.setOrderNo(byTeamOrder.getOrderNo());
        byOrders.setOrderAmount(byTeamOrder.getOrderAmount());
        byOrders.setCouponAmount(byTeamOrder.getCouponAmount());
        byOrders.setIntegralAmount(byTeamOrder.getIntegralAmount());
        byOrders.setGmtCreate(byTeamGoods.getGmtCreate());
        byOrders.setGoodsId(byTeam.getTeamGoodsId());
        byOrders.setOrderNo(byTeamOrder.getOrderNo());
        byOrders.setTeamType(1);
        Example example12 = new Example(WriteOffCode.class);
        example12.createCriteria()
                .andEqualTo("detailId", byTeamOrder.getId())
                .andEqualTo("orderType", 1)
                .andNotEqualTo("status", 0)
                .andEqualTo("custUserId", user.getId());
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example12);

        ByOrderGoods byOrderGoods = new ByOrderGoods();
        Example example11 = new Example(ByOrderAfter.class);
        example11.createCriteria()
                .andEqualTo("detailId", byTeamOrder.getId())
                .andEqualTo("resouceType", 2)
                .andEqualTo("afterStatus", 1);
        List<ByOrderAfter> byOrderAfters1 = this.byOrderAfterMapper.selectByExample(example11);
        if (byOrderAfters1.size() != 0) {
            byTeamOrder.setOrderStatus(4);
            byOrders.setGmtUpdate(byOrderAfters1.get(0).getGmtUpdate());
        }
        byOrders.setOrderStatus(byTeamOrder.getOrderStatus());
        byOrderGoods.setId(byTeamOrder.getId());
        byOrderGoods.setGoodsImg(byTeamGoods.getGoodsImg());
        byOrderGoods.setGoodsName(byTeamGoods.getTeamName());
        byOrderGoods.setTeamType(1);
        byOrderGoods.setGoodsNum(1);
        byOrderGoods.setOrderNo(byTeamOrder.getOrderNo());
        byOrderGoods.setOrderId(byTeamOrder.getId());
        byOrderGoods.setGoodsId(byTeam.getTeamGoodsId());
        Example example13 = new Example(ByOrderAfter.class);
        example13.createCriteria()
                .andEqualTo("detailId", byTeamOrder.getId())
                .andEqualTo("resouceType", 2)
                .andEqualTo("afterStatus", 1);
        example13.orderBy("id").desc();
        List<ByOrderAfter> byOrderAfters2 = this.byOrderAfterMapper.selectByExample(example13);
        byOrderGoods.setRefundType(0);
        if (byOrderAfters2.size() > 0) {
            byOrders.setGmtUpdate(byOrderAfters2.get(0).getGmtUpdate());
            byOrderGoods.setRefundType(2);
            byOrderGoods.setRefundNum(1);
        }
        if (writeOffCodes.size() != 0) {
            byOrderGoods.setOrderStatus(4);
        }
        byOrderGoods.setGoodsPrice(byTeamGoods.getTeamPrice());
        List<ByOrderGoods> list = new ArrayList();
        list.add(byOrderGoods);

        if (byOrders.getOrderStatus().equals(3)) {
            byOrders.setUseTime(byTeamOrder.getGmtUpdate());
        }
        map.put("orderInfo", byOrders);
        map.put("goodsList", list);
        List<Map<String, String>> byTeamOrders = this.byTeamOrderMapper.selectTeamList(id);
//        map.put("user",byTeamOrders);
//        map.put("userCount",byTeamGoods.getTeamNum()-byTeamOrders.size());
//       ? map.put("id",id);
        return map;
    }

    /**
     * @Description 订单详情
     * <AUTHOR>
     * @Date 2019-08-21 14:59
     * @Version 1.0
     */
    @Override
    public Map<String, Object> teamGoodsDetail(Integer id, Integer type) {
        if (type == 2) {
            ByTeam byTeam = this.byTeamMapper.selectByPrimaryKey(id);
            if (byTeam.getOrderStatus() == 1) {
                throw new CustomException("拼团已结束");
            }
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeam.getTeamGoodsId());
            ByTeamGoods byTeamGoods1 = byTeamGoodsMapper.selectByPrimaryKey(byTeam.getTeamGoodsId());
            byTeamGoods.setTeamName(byTeamGoods1.getTeamName());
            Goods goods = new Goods();
            goods.setId(byTeamGoods1.getGoodsId().toString());
            goods.setGoodsCount(1);
            goods.setGoodsType(1);
            goods.setType(1);
            goods.setTeamType(1);
            goods.setTeamId(byTeam.getTeamGoodsId());
            Map<String, Object> pay = payService.pay(goods);
            pay.put("byTeamGoods", byTeamGoods);
            ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
            if (byPlatformSet.getTeamIntegralIsEnable() == 0) {
                pay.put("type", false);
            }
            if (byPlatformSet.getTeamCouponIsEnable() == 0) {
                pay.put("coupon", new ArrayList<>());
            }
            if (byTeamGoods.getTeamPrice().compareTo((BigDecimal) pay.get("goodsAmount")) == -1) {
                pay.put("count", byTeamGoods.getTeamPrice());
            }
            //处理优惠卷折扣显示
            dealCoupon(pay);
            return pay;
        } else {
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(id);
            if(ObjectUtil.isNotEmpty(byTeamGoods)){
                byTeamGoods.setTeamName(byTeamGoods.getTeamName());
                Goods goods = new Goods();
                goods.setId(byTeamGoods.getGoodsId().toString());
                goods.setGoodsCount(1);
                goods.setGoodsType(1);
                goods.setType(1);
                goods.setTeamType(1);
                goods.setTeamId(id);
                Map<String, Object> pay = payService.pay(goods);
                pay.put("byTeamGoods", byTeamGoods);
                ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
                if (byTeamGoods.getTeamPrice().compareTo((BigDecimal) pay.get("goodsAmount")) == -1) {
                    pay.put("count", byTeamGoods.getTeamPrice());
                }
                if (byPlatformSet.getTeamIntegralIsEnable() == 0) {
                    pay.put("type", false);
                }
                if (byPlatformSet.getTeamCouponIsEnable() == 0) {
                    pay.put("coupon", new ArrayList<>());
                }
                //处理优惠卷折扣显示
                dealCoupon(pay);
                return pay;
            }

            return new HashMap<>();

        }

    }

    private void dealCoupon(Map<String, Object> pay) {
        if (null != pay.get("coupon")) {
            List<ByCouponUser> listUser = (List<ByCouponUser>) pay.get("coupon");
            for (ByCouponUser user : listUser) {
                //处理折扣字段  db中是 97 % 折换成 9.7折
                //折扣卷类型
                if (user.getCouponType().equals(2)) {
                    //前端展示 例 97/10 9.7
                    user.setDiscount(user.getDiscount().divide(new BigDecimal(10)));
                }
            }
            pay.put("coupon", listUser);
        }
    }

    @Autowired
    private ByCouponUserMapper byCouponUserMapper;

    @Override
    public Map<String, Object> pay(Integer id, Integer type, String remark, BigDecimal payAmount, String fromId, Integer couponId, BigDecimal couponAmount, BigDecimal scoreAmount, BigDecimal orderAmount) {
        String wechatPayName = "";
        String orderNo = "";
        if (id == null || type == null || payAmount == null || orderAmount == null) {
            throw new CustomException("参数有误");
        }
        ByCustUser user = SecurityContext.getUser();
        ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
        if (byPlatformSet != null && byPlatformSet.getTeamIntegralIsEnable() != 0) {
            if (scoreAmount != null && scoreAmount.compareTo(new BigDecimal(0)) != 0) {
                BigDecimal divide = scoreAmount.divide(new BigDecimal(byPlatformSet.getCommentIntegral()), 2, BigDecimal.ROUND_HALF_EVEN);
                ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
                if (new BigDecimal(byCustUser.getNowPoint() == null ? 0 : byCustUser.getNowPoint()).compareTo(divide) == -1) {
                    throw new CustomException("积分不足");
                }
            }
        } else {
            if (scoreAmount != null && scoreAmount.compareTo(new BigDecimal(0)) != 0) {
                throw new CustomException("该商品无法使用积分");
            }
        }
        if (byPlatformSet != null && byPlatformSet.getTeamCouponIsEnable() != 0 && couponId != null && couponId != 0) {
            ByCouponUser byCouponUsers = this.byCouponUserMapper.selectByPrimaryKey(couponId);
            Calendar instance = Calendar.getInstance();
            instance.setTime(new Date());
            if (byCouponUsers.getIsUse() != 0 && byCouponUsers.getAuditStatus() != 1 && instance.getTime().getTime() > byCouponUsers.getEndDate().getTime()) {
                throw new CustomException("优惠券已过期");
            }
            if (byCouponUsers.getCouponType() == 1) {
                if (byCouponUsers.getFull() == null && byCouponUsers.getFull().compareTo(new BigDecimal(0)) == 0) {
                    if (couponAmount.compareTo(byCouponUsers.getDiscount()) != 0 && payAmount.compareTo(new BigDecimal(0)) != 0) {
                        throw new CustomException("优惠卷金额有误");
                    }
                } else {
                    if (orderAmount.compareTo(byCouponUsers.getFull()) == -1) {
                        throw new CustomException("此优惠卷不能使用，不满足优惠条件");
                    }
                }
            }
            if (byCouponUsers.getCouponType() == 2) {
                if (byCouponUsers.getFull() == null && byCouponUsers.getFull().compareTo(new BigDecimal(0)) == 0) {
                    if (payAmount.compareTo(orderAmount.multiply(byCouponUsers.getDiscount()).divide(new BigDecimal(100))) != 0) {
                        throw new CustomException("优惠卷金额有误");
                    }
                } else {
                    if (orderAmount.compareTo(byCouponUsers.getFull()) == -1) {
                        throw new CustomException("此优惠卷不能使用，不满足优惠条件");
                    }
                }
            }
        }
        Integer goodsId = 0;
        ByTeamOrder byTeamOrder = new ByTeamOrder();
        if (type != 3) {
            if (StringUtil.isEmpty(fromId)) {
                throw new CustomException("参数有误");
            }
            /*开团*/
            Boolean team = false;
            int i = 0;
            if (type == 1) {
                goodsId = id;
                ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(id);
                if (byTeamGoods.getStockNum() <= 0) {
                    throw new CustomException("库存不足");
                }
                Date gmtCreate = byTeamGoods.getGmtCreate();
                Calendar c = Calendar.getInstance();
                c.setTime(gmtCreate);
                c.add(Calendar.HOUR_OF_DAY, byTeamGoods.getTeamHour());
                Calendar c1 = Calendar.getInstance();
                c1.setTime(new Date());
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byTeamGoods.getGoodsId());
                if (byGoodsInfo == null) {
                    throw new CustomException("拼团失败");
                }
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date());
                instance.add(Calendar.HOUR, byTeamGoods.getTeamHour());
                ByTeam byTeam = new ByTeam();
                byTeam.setEndDate(instance.getTime());
                byTeam.setTeamGoodsId(id);
                byTeam.setUserId(user.getId());
                byTeam.setGoodsName(byGoodsInfo.getGoodsName());
                byTeam.setGoodsNo(byGoodsInfo.getGoodsNo());
                byTeam.setGoodsImg(byTeamGoods.getGoodsImg());
                byTeam.setTeamPrice(byTeamGoods.getTeamPrice());
                byTeam.setTeamNum(byTeamGoods.getTeamPerson());
                byTeam.setOrderStatus(-1);
                byTeam.setGmtCreate(new Date());
                byTeam.setIsDel(Boolean.FALSE);
                byTeam.setTeamName(byTeamGoods.getTeamName());
                byTeamMapper.insertSelective(byTeam);
                i = byTeam.getId();
                team = true;
            } else {
                team = false;
                i = id;
                ByTeam byTeam = this.byTeamMapper.selectByPrimaryKey(id);
                if (byTeam == null || byTeam.getOrderStatus() != 0) {
                    throw new CustomException("拼团已结束");
                }
                goodsId = byTeam.getTeamGoodsId();
                ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(goodsId);
                if (byTeamGoods.getStockNum() <= 0) {
                    throw new CustomException("库存不足");
                }
            }
            ByTeamGoods byTeamGoods1 = this.byTeamGoodsMapper.selectByPrimaryKey(goodsId);
            if (byTeamGoods1.getTeamNum() != null && byTeamGoods1.getTeamNum() != 0) {
                Example example1 = new Example(ByTeamOrder.class);
                example1.createCriteria().andEqualTo("teamGoodsId", goodsId).andEqualTo("userId", user.getId())
                        .andNotEqualTo("orderStatus", -1)
                        .andNotEqualTo("orderStatus", 1)
                        .andNotEqualTo("orderStatus", 4);
                List<ByTeamOrder> byTeamGoods = this.byTeamOrderMapper.selectByExample(example1);
                if (byTeamGoods.size() > byTeamGoods1.getTeamNum()) {
                    throw new CustomException("你已达到最高拼团次数");
                }
            }

            byTeamOrder.setFromId(fromId);
            byTeamOrder.setTeamId(i);
            byTeamOrder.setTeamGoodsId(goodsId);
            byTeamOrder.setUserId(user.getId());
            byTeamOrder.setOrderNo(UUIDOrder.getUUID());
            byTeamOrder.setPayType(1);
            byTeamOrder.setOrderAmount(orderAmount);
            byTeamOrder.setNum(1);
            byTeamOrder.setOrderRemark(remark);
            byTeamOrder.setOrderStatus(-1);
            byTeamOrder.setIsTeam(team);
            byTeamOrder.setIsAfter(Boolean.FALSE);
            byTeamOrder.setGmtCreate(new Date());
            byTeamOrder.setIsDel(Boolean.FALSE);
            byTeamOrder.setOrderTime(new Date());
            byTeamOrder.setFromId(fromId);
            byTeamOrder.setCouponAmount(couponAmount);
            byTeamOrder.setIntegralAmount(scoreAmount);
            byTeamOrder.setCouponId(couponId);
            byTeamOrder.setPayAmount(payAmount);
            orderNo = byTeamOrder.getOrderNo();
            byTeamOrderMapper.insertSelective(byTeamOrder);
            /*生成二维码*/
            Example example = new Example(ByGoodsStore.class);
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
            ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byTeamGoods.getGoodsId());
            example.createCriteria().andEqualTo("goodsId", byTeamGoods.getGoodsId());
            List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
            String str = "";
            if (byGoodsStores.size() != 0) {
                for (ByGoodsStore byGoodsStore : byGoodsStores) {
                    str += byGoodsStore.getStoreId() + ",";
                }
            }
            WriteOffCode writeOffCode = new WriteOffCode();
            if (str != "") {
                str = str.substring(0, str.length() - 1);
                writeOffCode.setStoreIds(str);
            }
            wechatPayName = byTeamGoods.getTeamName();
            writeOffCode.setDetailId(byTeamOrder.getId());
            writeOffCode.setWriteOffName(byTeamGoods.getTeamName());
            writeOffCode.setCustUserId(user.getId());
            writeOffCode.setGoodsName(byGoodsInfo.getGoodsName());
            writeOffCode.setSourceGoodsId(byTeamGoods.getId());
            writeOffCode.setSurplusNum(1);
            writeOffCode.setTotalNum(1);
            writeOffCode.setOrderType(1);
            if(ObjectUtil.isNotEmpty(byGoodsInfo.getHasVerificationDay()) && byGoodsInfo.getHasVerificationDay()){
                Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                writeOffCode.setExpiryDate(date);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                // 这里是加天数
                calendar.add(Calendar.DAY_OF_MONTH, byGoodsInfo.getVerificationDay());
                writeOffCode.setEndDate(calendar.getTime());
            }else{
                writeOffCode.setExpiryDate(byGoodsInfo.getVerificationStart());
                writeOffCode.setEndDate(byGoodsInfo.getVerificationEnd());
            }

            writeOffCode.setOrderNo(byTeamOrder.getOrderNo());
            writeOffCode.setGmtCreate(new Date());
            this.writeOffCodeMapper.insertSelective(writeOffCode);

        } else {
            byTeamOrder = this.byTeamOrderMapper.selectByPrimaryKey(id);
            byTeamOrder.setOrderNo(UUIDOrder.getUUID());
        }
        Map<String, Object> data = new HashMap<>(1);
        if (payAmount.compareTo(new BigDecimal(0)) == 0) {
            data.put("type", 0);
            payService.updateTicketOrder(orderNo, null);
        } else {
            WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
            wechatJsPayDTO.setOrderNo(byTeamOrder.getOrderNo());
            wechatJsPayDTO.setTotalFee(byTeamOrder.getPayAmount());
//            wechatJsPayDTO.setTotalFee(new BigDecimal(0.01));
            wechatJsPayDTO.setNotifyUrl(wechatPayProperties.getPayNotifyUrl());
//            wechatJsPayDTO.setNotifyUrl("https://wx.byloft.net/api/pay/updateTicketOrder");
            Wechat wechat = wechatService.getApiComponent();
            wechatJsPayDTO.setOpenid(user.getWxOpenId());
            wechatJsPayDTO.setAppid(wechat.getAppId());
            wechatJsPayDTO.setIsTest(false);
            wechatJsPayDTO.setBody(wechatPayName);
            JsPayResponse jsPayResponse = wepayService.prePay(wechatJsPayDTO);
            data.put("pay", jsPayResponse);
            data.put("type", 1);
        }
        return data;
    }


    @Override
    public PageInfo presonTeam(Integer pageIndex, Integer pageSize) {
        ByCustUser user = SecurityContext.getUser();
        PageHelper.startPage(pageIndex, pageSize);
        List<ByTeam> byTeams = this.byTeamMapper.presonTeam(user.getId());
        return new PageInfo<>(byTeams);
    }

    @Autowired
    private ByCustUserMapper byCustUserMapper;

    @Override
    public Map<String, Object> teamDetails(Integer id) {
        ByCustUser user = SecurityContext.getUser();
        ByTeam byTeam = this.byTeamMapper.userTeam(id);
        if (byTeam == null) {
            throw new CustomException("订单不存在");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("type", byTeam.getOrderStatus());
        map.put("goodsName", byTeam.getTeamName());
        map.put("goodsImg", byTeam.getGoodsImg());
        map.put("lessNum", byTeam.getLessNum());
        map.put("teamNum", byTeam.getTeamNum());
        map.put("teamPrice", byTeam.getTeamPrice());
        map.put("endDate", DateUtils.formatDate(byTeam.getEndDate(), "yyyy-MM-dd hh:mm:ss"));
        map.put("endDate2", byTeam.getEndDate());
        map.put("goodsId", byTeam.getTeamGoodsId());
        Example example = new Example(ByTeamOrder.class);
        example.createCriteria().andEqualTo("teamId", byTeam.getId()).andNotEqualTo("orderStatus", -1);
        List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
        List<Map<String, Object>> maps = new ArrayList<>();
        Integer type = 0;
        if (byTeamOrders.size() != 0) {
            for (ByTeamOrder e : byTeamOrders) {
                Map<String, Object> map1 = new HashMap<>();
                if (e.getUserId() != 0) {
                    ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(e.getUserId());
                    map1.put("userName", byCustUser.getNickName());
                    map1.put("userImg", byCustUser.getHeadImg());
                } else {
                    map1.put("userName", e.getUserName());
                    map1.put("userImg", e.getUserImg());
                }
                map1.put("isTeam", e.getIsTeam());
                map1.put("payTime", e.getPayTime());
                if (type == 0 && user.getId().equals(e.getUserId())) type = 1;
                maps.add(map1);
            }
            ;
        }
        map.put("participateType", type);
        map.put("team", maps);
        return map;
    }

    private List<ByTeam> getList(Integer id) {
        PageHelper.startPage(1, 2);
        List<ByTeam> team = byTeamMapper.selectList(id);

        return team;
    }

    private List<ByEvaluate> getEvaluate(Integer id) {
        PageHelper.startPage(1, 2);
        List<ByEvaluate> team = byEvaluateMapper.selectList(id);
        if (team.size() != 0) {
        }
        return team;
    }
}
