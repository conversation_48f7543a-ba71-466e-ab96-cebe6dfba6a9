package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.service.TcBrandService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 小程序淘潮玩(TcBrand)
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@RestController
@RequestMapping("tc/tcBrand")
@Slf4j
public class WxTcBrandController {


    @Resource
    private TcBrandService tcBrandService;

    @GetMapping("/")
    public PageInfo<? extends TcBrand> queryPage(TcBrand tcBrand){
        PageContext.startPage();
        log.info("wx === get=> query [入参]============={}",tcBrand);
        return new PageInfo<>(this.tcBrandService.queryPage(tcBrand));
    }

    @GetMapping("/{id}")
    public TcBrand queryByIdTcBrand(@PathVariable Integer id){
        return  tcBrandService.queryById(id);
    }




}