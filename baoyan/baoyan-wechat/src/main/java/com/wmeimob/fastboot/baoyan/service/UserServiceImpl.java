package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 个人中心
 * @date 2019-08-23 17:31
 * @Version 1.0
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {

    @Autowired
    private ByCustUserMapper byCustUserMapper;


    @Override
    public ByCustUser getUser() {
        ByCustUser user = SecurityContext.getUser();
        return this.byCustUserMapper.selectByPrimaryKey(user.getId());
    }

    /**
    * @Description 小红点
    * <AUTHOR>
    * @Date        2019-11-20 9:13
    * @Version    1.0
    */
    @Override
    public void showRed() {
        ByCustUser user = SecurityContext.getUser();
        user.setShowRedCount(0);
        this.byCustUserMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    public void addRed() {
        ByCustUser user = SecurityContext.getUser();
        ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
        byCustUser.setShowRedCount(byCustUser.getShowRedCount()+1);
        this.byCustUserMapper.updateByPrimaryKeySelective(byCustUser);
    }


}
