package com.wmeimob.fastboot.baoyan.controller.tc;

import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.service.TcTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * (TcTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-20 19:03:42
 */
@RestController
@RequestMapping("/tc/template")
@Slf4j
public class WxTcTemplateController {


    @Resource(name = "wxTcTemplateServiceImpl")
    private TcTemplateService tcTemplateService;

    /**
     * 查询 商城首页 板块
     *
     * @return
     */
    @GetMapping("/home")
    public List<? extends TcTemplate> queryHome() {
        TcTemplate tcTemplate = new TcTemplate();
        tcTemplate.setIsHome(Boolean.TRUE);
        return this.tcTemplateService.queryPage(tcTemplate);
    }

    @GetMapping("/tcHome")
    public List<? extends TcTemplate> queryTcHome() {
        TcTemplate tcTemplate = new TcTemplate();
        tcTemplate.setIsHome(Boolean.FALSE);
        return this.tcTemplateService.queryPage(tcTemplate);
    }

    /**
     * 查询淘潮玩 顶部
     */

    @GetMapping("/tcHomeTop")
    public TcTemplate queryTcHomeTop() {
        TcTemplate tcTemplate = new TcTemplate();
        tcTemplate.setJumpType(JumpType.AMOY_PLAY_TOP.getId());
        List<TcTemplate> tcTemplates = this.tcTemplateService.queryPage(tcTemplate);
        return tcTemplates == null || tcTemplates.isEmpty() ? null : tcTemplates.get(0);
    }
}
