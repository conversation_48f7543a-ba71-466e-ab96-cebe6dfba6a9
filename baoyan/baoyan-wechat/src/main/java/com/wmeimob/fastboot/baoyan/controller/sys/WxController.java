package com.wmeimob.fastboot.baoyan.controller.sys;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.mzlion.easyokhttp.HttpClient;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.PayService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.JwtAuthenticationFilter;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wechat.core.Wechat;
import me.hao0.wechat.model.base.AccessToken;
import me.hao0.wechat.model.base.JSCode2SessionResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> :Yao.ck
 * @Description:
 * @date 2019-07-02 11:22
 * @Version 1.0
 */
@RestController
@RequestMapping("wx")
@Slf4j
public class WxController {
    /**
     * 微信APPID
     */
    private final static String APPID = "";
    /**
     * 微信SECRET
     */
    private final static String SECRET = "";
    /**
     * 微信获取accessToken接口
     */
    @Resource
    private WechatService wechatService;
    @Resource
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Resource
    private WxCustUserService userService;

    @Resource
    private RestTemplate restTemplate;

    /**
     * <AUTHOR>
     * @date 2018-05-08 14:04
     * @Description: 获取本系统接口调用凭证
     */
    @GetMapping("/wx-token")
    public Map<String, Object> getToken(HttpServletRequest request, @RequestParam("code") String code, ByCustUser byCustUser) {
        log.info("==============================================#用户授权==============================================");
        if (StringUtils.isEmpty(code)) {
            throw new CustomException("code不能为空");
        }
        byCustUser.setNickName(byCustUser.getNickname());
        Wechat wechat = wechatService.getApiComponent();
        log.info("appid"+wechat.getAppId());
        log.info("appSecret"+wechat.getAppSecret());
        JSCode2SessionResponse openidAndSessionKey = wechat.base().getOpenidAndSessionKey(code);
        if (StringUtils.isEmpty(openidAndSessionKey.getOpenid())) {
            throw new CustomException("系统繁忙,请稍后重试");
        }
        Map<String, Object> map = Maps.newHashMap();
        String openid = openidAndSessionKey.getOpenid();
        ByCustUser user = userService.queryUserInfo(openid);
        map.put("openid", openid);
        map.put("registerFlag", null == user || null == user.getMobile() ? false : true);
        synchronized (openid.intern()){
            if (null != user) {
                user.setGmtModified(new Date());
                user.setMobile(byCustUser.getMobile());
                user.setNickName(byCustUser.getNickName());
                user.setHeadImg(byCustUser.getHeadImg());
                userService.updateByCustUserInformation(user);
                String token = jwtAuthenticationFilter.getJsonWebTokenHandler().generateToken(user);
                map.put("token", token);
                return map;
            } else {
                /*保存授权信息*/
                byCustUser.setWxOpenId(openid);
                byCustUser.setRegisterTime(new Date());
                byCustUser.setGmtCreate(new Date());
                log.info("WxController："+byCustUser);
                user = userService.saveByCustUser(byCustUser);
                String token = jwtAuthenticationFilter.getJsonWebTokenHandler().generateToken(user);
                map.put("token", token);
                return map;
            }
        }
    }

    @GetMapping("/wx-phone")
    public R getPhone(HttpServletRequest request,
                                       @RequestParam("encryptedData") String encryptedData,
                                       @RequestParam("iv") String iv,
                                       @RequestParam("code") String code) {
        if (StringUtils.isEmpty(encryptedData) || StringUtils.isEmpty(iv) || StringUtils.isEmpty(code)) {
            throw new CustomException("参数不能为空");
        }
        try{
            Wechat wechat = wechatService.getApiComponent();
            AccessToken token = wechat.base().accessToken();
            Map<String, Object> map = Maps.newHashMap();
            // 使用前端code获取手机号码 参数为json格式
            String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + token.getAccessToken();
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("code", code);
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(paramMap, headers);
            System.out.println(httpEntity);
            ResponseEntity<Object> response = restTemplate.postForEntity(url, httpEntity, Object.class);
            return R.data(response.getBody());
        }catch (Exception ex){
            return R.fail("认证失败");
        }

    }

    /**
     * @Description 授权
     * <AUTHOR>
     * @Date 2019-08-06 15:29
     * @Version 1.0
     */
    @GetMapping("create")
    public Map<String, String> create() {
        ByCustUser byCustUser = new ByCustUser();
        byCustUser.setWxOpenId("o3xGI5E8u7dC0s9S3fNLIUnNV0Vw");
        byCustUser.setId(1);
        String token = jwtAuthenticationFilter.getJsonWebTokenHandler().generateToken(byCustUser);
        Map<String, String> data = new HashMap<>(1);
        data.put("token", token);
        return data;
    }


    @Autowired
    private PayService payService;

    @GetMapping("update")
    public RestResult update(String id) {
        this.payService.updateTicketOrder(id, "");
        return RestResult.success();
    }

}
