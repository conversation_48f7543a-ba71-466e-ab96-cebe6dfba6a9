package com.wmeimob.fastboot.baoyan.controller.tc;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.baoyan.mapper.ByStoreStaffMapper;
import com.wmeimob.fastboot.baoyan.service.ByStoreStaffService;
import com.wmeimob.fastboot.baoyan.service.TcWriteOffCodeService;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/12
 */
@RestController
@RequestMapping("/tc/code")
public class WxTcWriteOffCodeController {

    @Resource
    private TcWriteOffCodeService tcWriteOffCodeService;

    @Resource
    private QrCodeService qrCodeService;

    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;

    @GetMapping("/findById")
    public TcWriteOffCode findById(Integer id){
        //获得登录的用户
        UserDetails user = SecurityContext.getUser();
        if ( user.getClass()!=ByCustUser.class ){
            throw new CustomException("您没有登录");
        }

        ByCustUser loginUser = (ByCustUser) user;
//        ByCustUser loginUser = ByCustUser.builder().id(52).wxOpenId("o3xGI5Hsnc1dmwpJBi54qTEXHdLQ").build();
        //获得当前用户绑定的员工信息
        ByStoreStaff dbStaff = byStoreStaffMapper.findByOpenId(loginUser.getWxOpenId());
        if ( dbStaff==null ){
            throw new CustomException("您不是管理员，无法核销");
        }

        return tcWriteOffCodeService.findById(id);
    }

    /**
     * 根据订单号查询 淘潮核销码
     * @param orderNo
     * @return
     */
    @GetMapping("/{orderNo}")
    public List<TcWriteOffCode> findByOrderNo(@PathVariable String orderNo){
        return tcWriteOffCodeService.findByOrderNo(orderNo);
    }

    /**
     * 核销某个核销码
     * @return
     */
    @PutMapping
    public boolean writeOff(@RequestBody TcWriteOffCode writeOffCode){
        //获得登录的用户
        UserDetails user = SecurityContext.getUser();
        if ( user.getClass()!=ByCustUser.class ){
            throw new CustomException("您没有登录");
        }

        ByCustUser loginUser = (ByCustUser) user;
//        ByCustUser loginUser = ByCustUser.builder().id(52).wxOpenId("o3xGI5Hsnc1dmwpJBi54qTEXHdLQ").build();
        //获得当前用户绑定的员工信息
        ByStoreStaff dbStaff = byStoreStaffMapper.findByOpenId(loginUser.getWxOpenId());
        if ( dbStaff==null ){
            throw new CustomException("您不是管理员, 无法核销");
        }

        int staffId = dbStaff.getId().intValue();


        return tcWriteOffCodeService.writeOff(writeOffCode, staffId, dbStaff.getStoreId());
    }

}
