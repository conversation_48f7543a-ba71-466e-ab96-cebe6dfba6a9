package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import com.wmeimob.fastboot.baoyan.mapper.TcImgModuleMapper;
import com.wmeimob.fastboot.baoyan.service.TcImgModuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * @systemName: king
 * @CreationDate: 2021/9/3
 * @packageName: com.wmeimob.fastboot.baoyan.service.tc
 */

@Service("wxTcImgModuleServiceImpl")
public class WxTcImgModuleServiceImpl implements TcImgModuleService {
    @Resource
    private TcImgModuleMapper tcImgModuleMapper;

    @Override
    public List<TcImgModule> queryAll(TcImgModule tcImgModule) {

        return tcImgModuleMapper.wxQueryALL();
    }
}
