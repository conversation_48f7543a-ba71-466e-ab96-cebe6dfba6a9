package com.wmeimob.fastboot.baoyan.service;

import com.google.common.collect.Lists;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.vo.BaseAdvertisingVO;
import com.wmeimob.fastboot.baoyan.vo.ByArticlesVO;
import com.wmeimob.fastboot.baoyan.vo.ImgVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import org.apache.ibatis.annotations.Case;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 首页
 * @date 2019-08-06 16:13
 * @Version 1.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HomeServerImpl implements HomeService {


    @Resource
    private BaseBannerMapper baseBannerMapper;
    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;
    @Resource
    private WxCustUserService userService;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private ByCouponTempMapper byCouponTempMapper;
    @Resource
    private ByCouponUserMapper byCouponUserMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private BaseClassifyMapper baseClassifyMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BaseActivityMapper baseActivityMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByCouponMapper byCouponMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private ByGoodsShopingMapper byGoodsShopingMapper;
    @Resource
    private BaseAdvertisingMapper baseAdvertisingMapper;
    @Resource
    private BaseManyImgMapper baseManyImgMapper;
    /**
     * 首页
     *
     * @return
     */
    @Override
    public Map<String, Object> showHome() {
        Map<String, Object> dataMap = new HashMap<>(3);
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!principal.equals("anonymousUser")) {
            ByCustUser user = SecurityContext.getUser();
            ByCustUser byCustUser = this.userService.queryUserInfo(user.getWxOpenId());
            if (byCustUser.getBirthday() != null) {
                /*判断用户是有在该时间点是否有生日*/
                Date birthday = byCustUser.getBirthday();
                if (isBirthday(birthday)) {
                    if (byCustUser.getYearCouponFlag()) {
                        dataMap.put("type", Boolean.FALSE);
                    } else {
                        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
                        if (byPlatformSet == null && byPlatformSet.getBirthdayCouponId() == null) {
                            dataMap.put("type", Boolean.FALSE);
                        } else {
                            //查询优惠券表
                            // ByCoupon coupon = byCouponMapper.selectByPrimaryKey(byPlatformSet.getPerfectInfoCouponId());
                            ByCouponTemp byCouponTemp = this.byCouponTempMapper.selectByPrimaryKey(byPlatformSet.getPerfectInfoCouponId());
                            //ByCouponTemp byCouponTemp = this.byCouponTempMapper.selectByPrimaryKey(byPlatformSet.getBirthdayCouponId());
                            Calendar instance = Calendar.getInstance();
                            instance.setTime(new Date());

                            if (byCouponTemp == null || byCouponTemp.getIsDel() || instance.getTime().getTime() < byCouponTemp.getStartDate().getTime()) {
                                dataMap.put("type", Boolean.FALSE);
                            } else {
                                if (byCouponTemp.getType() == 1) {
                                    BaseClassify baseClassify = baseClassifyMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                                    byCouponTemp.setGoodsName(baseClassify == null ? null : baseClassify.getClassifyTitle());
                                }
                                if (byCouponTemp.getType() == 2) {
                                    ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                                    byCouponTemp.setGoodsName(byGoodsInfo == null ? null : byGoodsInfo.getGoodsName());
                                }
                                /*发放优惠券*/
                                ByCouponUser byCouponUser = new ByCouponUser();
                                byCouponUser.setUserId(byCustUser.getId());
                                byCouponUser.setCouponId(byCouponTemp.getId());
                                byCouponUser.setTargetId(byCouponTemp.getTargetId());
                                byCouponUser.setName(byCouponTemp.getName());
                                byCouponUser.setDiscount(byCouponTemp.getDiscount());
                                byCouponUser.setFull(byCouponTemp.getFull());
                                //效验当前优惠券类型是否是天数  若是则增加对应天数
                                if (byCouponTemp.getEffectiveType().equals(BaoYanConstant.CONSTANT_TWO)) {
                                    //计算时间 选择天数 增加
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(new Date());
                                    calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + byCouponTemp.getDayNum());//让日期加天数 
                                    byCouponUser.setStartDate(new Date());
                                    byCouponUser.setEndDate(calendar.getTime());
                                } else {
                                    byCouponUser.setStartDate(byCouponTemp.getStartDate());
                                    byCouponUser.setEndDate(byCouponTemp.getEndDate());
                                }
                                byCouponUser.setIsUse(0);
                                byCouponUser.setGetType(2);
                                byCouponUser.setGmtCreate(new Date());
                                byCouponUser.setAuditStatus(1);
                                byCouponUser.setDiscount(byCouponTemp.getDiscount());
                                byCouponUser.setCouponType(byCouponTemp.getCouponType());
                                byCouponUser.setAuditStatus(CommonFinal.ONE);
                                byCouponUser.setType(byCouponTemp.getType());
                                byCouponUser.setIsGive(1);
                                byCouponUser.setSingleGoodsType(byCouponTemp.getSingleGoodsType());
                                this.byCouponUserMapper.insertSelective(byCouponUser);
                                byCustUser.setYearCouponFlag(Boolean.TRUE);
                                this.byCustUserMapper.updateByPrimaryKeySelective(byCustUser);

                                dataMap.put("type", Boolean.TRUE);
                                dataMap.put("birthday", byCouponTemp);
                            }
                        }
                    }

                } else {
                    dataMap.put("type", Boolean.FALSE);
                }
            }
        }
        List<BaseBanner> baseBanners = this.baseBannerMapper.selectByExampleList();
        if (!CollectionUtils.isEmpty(baseBanners)) {
            for (BaseBanner banner : baseBanners) {
                //针对前段 特殊处理
                banner.setBannerImgUrl(banner.getImgUrl());
                //判断是否是分配类型若是 去查询 下分类图片set下
                if (banner.getJumpType().equals(BaoYanConstant.CONSTANT_ONE)) {
                    BaseClassify queryClassify = baseClassifyMapper.selectByPrimaryKey(banner.getTarget());
                    if (null != queryClassify) {
                        banner.setImgUrl(queryClassify.getClassifyImg());
                    }
                }
            }
        } else {
            baseBanners = Lists.newArrayList();
        }
        /*banner 图*/
        dataMap.put("banner", baseBanners);

        List<BaseNavigationConf> navigation = this.baseNavigationConfMapper.tcNavigation(Boolean.TRUE);
        for (BaseNavigationConf baseNavigationConf : navigation) {
            if (baseNavigationConf.getJumpType().equals(BaoYanConstant.CONSTANT_TWO) || baseNavigationConf.getJumpType().equals(BaoYanConstant.CONSTANT_THREE)|| baseNavigationConf.getJumpType().equals(BaoYanConstant.RICH_TYPE_4)){
                baseNavigationConf.setTargetImgUrl(baseNavigationConf.getTarget());
            }
        }
        /*导航*/
        dataMap.put("navigation", navigation);

        /*优惠券*/
        ByCouponTemp coupon = this.byCouponTempMapper.selectHome(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));

        ByCoupon couponParam = new ByCoupon();
        List<ByCoupon> byCouponUsers = byCouponMapper.getWechatCouponList(couponParam);
        if (!CollectionUtils.isEmpty(byCouponUsers)) {
            //处理9.7折数据
            ByCoupon resultCoupon = byCouponUsers.get(0);
            if (null != resultCoupon) {
                //折扣卷类型
                if (resultCoupon.getCouponType().equals(2)) {
                    //前端展示 例 97/10 9.7
                    resultCoupon.setDiscount(resultCoupon.getDiscount().divide(new BigDecimal(10)));
                }
            }
            dataMap.put("coupon", byCouponUsers.size() == 0 ? coupon : resultCoupon);
        }

        return dataMap;
    }

    @Override
    public List<BaseActivity> showActivity() {
        List<BaseActivity> baseActivities = this.baseActivityMapper.selectByExampleDel();
        if (baseActivities.size() != 0) {
            for (BaseActivity baseActivity : baseActivities) {
               //String target = baseActivity.getTarget();
                baseActivity.setTargetImgUrl(baseActivity.getImgUrl());
                baseActivity.setImgUrl(baseActivity.getImgUrl());
                baseActivity.setBaseBanner(baseActivity.getBaseBanner());
                //需要单独判断下 如果是商品分类 需要把 上面两个图imgUrl 对应取分类图片
                if (baseActivity.getJumpType().equals(BaoYanConstant.CONSTANT_ONE)) {
                    //是分类
                    BaseClassify classify = baseClassifyMapper.selectByPrimaryKey(baseActivity.getTarget());
                    baseActivity.setImgUrl(classify.getClassifyImg());
                }
            }
        }
        return baseActivities;
    }


    /**
     * @Description 文章详情
     * <AUTHOR>
     * @Date 2019-08-07 15:32
     * @Version 1.0
     */

    @Override
    public ByArticle byarticle(Integer id) {
        ByArticle byArticle = byArticleMapper.selectByPrimaryKey(id);
        //购物车商品数量
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal) ){
            ByCustUser user = SecurityContext.getUser();
            byArticle.setShoppingCount(getShoppingCount(user.getId()));
        }
        return byArticle;

    }

    private Integer getShoppingCount(Integer id){
        return this.byGoodsShopingMapper.selectShopCount(id);
    }
    /**
     * @Description 分类
     * <AUTHOR>
     * @Date 2019-08-07 15:30
     * @Version 1.0
     */
    @Override
    public List<BaseClassify> classification() {
        Example example = new Example(BaseClassify.class);
        example.createCriteria().andEqualTo("isDel", Boolean.FALSE).andEqualTo("state",1);
        example.orderBy("sort").desc();
        return this.baseClassifyMapper.selectByExample(example);
    }

    /**
     * @Description 联票商品
     * <AUTHOR>
     * @Date 2019-08-07 15:39
     * @Version 1.0
     */
    @Override
    public List<ByTicketGoods> ticketGoods() {
        Example example = new Example(ByTicketGoods.class);
        example.createCriteria()
                .andEqualTo("isDel", Boolean.FALSE)
                .andEqualTo("status", Boolean.TRUE);
        example.orderBy("sort").desc();
        return this.byTicketGoodsMapper.selectByExample(example);
    }

    /**
     * @Description 次卡商品
     * <AUTHOR>
     * @Date 2019-08-07 15:39
     * @Version 1.0
     */
    @Override
    public List<BySubCardGoods> cardGoods() {
        Example example = new Example(BySubCardGoods.class);
        example.createCriteria()
                .andEqualTo("isDel", Boolean.FALSE)
                .andEqualTo("status", Boolean.TRUE);
        example.orderBy("sort").desc();
        return this.bySubCardGoodsMapper.selectByExample(example);
    }

    private Boolean isBirthday(Date birthday) {
        Calendar cd = Calendar.getInstance();
        Calendar calendar = cd;
        calendar.setTime(new Date());
        cd.setTime(birthday);
        cd.add(Calendar.DAY_OF_MONTH, -15);
        if (cd.getTime().getTime() > calendar.getTime().getTime()) {
            return false;
        }
        return isDay(birthday, cd.getTime(), new Date());
    }

    private Boolean isDay(Date startDate, Date endDate, Date date) {
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        if (dateFormat.format(cd.getTime()).equals(dateFormat.format(startDate))) {
            if (cd.getTime().getTime() < startDate.getTime() && cd.getTime().getTime() > endDate.getTime()) {
                return true;
            } else {
                return false;
            }
        }
        cd.add(Calendar.YEAR, -1);
        return isDay(startDate, endDate, cd.getTime());
    }

    @Override
    public RestResult checkUserDisable(ByCustUser user, Integer type, String ids) {
        if (type == null || StringUtils.isEmpty(ids)) {
            throw new CustomException("该商品已过期");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);//+1今天的时间加一天
        long time = calendar.getTime().getTime();
        // 跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.购物车 6.普通商品7.联票商品8.次卡商品9.拼团商品,10规格商品）
        if (type == 1 || type == 6) {
            ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(ids);
            if (byGoodsInfo == null) {
                throw new CustomException("商品不存在");
            }
            if (byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
                throw new CustomException("商品已下架请重新选择");
            }
            if (ObjectUtil.isNotEmpty(byGoodsInfo.getVerificationEnd()) && time > byGoodsInfo.getVerificationEnd().getTime()) {
                throw new CustomException("该商品已过期");
            }
        }
        if (type == 10){
            ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(ids);
            if ((combinationGoods.getIsDel()!=null && combinationGoods.getIsDel()==1) || (combinationGoods.getStatus()!=null && combinationGoods.getStatus() != 1)) {
                throw new CustomException("商品已下架,请重新选择");
            }
            if (!combinationGoods.getHasVerificationDay() && time > combinationGoods.getVerificationEnd().getTime()) {
                throw new CustomException("该商品已过期");
            }
        }
        if (type == 2 || type == 7) {
            ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(ids);
            if (byTicketGoods.getIsDel() || !byTicketGoods.getStatus()) {
                throw new CustomException("商品已下架,请重新选择");
            }
            if (!byTicketGoods.getHasVerificationDay() && time > byTicketGoods.getEffectiveEnd().getTime()) {
                throw new CustomException("该商品已过期");
            }
        }
        if (type == 3 || type == 8) {
            BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(ids);
            if (bySubCardGoods.getIsDel()|| bySubCardGoods.getStatus().equals(0)) {
                throw new CustomException("商品已下架,请重新选择");
            }
            if ((ObjectUtil.isEmpty(bySubCardGoods.getHasVerificationDay()) || !bySubCardGoods.getHasVerificationDay()) &&  time > bySubCardGoods.getEffectiveEnd().getTime()) {
                throw new CustomException("该商品已过期");
            }
        }
        if (type == 5) {
            String[] split = ids.split(",");
            if (split.length < 0) {
                throw new CustomException("请至少添加一种商品");
            }
            String str = "";
            for (int i = 0; i < split.length; i++) {
                ByGoodsShoping byGoodsShoping = this.byGoodsShopingMapper.selectByPrimaryKey(split[i]);
                if (byGoodsShoping == null) {
                    throw new CustomException("该商品已过期");
                }
                switch (byGoodsShoping.getGoodsType()) {
                    case 1:
                        ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
                        if (byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
                            str += byGoodsInfo.getGoodsName() + " ";
                        }
                        if (ObjectUtil.isNotEmpty(byGoodsInfo.getVerificationEnd()) && (byGoodsInfo == null || time > byGoodsInfo.getVerificationEnd().getTime())) {
                            str += byGoodsInfo.getGoodsName() + " ";
                        }
                        continue;
                    case 2:
                        BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
                        if (bySubCardGoods.getIsDel()|| bySubCardGoods.getStatus().equals(0)) {
                            str += bySubCardGoods.getSubCardGoodsName() + " ";
                        }
                        if (bySubCardGoods == null || time > bySubCardGoods.getEffectiveEnd().getTime()) {
                            str += bySubCardGoods.getSubCardGoodsName() + " ";
                        }
                        continue;
                    case 3:
                        ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byGoodsShoping.getGoodsId());
                        if (byTicketGoods.getIsDel()|| !byTicketGoods.getStatus()) {
                            str += byTicketGoods.getTicketGoodsName() + " ";
                        }
                        if (time > byTicketGoods.getEffectiveEnd().getTime()) {
                            str += byTicketGoods.getTicketGoodsName() + " ";
                        }
                        continue;
                }
            }
            if (StringUtil.isNotEmpty(str)) {
                throw new CustomException(str + "商品已下架/已过期,请移除商品");
            }
        }
        if (type == 4) {
            ByTeamGoods byTeamGoods = byTeamGoodsMapper.selectByPrimaryKey(ids);
            if (byTeamGoods != null) {
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byTeamGoods.getGoodsId());
                if (byGoodsInfo == null) {
                    throw new CustomException("商品不存在");
                }
                if (byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
                    throw new CustomException("商品已下架请重新选择");
                }
                if (ObjectUtil.isNotEmpty(byGoodsInfo.getVerificationEnd()) && time > byGoodsInfo.getVerificationEnd().getTime()) {
                    throw new CustomException("该商品已过期");
                }
            } else {
                throw new CustomException("该商品已过期");
            }
        }
        if (null == user || null == user.getId()) {
            return RestResult.fail("请重新授权");
        }
        ByCustUser resultUser = byCustUserMapper.selectByPrimaryKey(user.getId());
        if (resultUser.getIsDisable()) {
            return RestResult.fail("您的账号已被禁用，不可购买及预约，如有疑问，请联系客服");
        }
        return RestResult.success();
    }

    @Override
    public RestResult checkGoodsShelf(Integer jumpType, Integer id) {
        // 跳转类型 1 商品分类 2 联票列表 3次卡列表 4拼团列表 5 文章 6 普通商品 7联票商品 8次卡商品 9 拼团商品
        boolean falg = false;
        String msg = "";
        switch (jumpType) {
            case 1:
                break;
            case 2:
                break;
            case 3:
                break;
            case 4:
                break;
            case 5:
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", id);
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    falg = true;
                    msg = "文章不存在";
                }
                break;
            case 6:
                Example goodExample = new Example(ByGoodsInfo.class);
                goodExample.createCriteria().andEqualTo("id", id).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(goodExample);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    falg = true;
                    msg = "商品不存在";
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        falg = true;
                        msg = "商品已下架";
                    }
                }
                break;
            case 7:
                Example byTicketExample = new Example(ByTicketGoods.class);
                byTicketExample.createCriteria().andEqualTo("id", id).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(byTicketExample);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    falg = true;
                    msg = "商品不存在";
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        falg = true;
                        msg = "商品已下架";
                    }
                }
                break;
            case 8:
                Example bySubCardExample = new Example(BySubCardGoods.class);
                bySubCardExample.createCriteria().andEqualTo("id", id).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(bySubCardExample);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    falg = true;
                    msg = "商品不存在";
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        falg = true;
                        msg = "商品已下架";
                    }
                }
                break;
            case 9:
                //判断当前拼团商品是否存在
                Example byTeamGoodsExample = new Example(ByTeamGoods.class);
                byTeamGoodsExample.createCriteria().andEqualTo("id", id).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(byTeamGoodsExample);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    falg = true;
                    msg = "商品不存在";
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        falg = true;
                        msg = "商品已下架";
                    }
                }
                break;
        }
        if (falg) {
            return RestResult.fail(msg);
        }
        return RestResult.success();
    }

    @Override
    public BaseAdvertisingVO advertising() {
        BaseAdvertisingVO advertising = baseAdvertisingMapper.selectBySetting();
        //判断是否是分配类型若是 去查询 下分类图片set下
        if (advertising != null && advertising.getJumpType().equals(BaoYanConstant.CONSTANT_ONE)) {
            BaseClassify queryClassify = baseClassifyMapper.selectByPrimaryKey(advertising.getTarget());
            if (null != queryClassify) {
                advertising.setTargetImgUrl(queryClassify.getClassifyImg()).setClassifyTitle(queryClassify.getClassifyTitle());
            }
        }
        return advertising;
    }

    @Override
    public List<ByArticlesVO> byArticles() {
        List<ByArticlesVO> list = new ArrayList<>();
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
        if (byPlatformSet != null && byPlatformSet.getArticleStatus() != null && byPlatformSet.getArticleStatus().equals(1)){
            List<ByArticlesVO> vos = byArticleMapper.selectByState();
            vos.stream().forEach((v)->{
                v.setTime(byPlatformSet.getArticleSeconds());
            });
            return vos;
        }else {
            return list;
        }
    }

    @Override
    public List<ImgVO> imgs() {
        List<ImgVO> list = new ArrayList<>();
        List<ImgVO> vo = baseManyImgMapper.selectByStatus();
        if (vo != null){
            for (ImgVO img : vo) {
                //判断是否是分配类型若是 去查询 下分类图片set下
                if (img.getJumpType().equals(BaoYanConstant.CONSTANT_ONE)) {
                    BaseClassify queryClassify = baseClassifyMapper.selectByPrimaryKey(img.getTarget());
                    if (null != queryClassify) {
                        img.setTargetImgUrl(queryClassify.getClassifyImg()).setClassifyTitle(queryClassify.getClassifyTitle());
                    }
                }else if (img.getJumpType().equals(BaoYanConstant.CONSTANT_TWO) || img.getJumpType().equals(BaoYanConstant.CONSTANT_THREE)|| img.getJumpType().equals(BaoYanConstant.RICH_TYPE_4)){
                    img.setTargetImgUrl(img.getTarget());
                }
            }
            return vo;
        }else {
            return list;
        }
    }

    @Override
    public String selectPrivacyAgreement() {
        return baseBannerMapper.selectPrivacyAgreement();
    }

    @Override
    public String selectPurchaseAgreement() {
        return baseBannerMapper.selectPurchaseAgreement();
    }
//    @Override
//    public BaseBanner singlebanner(Object id){
//        return baseBannerMapper.selectByPrimaryKey(id);
//    }
}
