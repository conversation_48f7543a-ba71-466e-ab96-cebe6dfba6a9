package com.wmeimob.fastboot.baoyan.controller.coupon;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.service.ByCustUserService;
import com.wmeimob.fastboot.baoyan.service.WechatCouponUserService;
import com.wmeimob.fastboot.baoyan.vo.CouponListByGoodsVo;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName ByCouponController
 * @Description 【优惠券表】控制器
 * <AUTHOR>
 * @Date Tue Jul 09 13:58:11 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("couponUser")
@Slf4j
public class WechatCouponController {

    @Resource
    private WechatCouponUserService byCouponUserService;
    @Autowired
    private ByCustUserMapper byCustUserService;

    /**
     * 我的优惠券
     * @param request
     * @param byCouponUser
     * @return
     */
    @GetMapping("/list")
    public PageInfo queryForCouponUser(HttpServletRequest request, ByCouponUser byCouponUser){
        PageContext.startPage();
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        byCouponUser.setUserId(user.getId());
        return new PageInfo<ByCouponUser>(byCouponUserService.findByCondition(byCouponUser));
    }
    /**
     * 优惠券列表
     * @param request
     * @param coupon
     * @return
     */
    @GetMapping("/couponList")
    public List<ByCoupon> couponList(HttpServletRequest request, ByCoupon coupon){
        return byCouponUserService.couponList(coupon);
    }

    /**
     * 商品可以领取的优惠卷列表
     */
    @GetMapping("/couponListByGoods")
    public List<CouponListByGoodsVo> couponListByGoods(Integer type, Integer goodsId){
        return byCouponUserService.couponListByGoods(type,goodsId);
    }


    /**
     * 领取优惠券
     * @param request
     * @param couponUser
     * @return
     */
    @PostMapping("/receiveCoupon")
    public void receiveCoupon(HttpServletRequest request, @RequestBody ByCouponUser couponUser){
        if(couponUser.getCouponId() == null){
            throw new CustomException("优惠券错误");
        }
        ByCustUser user = null;
        try {
            Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            user = (ByCustUser)principal;
            log.info("领取优惠券 couponUser: {} 当前登录人userId：{}", couponUser,user.getId());
        }catch (Exception e){
            throw new CustomException("请先授权在领取优惠券");
        }
        ByCustUser byCustUser = byCustUserService.selectByPrimaryKey(user.getId());
        if (byCustUser.getIsDisable()){
            throw new CustomException("你已被禁用，请联系管理员");
        }
        couponUser.setUserId(user.getId());
        byCouponUserService.addCouponUser(couponUser);
    }
}
