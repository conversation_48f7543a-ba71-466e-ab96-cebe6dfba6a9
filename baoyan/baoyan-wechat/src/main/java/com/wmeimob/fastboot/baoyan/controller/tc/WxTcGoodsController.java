package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.baoyan.service.tc.WxTcGoodsServiceImpl;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/7/23
 * packageName:com.wmeimob.fastboot.baoyan.controller.tc
 */
@RestController
@RequestMapping("tc/tcGoods")
@Slf4j
public class WxTcGoodsController
{

    @Resource(name = "WxTcGoodsServiceImpl")
    private TcGoodsService tcGoodsService;

    @PostMapping("/")
    public PageInfo<? extends TcGoods> queryPage1(@RequestBody  TcGoods queryObject)
    {
        log.info(" 微信小程序  post=> queryPage [入参]============={}", queryObject);
        Page<Object> page = PageContext.startPage();
        log.info(" 微信小程序  post=> page [入参]============={}", page);

        PageInfo<TcGoods> objectPageInfo = new PageInfo<>();
        objectPageInfo.setList(
                tcGoodsService.queryPage(queryObject)
        );
        objectPageInfo.setTotal(
                tcGoodsService.queryAllCount(queryObject)
        );
        objectPageInfo.setPageSize(page.getPageSize());
        objectPageInfo.setPageNum(page.getPageNum());
        return objectPageInfo;
    }

    @GetMapping("/{id}")
    public TcGoods queryById(@PathVariable Integer id)
    {
        if (null == id) {
            throw new CustomException("商品编号 不能为空");
        }
        log.info(" 微信小程序  get=> queryById [入参]============={}", id);

        return tcGoodsService.queryById(id);
    }

}
