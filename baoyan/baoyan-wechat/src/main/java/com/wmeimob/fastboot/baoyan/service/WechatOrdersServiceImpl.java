package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.UUIDOrder;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.vo.OrderInfoVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.Md5Crypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * @ClassName ByOrdersServiceImpl
 * @Description 商品订单表 服务类实现
 * <AUTHOR>
 * @Date Mon Jul 15 15:41:21 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WechatOrdersServiceImpl implements WechatOrdersService {

    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private ByEvaluateMapper byEvaluateMapper;
    @Resource
    private ByOrderAfterMapper byOrderAfterMapper;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private BySubGoodsStoreMapper bySubGoodsStoreMapper;
    @Resource
    private ByIntegralLogMapper byIntegralLogMapper;

    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Autowired
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ByCombinationGoodsMapper byCombinationGoodsMapper;

    @Resource
    private TcOrderMapper tcOrderMapper;
    @Resource
    private ByOrderLogService orderLogService;
    @Resource
    private PayService payService;
    @Resource
    private ByChannelCustMapper byChannelCustMapper;
    @Resource
    private YchApiService ychApiService;

    @Override
    public List<OrderInfoVo> userOrderList(ByCustUser user, Integer orderStatus,Integer payType) {
        ByOrders orders = new ByOrders();
        orders.setOrderStatus(orderStatus);
        if(payType != null){
            orders.setPayType(payType);
        }
        orders.setUserId(user.getId());
        List<OrderInfoVo> list = byOrdersMapper.userOrderList(orders);
        for (OrderInfoVo order : list) {
            for (ByOrderGoods byOrderGoods : order.getOrderGoodsList()) {
                if ("5".equals(byOrderGoods.getProductType())){
                    ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods.getGoodsId());
                    ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                    byOrderGoods.setGoodsName(goodsInfo.getGoodsName()+"（"+combinationGoods.getName()+"）");
                }else if ("1".equals(byOrderGoods.getProductType())){
                    ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods.getGoodsId());
                    if (!StringUtils.isEmpty(goodsInfo.getSpecName())){
                        byOrderGoods.setGoodsName(goodsInfo.getGoodsName()+"（"+goodsInfo.getSpecName()+"）");
                    }
                }
            }
        }
        log.info(list.size()+"");
        for (OrderInfoVo orderInfoVo : list) {
            log.info(orderInfoVo.toString());
        }
//        List<OrderInfoVo> returnList = new ArrayList<>();
//        已赠送显示在已完成列表中
//        if (orderStatus ==null || orderStatus == 2){
//            for (OrderInfoVo orderInfo : list) {
//                if (orderInfo.getIsCurrent() == 1 || (orderInfo.getIsCurrent() == 0 && orderInfo.getIsPresenter() == 0)){
//                    returnList.add(orderInfo);
//                }
//            }
//        }else if (orderStatus != null && orderStatus == 3){
//            returnList.addAll(list);
//            orders.setOrderStatus(2);
//            List<OrderInfoVo> orderList = byOrdersMapper.userOrderList(orders);
//            orderList.stream().forEach((o)->{
//                if (o.getIsCurrent() == 0 && o.getIsPresenter() == 1){
//                    returnList.add(o);
//                }
//            });
//        }else {
//
//        }
        return list;
    }


    @Override
    public Map<String, Object> userOrderDetail(Integer id) {
        ByCustUser user = SecurityContext.getUser();
        ByOrders orderInfo = byOrdersMapper.selectByPrimaryKey(id);
        if (null == orderInfo) {
            throw new CustomException("订单信息异常");
        }
        orderInfo.setRandom(Md5Crypt.apr1Crypt(orderInfo.getOrderNo(), orderInfo.getOrderNo()));
        if (orderInfo.getCurrentUserId() != null){
            orderInfo.setIsCurrent(0);
        }else {
            orderInfo.setIsCurrent(1);
        }
        if (user.getId().equals(orderInfo.getUserId()) && orderInfo.getCurrentUserId() != null && !orderInfo.getUserId().equals(orderInfo.getCurrentUserId())){
            orderInfo.setIsPresenter(1);
        }else {
            orderInfo.setIsPresenter(0);
        }
        //使用完成时间（商品全部使用完成时间）
        List<ByOrderGoods> orderGoods = byOrderGoodsMapper.selectByOrderId(id);



        //返回给前端的数据
        Map<String, Object> map = new HashMap<>(3);
        map.put("orderInfo", orderInfo);

        //查询订单描述信息
        List<ByWriteOffInfo> orderGoodsInfos = queryWriteOffInfo(orderGoods);
        map.put("writeInfo",orderGoodsInfos);

        //当订单状态为已完成 取值更新字段 为使用完成时间
        if ( orderInfo.getOrderStatus().equals(3) ){
            //取出最后一次核销时间
            Date lastCheckTime = orderGoodsInfos.get(0).getLastWriteDate();
            for (int i = 1; i < orderGoodsInfos.size() && lastCheckTime!=null; i++) {
                Date lastWriteDate = orderGoodsInfos.get(i).getLastWriteDate();
                if ( lastCheckTime.compareTo( lastWriteDate ) < 0 ){
                    lastCheckTime = lastWriteDate;
                }
            }

            orderInfo.setUseTime( lastCheckTime );
        }


        Integer j = 0;
        if (orderGoods.size() != 0) {
            for (ByOrderGoods goods : orderGoods) {
                if (goods.getProductType().equals("1")){
                    ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goods.getGoodsId());
                    if (!StringUtils.isEmpty(goodsInfo.getSpecName())){
                        goods.setGoodsName(goodsInfo.getGoodsName()+"（"+goodsInfo.getSpecName()+"）");
                    }
                }else if (goods.getProductType().equals("5")){
                    ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(goods.getGoodsId());
                    ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                    goods.setGoodsName(goodsInfo.getGoodsName()+"（"+combinationGoods.getName()+"）");
                }
                /** 查看核销码*/
                Integer count = this.writeOffCodeMapper.selectById(goods.getId());
                j += count;
                if (count == 0) {
                    goods.setOrderStatus(4);
                }

                Example example1 = new Example(ByOrderAfter.class);
                example1.createCriteria()
                        .andEqualTo("detailId", goods.getId())
                        .andEqualTo("afterStatus", 1)
                        .andEqualTo("resouceType", 1);
                List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example1);
                if (byOrderAfters.size() > 0) {
                    goods.setRefundType(2);
                    int refundNum = 0;
//                    byOrderAfters.forEach( after -> refundNum=after.getGoodsNum()+refundNum );
                    for (ByOrderAfter after : byOrderAfters) {
                        refundNum += after.getGoodsNum();
                    }
                    goods.setRefundNum( refundNum );
                }
                if (orderInfo.getOrderStatus() == -1 || orderInfo.getOrderStatus() == 1) {
                    goods.setRefundType(3);
                }
                Example example2 = new Example(WriteOffCode.class);
                example2.createCriteria()
                        .andEqualTo("detailId", goods.getId())
                        .andEqualTo("orderType", 0)
                        .andNotEqualTo("status", 0)
                        .andEqualTo("custUserId", orderInfo.getUserId());
                List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example2);
                if (writeOffCodes.size() > 0) {
                    Example example11 = new Example(WriteOffCodeLog.class);
                    example11.createCriteria().andEqualTo("writeOffId", writeOffCodes.get(0).getId());
                    List<WriteOffCodeLog> writeOffCodeLogs = this.writeOffCodeLogMapper.selectByExample(example11);
                    if (writeOffCodeLogs.size() != 0) {
                        goods.setGmtUpdate(writeOffCodeLogs.get(0).getGmtCreate());
                    } else {
                        goods.setGmtUpdate(new Date());
                    }
                }
                if (goods.getProductType().equals("2")) {
                    goods.setProductType("3");
                    continue;
                }
                if (goods.getProductType().equals("3")) {
                    goods.setProductType("2");
                    continue;
                }
            }

        }


        //处理订单是否是待支付状态 （目前没有回写详情 订单状态 单独判断）
        if (orderInfo.getOrderStatus().equals(1)) {
            for (ByOrderGoods goods : orderGoods) {
                goods.setOrderStatus(1);
            }
        }
        else {
            //處理訂單狀態沒有回寫 情況
            for (ByOrderGoods goods : orderGoods) {
//                if (goods.getOrderStatus() != 4){
                    goods.setOrderStatus(orderInfo.getOrderStatus());
//                }
                if (orderInfo.getOrderStatus() == 3){
                    Integer isExpire = writeOffCodeMapper.selectWriteStstus(user.getId(),goods.getOrderNo(),goods.getId());
                    if (goods.getRefundType() != 2 && isExpire != null && isExpire > 0){
                        goods.setIsExpire(1);
                    }else {
                        goods.setIsExpire(0);
                    }
                }
                //查询当前订单商品的核销码是否全部过期
//                if (orderInfo.getOrderStatus()==2){
                //查询过期的核销码
                Integer integer = writeOffCodeMapper.countExpire(goods.getId());
                if(integer!=null && integer>0){
                    goods.setIsExpire(1);
                }
//                }
            }
        }
        map.put("goodsList", orderGoods);
        return map;
    }

    /**
     * 查询订单商品的详情描述
     * @param orderGoods
     * @return
     */
    @Override
    public List<ByWriteOffInfo> queryWriteOffInfo(List<ByOrderGoods> orderGoods) {
        //返回的结果
        ArrayList<ByWriteOffInfo> goodsInfos = new ArrayList<>();
        //订单商品必须有
        if (CollectionUtils.isEmpty(orderGoods)){
            return goodsInfos;
        }

        long now = System.currentTimeMillis();

        for (ByOrderGoods orderGood : orderGoods) {
            //遍历查询
            ByWriteOffInfo goodsInfo = writeOffCodeMapper.queryWriteOffInfo(orderGood.getId());
            //基础信息和商品同步
            log.info(goodsInfo.toString());
            goodsInfo
                    .setGoodsId(orderGood.getGoodsId())
                    .setGoodsName(orderGood.getGoodsName())
                    .setWriteOffStatus(orderGood.getOrderStatus())
                    .setRefundType(orderGood.getRefundType())
                    //剩余次数
                    .setWriteOffCount( goodsInfo.getWriteOffTotal()-goodsInfo.getWriteOffSurplus() );

            if (now>goodsInfo.getEndDate().getTime()){
                goodsInfo.setWriteOffStatus(4);
            }else if (goodsInfo.getWriteOffCount().compareTo(goodsInfo.getWriteOffTotal())==0){
                goodsInfo.setWriteOffStatus(3);
            }

            goodsInfos.add(goodsInfo);


        }

        return goodsInfos;
    }

    @Autowired
    private ByCouponUserMapper byCouponUserMapper;

    @Override
    public void cancelUserOrder(ByCustUser user, Integer id) {
        ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(id);
        if (null == byOrders) {
            throw new CustomException("订单信息异常");
        }
        if (!CommonFinal.ONE.equals(byOrders.getOrderStatus())) {
            throw new CustomException("已支付,不能取消");
        }
        byOrders.setIsDel(Boolean.FALSE);
        byOrders.setOrderStatus(-1);
        byOrders.setCancelTime(new Date());
        byOrders.setGmtUpdate(new Date());
        byOrders.setOrderCloseType(BaoYanConstant.IS_USE_1);
        int i = byOrdersMapper.updateByPrimaryKeySelective(byOrders);
        if (i < 1) {
            throw new CustomException("取消失败");
        }
        /**还原积分和优惠券*/
        ByOrders byOrders1 = this.byOrdersMapper.selectByPrimaryKey(id);
        if (byOrders1.getIntegralAmount() != null && byOrders1.getIntegralAmount().compareTo(new BigDecimal(0)) != 0) {
            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
            if (byOrders1.getIntegralAmount() != null && byPlatformSet.getIntegrationDeduction() != null && byPlatformSet.getIntegrationDeduction().equals(0) == false) {
                BigDecimal divide = byOrders1.getIntegralAmount().multiply(new BigDecimal(byPlatformSet.getIntegrationDeduction()));
                ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(byOrders1.getUserId());
                ByIntegralLog byIntegralLog = new ByIntegralLog();
                byIntegralLog.setUserId(byOrders1.getUserId());
                byIntegralLog.setChangeType(1);
                byIntegralLog.setChangeNum(divide.intValue());
                byIntegralLog.setChangeReason("取消订单");
                byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                byIntegralLog.setGmtCreate(new Date());
                byIntegralLog.setIntegralType(1);
                if (divide.compareTo(new BigDecimal(0)) != 0) {
                    byIntegralLogMapper.insertSelective(byIntegralLog);
                }
                if (byCustUser.getNowPoint() >= 0) {
                    this.byCustUserMapper.updateByPointAdd(byCustUser.getId(),divide.intValue());
                }
            }
        }
        if (byOrders1.getCouponId() != null && byOrders1.getCouponId() != 0) {
            ByCouponUser byCouponUser = new ByCouponUser();
            byCouponUser.setId(byOrders1.getCouponId());
            byCouponUser.setIsUse(0);
            byCouponUserMapper.updateByPrimaryKeySelective(byCouponUser);
        }
        //还原库存和销量
        List<ByOrderGoods> orderGoods = byOrderGoodsMapper.selectByOrderId(id);
        for (ByOrderGoods goods : orderGoods) {

            goods.setOrderStatus(-1);
            byOrderGoodsMapper.updateByPrimaryKeySelective(goods);
        }
        updateGoodsStock(orderGoods);
        byOrders.setOrderCloseType(1);
        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders);
        //更新 核销码状态 为已取消 状态
        Example orderExample = new Example(WriteOffCode.class);
        orderExample.createCriteria().andEqualTo("orderNo", byOrders.getOrderNo());
        WriteOffCode writeOffCode = new WriteOffCode();
        writeOffCode.setOrderState(1);//已取消
        writeOffCodeMapper.updateByExampleSelective(writeOffCode, orderExample);

    }

    @Transactional(rollbackFor = {Exception.class})
    public int updateGoodsStock(List<ByOrderGoods> orderGoods) {
        String str = "";
        String str1 = "";
        String str2 = "";
        for (ByOrderGoods goods : orderGoods) {
            if ("1".equals(goods.getProductType())) {
                if (!str1.contains("#" + goods.getProductId() + "#")) {
                    this.byGoodsInfoMapper.updateByAddStock(Integer.valueOf(goods.getProductId()),goods.getProductCount());
                    str1 += "#" + goods.getProductId() + "#";
                }
            }
            if ("2".equals(goods.getProductType())) {
                if (!str2.contains("#" + goods.getProductId() + "#")) {
                    this.bySubCardGoodsMapper.updateByAddStock(Integer.valueOf(goods.getProductId()),goods.getProductCount());
                    str2 += "#" + goods.getProductId() + "#";
                }

            }
            if ("3".equals(goods.getProductType())) {
                if (!str.contains("#" + goods.getProductId() + "#")) {
                    this.byTicketGoodsMapper.updateByAddStock(Integer.valueOf(goods.getProductId()),goods.getProductCount());
                    str += "#" + goods.getProductId() + "#";
                }

            }

        }
        return 0;
    }

    /**
     * <AUTHOR>
     * @date 15:53 2019/5/14
     * @Description:评价订单
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Map<String, Object> evalOrder(ByCustUser user, List<ByEvaluate> evaluateInfoList) {
        String orderNO = "";
        for (ByEvaluate evaluateInfo : evaluateInfoList) {
            Example example = new Example(ByOrderGoods.class);
            example.createCriteria().andEqualTo("orderNo", evaluateInfo.getOrderNo())
                    .andEqualTo("goodsId", evaluateInfo.getGoodsId()).andEqualTo("id", evaluateInfo.getDetailId());
            ByOrderGoods orderGoods = byOrderGoodsMapper.selectOneByExample(example);
            if (null == orderGoods) {
                throw new CustomException("订单信息异常");
            }
            if (null == evaluateInfo.getMark()) {
                throw new CustomException("请给商品打分");
            }
            if (null == evaluateInfo.getEvaluateText()) {
                throw new CustomException("请给商品点评");
            }
            ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
            if (byCustUser == null) {
                throw new CustomException("用户不存在");
            }
            evaluateInfo.setUserId(user.getId());
            evaluateInfo.setUserName(byCustUser.getNickName());
            evaluateInfo.setGoodsName(orderGoods.getGoodsName());
            evaluateInfo.setImg(evaluateInfo.getImg());
            evaluateInfo.setResouceType(evaluateInfo.getResouceType());
            evaluateInfo.setIsDel(Boolean.FALSE);
            evaluateInfo.setGmtCreate(new Date());
            evaluateInfo.setGmtModified(new Date());
            evaluateInfo.setIsShow(1);
            evaluateInfo.setOrderId(orderGoods.getOrderId());
            orderNO = evaluateInfo.getOrderNo();
        }
        Example exampleOrder = new Example(ByOrders.class);
        exampleOrder.createCriteria().andEqualTo("orderNo", orderNO);
        ByOrders orderInfo = byOrdersMapper.selectOneByExample(exampleOrder);
        if (null == orderInfo) {
            throw new CustomException("订单信息异常");
        }
        int i = byEvaluateMapper.insertList(evaluateInfoList);
        if (i < 1) {
            throw new CustomException("评价失败,请稍后重试");
        }
        orderInfo.setEvalType(true);//已评价
        int i1 = byOrdersMapper.updateByPrimaryKeySelective(orderInfo);
        if (i1 < 1) {
            throw new CustomException("评价失败");
        }
        //新增积分 给userId
        return updatePointByUser(user);
    }

    /**
     * 根据 用户评价订单新增增加积分
     *
     * @param user
     */
    private Map<String, Object> updatePointByUser(ByCustUser user) {
        //查询评价获取积分数量
        ByPlatformSet set = new ByPlatformSet();
        user = this.byCustUserMapper.selectByPrimaryKey(user);
        ByPlatformSet resultSet = byPlatformSetMapper.selectOne(set);
        if (null != resultSet && null != resultSet.getCommentIntegral()) {
            //当前积分+加评价积分
            user.setNowPoint(user.getNowPoint() + resultSet.getCommentIntegral());
            //历史积分
            user.setHistoryPoint(user.getHistoryPoint() + resultSet.getCommentIntegral());
            byCustUserMapper.updateByPrimaryKey(user);
            //新增积分记录
            ByIntegralLog byIntegralLog = new ByIntegralLog();
            byIntegralLog.setIntegralType(CommonFinal.ONE);
            byIntegralLog.setBeforeNum(user.getNowPoint());
            byIntegralLog.setGmtCreate(new Date());
            byIntegralLog.setChangeReason("评价");
            byIntegralLog.setChangeType(CommonFinal.ONE);
            byIntegralLog.setUserId(user.getId());
            byIntegralLog.setChangeNum(resultSet.getCommentIntegral());
            byIntegralLogMapper.insertSelective(byIntegralLog);
            Map<String, Object> map = new HashMap<>();
            map.put("commentIntegral", resultSet.getCommentIntegral());
            return map;

        }
        log.info("ByPlatformSet 平台设置info ", resultSet);
        return null;
    }

    @Override
    public Map<String, Object> getUserOrderNum(Integer userId) {
        Map<String, Object> data = new HashMap<>();
        //待支付
        int ticktWaitNum = byOrdersMapper.getCountByUserIdAndState(userId, 1);
        int tcWaitNum = tcOrderMapper.getCountByUserIdAndState(userId, 1);
        data.put("waitPayNum", ticktWaitNum+tcWaitNum);
        //已付款 订单状态:-1订单超时自动取消 1-待付款, 2-已付款(待发货),  3-已发货, 4-已完成, 5-已退款
        int ticktPayNum = byOrdersMapper.getCountByUserIdAndState(userId, 2);
        int tcPayNum = tcOrderMapper.selectCount(TcOrder.builder().userId(userId).status(2).deliveryMode(1).build());
        data.put("payMentNum", tcPayNum+ticktPayNum);
        //已完成
        data.put("completeNum", byOrdersMapper.getCountByUserIdAndState(userId, 3));
        //待发货
        int notShippedNum  = tcOrderMapper.selectCount(TcOrder.builder().userId(userId).status(2).deliveryMode(2).build());
        data.put("notShippedNum", notShippedNum);

        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(userId);
        data.put("nickName", byCustUser.getNickName());
        data.put("headImg", byCustUser.getHeadImg());
        ByCustUser byCustUser1 = this.byCustUserMapper.selectByPrimaryKey(userId);
        data.put("showRedCount",byCustUser1 == null ? 0: byCustUser1.getShowRedCount());
        data.put("showAppointmentCount",byCustUser1 == null||null==stringRedisTemplate.opsForValue().get(byCustUser1.getWxOpenId()+"user") ? 0: Integer.valueOf(stringRedisTemplate.opsForValue().get(byCustUser1.getWxOpenId()+"user")));
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)) {
            if (StringUtils.isEmpty(byCustUser.getMobile())) {
                data.put("type", 1);
            }
            if (StringUtil.isEmpty(byCustUser.getChildName())) {
                data.put("type", 2);
            }
        } else {
            data.put("type", 0);
        }
        int i = this.byTeamOrderMapper.selectCountList(userId);
        data.put("teamType",i == 0 ? false:true);

        return data;
    }

    /**
     * <AUTHOR>
     * @date 9:44 2019/5/14
     * @Description:申请售后
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void applySale(ByCustUser user, ByOrderAfter byOrderAfter) throws IOException {
        //查询是否存在售后申请
        Example example = new Example(ByOrderAfter.class);
        example.createCriteria().andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                //用户必须是当前登录的用户
                .andEqualTo("userId", user.getId())
                //普通商品
                .andEqualTo("resouceType", 1)
                //不能是已拒绝的订单
                .andNotEqualTo("afterStatus", 2);
        List<ByOrderAfter> sfOrderAfters = byOrderAfterMapper.selectByExample(example);
        if (sfOrderAfters.size() > 0) {
            throw new CustomException("商品正在售后中");
        }
        // 如果订单核销码已经过期，则不能申请售后
        Example example1 = new Example(WriteOffCode.class);
        example1.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo())
                .andLessThan("endDate", new Date());
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
        if (writeOffCodes.size() > 0) {
            throw new CustomException("订单核销码已过期，不能申请售后");
        }
        //获取商品信息
        ByOrderGoods amountGoods = this.saleDetail(byOrderAfter.getOrderGoodsId(), byOrderAfter.getOrderNo(), byOrderAfter.getAfterType());
        //封装售后订单信息
        byOrderAfter = this.packOrderAfter(byOrderAfter, amountGoods);
        //设置售后单的用户|所属人|申请人
        byOrderAfter.setUserId(user.getId());
        //新增售后申请
        int i = byOrderAfterMapper.insertSelective(byOrderAfter);
        if (i < 1) {
            throw new CustomException("申请失败,请稍后重试");
        }
        ByOrderGoods updateOrderGoods = new ByOrderGoods();
        updateOrderGoods.setId(amountGoods.getId());
        //设置状态为已经售后
        updateOrderGoods.setIsAfterSale(Boolean.TRUE);
        updateOrderGoods.setGmtUpdate(new Date());

        //更新订单商品状态
        int i1 = byOrderGoodsMapper.updateByPrimaryKeySelective(updateOrderGoods);

        ByOrders sfOrderInfo = new ByOrders();
        sfOrderInfo.setId(amountGoods.getOrderId());
        sfOrderInfo.setIsAfterSale(Boolean.TRUE);
        //更新订单状态
        int i2 = byOrdersMapper.updateByPrimaryKeySelective(sfOrderInfo);


    }

    /**
     * <AUTHOR>
     * @date 15:03 2019/5/14
     * @Description:封装售后订单信息
     */
    private ByOrderAfter packOrderAfter(ByOrderAfter byOrderAfter, ByOrderGoods amountGoods) {

        //创建时间
        byOrderAfter.setGmtCreate(new Date());
        //更新时间
        byOrderAfter.setGmtUpdate(new Date());
        //申请售后的商品名
        byOrderAfter.setGoodsName(amountGoods.getGoodsName());
        //申请售后的图片
        byOrderAfter.setGoodsImg(amountGoods.getGoodsImg());
        //申请售后的数量
        byOrderAfter.setGoodsNum(amountGoods.getGoodsNum());

        byOrderAfter.setGoodsPrice(amountGoods.getGoodsPrice());
        //被售后的订单号
        byOrderAfter.setOrderNo(amountGoods.getOrderNo());
        //退款金额
        byOrderAfter.setAfterAmount(amountGoods.getAllPrice());

        byOrderAfter.setOrderGoodsId(amountGoods.getGoodsId());
        byOrderAfter.setDetailId(amountGoods.getId());
        byOrderAfter.setResouceType(1);
        return byOrderAfter;
    }

    /**
     * <AUTHOR>
     * @date 10:53 2019/5/14
     * @Description:售后详情
     */
    @Autowired
    private WriteOffCodeMapper writeOffCodeMapper;

    /**
     *
     * @param id 订单商品id
     * @param orderNo 订单号
     * @param afterType 售后类型
     * @return
     */
    @Override
    public ByOrderGoods saleDetail(Integer id, String orderNo, Integer afterType) {
      /*
        1、若购买一个商品，此商品为联票/次卡，此商品若已核销任意一次，则不可售后；
        2、若购买一种商品，购买多个，已核销使用一个，申请售后时退款剩余的全部商品；
        3、核销一次后，可进行评价，每种商品仅可评价一次。若商品尚未被核销，则不可评价。
        4、若正在售后中，再次点击申请售后，提示商品正在售后中。
        */
        log.info("------------------订单服务层------------------------");
        ByOrderGoods sfOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(id);
        if (null == sfOrderGoods) {
            throw new CustomException("订单商品不存在");
        }
        //正在退款
        log.info("查询到的订单"+sfOrderGoods);
        if (sfOrderGoods.getIsRefund()!=null && sfOrderGoods.getIsRefund()){
            throw new CustomException("已经在退款中了");
        }

        ByOrders queryOrder = new ByOrders();
        //查询对应的订单
        ByOrders byOrders = this.byOrdersMapper.selectByPrimaryKey(sfOrderGoods.getOrderId());
        queryOrder.setOrderNo(byOrders.getOrderNo());
        ByOrders sfOrderInfo = byOrdersMapper.selectOne(queryOrder);
        if (null == sfOrderInfo) {
            //没有查询到某个订单
            throw new CustomException("订单信息异常");
        }
        Example example1 = new Example(ByOrderGoods.class);
        example1.createCriteria().andEqualTo("id", id);
        //查询订单商品
        List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
        if (byOrderGoods.size() == 0) {
            //订单商品不存在
            throw new CustomException("订单不存在");
        }

        Example example = new Example(WriteOffCode.class);
        example.createCriteria()
                .andEqualTo("custUserId", sfOrderInfo.getCurrentUserId() != null ? sfOrderInfo.getCurrentUserId() : sfOrderInfo.getUserId())
                .andEqualTo("detailId", id)
                .andEqualTo("status", 0)
//                .andNotEqualTo("status", 1)
                .andEqualTo("orderType", 0);
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
        if (writeOffCodes.size() == 0) {
            throw new CustomException("已使用商品,不可售后");
        }

        //应该是set 对应商品数量*价格 (扣除核销的)
        ByOrderGoods resultOrderGoods = byOrderGoods.get(0);
        Integer goodsCount = 0;
        for (WriteOffCode wc : writeOffCodes){
            //核销码没有使用
            if (wc.getTotalNum().equals(wc.getSurplusNum())){
                goodsCount++;
            }else if (wc.getIsExpire() != null && wc.getIsExpire()==1){
                goodsCount++;
            }
        }
        if(goodsCount == 0){
            throw new CustomException("已使用商品,不可售后");
        }
        BigDecimal multiply = resultOrderGoods.getGoodsPrice().multiply(new BigDecimal(goodsCount));
        log.info("商品单价："+resultOrderGoods.getGoodsPrice().toString());
        log.info("商品数量："+goodsCount.toString());
        log.info("商品原价*数量："+multiply.toString());
        //积分抵扣的金额
        BigDecimal deduction =  resultOrderGoods.getCouponPrice().add(resultOrderGoods.getIntegralPrice());
        log.info("优惠价优惠金额："+resultOrderGoods.getCouponPrice().toString());
        log.info("积分抵扣金额："+resultOrderGoods.getIntegralPrice().toString());
        log.info("总抵扣金额："+deduction);
        //如果订单商品数量和实际退款数量不一致，代表可能有商品已经核销了
        if (resultOrderGoods.getGoodsNum().compareTo(goodsCount)!=0){
            deduction = deduction.divide( BigDecimal.valueOf(resultOrderGoods.getGoodsNum()) ).multiply( BigDecimal.valueOf(goodsCount) );
        }
        log.info("重新计算的抵扣金额："+deduction);
        BigDecimal subtract = multiply.subtract(deduction).setScale(2,BigDecimal.ROUND_DOWN);
        log.info("退款金额："+subtract.toString());
        //减去积分和优惠券优惠的价格之后的金额，就是退款的金额
        sfOrderGoods.setAllPrice(subtract);
        sfOrderGoods.setGoodsNum(goodsCount);

        return sfOrderGoods;
    }


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void delOrder(ByCustUser user, Integer id) {
        ByOrders sfOrderInfo = byOrdersMapper.selectByPrimaryKey(id);
        if (null == sfOrderInfo) {
            throw new CustomException("订单信息异常");
        }
        //已完成 已关闭订单可以删除（包含已取消）
        if (sfOrderInfo.getOrderStatus().equals(CommonFinal.ONE) || sfOrderInfo.getOrderStatus().equals(CommonFinal.TWO)) {
            throw new CustomException("订单未完成,不能删除");
        }
        sfOrderInfo.setIsDel(Boolean.TRUE);
        sfOrderInfo.setGmtUpdate(new Date());
        int i = byOrdersMapper.updateByPrimaryKeySelective(sfOrderInfo);
        if (i < 1) {
            throw new CustomException("删除失败");
        }
    }

    @Autowired
    private ByTeamOrderMapper byTeamOrderMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;

    @Override
    public void applyTeam(ByCustUser user, ByOrderAfter byOrderAfter) {
        //查询是否存在售后申请
        Example example = new Example(ByOrderAfter.class);
        example.createCriteria().andEqualTo("orderGoodsId", byOrderAfter.getOrderGoodsId())
                .andEqualTo("userId", user.getId())
                .andEqualTo("orderNo", byOrderAfter.getOrderNo())
                .andEqualTo("resouceType", 2)
                .andNotEqualTo("afterStatus", 2);//已拒绝
        List<ByOrderAfter> sfOrderAfters = byOrderAfterMapper.selectByExample(example);
        if (null != sfOrderAfters && sfOrderAfters.size() > 0) {
            throw new CustomException("商品正在售后中");
        }
        Example example1 = new Example(ByTeamOrder.class);
        example1.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo());
        List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example1);
        if (byTeamOrders.size() == 0) {
            throw new CustomException("订单不存在");
        }

        ByTeamOrder byTeamOrder = byTeamOrders.get(0);

        ByTeamGoods byTeamGoods = byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
        byOrderAfter.setUserId(byTeamOrder.getUserId());
        byOrderAfter.setGoodsName(byTeamGoods.getTeamName());
        byOrderAfter.setOrderGoodsId(byTeamOrder.getTeamGoodsId());
        byOrderAfter.setAfterType(0);
        byOrderAfter.setAfterAmount(byTeamOrder.getPayAmount());
        byOrderAfter.setResouceType(2);
        byOrderAfter.setAfterStatus(0);
        byOrderAfter.setGoodsImg(byTeamGoods.getGoodsImg());
        byOrderAfter.setDetailId(byTeamOrder.getId());
        byOrderAfter.setGmtCreate(new Date());
        byOrderAfter.setGoodsNum(1);
        byOrderAfter.setOrderNo(byTeamOrder.getOrderNo());
        this.byOrderAfterMapper.insertSelective(byOrderAfter);
        byTeamOrder.setIsAfter(Boolean.TRUE);
        byTeamOrder.setGmtUpdate(new Date());
        this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
    }

    /**
     * 生成渠道订单
     * @param channelCust
     * @return
     */
    @Override
    public ByOrders generateChannelOrder(ByChannelCust channelCust) {
        ByChannelCust channel = this.byChannelCustMapper.selectByPrimaryKey(channelCust.getId());
        if(channel.getIsGenerateOrder()){
            throw new CustomException("渠道用户已生成订单");
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ByCustUser user = (ByCustUser) principal;

        String orderNo = UUIDOrder.getUUID();
        ByOrders byOrders = new ByOrders();
        byOrders.setIntegralAmount(BigDecimal.ZERO);
        byOrders.setCouponAmount(BigDecimal.ZERO);
        byOrders.setOrderAmount(BigDecimal.ZERO);
        byOrders.setUserId(user.getId());
        byOrders.setIsChannel(1);
        byOrders.setChannelId(channelCust.getId());
        byOrders.setOrderStatus(2);
        byOrders.setPayOrderNo(orderNo);
        byOrders.setActualAmount(BigDecimal.ZERO);
        byOrders.setGoodsType(1);
        byOrders.setEvalType(Boolean.FALSE);
        byOrders.setGmtUpdate(new Date());
        //渠道支付的为 2
        byOrders.setPayType(2);
        byOrders.setPayTime(new Date());
        byOrders.setIsDel(Boolean.FALSE);
        byOrders.setIsRefund(Boolean.FALSE);
        byOrders.setGmtCreate(new Date());
        byOrders.setOrderTime(new Date());
        byOrders.setOrderNo(orderNo);
        //订单保存到数据库
        byOrdersMapper.insertSelective(byOrders);

        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .id(byOrders.getId())
                        .orderNo(orderNo)
                        .operatorType(1)
                        .logType(OrderLogType.CREATE)
                        .build()
        );

        ByOrderGoods byOrderGoods = new ByOrderGoods();
        byOrderGoods.setOrderNo(byOrders.getOrderNo());
        byOrderGoods.setOrderId(byOrders.getId());
        //门票商品
        BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(channelCust.getGoodsId());
        if (bySubCardGoods.getGoodsStock() < 1) {
            throw new CustomException("库存不足");
        }

        /*减少库存*/
        Integer count = bySubCardGoods.getGoodsStock() - 1;
        bySubCardGoods.setGoodsStock(count);
        this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);

        // 去掉预充值套餐，直接在首次核销时进行赠送-消费金
        //如果选择了油菜花预充值套餐
        // if(ObjectUtil.isNotEmpty(bySubCardGoods.getPreDepositId())){
        //     YchGoods query = new YchGoods();
        //     query.setId(bySubCardGoods.getPreDepositId());
        //     YchGoods ychGoods = ychApiService.getGoodsDetail(query);
        //     ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
        // }

        byOrderGoods.setGoodsId(bySubCardGoods.getId());
        byOrderGoods.setGoodsImg(bySubCardGoods.getGoodsImg());
        byOrderGoods.setGoodsName(bySubCardGoods.getGoodsName());
        byOrderGoods.setGoodsPrice(bySubCardGoods.getSellPrice());
        byOrderGoods.setGoodsNum(1);
        byOrderGoods.setGmtCreate(new Date());
        byOrderGoods.setOrderStatus(0);
        byOrderGoods.setProductId(bySubCardGoods.getId().toString());
        byOrderGoods.setProductType("2");
        byOrderGoods.setIntegralPrice(BigDecimal.ZERO);
        byOrderGoods.setCouponPrice(BigDecimal.ZERO);
        byOrderGoods.setProductCount(1);
        this.byOrderGoodsMapper.insertSelective(byOrderGoods);


        Example example = new Example(BySubGoodsStore.class);
        example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId());
        List<BySubGoodsStore> bySubGoodsStores = bySubGoodsStoreMapper.selectByExample(example);
        String str = "";
        if (bySubGoodsStores.size() != 0) {
            for (BySubGoodsStore bySubGoodsStore : bySubGoodsStores) {
                str += bySubGoodsStore.getStoreId() + ",";
            }
            str = str.substring(0, str.length() - 1);
        }

        /*次卡*/
        for (int i = 0; i < byOrderGoods.getGoodsNum(); i++) {
            WriteOffCode writeOffCode = new WriteOffCode();
            if (str != "") {
                writeOffCode.setStoreIds(str);
            }
            writeOffCode.setDetailId(byOrderGoods.getId());
            writeOffCode.setWriteOffName(bySubCardGoods.getSubCardGoodsName());
            writeOffCode.setCustUserId(user.getId());
            writeOffCode.setGoodsName(bySubCardGoods.getSubCardGoodsName());
            writeOffCode.setSourceGoodsId(bySubCardGoods.getId());
            writeOffCode.setSurplusNum(channel.getUsableNum());
            writeOffCode.setTotalNum(channel.getUsableNum());
            writeOffCode.setOrderState(0);
            writeOffCode.setOrderType(0);

            if(ObjectUtil.isNotEmpty(bySubCardGoods.getHasVerificationDay()) && bySubCardGoods.getHasVerificationDay()){
                Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                writeOffCode.setExpiryDate(date);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                // 这里是加天数
                calendar.add(Calendar.DAY_OF_MONTH, bySubCardGoods.getVerificationDay());
                writeOffCode.setEndDate(calendar.getTime());
            }else{
                writeOffCode.setExpiryDate(bySubCardGoods.getEffectiveStart());
                writeOffCode.setEndDate(bySubCardGoods.getEffectiveEnd());
            }
            writeOffCode.setOrderNo(byOrders.getOrderNo());
            writeOffCode.setGmtCreate(new Date());
            writeOffCode.setType(2);
            writeOffCode.setIsExpire(0);
            this.writeOffCodeMapper.insertSelective(writeOffCode);
        }

        channelCust.setIsGenerateOrder(Boolean.TRUE);
        channelCust.setOrderId(byOrders.getId());
        channelCust.setCustId(user.getId());
        channelCust.setGmtModified(new Date());
        byChannelCustMapper.updateByPrimaryKey(channelCust);

//        ByCustUser custUser = byCustUserMapper.selectByPrimaryKey(user.getId());
//        if(!ObjectUtil.isEmpty(custUser) && ObjectUtil.isEmpty(custUser.getMobile())){
//            custUser.setMobile(channelCust.getMobile());
//            byCustUserMapper.updateByPrimaryKey(custUser);
//        }

        payService.updateChannel(orderNo);

        return byOrders;
    }
}
