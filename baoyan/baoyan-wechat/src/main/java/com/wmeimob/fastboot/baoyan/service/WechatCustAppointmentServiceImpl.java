package com.wmeimob.fastboot.baoyan.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.mapper.BaseStoreMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByCustAppointmentMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.util.MessageUtil;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import com.wmeimob.fastboot.util.DateUtil;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wechat.core.Wechat;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByCustAppointmentServiceImpl
 * @Description 客户预约表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 09 14:34:33 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WechatCustAppointmentServiceImpl implements WechatCustAppointmentService {

    @Resource
    private ByCustAppointmentMapper byCustAppointmentMapper;
    @Resource
    private MessageUtil messageUtil;
    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private BaseStoreMapper baseStoreMapper;
    @Resource
     private WechatService wechatService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public List<ByCustAppointmentVO> custApponintByUserId(Integer userId,Integer apponintmentStatus) {
        Map<String,Object>queryMap= Maps.newHashMap();
        queryMap.put("userId", userId);
        queryMap.put("apponintmentStatus", apponintmentStatus);
        List<ByCustAppointmentVO> byCustAppointmentVOS = byCustAppointmentMapper.custApponintByUserId(queryMap);
        return byCustAppointmentVOS;
    }

    @Override
    public RestResult cancelAppointment(Integer id,String fromId) {
        if (StringUtils.isEmpty(fromId)) throw new CustomException("参数有误");
        ByCustAppointment resultApp = byCustAppointmentMapper.selectByPrimaryKey(id);
        if (null == resultApp) {
            throw new CustomException("当前预约记录不存在");
        }
        if (!resultApp.getApponintmentStatus().equals(0)) {
            throw new CustomException("当前预约状态已改变");
        }
        ByCustAppointment appointment = new ByCustAppointment();
        appointment.setId(id);
        appointment.setApponintmentStatus(1);
        appointment.setGmtModified(new Date());
        int num = byCustAppointmentMapper.updateByPrimaryKeySelective(appointment);
        ByCustUser user = SecurityContext.getUser();
        /*消息模板推送*/
        if (num > 0){
            ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
            BaseStore baseStore = this.baseStoreMapper.selectByPrimaryKey(resultApp.getStoreId());
            Map<String, String> map = Maps.newHashMap();
            map.put("keyword1", resultApp.getProductName());
            map.put("keyword2", baseStore.getName());
            map.put("keyword3", DateUtils.formatDate(new Date(),"yyyy-MM-dd hh:mm:ss"));
            Wechat apiComponent = wechatService.getApiComponent();
            messageUtil.sendWxTemplateMsg(map,byCustUser.getWxOpenId(),"ifobGUhWjz_0O6Ccc1W85mxgAfk7m521zr_7sdqRQRs",fromId,null);
        }
        if (num > 0){
           try {
               Integer count = Integer.valueOf( stringRedisTemplate.opsForValue().get(user.getWxOpenId() + "user"));
               if (count != 0){
                    stringRedisTemplate.opsForValue().set(user.getWxOpenId()+"user",String.valueOf(count+1));
               }
           }catch (Exception e){
               stringRedisTemplate.opsForValue().set(user.getWxOpenId()+"user","0");
           }
        }
        return num > 0 ? RestResult.success() : RestResult.fail("取消预约失败");
    }

    @Override
    public RestResult removeForByAppointment(Integer id) {
        ByCustAppointment appointment = new ByCustAppointment();
        appointment.setId(id);
        appointment.setIsDel(true);
        int delNum = byCustAppointmentMapper.updateByPrimaryKeySelective(appointment);
        if (delNum <= 0) {
            return RestResult.fail("删除失败");
        }
        return RestResult.success();
    }
    @Override
    public ByCustAppointmentVO getDetailByAppointmentId(Integer id) {
        Map<String,Object>queryMap= Maps.newHashMap();
        queryMap.put("id", id);
       List<ByCustAppointmentVO> list=byCustAppointmentMapper.custApponintByUserId(queryMap);
        if (null!=list&&list.size()>0 ) {
            ByCustAppointmentVO vo= list.get(0);
            if (null!=vo.getChildSex()) {
                vo.setChildSexStr(vo.getChildSex().equals(0)?"男":"女");
            }
            if (null!=vo.getTicketChannel()) {
                vo.setTicketChannelName(vo.getTicketChannel().equals(0)?"小程序":"美团");
            }
            if (null!=vo.getApponintmentStatus()) {
                switch (vo.getApponintmentStatus()){
                    case 0:
                        vo.setApponintmentStatusStr("已预约");
                        break;
                    case 1:
                        vo.setApponintmentStatusStr("已取消");
                        break;
                    case 2 :
                        vo.setApponintmentStatusStr("已结束");
                        break;
                }

            }
            return  vo;
        }
       return null;
    }

}
