package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.mapper.BaseStoreMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.ByGoodsShoppingService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * 淘潮玩商品
 * @author: wangShun
 * CreationDate:2021/7/23
 * packageName:com.wmeimob.fastboot.baoyan.service.tc
 */

@Service("WxTcGoodsServiceImpl")
@Slf4j
public class WxTcGoodsServiceImpl implements TcGoodsService
{
    @Resource
    private TcGoodsMapper tcGoodsMapper;

    @Resource
    private ByGoodsShoppingService byGoodsShoppingService;

    @Resource
    private BaseStoreMapper baseStoreMapper;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reduceStock(Integer id, Integer change) {
        TcGoods tcGoods = tcGoodsMapper.queryById(id);
        //商品不存在 或者 下架
        if ( tcGoods==null || !tcGoods.getIsDeleted() ){
            throw new CustomException("商品不存在或已下架——"+id);
        }

        //数量不足
        if ( tcGoods.getStock()+change < 0 ){
            throw new CustomException("商品库存不足");
        }

        //改变数量
        tcGoodsMapper.reduceStock(id, change);


        return true;
    }

    @Override
    public TcGoods queryById(Integer id) {
        if (id == null){
            return null;
        }
        //查询商品
        TcGoods tcGoods = tcGoodsMapper.wxQueryById(id);

        //可以使用的门店
        List<BaseStore> stores = baseStoreMapper.findStoreByTcId(tcGoods.getId());
        tcGoods.setBaseStores(stores);

        //购物车商品数量
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = SecurityContext.getUser();
            //查询购物车数量
            int cartCount = byGoodsShoppingService.cartCount(user.getId());

            tcGoods.setCartCount( cartCount );
        }

        return tcGoods;
    }

    /**
     *
     * @param queryObject 查询对象
     * @return 结果集
     */

    @Override
    public List<TcGoods> queryPage(TcGoods queryObject) {
        return tcGoodsMapper.wxQueryAll(loadQueryObject(queryObject));
    }

    private TcGoods loadQueryObject(TcGoods queryObject){
        if (null != queryObject) {
            queryObject.setMaxPrice(
                    queryObject.getMaxPrice() == null ?
                            Double.MAX_VALUE : queryObject.getMaxPrice());
            queryObject.setMinPrice(
                    queryObject.getMinPrice() == null ?
                            0 : queryObject.getMinPrice());
            if (null != queryObject.getGmtCreate()) {
                Date gmtCreate = queryObject.getGmtCreate();
                Calendar c = Calendar.getInstance();
                c.setTime(gmtCreate);
                c.add(Calendar.DAY_OF_MONTH, 1);
                queryObject.setGmtCreateEnd(c.getTime());
            }
        }
        return queryObject;
    }

    @Override
    public Long queryAllCount(TcGoods queryObject){
        return tcGoodsMapper.wxQueryAllCount(loadQueryObject(queryObject));
    }


}
