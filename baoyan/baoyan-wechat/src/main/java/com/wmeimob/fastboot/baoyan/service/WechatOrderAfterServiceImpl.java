package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ByOrderAfterServiceImpl
 * @Description 订单售后 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WechatOrderAfterServiceImpl implements WechatOrderAfterService {

    @Resource
    private ByOrderAfterMapper byOrderAfterMapper;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Autowired
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Autowired
    private ByTeamOrderMapper byTeamOrderMapper;
    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Autowired
    private ByOrdersMapper byOrdersMapper;
    @Autowired
    private ByGoodsInfoMapper ByGoodsInfoMapper;
    @Autowired
    private ByCombinationGoodsMapper byCombinationGoodsMapper;

    @Override
    public List<ByOrderAfter> findByCondition(ByOrderAfter byOrderAfter) {
        List<ByOrderAfter> byOrderAfters = byOrderAfterMapper.queryAfterByCondition(byOrderAfter);
        for (ByOrderAfter orderAfter : byOrderAfters) {
            ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(orderAfter.getDetailId());
            if ( "1".equals(byOrderGoods.getProductType()) ){
                ByGoodsInfo goodsInfo = ByGoodsInfoMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                if (!StringUtils.isEmpty(goodsInfo.getSpecName())){
                    orderAfter.setGoodsName(goodsInfo.getGoodsName()+"（"+goodsInfo.getSpecName()+"）");
                }
            }else if ("5".equals(byOrderGoods.getProductType())){
                ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods.getProductId());
                ByGoodsInfo goodsInfo = ByGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                orderAfter.setGoodsName(goodsInfo.getGoodsName()+"（"+combinationGoods.getName()+"）");
            }
        }
        return byOrderAfters;
    }

    @Override
    public ByOrderAfter queryByOrderAfterById(Integer id) {
        ByOrderAfter byOrderAfter = byOrderAfterMapper.selectByPrimaryKey(id);
        ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
        if ("1".equals(byOrderGoods.getProductType())){
            ByGoodsInfo goodsInfo = ByGoodsInfoMapper.selectByPrimaryKey(byOrderGoods.getProductId());
            if (!StringUtils.isEmpty(goodsInfo.getSpecName())){
                byOrderAfter.setGoodsName(goodsInfo.getGoodsName()+"（"+goodsInfo.getSpecName()+"）");
            }
        }else if ("5".equals(byOrderGoods.getProductType())){
            ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods.getProductId());
            ByGoodsInfo goodsInfo = ByGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
            byOrderAfter.setGoodsName(goodsInfo.getGoodsName()+"（"+combinationGoods.getName()+"）");
        }
        if (byOrderAfter.getResouceType() == 1) {
            byOrderAfter.setOrderGoodsId(byOrderGoods.getGoodsId());
            byOrderAfter.setProductType("1".equals(byOrderGoods.getProductType()) ? 1 : "2".equals(byOrderGoods.getProductType()) ? 3 : 2);
        } else {
            byOrderAfter.setProductType(4);
            ByTeamOrder byTeamOrder = byTeamOrderMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
            byOrderAfter.setOrderGoodsId(byTeamOrder.getTeamGoodsId());
        }
        return byOrderAfter;
    }

    /**
     * 查询该订单可以退多少件，退多少钱
     * @param byOrderAfter
     * @return
     */
    @Override
    public OrderAfterDetailVO queryAfterByOrderIdAndGoodsId(ByOrderAfter byOrderAfter) {
        byOrderAfter.setResouceType( byOrderAfter.getResouceType() + 1 );
        ByCustUser user = SecurityContext.getUser();
        if (byOrderAfter.getOrderGoodsId() == null || byOrderAfter.getResouceType() == null)
            throw new CustomException("参数错误");
        Example example = new Example(ByOrderAfter.class);
        example.createCriteria().andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                .andNotEqualTo("afterStatus", 2)
                .andEqualTo("resouceType", byOrderAfter.getResouceType())
                .andEqualTo("userId", user.getId());
        List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example);
        if (byOrderAfters.size() > 0){
            throw new CustomException("商品正在售后中");
        }

        /*普通商品*/
        OrderAfterDetailVO orderAfterDetailVO = new OrderAfterDetailVO();
        if (byOrderAfter.getResouceType() == 1) {
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                    .andEqualTo("custUserId", user.getId())
                    .andEqualTo("orderType", 0)
                    .andEqualTo("orderState", 0);
            //没有核销码就代表没有可售后
            List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
//            if (writeOffCodes.size() == 0) throw new CustomException("商品正在售后中");
            if (CollectionUtils.isEmpty(writeOffCodes)) throw new CustomException("暂无商品可售后1");
            // 商品是否已经核销  已核销不能申请售后
       /*     WriteOffCode writeOffCode2 = writeOffCodes.get(0);
            if (writeOffCode2.getTotalNum().equals(writeOffCode2.getSurplusNum()) ==  false){
                throw new CustomException("商品已核销过暂不能售后");
            }*/
            orderAfterDetailVO.setResouceType(1);
            ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getOrderGoodsId());
            orderAfterDetailVO.setProductType(byOrderAfter.getProductType());
            orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getGoodsId());
            //设置商品单价
            orderAfterDetailVO.setGoodsPrice(byOrderGoods.getGoodsPrice());
            ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(byOrderGoods.getOrderId());
            if (byOrders == null || byOrders.getActualAmount().compareTo(new BigDecimal(0)) == 0) {
                throw new CustomException("该商品不能售后");
            }
            //判断商品是否 被使用核销
            Example example2 = new Example(WriteOffCode.class);
            example2.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                    .andEqualTo("custUserId", user.getId())
//                    .andEqualTo("status", 0)
                    .andNotEqualTo("status",1)
                    .andEqualTo("orderType", byOrderAfter.getResouceType() == 1 ? 0 : 1);
            List<WriteOffCode> writeOffCode = this.writeOffCodeMapper.selectByExample(example2);
            //if (writeOffCode.size() == 0) throw new CustomException("已使用商品,不可售后");
            if (CollectionUtils.isEmpty(writeOffCode)) throw new CustomException("暂无商品可售后2");
            Integer integer = this.writeOffCodeMapper.selectCountList(byOrderAfter.getOrderGoodsId());
            if (integer != null && integer.equals(byOrderGoods.getGoodsNum())) {
                throw new CustomException("已使用商品,不可售后");
            }
            Integer goodsCount = 0;
            for (WriteOffCode writeOffCode1 : writeOffCode){
                if (writeOffCode1.getTotalNum().equals(writeOffCode1.getSurplusNum())){
                    goodsCount++;
                }else if (writeOffCode1.getIsExpire() != null && writeOffCode1.getIsExpire()==1){
                    goodsCount++;
                }
            }
            if(goodsCount == 0){
                throw new CustomException("已使用商品,不可售后");
            }



            if ("1".equals(byOrderGoods.getProductType())) {
                Integer orderCount = byOrderGoods.getGoodsNum();
                byOrderGoods.setGoodsNum(goodsCount);
                //金额比
                BigDecimal proportion = new BigDecimal(orderCount).divide(new BigDecimal(orderCount),2,BigDecimal.ROUND_HALF_DOWN);
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(byOrderGoods.getGoodsNum()));
                BigDecimal proportionCoupon = byOrderGoods.getCouponPrice().multiply(proportion);
                BigDecimal subtract = multiply.subtract(proportionCoupon);
                BigDecimal proportIntegral = byOrderGoods.getIntegralPrice().multiply(proportion);
                BigDecimal subtract1 = subtract.subtract(proportIntegral);
                BigDecimal divide = subtract1;
                orderAfterDetailVO.setAfterAmount(divide);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(byOrderGoods.getGoodsNum());
                orderAfterDetailVO.setOrderNo(byOrderGoods.getOrderNo());
            }
            if (!"1".equals(byOrderGoods.getProductType())) {
                byOrderGoods.setGoodsNum(goodsCount);
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(goodsCount));
                BigDecimal subtract = multiply.subtract((byOrderGoods.getCouponPrice().add(byOrderGoods.getIntegralPrice())));
                orderAfterDetailVO.setAfterAmount(subtract);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(byOrderGoods.getGoodsNum());
                orderAfterDetailVO.setOrderNo(byOrderGoods.getOrderNo());

            }
        }
        if (byOrderAfter.getResouceType() == 2) {
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                    .andEqualTo("custUserId", user.getId())
                    .andNotEqualTo("status", 0)
                    .andEqualTo("orderType", 1)
                    .andEqualTo("orderState", 0);
            List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
            if (writeOffCodes.size() != 0) throw new CustomException("商品已核销，不可通过售后申请");

            orderAfterDetailVO.setProductType(4);
            Example example11 = new Example(ByOrderAfter.class);
            example11.createCriteria().andEqualTo("detailId", byOrderAfter.getOrderGoodsId()).andEqualTo("resouceType", 2).andEqualTo("afterStatus", 0);
            List<ByOrderAfter> byOrderAfters1 = this.byOrderAfterMapper.selectByExample(example11);
            if (byOrderAfters1.size() != 0) throw new CustomException("商品正在售后中");


            ByTeamOrder byTeamOrder = byTeamOrderMapper.selectByPrimaryKey(byOrderAfter.getOrderId());
            if (byTeamOrder.getPayAmount().compareTo(new BigDecimal(0)) == 0) {
                throw new CustomException("该商品不能售后");
            }
            orderAfterDetailVO.setAfterAmount(byTeamOrder.getPayAmount());
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
            orderAfterDetailVO.setGoodsImg(byTeamGoods.getGoodsImg());
            orderAfterDetailVO.setGoodsName(byTeamGoods.getTeamName());
            orderAfterDetailVO.setGoodsNum(writeOffCodes.size());
            orderAfterDetailVO.setOrderGoodsId(byTeamGoods.getId());
            orderAfterDetailVO.setOrderNo(byTeamOrder.getOrderNo());
            orderAfterDetailVO.setResouceType(byOrderAfter.getResouceType());
            orderAfterDetailVO.setGoodsNum(1);
        }
        //判断当前商品是否可以售后
        return orderAfterDetailVO;
    }

    /**
     * 判断
     *
     * @param byOrderAfter
     */
    private void checkOrderGoodsAfter(ByOrderAfter byOrderAfter) {
        if (byOrderAfter.getAfterType() == 1) {
            //判断当前商品是否是 联票或者次卡商品是否有被消费过
            if (byOrderAfter.getProductType().equals(1)) {
                //普通商品
                //查询是否存在售后申请
                Example example = new Example(ByOrderAfter.class);
                example.createCriteria().andEqualTo("orderGoodsId", byOrderAfter.getOrderGoodsId())
                        .andEqualTo("userId", byOrderAfter.getUserId())
                        .andEqualTo("orderNo", byOrderAfter.getOrderNo())
                        .andEqualTo("resouceType", 1)
                        .andNotEqualTo("afterStatus", 2);//已拒绝
                List<ByOrderAfter> sfOrderAfters = byOrderAfterMapper.selectByExample(example);
                if (null != sfOrderAfters && sfOrderAfters.size() > 0) {
                    throw new CustomException("商品正在售后中");
                }

            } else {
                //拼团联票下面关联的商品或者次卡 是否有被使用过
              /*  Example example = new Example(WriteOffCode.class);
                example.createCriteria().andEqualTo("orderType", 0)
                    .andEqualTo("orderNo", byOrderAfter.getOrderNo());*/

                //writeOffCodeMapper.queryOrderWriteCodeByCondition();
            }

        } else {
            //拼团
        }
    }

    @Override
    public RestResult deleteOrderAfter(ByOrderAfter byOrderAfter) {
        if (null == byOrderAfter || null == byOrderAfter.getId()) {
            return RestResult.fail("请效验参数");
        }
        ByOrderAfter after = byOrderAfterMapper.selectByPrimaryKey(byOrderAfter.getId());
        if (null == after) {
            return RestResult.fail("售后订单不存在");
        }
        if (after.getAfterStatus().equals(0)) {
            return RestResult.fail("当前售后订单不能删除");
        }
        byOrderAfter.setIsDel(1);
        byOrderAfter.setGmtUpdate(new Date());
        int i = byOrderAfterMapper.updateByPrimaryKeySelective(byOrderAfter);
        if (i > 0) {
            return RestResult.success();
        }
        return RestResult.fail("删除失败");
    }
}
