package com.wmeimob.fastboot.baoyan.service.tc;
import com.wmeimob.fastboot.baoyan.entity.TcBanner;
import com.wmeimob.fastboot.baoyan.mapper.TcBannerMapper;
import com.wmeimob.fastboot.baoyan.service.TcBannerService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service("wxTcBannerServiceImpl")
public class WxTcBannerServiceImpl implements TcBannerService {
    @Resource
    private TcBannerMapper tcBannerMapper;

    @Override
    public List<? extends TcBanner> wxQueryAll() {
        Example example = new Example(TcBanner.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", 1);
        criteria.andEqualTo("isDel", 0);
        example.orderBy("sort").asc();
        return tcBannerMapper.selectByExample(example);
    }
}
