package com.wmeimob.fastboot.baoyan.controller.apponintment;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.WechatCustAppointmentService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @title: ApponintController
 * @projectName baoyan
 * @description: 预约
 * @date 2019/8/5 11:30
 */
@RestController
@RequestMapping("custApp")
@Slf4j
public class CustApponintController {

    @Resource
    private WechatCustAppointmentService wechatCustAppointmentService;
    @Resource
    private WxCustUserService wxCustUserService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 我的预约
     *
     * @return
     */
    @GetMapping("/list")
    public PageInfo custApponintByUserId(Integer apponintmentStatus) {
        PageContext.startPage();
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        stringRedisTemplate.opsForValue().set(user.getWxOpenId() + "user", "0");
        return new PageInfo<ByCustAppointmentVO>(wechatCustAppointmentService.custApponintByUserId(user.getId(), apponintmentStatus));
    }

    /**
     * 预约详情查询
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCustAppointmentVO getDetailByAppointmentId(HttpServletRequest request, @PathVariable("id") Integer id) {
        return wechatCustAppointmentService.getDetailByAppointmentId(id);
    }

    /**
     * 取消预约
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/cancel")
    public RestResult cancelAppointment(HttpServletRequest request, Integer id, String fromId) {
        log.info("取消 预约 id: {},fromId:{}", id, fromId);
        return wechatCustAppointmentService.cancelAppointment(id, fromId);
    }

    /**
     * 预约删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public RestResult removeForByAppointment(HttpServletRequest request, @PathVariable("id") Integer id) {
        log.info("预约删除 id: {}", id);
        return wechatCustAppointmentService.removeForByAppointment(id);
    }


}
