package com.wmeimob.fastboot.baoyan.controller.IntegralLog;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.WechatIntegralLogService;
import com.wmeimob.fastboot.baoyan.vo.IntegraLogVO;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @title: IntegralLogController
 * @projectName baoyan
 * @description: 积分记录
 * @date 2019/8/5 14:06
 */
@RestController
@RequestMapping("integraLog")
@Slf4j
public class IntegralLogController {

    @Resource
    private WechatIntegralLogService wechatIntegralLogService;


    /**
     * 我的积分记录
     * @param request
     * @return
     */
    @GetMapping("/list")
    public IntegraLogVO queryIntegraLogByByUserId(HttpServletRequest request){
        ByCustUser user  = (ByCustUser) SecurityContext.getUser();
        return wechatIntegralLogService.queryIntegraLogByByUserId(user.getId());
    }


}
