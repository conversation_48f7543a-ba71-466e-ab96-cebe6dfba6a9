package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.mapper.ByClassifyRecommendMapper;
import com.wmeimob.fastboot.baoyan.service.ByClassifyRecommendService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (ByClassifyRecommend)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:53
 */
@Service("wxByClassifyRecommendService")
public class WxByClassifyRecommendServiceImpl implements ByClassifyRecommendService {
    @Resource
    private ByClassifyRecommendMapper byClassifyRecommendMapper;

    @Override
    public List<ByClassifyRecommend> queryAll(ByClassifyRecommend queryItem) {
        return byClassifyRecommendMapper.wxQueryAll();
    }
}