package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.mapper.TcRecommendMapper;
import com.wmeimob.fastboot.baoyan.service.TcRecommendService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/8/2
 * packageName:com.wmeimob.fastboot.baoyan.service.tc
 */

@Service("wxTcRecommendServiceImpl")
public class WxTcRecommendServiceImpl implements TcRecommendService {
    @Resource
    private TcRecommendMapper tcRecommendMapper;


    @Override
    public TcRecommend queryById(Integer id) {
        if (null == id) return null;
        return tcRecommendMapper.wxQueryById(id);
    }

    @Override
    public List<TcRecommend> queryAll(TcRecommend tcRecommend) {
        return tcRecommendMapper.wxQueryAll(tcRecommend);
    }
}
