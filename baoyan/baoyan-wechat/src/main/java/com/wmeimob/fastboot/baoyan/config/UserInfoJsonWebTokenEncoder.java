package com.wmeimob.fastboot.baoyan.config;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.starter.security.interfaces.JsonWebTokenEncoder;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-05-02 10:25
 * @Description: 加密
 */
public class UserInfoJsonWebTokenEncoder implements JsonWebTokenEncoder {

    @Override
    public Map<String, Object> encode(UserDetails userDetails) {

        ByCustUser userInfo = (ByCustUser) userDetails;
        Map<String,Object> claims = new HashMap<>(4);
        claims.put("id", userInfo.getId());
        claims.put("nickname",userInfo.getNickName());
        claims.put("headimgurl",userInfo.getHeadImg());
        claims.put("openid",userInfo.getWxOpenId());
        claims.put("mobile",userInfo.getMobile());
        return claims;
    }
}
