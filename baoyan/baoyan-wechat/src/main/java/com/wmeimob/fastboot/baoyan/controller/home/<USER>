package com.wmeimob.fastboot.baoyan.controller.home;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.service.WriteService;
import com.wmeimob.fastboot.baoyan.ws.WebSocketServer;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 核销
 * @date 2019-08-22 10:11
 * @Version 1.0
 */
@RestController
@RequestMapping("write")
@Slf4j
public class WriteController {

    @Autowired
    private WriteService writeService;

    /**
     * @Description 查看核销
     * <AUTHOR>
     * @Date 2019-08-22 10:13
     * @Version 1.0
     */
    @GetMapping("checkWrite")
    public List<WriteOffCode>
    checkWrite(HttpServletResponse resp, String orderNo, Integer type) {
        log.info("查看核销checkWrite()  orderNo: {},type: {}", orderNo, type);
        return this.writeService.checkWrite(resp, orderNo, type);
    }

    /***
     * 核销详情
     *
     * */
    @GetMapping("check")
    public Map<String, Object> checkWrit(Integer id) {
        ByCustUser user = (ByCustUser) SecurityContext.getUser();
        log.info("核销详情 checkWrit()  id: {},userId:{}", id, user.getId());
        return this.writeService.check(id, user);
    }

    /**
     * @Description 核销门票
     * <AUTHOR>
     * @Date 2019-08-23 11:43
     * @Version 1.0
     */
    @GetMapping("writeGoods")
    public WriteOffCodeLog writeGoods(Integer id, Integer count) {
        log.info("核销门票writeGoods()  id: {},count: {}", id, count);
        if (id == null || count == null) {
            throw new CustomException("参数有误");
        }
        return this.writeService.writeGoods(id, count);
    }

    @Autowired
    private WebSocketServer socketServer;

    @GetMapping("test")
    public String test() {
        socketServer.sendInfo("123","成功");
        return "核销成功";
    }



}
