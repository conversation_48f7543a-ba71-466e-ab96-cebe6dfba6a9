package com.wmeimob.fastboot.baoyan.controller.home;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import com.wmeimob.fastboot.baoyan.mapper.ByCombinationGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.GoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 商品
 * @date 2019-08-07 17:45
 * @Version 1.0
 */
@RestController
@RequestMapping("goods")
@Slf4j
public class GoodsController {

    @Resource
    private GoodsService goodsService;

    /**
    * @Description 普通商品
    * <AUTHOR>
    * @Date        2019-08-07 17:47
    * @Version    1.0
    */
    /**
     * 商品类型 1  普通 2 次卡 3  联票 5 规格
     *跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品）
     * */
    @GetMapping("detail")
    public Map<String,Object> detail(Integer id,Integer type){
        if (type == 5 || type == 10){
            return this.goodsService.combinationGoods(id);
        }
        if (type == 1 || type == 6){
            return this.goodsService.detail(id);
        }
        if (type == 3 || type == 8){
            return this.goodsService.cardGoods(id);
        }
        return this.goodsService.votingGoods(id);
    }

    /**
    * @Description 评价列表
    * <AUTHOR>
    * @Date        2019-08-13 11:26
    * @Version    1.0
    */
    @GetMapping("evaluate")
    public PageInfo evaluate(Integer id, Integer type, Integer pageIndex, Integer pageSize){
        return this.goodsService.evaluate(id,type,pageIndex,pageSize);
    }
}
