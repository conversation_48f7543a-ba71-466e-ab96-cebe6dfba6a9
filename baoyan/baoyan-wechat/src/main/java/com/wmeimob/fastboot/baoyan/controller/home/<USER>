package com.wmeimob.fastboot.baoyan.controller.home;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.UserService;
import com.wmeimob.fastboot.core.rest.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 用户信息
 * @date 2019-08-23 17:30
 * @Version 1.0
 */
@RestController
@RequestMapping("user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    /**
    * @Description 获取用户信息
    * <AUTHOR>
    * @Date        2019-08-23 17:36
    * @Version    1.0
    */
    @GetMapping("user")
    public ByCustUser getUser(){
        return this.userService.getUser();
    }


    /**
     * 新增用户小红点
     */
    @GetMapping("showOrderRedDot")
    public void showRetDot(){
        this.userService.showRed();
    }


}
