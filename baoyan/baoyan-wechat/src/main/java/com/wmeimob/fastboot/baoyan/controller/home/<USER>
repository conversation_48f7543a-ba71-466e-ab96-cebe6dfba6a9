package com.wmeimob.fastboot.baoyan.controller.home;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsShoping;
import com.wmeimob.fastboot.baoyan.entity.CartIds;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import com.wmeimob.fastboot.baoyan.service.ByGoodsShoppingService;
import com.wmeimob.fastboot.baoyan.service.TcGoodsShopingService;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 购物车
 * @date 2019-08-13 13:43
 * @Version 1.0
 */
@RestController
@RequestMapping("shop")
@Slf4j
public class ByGoodsShoppingController {


    @Autowired
    private ByGoodsShoppingService goodsShoppingService;

    @Resource
    private TcGoodsShopingService tcGoodsShopingService;

    /**
     * 展示用户购物车里的门票和淘潮玩商品
     * @return
     */
    @GetMapping("showAll")
    public HashMap<String,List> showAll(){
        List<ByGoodsShoping> goodsShop = this.goodsShoppingService.show();
        List<TcGoodsShoping> tcGoodsShop = this.tcGoodsShopingService.show();

        HashMap<String, List> cartMap = new HashMap<>(2);
        cartMap.put("ticket",goodsShop);
        cartMap.put("taocao",tcGoodsShop);

        return cartMap;
    }

    /**
    * @Description 我的购物车 门票
    * <AUTHOR>
    * @Date        2019-08-13 13:47
    * @Version    1.0
    */
    @GetMapping("showShop")
    public List<ByGoodsShoping> showShop(){
        return this.goodsShoppingService.show();
    }

    /**
     * 查看用户的淘潮玩购物车
     * @return
     */
    @GetMapping("showTcShop")
    public List<TcGoodsShoping> showTcShop(){
        return this.tcGoodsShopingService.show();
    }


    /**
     * 添加淘潮玩购物车
     * @param tcGoodsShoping
     * <AUTHOR>
     * @return
     */
    @PostMapping("/insertTc")
    public RestResult insertTc(@RequestBody TcGoodsShoping tcGoodsShoping){
        InputValidator.checkEmpty(tcGoodsShoping.getGoodsId(),"请选择商品");
        InputValidator.checkEmpty(tcGoodsShoping.getGoodsCount(),"选择购买数量");

        boolean flag = tcGoodsShopingService.addCart(tcGoodsShoping);
        return RestResult.msg(200,"添加成功");
    }

    /**
    * @Description 添加购物车
    * <AUTHOR>
    * @Date        2019-08-13 13:49
    * @Version    1.0
    */
    @PostMapping("insert")
    public RestResult insert(@RequestBody ByGoodsShoping byGoodsShoping){
        /**
         * 商品类型 1  普通 2 次卡 3  联票  5规格
         *跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品 10 规格）
         * */
        if (byGoodsShoping.getGoodsType() == 2) {
            byGoodsShoping.setGoodsType(3);
            return this.goodsShoppingService.insert(byGoodsShoping);
        }
        if (byGoodsShoping.getGoodsType() == 3) {
            byGoodsShoping.setGoodsType(2);
            return this.goodsShoppingService.insert(byGoodsShoping);
        }
        if (byGoodsShoping.getGoodsType() == 7) {
            byGoodsShoping.setGoodsType(3);
            return this.goodsShoppingService.insert(byGoodsShoping);
        }
        if (byGoodsShoping.getGoodsType() == 8) {
            byGoodsShoping.setGoodsType(2);
            return this.goodsShoppingService.insert(byGoodsShoping);
        }
        if (byGoodsShoping.getGoodsType() == 6){
            byGoodsShoping.setGoodsType(1);
        };
        if (byGoodsShoping.getGoodsType() == 10){
            byGoodsShoping.setGoodsType(5);
        }
        return this.goodsShoppingService.insert(byGoodsShoping);
    }
    /**
    * @Description 删除购物车
    * <AUTHOR>
    * @Date        2019-08-13 16:52
    * @Version    1.0
    */
    @DeleteMapping("delete/{ids}")
    public RestResult delete(@PathVariable String ids){
        return this.goodsShoppingService.delete(ids);
    }

    /**
     * 删除购物车
     * @return
     */
    @PostMapping("deleteCart")
    public RestResult deleteCart(@RequestBody CartIds cartDelete){
        boolean flag = tcGoodsShopingService.deleteByIds(cartDelete);
        return RestResult.msg(200,"删除成功");
    }

    /**
    * @Description 修改购物车
    * <AUTHOR>
    * @Date        2019-08-13 17:01
    * @Version    1.0
    */
    @PutMapping("update")
    public RestResult update(@RequestBody ByGoodsShoping byGoodsShoping){
        return this.goodsShoppingService.update(byGoodsShoping);
    }

    /**
    * @Description 计算购物车
    * <AUTHOR>
    * @Date        2019-09-02 14:38
    * @Version    1.0
    */
    @PostMapping("calculation")
    public Map<String,Object> calculation(@RequestBody List<ByGoodsShoping> byGoodsShoping){
        return this.goodsShoppingService.showAll(byGoodsShoping);
    }
}
