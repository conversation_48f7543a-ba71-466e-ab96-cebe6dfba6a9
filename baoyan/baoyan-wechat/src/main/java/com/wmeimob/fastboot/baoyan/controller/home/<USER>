package com.wmeimob.fastboot.baoyan.controller.home;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf;
import com.wmeimob.fastboot.baoyan.service.BaseNavigationConfService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName BaseNavigationConfController
 * @Description 【导航设置】控制器
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("/navigation")
@Slf4j
public class BaseNavigationConfController {

    @Resource(name = "wxBaseNavigationConfServiceImpl")
    private BaseNavigationConfService baseNavigationConfService;

    @GetMapping("/home")
    public List<? extends BaseNavigationConf> queryHome() {
        List<BaseNavigationConf> navigationConfList = baseNavigationConfService.wxQueryAll(Boolean.TRUE);
        log.info("queryHome [get] ==== 出参 =======>{}",navigationConfList);
        return navigationConfList;

    }
    @GetMapping("/sort")
    public List<? extends BaseNavigationConf> querySort() {
        List<BaseNavigationConf> navigationConfList = baseNavigationConfService.wxQueryAll(Boolean.FALSE);
        log.info("querySort [get] ==== 出参 =======>{}",navigationConfList);
        return navigationConfList;

    }


}
