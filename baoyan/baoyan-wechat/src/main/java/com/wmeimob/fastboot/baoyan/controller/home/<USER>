package com.wmeimob.fastboot.baoyan.controller.home;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.ActivityService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 活动
 * @date 2019-08-16 17:26
 * @Version 1.0
 */
@RestController
@RequestMapping("activity")
@Slf4j
public class ActivityController {

    @Autowired
    private ActivityService activityService;
    @Resource
    private WxCustUserService userService;

    @Resource
    private RedisTemplate redisTemplate;

    /**
    * @Description 所有门店
    * <AUTHOR>
    * @Date        2019-08-16 17:30
    * @Version    1.0
    */
    @GetMapping("showActivity")
    public PageInfo showActivity(Integer pageIndex, Integer pageSize, String storeName,String longitude,String latitude,Integer orderId){
        if (StringUtil.isEmpty(latitude) || StringUtil.isEmpty(longitude)){
            throw new CustomException("经纬度不能为空");
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!"anonymousUser".equals(principal)){
            ByCustUser user = (ByCustUser)principal;
            ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
        }
        if (pageIndex == null){
            pageIndex = 1;
        }
        Set<Integer> set = null;
        // 查看商品包含的门店
        if (orderId != null){
           set  = this.activityService.showOrder(orderId);
        }
        PageHelper.startPage(pageIndex,pageSize);
        return activityService.showActivity(pageIndex,pageSize,storeName,longitude,latitude,set);
    }
    /**
     * 全部商品
     * */
    @GetMapping("showProduct")
    public PageInfo showProduct(Integer pageIndex,Integer pageSize,String productName,Integer id){
        return this.activityService.showProduct(pageIndex,pageSize,productName,id);
    }

    /**
    * @Description 门店详情
    * <AUTHOR>
    * @Date        2019-08-19 11:22
    * @Version    1.0
    */
    @GetMapping("detail")
    public Map<String,Object> getDetail(Integer id){
        return this.activityService.storeDetail(id);
    }

    /**
    * @Description 返回可预约的日期
    * <AUTHOR>
    * @Date        2019-08-19 14:02
    * @Version    1.0
    */
    @GetMapping("checkDate")
    public List<Map<String,Object>> getList(Integer id, String date,String newDate){
        return this.activityService.showDate(id,date,newDate);
    }

    /**
    * @Description 添加预约
    * <AUTHOR>
    * @Date        2019-08-19 15:11
    * @Version    1.0
    */
    @PostMapping("insert")
     public RestResult insert(@RequestBody ByCustAppointment byCustAppointment){
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ByCustUser user = (ByCustUser)principal;
        final String apponint = "apponint%s";
        String format = String.format(apponint, user.getId());
        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(format,format);
        if (aBoolean){
            try {
                log.info("添加预约 byCustAppointment: {} 当前登录人userId：{}", byCustAppointment,user.getId());
                ByCustUser byCustUser = userService.queryUserInfo(user.getWxOpenId());
                if (byCustUser.getIsDisable()) throw new CustomException("你已被禁用，请联系管理员");
                return this.activityService.insert(byCustAppointment);
            }catch (Exception var4){
                throw new CustomException(var4.getMessage());
            }finally {
                redisTemplate.delete(format);
            }
        }
        throw new CustomException("您的信息已提交，请稍后再试");
    }

    /**
    * 分类商品
    * jumpType  1.普通商品2.联票列表3.次卡列表4.拼团列表
    */
    @GetMapping("classProduct")
    public PageInfo getClassList(Integer jumpType,Integer type,String goodsName,String longitude,String latitude,Integer pageIndex,Integer pageSize,String classId){
        return this.activityService.getClassList(jumpType,type,goodsName,longitude,latitude,pageIndex,pageSize,classId);
    }
}
