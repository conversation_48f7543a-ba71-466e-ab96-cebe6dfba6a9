package com.wmeimob.fastboot.baoyan.controller.home;

import cn.hutool.core.collection.CollectionUtil;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatPayProperties;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.qo.GiftsQo;
import com.wmeimob.fastboot.baoyan.service.ByCustUserService;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import com.wmeimob.fastboot.baoyan.service.PayService;
import com.wmeimob.fastboot.baoyan.vo.Goods;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.common.xml.XmlReaders;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.core.WepayBuilder;
import me.hao0.wepay.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 支付
 * @date 2019-08-13 17:06
 * @Version 1.0
 */
@RestController
@RequestMapping("pay")
@Slf4j
public class PayContoller {

    @Autowired
    private PayService payService;
    @Autowired
    private WechatPayProperties wechatPayProperties;
    @Autowired
    private ByGoodsShopingMapper byGoodsShopingMapper;
    @Autowired
    private ByCustUserMapper byCustUserMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ByCouponTempMapper byCouponTempMapper;
    @Autowired
    private BaseClassifyMapper baseClassifyMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByOrderLogService orderLogService;

    /**
     * 购物车里支付，可以同时支付门票和淘潮玩
     * @param payAllOrder
     * @return
     */
    @PostMapping("payAll")
    public PayVo payAll(@RequestBody PayAllOrder payAllOrder){
        //普通门票
        Goods goods = payAllOrder.getGoods();
        if ( goods!=null && CollectionUtil.isNotEmpty(goods.getGoods())  && CollectionUtil.isNotEmpty(payAllOrder.getTcGoods()) ){
            //购物车结算时 如果有门票也有淘潮玩
            //逻辑比较复杂
            payAllOrder.setPayAll( true );
            return payService.payAll(payAllOrder);
        }
        if ( goods!=null && CollectionUtil.isNotEmpty(goods.getGoods()) ) {
            //只有门票，直接走门票的逻辑
            return payService.payOrderNew(payAllOrder);
        }
        if ( CollectionUtil.isNotEmpty(payAllOrder.getTcGoods())  ) {
            //只有淘潮玩，直接走淘潮玩的逻辑
            return payService.payTc(payAllOrder);
        }
        //啥都没有，有问题
        throw new CustomException("您没有选中任何商品");
    }

    /**
     * 淘潮玩付款
     * tcGoods 必须有东西
     * justAloneTc 必须有东西
     * orderAmount 必须有东西
     * @param payAllOrder
     * @return
     */
    @PostMapping("payTc")
    public PayVo payTc(@RequestBody PayAllOrder payAllOrder){
        return payService.payTc(payAllOrder);
    }

    /**
     * 油菜花微信付款
     * @param
     */
    @PostMapping("payYch")
    public PayVo payYch(@RequestBody YchOrder ychOrder){
        return payService.payYch(ychOrder);
    }

    /**
     * 查询准备支付的订单详情 淘潮和门票可以一起提交
     * @param payAllOrder
     * @return
     */
    @PostMapping("payAllDetail")
    public OrderDetailVo payAllDetail(@RequestBody PayAllOrder payAllOrder){
        return payService.payAllDetail(payAllOrder);
    }

    /**
     * 查看准备支付的淘潮玩订单详情
     * @param payAllOrder
     * @return
     */
    @PostMapping("payTcDetail")
    public OrderDetailVo payTcDetail(@RequestBody PayAllOrder payAllOrder){
        return payService.payTcDetail(payAllOrder);
    }

    /**
     * @Description 详情，门票支付可用
     * 跳转类型（1.商品分类2.联票列表3.次卡列表4.拼团列表5.文章6.普通商品7.联票商品8.次卡商品9.拼团商品 10 规格）
     */
    @PostMapping("payDetail")
    public Map<String, Object> pay(@RequestBody Goods goods) {

        return this.payService.computeDetailMap(goods);
    }

    /**
     * 处理名称
     * @param list
     */
    private void dealByCouponUserList(List<ByCouponUser> list) {
        //log.info("dealByCouponUserList()  list:{}", list);
        if (null != list && list.size() > 0) {
            for (ByCouponUser user : list) {
                //处理赠送优惠卷情况 赠送优惠卷
                ByCouponTemp byCouponTemp = byCouponTempMapper.selectByPrimaryKey(user.getCouponId());
                if (byCouponTemp==null){
                    continue;
                }
                switch (byCouponTemp.getType()) {
                    case 1:
                        BaseClassify classify = baseClassifyMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                        user.setLimitation(classify.getClassifyTitle() + "类");
                        break;
                    case 2:
                        if (null != user.getSingleGoodsType()) {
                            if (user.getSingleGoodsType() == 1) {
                                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                                user.setLimitation(byGoodsInfo.getGoodsName());
                            } else if (user.getSingleGoodsType() == 2) {
                                BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                                user.setLimitation(bySubCardGoods.getSubCardGoodsName());
                            } else {
                                ByTicketGoods byTicketGoods = byTicketGoodsMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                                user.setLimitation(byTicketGoods.getTicketGoodsName());
                            }
                        }
                        break;

                    case 0:
                        user.setLimitation("全部商品");
                        break;
                    default:
                }
                //处理折扣字段  db中是 97 % 折换成 9.7折
                //折扣卷类型
                if (user.getCouponType().equals(2)) {
                    //前端展示 例 97/10 9.7
                    user.setDiscount(user.getDiscount().divide(new BigDecimal(10)));
                }
            }
        }
    }

    private static final String LIMIT_CHECK_KEY = "check-limit:%s";


    /**
     * 支付门票订单，参数和返回值进行了优化
     * @param allOrder
     * @return
     */
    @PostMapping("payOrderNew")
    public PayVo payOrderNew(@RequestBody PayAllOrder allOrder){
        return this.payService.payOrderNew(allOrder);
    }

    /**
     * 未支付的淘潮订单进行付款
     * @param orderId
     * @return
     */
    @PostMapping("/payUnpaidTc/{orderId}")
    public PayVo payUnpaidTc(@PathVariable Integer orderId){
        return this.payService.payUnpaidTc(orderId);
    }

    /**
     * @Description 支付
     */
    @PostMapping("pay")
    public Map<String, Object> payOrder(@RequestBody Goods goods) {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ByCustUser user = (ByCustUser) principal;
        log.info("支付pay() goods: {} 当前登录人userId：{}", goods, user.getId());
        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(user.getId());
        if (byCustUser.getIsDisable()) {
            throw new CustomException("你已被禁用，请联系管理员");
        }
        if (goods.getPayAmount() != null && goods.getPayAmount().compareTo(new BigDecimal(0)) != 1) {
            throw new CustomException("价格不能为0");
        }
        if (goods.getType() == 1) {
            if (goods.getGoodsType() == 7) {
                goods.setGoodsType(2);
            }
            if (goods.getGoodsType() == 8) {
                goods.setGoodsType(3);
            }
            if (goods.getGoodsType() == 6) {
                goods.setGoodsType(1);
            }
            if (goods.getGoodsType() == 10) {
                goods.setGoodsType(10);
            }
        }

        Map<String, Object> map = this.payService.payOrder(goods);
        //记录创建订单的日志
        String orderId = map.get("orderId").toString();
        String orderNo = map.get("orderNo").toString();
        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .id(Integer.parseInt(orderId))
                        .orderNo(orderNo)
                        .operatorType(1)
                        .logType(OrderLogType.CREATE)
                        .build()
        );
        return map;

    }

    /**
     * @Description 计算总金额
     * <AUTHOR>
     * @Date 2019-09-02 14:12
     * @Version 1.0
     */
    @PostMapping("calculation")
    public Map<String, Object> calculation(@RequestBody Goods goods) {
        log.info("计算总金额 goods: {}", goods);
        if (goods.getOrderAmount().compareTo(new BigDecimal(0)) == 0) {
            throw new CustomException("不可使用优惠券");
        }

        Map<String, Object> map = new HashMap<>(3);
        if (goods.getScoreAmount() != null && goods.getScoreAmount().compareTo(new BigDecimal(0)) != 0) {
            map.put("type", Boolean.TRUE);
        } else {
            map.put("type", Boolean.FALSE);
        }
        //计算优惠券金额
        BigDecimal calculation = this.payService.calculation(goods);
        //计算积分金额
        map.put("orderAmount", calculation.compareTo(new BigDecimal(0)) == -1 ? new BigDecimal(0) : calculation);
        if (goods.getCouponId() != null) {
            goods.setScoreAmount(new BigDecimal(0));
            BigDecimal calculation1 = this.payService.calculation(goods);
            if (calculation.compareTo(new BigDecimal(0)) == -1) {
                calculation = new BigDecimal(0);
            }
            map.put("orderAmount", calculation);
            map.put("discountAmount", goods.getOrderAmount().subtract(calculation1));
        } else {
            if (calculation.compareTo(new BigDecimal(0)) == -1) {
                calculation = new BigDecimal(0);
            }
            map.put("orderAmount", calculation);
            map.put("discountAmount", 0);
        }
        return map;
    }

    /**
     * @Description 支付回调
     */
    @RequestMapping("updateOrder")
    public String orderPay(HttpServletRequest request) throws Exception {
        log.info("=======================订单支付回调=======================");

        Map<String, Object> map = Maps.toMap(XmlReaders.create(request.getInputStream()));
        map.remove("#text");
        Wepay wepay = WepayBuilder.newBuilder("", this.wechatPayProperties.getMchKey(), this.wechatPayProperties.getMchNo()).build();
        Object out_trade_no = map.get("out_trade_no");
        if (wepay.notifies().verifySign(map)) {
            if ( "SUCCESS".equals(map.get("return_code")) ) {
                // 跟新订单
                this.payService.updateByParentNo(out_trade_no.toString(), map.get("transaction_id").toString());

                return wepay.notifies().ok();
            }
            return wepay.notifies().notOk("未支付");
        }
        return wepay.notifies().notOk("valid sign error");
    }

    /**
     * 测试更新订单
     * @param out_trade_no
     * @param transaction_id
     * @return
     */
    @PostMapping("testUpdateOrder")
    public String testUpdateOrder(String out_trade_no, String transaction_id){
        this.payService.updateByParentNo(out_trade_no, transaction_id);
        return "ok";
    }

    /**
     * @Description 支付回调
     * <AUTHOR>
     * @Date 2019-06-03 18:02
     * @Version 1.0
     */
    @RequestMapping("updateTicketOrder")
    public String updateTicketOrder(HttpServletRequest request) throws Exception {
        log.info("==========================支付回调 updateTicketOrder()==========================");
        Map<String, Object> map = Maps.toMap(XmlReaders.create(request.getInputStream()));
        map.remove("#text");
        Wepay wepay = WepayBuilder.newBuilder("", this.wechatPayProperties.getMchKey(), this.wechatPayProperties.getMchNo()).build();
        Object out_trade_no = map.get("out_trade_no");
        if (wepay.notifies().verifySign(map)) {
            if (map.get("return_code").equals("SUCCESS")) {
                // 跟新订单
                this.payService.updateTicketOrder(out_trade_no, map.get("transaction_id"));
                return wepay.notifies().ok();
            }
            return wepay.notifies().notOk("未支付");
        }
        return wepay.notifies().notOk("valid sign error");
    }


    /**
     * 赠送朋友
     */
    @PostMapping("gifts")
    public void gifts(@RequestBody GiftsQo gifts) {
        log.info("赠送朋友");
        payService.gifts(gifts);
    }
}
