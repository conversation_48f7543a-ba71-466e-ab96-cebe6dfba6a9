package com.wmeimob.fastboot.baoyan.controller.home;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileReader;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.service.HomeService;
import com.wmeimob.fastboot.baoyan.service.WxCustUserService;
import com.wmeimob.fastboot.baoyan.vo.BaseAdvertisingVO;
import com.wmeimob.fastboot.baoyan.vo.ByArticlesVO;
import com.wmeimob.fastboot.baoyan.vo.ImgVO;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wepay.core.Refunds;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.model.refund.RefundApplyRequest;
import me.hao0.wepay.model.refund.RefundApplyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 首页
 * @date 2019-08-06 16:04
 * @Version 1.0
 */
@RestController
@RequestMapping("home")
@Slf4j
public class HomeController {


    @Resource
    private HomeService homeService;

    @Resource
    private WxCustUserService userService;


    /**
     * 首页
     */
    @GetMapping("index")
    public Map<String, Object> showHome() {
        return this.homeService.showHome();
    }

    /**
     * 首页活动分类
     */
    @GetMapping("activity")
    public PageInfo activity(HttpServletRequest request) {
        return new PageInfo<>(this.homeService.showActivity());
    }

    /**
     * @Description 文章详情
     * <AUTHOR>
     * @Date 2019-08-07 15:14
     * @Version 1.0
     */
    @GetMapping("byarticle")
    public ByArticle byarticle(Integer id) {
        return this.homeService.byarticle(id);
    }

    /**
     * @Description 分类
     * <AUTHOR>
     * @Date 2019-08-07 15:14
     * @Version 1.0
     */
    @GetMapping("classification")
    public List<BaseClassify> byarticle() {
        return this.homeService.classification();
    }

    /**
     * @Description 联票商品
     * <AUTHOR>
     * @Date 2019-08-07 15:39
     * @Version 1.0
     */
    @GetMapping("ticketGoods")
    public PageInfo ticketGoods(HttpServletRequest request, Integer type) {
        if (type == 2) {
            PageContext.startPage();
            return new PageInfo<>(this.homeService.ticketGoods());
        }
        PageContext.startPage();
        return new PageInfo<>(this.homeService.cardGoods());
    }

    /**
     * @Description 次卡商品
     * <AUTHOR>
     * @Date 2019-08-07 15:39
     * @Version 1.0
     */
    @GetMapping("cardGoods")
    public PageInfo cardGoods(HttpServletRequest request) {
        PageContext.startPage();
        return new PageInfo<>(this.homeService.cardGoods());
    }

    /**
     * 效验用户是否被禁用
     *
     * @param request
     * @return
     */
    @PostMapping("checkUserDisable")
    public RestResult checkUserDisable(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        if (StringUtils.isEmpty(jsonObject.get("type"))){
            throw new CustomException("商品已下架");
        }
        Integer type = Integer.parseInt(jsonObject.get("type").toString());
        String ids = (String)jsonObject.get("ids");
        ByCustUser user = SecurityContext.getUser();
        return homeService.checkUserDisable(user,type,ids);
    }

    /**
     * 效验对应商品是否 下架/删除
     *
     * @param request
     * @return
     */
    @GetMapping("checkGoodsShelf")
    public RestResult checkGoodsShelf(HttpServletRequest request, @RequestParam("jumpType") Integer jumpType, @RequestParam("id") Integer id) {
        return homeService.checkGoodsShelf(jumpType,id);
    }

    /**
     * 首页-广告-查询
     */
    @GetMapping("advertising")
    public BaseAdvertisingVO advertising(){
        return homeService.advertising();
    }


    /**
     * 首页-头条文章-滚动列表
     */
    @GetMapping("byArticles")
    public List<ByArticlesVO> byArticles(){
        return homeService.byArticles();
    }

    /**
     * 首页-多图-查询
     */
    @GetMapping("imgs")
    public List<ImgVO> imgs(){
        return homeService.imgs();
    }

    /**
     * 隐私政策
     */
    @GetMapping(value = "privacy", produces = "text/html;charset=utf-8")
    public String privacy()throws Exception{
        return homeService.selectPrivacyAgreement();
    }



    /**
     * 购买协议
     */
    @GetMapping(value = "purchase", produces = "text/html;charset=utf-8")
    public String purchase()throws Exception{
        return homeService.selectPurchaseAgreement();
    }

//    /**
//     * 单个banner
//     * */
//    @GetMapping("/banner/{id}")
//    public BaseBanner singlebanner(@PathVariable Object id){
//        return homeService.singlebanner(id);
//    }

}
