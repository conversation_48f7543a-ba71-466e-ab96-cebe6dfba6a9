package com.wmeimob.fastboot.baoyan.config;

import com.wmeimob.fastboot.core.startup.WmeimobStartUpRunnable;
import com.wmeimob.fastboot.core.startup.WmeimobStartUpRunnableProvider;
import com.wmeimob.fastboot.starter.security.JsonWebTokenHandler;
import com.wmeimob.fastboot.starter.security.JwtAuthenticationFilter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MyStartUpRunner implements WmeimobStartUpRunnableProvider {

    @Resource
    protected JwtAuthenticationFilter jwtAuthenticationTokenFilter;

    @Override
    public void run(WmeimobStartUpRunnable wmeimobStartUpRunnable) {
        wmeimobStartUpRunnable.run(WmeimobStartUpRunnable.Mode.OVERRIDE, () -> {

            JsonWebTokenHandler jsonWebTokenHandler = this.jwtAuthenticationTokenFilter.getJsonWebTokenHandler();
            jsonWebTokenHandler.setJsonWebTokenDecoder(new UserInfoJsonWebTokenDecoder());
            jsonWebTokenHandler.setJsonWebTokenEncoder(new UserInfoJsonWebTokenEncoder());
        });
    }
}
