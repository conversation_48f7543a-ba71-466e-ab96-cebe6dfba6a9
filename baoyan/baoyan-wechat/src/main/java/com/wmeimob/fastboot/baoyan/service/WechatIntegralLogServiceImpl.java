package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByIntegralLogMapper;
import com.wmeimob.fastboot.baoyan.vo.IntegraLogVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByIntegralLogServiceImpl
 * @Description 积分明细记录表 服务类实现
 * <AUTHOR>
 * @Date Fri Jul 05 15:35:59 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WechatIntegralLogServiceImpl implements WechatIntegralLogService {

    @Resource
    private ByIntegralLogMapper byIntegralLogMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;


    @Override
    public List<ByIntegralLog> findByCondition(ByIntegralLog byIntegralLog) {
        Example example = new Example(ByIntegralLog.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byIntegralLog.getId())) {
            criteria.andEqualTo("id", byIntegralLog.getId());
        }
        if (!StringUtils.isEmpty(byIntegralLog.getUserId())) {
            criteria.andEqualTo("userId", byIntegralLog.getUserId());
        }
        if (!StringUtils.isEmpty(byIntegralLog.getChangeType())) {
            criteria.andEqualTo("changeType", byIntegralLog.getChangeType());
        }
        if (!StringUtils.isEmpty(byIntegralLog.getChangeNum())) {
            criteria.andEqualTo("changeNum", byIntegralLog.getChangeNum());
        }
        if (!StringUtils.isEmpty(byIntegralLog.getChangeReason())) {
            criteria.andLike("changeReason", StringUtils.fullFuzzy(byIntegralLog.getChangeReason()));
        }
        if (!StringUtils.isEmpty(byIntegralLog.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", byIntegralLog.getGmtCreate());
        }
        if (!StringUtils.isEmpty(byIntegralLog.getGmtModified())) {
            criteria.andEqualTo("gmtModified", byIntegralLog.getGmtModified());
        }
        example.orderBy("id").desc();
        List<ByIntegralLog> byIntegralLogs = byIntegralLogMapper.selectByExample(example);
        if (null != byIntegralLogs && byIntegralLogs.size() > 0) {
            for (ByIntegralLog log : byIntegralLogs) {
                ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(log.getUserId());
                if (null != byCustUser) {
                    log.setMobile(byCustUser.getMobile());
                    log.setUserName(byCustUser.getNickName());
                }
            }
        }
        return byIntegralLogs;
    }

    @Override
    public ByIntegralLog queryByIntegralLogById(Object id) {
        return byIntegralLogMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addByIntegralLog(ByIntegralLog byIntegralLog) {
        byIntegralLog.setGmtCreate(new Date());
        byIntegralLogMapper.insertSelective(byIntegralLog);
    }

    @Override
    public void removeByIntegralLog(Object id) {
        ByIntegralLog byIntegralLog = new ByIntegralLog();
        byIntegralLog.setId(Integer.parseInt(id.toString()));
        byIntegralLogMapper.updateByPrimaryKeySelective(byIntegralLog);
    }

    @Override
    public void modifyByIntegralLog(ByIntegralLog byIntegralLog) {
        byIntegralLog.setGmtModified(new Date());
        byIntegralLogMapper.updateByPrimaryKeySelective(byIntegralLog);
    }

    @Override
    public IntegraLogVO queryIntegraLogByByUserId(Integer userId) {
        IntegraLogVO vo = new IntegraLogVO();
        ByCustUser user = byCustUserMapper.selectByPrimaryKey(userId);
        if (null == user) {
            throw new CustomException("用户不存在");
        }
        vo.setBePoint(user.getBePoint());
        vo.setHistoryPoint(user.getHistoryPoint());
        vo.setNowPoint(user.getNowPoint());
        Example example = new Example(ByIntegralLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        example.orderBy("id").desc();
        List<ByIntegralLog> logList = byIntegralLogMapper.selectByExample(example);
        if (logList.size() > 0) {
            for(ByIntegralLog log:logList){
                if (null!=log.getIntegralType()) {
                    switch (log.getIntegralType()){
                        case 1 :
                            log.setIntegralTypeStr("评价");
                            break;
                        case 2:
                            log.setIntegralTypeStr("购买商品");
                            break;
                        case 3 :
                            log.setIntegralTypeStr("积分抵扣");
                            break;
                        default:
                            log.setIntegralTypeStr("");
                    }
                }else{
                    log.setIntegralTypeStr("");
                }

            }
        }
        vo.setLogList(logList);
        return vo;
    }

}
