package com.wmeimob.fastboot.baoyan.service.tc;

import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.baoyan.entity.TcOrderAfter;
import com.wmeimob.fastboot.baoyan.entity.TcOrderGoods;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderAfterMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderAfterService;
import com.wmeimob.fastboot.baoyan.service.TcOrderGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.utils.DecimalUtil;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class WxTcOrderAfterServiceImpl implements TcOrderAfterService {

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private TcOrderAfterMapper tcOrderAfterMapper;

    @Resource
    private TcOrderMapper tcOrderMapper;



    @Override
    public boolean applyAfter(TcOrderAfter tcOrderAfter) {
        //根据申请的订单详情id查询订单详情
        TcOrderGoods orderGoods = tcOrderGoodsMapper.selectByPrimaryKey( tcOrderAfter.getDetailId() );

        //退款数量 —— 商品总数 - 已退款数量
        int refundCount = orderGoods.getGoodsCount() - orderGoods.getRefundCount();

        //实付单价是 0
        if ( DecimalUtil.zeroNull(orderGoods.getUnitPrice()) ){
            throw new CustomException("金额太少，不予退款");
        }

        TcOrder dbTcOrder = tcOrderMapper.selectByPrimaryKey( orderGoods.getTcOrderId() );
        Assert.notEq(tcOrderAfter.getUserId(), dbTcOrder.getUserId(),"这不是你的订单");
        //订单必须是已付款的状态
        Integer status = dbTcOrder.getStatus();
        Assert.eq(status,-1,"已取消订单无法售后");
        Assert.eq(status,0,"已关闭订单无法该订单不能售后售后");
        Assert.eq(status,1,"待付款订单无法售后");

        //查看当前订单是否正在售后----这里目前只支持全部一起退款
        if ( !orderGoods.getCanAfter() ){
            throw new CustomException("该订单不能售后");
        }


        //补足一些售后申请的信息
        tcOrderAfter.setStatus( 1 );
        tcOrderAfter.setApplyTime(new Date());
        tcOrderAfter.setGoodsId( orderGoods.getTcGoodsId() );
        tcOrderAfter.setOrderNo( orderGoods.getTcOrderNo() );
        //退款数量和金额
        //目前退款全部数量都退完
//        Integer goodsCount = orderGoods.getGoodsCount();
        tcOrderAfter.setRefundCount( refundCount );
        tcOrderAfter.setRefundPrice( orderGoods.getUnitPrice().multiply( BigDecimal.valueOf(refundCount) )   );

        //更新订单详情为不能售后
        tcOrderGoodsMapper.updateByPrimaryKeySelective(
                TcOrderGoods.builder()
                        .id( orderGoods.getId() )
                        .canAfter( false )
                        .build()
        );


        return tcOrderAfterMapper.insertSelective(tcOrderAfter)>0;
    }
}
