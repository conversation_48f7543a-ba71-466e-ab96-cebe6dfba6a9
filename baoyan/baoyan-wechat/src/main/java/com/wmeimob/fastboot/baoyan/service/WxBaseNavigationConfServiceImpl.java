package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf;
import com.wmeimob.fastboot.baoyan.mapper.BaseNavigationConfMapper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/8/8
 * packageName:com.wmeimob.fastboot.baoyan.service
 */

@Service("wxBaseNavigationConfServiceImpl")
public class WxBaseNavigationConfServiceImpl implements BaseNavigationConfService {
    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;

    @Override
    public List<BaseNavigationConf> wxQueryAll(Boolean isHome) {
       return baseNavigationConfMapper.tcNavigation(isHome);
    }
}
