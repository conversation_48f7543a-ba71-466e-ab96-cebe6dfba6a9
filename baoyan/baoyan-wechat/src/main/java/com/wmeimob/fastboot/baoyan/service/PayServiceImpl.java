package com.wmeimob.fastboot.baoyan.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatPayProperties;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.constant.OrderGoodsConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.qo.GiftsQo;
import com.wmeimob.fastboot.baoyan.util.MessageUtil;
import com.wmeimob.fastboot.baoyan.utils.UUIDOrder;
import com.wmeimob.fastboot.baoyan.utils.*;
import com.wmeimob.fastboot.baoyan.vo.Goods;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
//import com.wmeimob.fastboot.starter.wechat.dto.WechatJsPayDTO;
import com.wmeimob.fastboot.baoyan.dto.WechatJsPayDTO;
import com.wmeimob.fastboot.starter.wechat.service.WechatService;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wechat.core.Wechat;
import me.hao0.wepay.model.pay.JsPayResponse;
import me.hao0.wepay.model.refund.RefundApplyRequest;
import org.apache.commons.codec.digest.Md5Crypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> :Deng.YH
 * @Description: 支付
 * @date 2019-08-13 17:15
 * @Version 1.0
 */
@Slf4j
@Service
public class PayServiceImpl implements PayService {

    @Autowired
    private ByGoodsShopingMapper byGoodsShopingMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Autowired
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    private ByCustUserMapper byCustUserMapper;
    @Autowired
    private ByPlatformSetMapper byPlatformSetMapper;
    @Autowired
    private ByCouponUserMapper byCouponUserMapper;
    @Autowired
    private ByGoodsClassifyMapper byGoodsClassifyMapper;
    @Autowired
    private ByOrdersMapper byOrdersMapper;
    @Autowired
    private YchOrderMapper ychOrderMapper;
    @Autowired
    private YchApiService ychApiService;
    
    @Autowired
    private MemberCardService memberCardService;

    @Autowired
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private WepayService wepayService;

    @Resource
    private Wepay wepay;

    @Resource
    private BaseClassifyMapper baseClassifyMapper;
    @Resource
    private GoodsService goodsService;
    @Resource
    private WechatService wechatService;
    @Autowired
    private ByIntegralLogMapper byIntegralLogMapper;
    @Autowired
    private WriteOffCodeMapper writeOffCodeMapper;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Autowired
    BySubGoodsClassifyMapper bySubGoodsClassifyMapper;
    @Autowired
    BySubGoodsStoreMapper bySubGoodsStoreMapper;
    @Resource
    private WechatPayProperties wechatPayProperties;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> pay(Goods goods)  {
        Map<String, Object> data = new HashMap<>();
        /*查询我的积分*/
        ByCustUser user = SecurityContext.getUser();
        ByCustUser dbCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
        ByCustUser byCustUser = null;
        try {
            byCustUser = dbCustUser.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);

//        普通商品 商品详情支付
        if (goods.getType() == 1) {
            if (goods.getGoodsType() == 1 || goods.getGoodsType() == 6) {
                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goods.getId());
                byGoodsInfo.setGoodsStock(goods.getGoodsCount());
                List<ByCouponUser> arrayList = new ArrayList<>();
                //商品能适用的分类优惠卷
                List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser(user.getId(), byGoodsInfo.getId(), 1);
                //适用全部商品
                /*Example example = new Example(ByCouponUser.class);
                example.createCriteria()
                        .andEqualTo("isUse", 0)
                        .andEqualTo("auditStatus", 1)
                        .andEqualTo("targetId", 0)
                        .andEqualTo("userId", user.getId());
                List<ByCouponUser> byCouponUsers1 = this.byCouponUserMapper.selectByExample(example);*/

                Date now = new Date();
                List<ByCouponUser> allTicketCoupon = byCouponUserMapper.selectCouponByType(user.getId(), 0, now);

                arrayList.addAll(allTicketCoupon);
                arrayList.addAll(byCouponUsers);
                // 适用单个商品类型 1 普通商品 2 次卡 3 联票
                List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsInfo.getId(), 1);
                arrayList.addAll(byCouponUsers2);
                List<ByGoodsInfo> objects = new ArrayList<>();
                objects.add(byGoodsInfo);
                if (goods.getTeamType() != null && goods.getTeamType() == 1) {
                    data.put("goods", objects);
                    ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(goods.getTeamId());
                    data.put("goodsAmount", byTeamGoods.getTeamPrice().multiply(new BigDecimal(goods.getGoodsCount())));
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    if (new BigDecimal(count).compareTo(byTeamGoods.getTeamPrice().multiply(new BigDecimal(goods.getGoodsCount()))) == 1) {
                        count = byTeamGoods.getTeamPrice().multiply(new BigDecimal(goods.getGoodsCount())).setScale(0, BigDecimal.ROUND_UP).intValue();
                    }
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                    byCustUser.setNowPoint(byCustUser.getNowPoint() - byPlatformSet.getIntegrationDeduction());
                    data.put("goodsCount", goods.getGoodsCount());
                    if (arrayList.size() != 0) {
                        goods.setOrderAmount((BigDecimal) data.get("goodsAmount"));
                        List<ByCouponUser> remove = remove(arrayList, goods);
                        data.put("couponCount", remove.size());
                        dealByCouponUserList(remove);
                        data.put("coupon", remove);
                    } else {
                        data.put("coupon", new ArrayList<>());
                    }
                    return data;
                }
                data.put("goods", objects);
                Integer couponIsEnable = byPlatformSet.getGeneralCouponIsEnable() != null && byPlatformSet.getGeneralCouponIsEnable() == 0 ? byPlatformSet.getGeneralCouponIsEnable() : byGoodsInfo.getIsCoupon();
                data.put("couponType", couponIsEnable != null ? couponIsEnable : byPlatformSet.getGeneralCouponIsEnable());
                data.put("goodsAmount", byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())));
                if (byGoodsInfo == null) {
                    throw new CustomException("商品不存在");
                }
                if (byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
                    throw new CustomException("商品已下架请重新选择");
                }
                if (byGoodsInfo.getGoodsStock() < goods.getGoodsCount()) {
                    throw new CustomException("库存不足,请重新选择");
                }
                if (byGoodsInfo.getLimited() != null && byGoodsInfo.getLimited() != 0 && goods.getGoodsCount() > byGoodsInfo.getLimited()) {
                    throw new CustomException("商品限购" + byGoodsInfo.getLimited());
                }
                //已购买商品数量
                Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), byGoodsInfo.getId(), 1);
                if (beforeCount != null && byGoodsInfo.getLimited() != null && byGoodsInfo.getLimited() != 0 && goods.getGoodsCount() + beforeCount > byGoodsInfo.getLimited()) {
                    throw new CustomException("商品限购" + byGoodsInfo.getLimited() + ",已购买" + beforeCount);
                }
                if (byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getGeneralIntegralIsEnable() == 0 || (byGoodsInfo.getIsIntegral() != null && byGoodsInfo.getIsIntegral() == 0)) {
                    data.put("type", false);
                } else {
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    if (new BigDecimal(count).compareTo(byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount()))) == 1) {
                        count = byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())).setScale(0, BigDecimal.ROUND_UP).intValue();
                    }
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                    byCustUser.setNowPoint(byCustUser.getNowPoint() - byPlatformSet.getIntegrationDeduction());
                }
                data.put("goodsCount", goods.getGoodsCount());
                if (arrayList.size() != 0 && byPlatformSet.getGeneralCouponIsEnable() == 1 && byGoodsInfo.getIsCoupon() != null && byGoodsInfo.getIsCoupon() == 1) {
                    goods.setOrderAmount((BigDecimal) data.get("goodsAmount"));
                    List<ByCouponUser> remove = remove(arrayList, goods);
                    data.put("couponCount", remove.size());
                    dealByCouponUserList(remove);
                    data.put("coupon", remove);
                } else {
                    data.put("coupon", new ArrayList<>());
                }
                return data;
            }
            if (goods.getGoodsType() == 3 || goods.getGoodsType() == 8) {
                BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.selectByPrimaryKey(goods.getId());
                //bySubCardGoods.setGoodsStock(goods.getGoodsCount());
                if (bySubCardGoods == null) {
                    throw new CustomException("商品不存在");
                }
                if (bySubCardGoods.getIsDel() || bySubCardGoods.getStatus().equals(0)) {
                    throw new CustomException("商品已下架,请重新选择");
                }
                if (bySubCardGoods.getGoodsStock() < goods.getGoodsCount()) {
                    throw new CustomException("库存不足,请重新选择");
                }
                if (bySubCardGoods.getLimited() != null && bySubCardGoods.getLimited() != 0 && goods.getGoodsCount() > bySubCardGoods.getLimited()) {
                    throw new CustomException("商品限购" + bySubCardGoods.getLimited());
                }
                //已购买商品数量
                Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), bySubCardGoods.getId(), 2);
                if (beforeCount != null && bySubCardGoods.getLimited() != null && bySubCardGoods.getLimited() != 0 && goods.getGoodsCount() + beforeCount > bySubCardGoods.getLimited()) {
                    throw new CustomException("商品限购" + bySubCardGoods.getLimited() + ",已购买" + beforeCount);
                }
                data.put("goodsAmount", bySubCardGoods.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())));
                data.put("goodsCount", goods.getGoodsCount());
                if (byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null
                        || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getSubCardIntegralIsEnable() == 0 || (bySubCardGoods.getIsIntegral() != null && bySubCardGoods.getIsIntegral() == 0)) {
                    data.put("type", false);
                } else {
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    if (new BigDecimal(count).compareTo(bySubCardGoods.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount()))) == 1) {
                        count = bySubCardGoods.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())).setScale(0, BigDecimal.ROUND_UP).intValue();
                    }
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                    byCustUser.setNowPoint(byCustUser.getNowPoint() - byPlatformSet.getIntegrationDeduction());
                }
                if (byPlatformSet == null || byPlatformSet.getSubCardCouponIsEnable() == 0 || (bySubCardGoods.getIsCoupon() != null && bySubCardGoods.getIsCoupon() == 0)) {
                    data.put("couponType", false);
                    data.put("coupon", new ArrayList<>());
                } else {
                    List<ByCouponUser> arrayList = new ArrayList<>();
                    List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser(user.getId(), bySubCardGoods.getId(), 2);
                    Example example = new Example(ByCouponUser.class);
                    example.createCriteria()
                            .andEqualTo("isUse", 0)
                            .andEqualTo("auditStatus", 1)
                            .andEqualTo("targetId", 0).andEqualTo("userId", user.getId());

                    List<ByCouponUser> byCouponUsers1 = this.byCouponUserMapper.selectByExample(example);
                    arrayList.addAll(byCouponUsers1);
                    arrayList.addAll(byCouponUsers);
                    List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), bySubCardGoods.getId(), 2);
                    arrayList.addAll(byCouponUsers2);
                    if (arrayList.size() != 0) {
                        goods.setOrderAmount((BigDecimal) data.get("goodsAmount"));
                        List<ByCouponUser> remove = remove(arrayList, goods);
                        data.put("couponCount", remove.size());
                        dealByCouponUserList(remove);
                        data.put("coupon", remove);
                    } else {
                        data.put("coupon", new ArrayList<>());
                    }
                }
                List<BySubCardGoods> objects = new ArrayList<>();
                objects.add(bySubCardGoods);
                data.put("goods", objects);
                data.put("couponType", true);
                return data;
            }
            if (goods.getGoodsType() == 2 || goods.getGoodsType() == 7) {
                List<ByTicketGoods> objects = new ArrayList<>();
                ByTicketGoods byGoodsInfo = byTicketGoodsMapper.selectByPrimaryKey(goods.getId());
                objects.add(byGoodsInfo);
                //  byGoodsInfo.setGoodsStock(goods.getGoodsCount());
                data.put("goods", objects);
                if (byGoodsInfo == null) {
                    throw new CustomException("商品不存在");
                }
                if (byGoodsInfo.getIsDel() || !byGoodsInfo.getStatus()) {
                    throw new CustomException("商品已下架,请重新选择");
                }
                if (byGoodsInfo.getGoodsStock() < goods.getGoodsCount()) {
                    throw new CustomException("库存不足,请重新选择");
                }
                if (byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null
                        || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getTicketIntegralIsEnable() == 0) {
                    data.put("type", false);
                } else {
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                }
                data.put("goodsAmount", byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())));
                if (byPlatformSet == null || byPlatformSet.getTicketCouponIsEnable() == 0) {
                    data.put("couponType", false);
                    data.put("couponCount", 0);
                    data.put("coupon", new ArrayList<>());
                } else {
                    List<ByCouponUser> arrayList = new ArrayList<>();
                    List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser(user.getId(), byGoodsInfo.getId(), 3);
                    Example example = new Example(ByCouponUser.class);
                    example.createCriteria()
                            .andEqualTo("isUse", 0)
                            .andEqualTo("auditStatus", 1)
                            .andEqualTo("targetId", 0).andEqualTo("userId", user.getId());
                    List<ByCouponUser> byCouponUsers1 = this.byCouponUserMapper.selectByExample(example);
                    arrayList.addAll(byCouponUsers1);
                    arrayList.addAll(byCouponUsers);
                    List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsInfo.getId(), 3);
                    arrayList.addAll(byCouponUsers2);
                    if (arrayList.size() != 0) {
                        goods.setOrderAmount((BigDecimal) data.get("goodsAmount"));
                        List<ByCouponUser> remove = remove(arrayList, goods);
                        data.put("couponCount", remove.size());
                        dealByCouponUserList(remove);
                        data.put("coupon", remove);
                    } else {
                        data.put("coupon", new ArrayList<>());
                    }
                   /* objects.add(byGoodsInfo);
                    data.put("goods", objects);*/
                    data.put("couponType", true);
                }
                if (byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null
                        || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getTicketIntegralIsEnable() == 0) {
                    data.put("type", false);
                } else {
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    if (new BigDecimal(count).compareTo(byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount()))) == 1) {
                        count = byGoodsInfo.getSellPrice().multiply(new BigDecimal(goods.getGoodsCount())).setScale(0, BigDecimal.ROUND_UP).intValue();
                    }
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                }
                data.put("goodsCount", goods.getGoodsCount());
                return data;
            }
            if (goods.getGoodsType() == 10) {
                ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(goods.getId());
                if (combinationGoods == null) {
                    throw new CustomException("商品不存在");
                }
                if ((combinationGoods.getIsDel() != null && combinationGoods.getIsDel() == 1) || (combinationGoods.getStatus() != null && combinationGoods.getStatus() == 0)) {
                    throw new CustomException("商品已下架请重新选择");
                }
                if (combinationGoods.getGoodsStock() < goods.getGoodsCount()) {
                    throw new CustomException("库存不足,请重新选择");
                }
                if (combinationGoods.getLimited() != null && combinationGoods.getLimited() != 0 && goods.getGoodsCount() > combinationGoods.getLimited()) {
                    throw new CustomException("商品限购" + combinationGoods.getLimited());
                }
                //已购买商品数量
                Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), combinationGoods.getId(), 5);
                if (beforeCount != null && combinationGoods.getLimited() != null && combinationGoods.getLimited() != 0 && goods.getGoodsCount() + beforeCount > combinationGoods.getLimited()) {
                    throw new CustomException("商品限购" + combinationGoods.getLimited() + ",已购买" + beforeCount);
                }
                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                combinationGoods.setGoodsStock(goods.getGoodsCount()).setGoodsImg(byGoodsInfo.getGoodsImg())
                ;
                List<ByCouponUser> arrayList = new ArrayList<>();
                //商品能适用的分类优惠卷
                List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser(user.getId(), byGoodsInfo.getId(), 1);
                //适用全部商品
                Example example = new Example(ByCouponUser.class);
                example.createCriteria()
                        .andEqualTo("isUse", 0)
                        .andEqualTo("auditStatus", 1)
                        .andEqualTo("targetId", 0)
                        .andEqualTo("userId", user.getId());
                List<ByCouponUser> byCouponUsers1 = this.byCouponUserMapper.selectByExample(example);
                arrayList.addAll(byCouponUsers1);
                arrayList.addAll(byCouponUsers);
                // 适用单个商品类型 1 普通商品 2 次卡 3 联票
                List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsInfo.getId(), 1);
                arrayList.addAll(byCouponUsers2);
                List<ByCombinationGoods> objects = new ArrayList<>();
                objects.add(combinationGoods);
                data.put("goods", objects);
                Integer couponIsEnable = byPlatformSet.getGeneralCouponIsEnable() != null && byPlatformSet.getGeneralCouponIsEnable() == 0 ? byPlatformSet.getGeneralCouponIsEnable() : byGoodsInfo.getIsCoupon();
                data.put("couponType", couponIsEnable != null ? couponIsEnable : byPlatformSet.getGeneralCouponIsEnable());
                data.put("goodsAmount", combinationGoods.getPrice().multiply(new BigDecimal(goods.getGoodsCount())));

                if (byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getGeneralIntegralIsEnable() == 0 || (byGoodsInfo.getIsIntegral() != null && byGoodsInfo.getIsIntegral() == 0)) {
                    data.put("type", false);
                } else {
                    data.put("type", true);
                    int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                    if (new BigDecimal(count).compareTo(combinationGoods.getPrice().multiply(new BigDecimal(goods.getGoodsCount()))) == 1) {
                        count = combinationGoods.getPrice().multiply(new BigDecimal(goods.getGoodsCount())).setScale(0, BigDecimal.ROUND_DOWN).intValue();
                    }
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                    byCustUser.setNowPoint(byCustUser.getNowPoint() - byPlatformSet.getIntegrationDeduction());
                }
                data.put("goodsCount", goods.getGoodsCount());
                if (arrayList.size() != 0 && byPlatformSet.getGeneralCouponIsEnable() == 1 && combinationGoods.getIsCoupon() != null && combinationGoods.getIsCoupon() == 1) {
                    goods.setOrderAmount((BigDecimal) data.get("goodsAmount"));
                    List<ByCouponUser> remove = remove(arrayList, goods);
                    data.put("couponCount", remove.size());
                    dealByCouponUserList(remove);
                    data.put("coupon", remove);
                } else {
                    data.put("coupon", new ArrayList<>());
                }
                return data;
            }
            return data;
        }
        if (goods.getType() == 2) {
            BigDecimal bigDecimal = new BigDecimal(0);
            int goodsCount = 0;
            data.put("type", false);
            InputValidator.checkEmpty(goods.getGoods(), "商品");
            //优惠券
            List<ByCouponUser> arrayList = new ArrayList<>();
            List<ByGoodsShoping> byGoodsShopings = goods.getGoods();
            Set<Integer> set = new HashSet<>();
            data.put("count", 0);
            data.put("fraction", 0);
            int i = 0;
            for (ByGoodsShoping byGoodsShoping : byGoodsShopings) {
                ByGoodsShoping byGoodsShoping1 = this.byGoodsShopingMapper.selectByPrimaryKey(byGoodsShoping.getId());
                if (byGoodsShoping1.getGoodsType() == 1) {
                    //查询这个商品 用户拥有的优惠券
                    List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping.getGoodsId(), 1);
                    arrayList.addAll(byCouponUsers);
                    //查询这个商品是否有效
                    ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byGoodsShoping1.getGoodsId());
                    if (byGoodsInfo == null) {
                        throw new CustomException("商品不存在");
                    }
                    //库存是否足够
                    if (byGoodsInfo.getGoodsStock() < byGoodsShoping.getGoodsCount() || byGoodsInfo.getIsDel() || byGoodsInfo.getStatus().equals(0)) {
                        throw new CustomException(byGoodsInfo.getGoodsName() + "商品已下架或者库存不足,请重新选择");
                    }
                    if (byGoodsInfo.getLimited() != null && byGoodsInfo.getLimited() != 0 && byGoodsShoping.getGoodsCount() > byGoodsInfo.getLimited()) {
                        throw new CustomException("商品限购:" + byGoodsInfo.getGoodsName() + byGoodsInfo.getLimited());
                    }
                    //已购买商品数量
                    Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), byGoodsInfo.getId(), 1);
                    if (beforeCount != null && byGoodsInfo.getLimited() != null && byGoodsInfo.getLimited() != 0 && byGoodsShoping.getGoodsCount() + beforeCount > byGoodsInfo.getLimited()) {
                        throw new CustomException("商品限购:" + byGoodsInfo.getGoodsName() + byGoodsInfo.getLimited() + ",已购买" + beforeCount);
                    }
                    bigDecimal = bigDecimal.add((byGoodsInfo.getSellPrice().multiply(new BigDecimal(byGoodsShoping1.getGoodsCount()))));
                    goodsCount += byGoodsShoping1.getGoodsCount();
                    byGoodsShoping.setGoodsName(byGoodsInfo.getGoodsName());
                    byGoodsShoping.setGoodsImg(byGoodsInfo.getGoodsImg());
                    byGoodsShoping.setSellPrice(String.valueOf(byGoodsInfo.getSellPrice()));
                    Boolean type = (Boolean) data.get("type");
                    if (type || byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getGeneralIntegralIsEnable() == 0 || (byGoodsInfo.getIsIntegral() != null && byGoodsInfo.getIsIntegral() == 0)) {
                        data.put("type", false);
                    } else {
                        byCustUser.setNowPoint(byCustUser.getNowPoint() - (Integer) data.get("fraction"));
                        int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                        if (new BigDecimal(count).compareTo(byGoodsInfo.getSellPrice().multiply(new BigDecimal(byGoodsShoping.getGoodsCount()))) == 1) {
                            count = byGoodsInfo.getSellPrice().multiply(new BigDecimal(byGoodsShoping.getGoodsCount())).intValue();
                        }
                        data.put("count", count + (Integer) data.get("count"));
                        data.put("fraction", count * byPlatformSet.getIntegrationDeduction() + (Integer) data.get("fraction"));

                    }
                    if (byPlatformSet != null && byPlatformSet.getGeneralCouponIsEnable() != 0 && byGoodsInfo.getIsCoupon() != null && byGoodsInfo.getIsCoupon() == 1) {
                        i = 1;
                        List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping1.getGoodsId(), byGoodsShoping.getGoodsType());
                        arrayList.addAll(byCouponUsers2);
                        Example example = new Example(ByGoodsClassify.class);
                        example.createCriteria()
                                .andEqualTo("goodsId", byGoodsShoping1.getGoodsId());
                        List<ByGoodsClassify> byGoodsClassifies = this.byGoodsClassifyMapper.selectByExample(example);
                        if (byGoodsClassifies.size() != 0 && byPlatformSet.getGeneralCouponIsEnable() == 1) {
                            for (ByGoodsClassify byGoodsClassify : byGoodsClassifies) {
                                set.add(byGoodsClassify.getClassifyId());
                            }
                        }
                    }
                }
                if (byGoodsShoping1.getGoodsType() == 2) {
                    BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byGoodsShoping1.getGoodsId());
                    if (bySubCardGoods == null) {
                        throw new CustomException("商品不存在");
                    }
                    if (byPlatformSet.getSubCardCouponIsEnable() == 1 && bySubCardGoods.getIsCoupon() != null && bySubCardGoods.getIsCoupon() == 1) {
                        List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping.getGoodsId(), 2);
                        arrayList.addAll(byCouponUsers);
                        i = 1;
                    }
                    if (bySubCardGoods.getGoodsStock() < byGoodsShoping.getGoodsCount() || bySubCardGoods.getIsDel() || bySubCardGoods.getStatus().equals(0)) {
                        throw new CustomException(bySubCardGoods.getGoodsName() + "商品已下架或者库存不足,请重新选择");
                    }
                    if (bySubCardGoods.getLimited() != null && bySubCardGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount() > bySubCardGoods.getLimited()) {
                        throw new CustomException("商品限购:" + bySubCardGoods.getGoodsName() + bySubCardGoods.getLimited());
                    }
                    //已购买商品数量
                    Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), bySubCardGoods.getId(), 2);
                    if (beforeCount != null && bySubCardGoods.getLimited() != null && bySubCardGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount() + beforeCount > bySubCardGoods.getLimited()) {
                        throw new CustomException("商品限购:" + bySubCardGoods.getGoodsName() + bySubCardGoods.getLimited() + ",已购买" + beforeCount);
                    }
                    bigDecimal = bigDecimal.add((bySubCardGoods.getSellPrice().multiply(new BigDecimal((byGoodsShoping1.getGoodsCount())))));
                    byGoodsShoping.setGoodsName(bySubCardGoods.getGoodsName());
                    byGoodsShoping.setGoodsImg(bySubCardGoods.getGoodsImg());
                    byGoodsShoping.setSellPrice(String.valueOf(bySubCardGoods.getSellPrice()));
                    Boolean type = (Boolean) data.get("type");
                    if ((type && data.get("count") == null) || byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null
                            || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getSubCardIntegralIsEnable() == 0 || (bySubCardGoods.getIsCoupon() != null && bySubCardGoods.getIsCoupon() == 0)) {
                        data.put("type", false);
                    } else {
                        byCustUser.setNowPoint(byCustUser.getNowPoint() - (Integer) data.get("fraction"));
                        data.put("type", false);
                        int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                        data.put("count", count + (Integer) data.get("count"));
                        data.put("fraction", count * byPlatformSet.getIntegrationDeduction() + (Integer) data.get("fraction"));
                    }
                    if (byPlatformSet != null && byPlatformSet.getSubCardCouponIsEnable() != 0 && bySubCardGoods.getIsCoupon() != null && bySubCardGoods.getIsCoupon() != 0) {
                        Example example = new Example(BySubGoodsClassify.class);
                        example.createCriteria()
                                .andEqualTo("subGoodsId", bySubCardGoods.getId());
                        List<BySubGoodsClassify> bySubGoodsClassifies = this.bySubGoodsClassifyMapper.selectByExample(example);
                        if (bySubGoodsClassifies.size() != 0) {
                            for (BySubGoodsClassify bySubGoodsClassify : bySubGoodsClassifies) {
                                set.add(bySubGoodsClassify.getClassifyId());
                            }
                        }
                    }

                }
                if (byGoodsShoping1.getGoodsType() == 3) {
                    if (byPlatformSet.getSubCardIntegralIsEnable() == 1) {
                        List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping.getGoodsId(), 3);
                        arrayList.addAll(byCouponUsers);
                        i = 1;
                    }
                    ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byGoodsShoping1.getGoodsId());

                    if (byTicketGoods == null) {
                        throw new CustomException("商品不存在");
                    }
                    if (byTicketGoods.getGoodsStock() < byGoodsShoping.getGoodsCount() || byTicketGoods.getIsDel() || !byTicketGoods.getStatus()) {
                        throw new CustomException(byTicketGoods.getGoodsName() + "商品已下架或者库存不足,请重新选择");
                    }
                    bigDecimal = bigDecimal.add((byTicketGoods.getSellPrice().multiply(new BigDecimal((byGoodsShoping1.getGoodsCount())))));
                    byGoodsShoping.setGoodsName(byTicketGoods.getGoodsName());
                    byGoodsShoping.setGoodsImg(byTicketGoods.getGoodsImg());
                    byGoodsShoping.setSellPrice(String.valueOf(byTicketGoods.getSellPrice()));
                    Boolean type = (Boolean) data.get("type");
                    if ((type && byPlatformSet == null) || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null
                            || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getTicketIntegralIsEnable() == 0) {
                        data.put("type", false);
                    } else {
                        byCustUser.setNowPoint(byCustUser.getNowPoint() - (Integer) data.get("fraction"));
                        int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                        data.put("count", count + (Integer) data.get("count"));
                        data.put("fraction", count * byPlatformSet.getIntegrationDeduction() + (Integer) data.get("fraction"));
                    }
                    if (byPlatformSet != null && byPlatformSet.getTicketIntegralIsEnable() != 0) {
                        Example example1 = new Example(ByTicketGoodsMapping.class);
                        example1.createCriteria().andEqualTo("ticketGoodsId", byGoodsShoping1.getGoodsId());
                        List<ByTicketGoodsMapping> byTicketGoodsMappings = this.byTicketGoodsMappingMapper.selectByExample(example1);
                        for (ByTicketGoodsMapping byTeamGoodsMapper : byTicketGoodsMappings) {
                            Example example = new Example(ByGoodsClassify.class);
                            example.createCriteria()
                                    .andEqualTo("goodsId", byTeamGoodsMapper.getGoodsId());
                            List<ByGoodsClassify> byGoodsClassifies = this.byGoodsClassifyMapper.selectByExample(example);
                            if (byGoodsClassifies.size() != 0) {
                                for (ByGoodsClassify byGoodsClassify : byGoodsClassifies) {
                                    set.add(byGoodsClassify.getClassifyId());
                                }
                            }
                        }
                    }
                }
                if (byGoodsShoping1.getGoodsType() == 5) {
                    List<ByCouponUser> byCouponUsers = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping.getGoodsId(), 1);
                    arrayList.addAll(byCouponUsers);
                    ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byGoodsShoping1.getGoodsId());
                    if (combinationGoods == null) {
                        throw new CustomException("商品不存在");
                    }
                    ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                    if (combinationGoods.getGoodsStock() < byGoodsShoping.getGoodsCount()
                            || (combinationGoods.getIsDel() != null && combinationGoods.getIsDel() == 1)
                            || (combinationGoods.getStatus() != null && combinationGoods.getStatus() == 0)) {
                        throw new CustomException(combinationGoods.getName() + "商品已下架或者库存不足,请重新选择");
                    }
                    if (combinationGoods.getLimited() != null && combinationGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount() > combinationGoods.getLimited()) {
                        throw new CustomException("商品限购：" + combinationGoods.getName() + combinationGoods.getLimited());
                    }
                    //已购买商品数量
                    Integer beforeCount = byOrdersMapper.selectGoodsNum(user.getId(), combinationGoods.getId(), 5);
                    if (beforeCount != null && combinationGoods.getLimited() != null && combinationGoods.getLimited() != 0 && byGoodsShoping.getGoodsCount() + beforeCount > combinationGoods.getLimited()) {
                        throw new CustomException("商品限购:" + combinationGoods.getName() + combinationGoods.getLimited() + ",已购买" + beforeCount);
                    }
                    bigDecimal = bigDecimal.add((combinationGoods.getPrice().multiply(new BigDecimal(byGoodsShoping1.getGoodsCount()))));
                    byGoodsShoping.setGoodsName(combinationGoods.getName());
                    byGoodsShoping.setGoodsImg(byGoodsInfo.getGoodsImg());
                    byGoodsShoping.setSellPrice(String.valueOf(combinationGoods.getPrice()));
                    Boolean type = (Boolean) data.get("type");
                    if (type || byPlatformSet == null || byPlatformSet.getIntegrationDeduction() == null || byPlatformSet.getIntegrationDeduction() == 0 || byCustUser.getNowPoint() == null || byCustUser.getNowPoint() == 0 || byCustUser.getNowPoint() < byPlatformSet.getIntegrationDeduction() || byPlatformSet.getGeneralIntegralIsEnable() == 0 || (combinationGoods.getIsIntegral() != null && combinationGoods.getIsIntegral() == 0)) {
                        data.put("type", false);
                    } else {
                        byCustUser.setNowPoint(byCustUser.getNowPoint() - (Integer) data.get("fraction"));
                        int count = getCount(byCustUser.getNowPoint(), byPlatformSet.getIntegrationDeduction(), 0);
                        if (new BigDecimal(count).compareTo(combinationGoods.getPrice().multiply(new BigDecimal(byGoodsShoping.getGoodsCount()))) == 1) {
                            count = combinationGoods.getPrice().multiply(new BigDecimal(byGoodsShoping.getGoodsCount())).intValue();
                        }
                        data.put("count", count + (Integer) data.get("count"));
                        data.put("fraction", count * byPlatformSet.getIntegrationDeduction() + (Integer) data.get("fraction"));

                    }
                    if (byPlatformSet != null && byPlatformSet.getGeneralCouponIsEnable() != 0 && combinationGoods.getIsCoupon() != null && combinationGoods.getIsCoupon() == 1) {
                        i = 1;
                        List<ByCouponUser> byCouponUsers2 = byCouponUserMapper.selectByCouponUser2(user.getId(), byGoodsShoping1.getGoodsId(), 1);
                        arrayList.addAll(byCouponUsers2);
                        Example example = new Example(ByGoodsClassify.class);
                        example.createCriteria()
                                .andEqualTo("goodsId", combinationGoods.getGoodsId());
                        List<ByGoodsClassify> byGoodsClassifies = this.byGoodsClassifyMapper.selectByExample(example);
                        if (byGoodsClassifies.size() != 0 && byPlatformSet.getGeneralCouponIsEnable() == 1) {
                            for (ByGoodsClassify byGoodsClassify : byGoodsClassifies) {
                                set.add(byGoodsClassify.getClassifyId());
                            }
                        }
                    }
                }
                if (i == 1 || arrayList.size() != 0 || !set.isEmpty()) {
                    /*Example example = new Example(ByCouponUser.class);
                    example.createCriteria()
                            .andEqualTo("isUse", 0)
                            .andEqualTo("auditStatus", 1)
                            .andEqualTo("targetId", 0)
                            .andEqualTo("userId", user.getId());ticket
                    List<ByCouponUser> byCouponUsers1 = this.byCouponUserMapper.selectByExample(example);*/
                    Date now = new Date();
                    List<ByCouponUser> allTicketCoupon = byCouponUserMapper.selectCouponByType(user.getId(), 0, now);
                    List<ByCouponUser> typeTicketCoupon = byCouponUserMapper.selectCouponByType(user.getId(), 1, now);


                    arrayList.addAll(allTicketCoupon);
                    arrayList.addAll(typeTicketCoupon);
                }
                if (i == 1 && !set.isEmpty()) {
                    List<ByCouponUser> byCouponUsers = this.byCouponUserMapper.selectList(set, user.getId());
                    arrayList.addAll(byCouponUsers);
                }
                data.put("couponCount", arrayList.size());
                dealByCouponUserList(arrayList);
                data.put("coupon", arrayList);
                data.put("goods", byGoodsShopings);
                data.put("goodsAmount", bigDecimal);
                data.put("goodsCount",goodsCount);
                if (arrayList.size() == 0) {
                    data.put("couponType", Boolean.FALSE);
                } else {
                    data.put("couponType", Boolean.TRUE);
                }
            }
            /*去除相同的优惠卷*/
            if ((Boolean) data.get("couponType")) {
                List<ByCouponUser> arrayList1 = (List<ByCouponUser>) data.get("coupon");
                List<ByCouponUser> byCouponUsers = removeDuplicte(arrayList1);
                /*去除不符合的优惠卷*/
                byCouponUsers = remove(byCouponUsers, goods);
                //modify名称 转换
                data.put("couponCount", byCouponUsers);
                dealByCouponUserList(byCouponUsers);
                data.put("coupon", byCouponUsers);
            }
            if ((Integer) data.get("count") != 0) {
                data.put("type", true);
                if (new BigDecimal((Integer) data.get("count")).compareTo(bigDecimal) == 1) {
                    Integer count = bigDecimal.setScale(0, BigDecimal.ROUND_DOWN).intValue();
                    data.put("count", count);
                    data.put("fraction", count * byPlatformSet.getIntegrationDeduction());
                }
            }

            if (null != data.get("coupon")) {
                List<ByCouponUser> listUser = (List<ByCouponUser>) data.get("coupon");
                dealByCouponUserList(listUser);
                data.put("coupon", listUser);
            }
            return data;
        }
        throw new CustomException("商品已下架");
    }


    /**
     * 处理名称
     *
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    private void dealByCouponUserList(List<ByCouponUser> list) {
        //log.info("dealByCouponUserList()  list:{}", list);
        if (null != list && list.size() > 0) {
            for (ByCouponUser user : list) {
                //处理赠送优惠卷情况 赠送优惠卷
                if (null != user.getIsGive() && user.getIsGive().equals(1)) {
                    ByCouponTemp byCouponTemp = byCouponTempMapper.selectByPrimaryKey(user.getCouponId());
                    switch (byCouponTemp.getType()) {
                        case 1:
                            BaseClassify classify = baseClassifyMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                            user.setLimitation(classify.getClassifyTitle() + "类");
                            break;
                        case 2:
                            ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                            user.setLimitation(byGoodsInfo.getGoodsName());
                            break;

                        case 0:
                            user.setLimitation("全部商品");
                            break;
                        default:
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private List<ByCouponUser> remove(List<ByCouponUser> userList, Goods goods) {
        List<ByCouponUser> byCouponUsers = new ArrayList<>();
        for (ByCouponUser byCouponUser : userList) {
            if (goods.getScoreAmount() == null) {
                goods.setScoreAmount(new BigDecimal(0));
            }
            try {
                goods.setCouponId(byCouponUser.getId());
                byCouponUsers.add(byCouponUser);
            } catch (Exception e) {
                log.info(e.getMessage());
                continue;
            }
        }
        return byCouponUsers;
    }

    //去重
    @Transactional(rollbackFor = Exception.class)
    private List<ByCouponUser> removeDuplicte(List<ByCouponUser> userList) {
        Set<ByCouponUser> s = new TreeSet<ByCouponUser>(new Comparator<ByCouponUser>() {

            @Override
            public int compare(ByCouponUser o1, ByCouponUser o2) {
                return o1.getId().compareTo(o2.getId());
            }

        });

        s.addAll(userList);
        return new ArrayList<ByCouponUser>(s);
    }

    @Autowired
    private ByGoodsStoreMapper byGoodsStoreMapper;


    /**
     * 支付
     * @param goods
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> payOrder(Goods goods) {
        log.info("payOrder()  goods:{}", goods);
        String originalGoodsId = goods.getId();
        ByCustUser user = SecurityContext.getUser();
        user = byCustUserMapper.selectByPrimaryKey(user.getId());
        Integer nowPoint = user.getNowPoint();
        String wechatPayName = "";
        //新订单的支付
        String orderGoodsTag = "";
        if (goods.getType() != 3) {
            Integer couponId = goods.getCouponId();
            Map<String, Object> pay = pay(goods);
            user.setNowPoint(nowPoint);
            goods.setCouponId(couponId);
            ByCouponUser byCouponUser1 = this.byCouponUserMapper.selectByPrimaryKey(goods.getCouponId());
            if (byCouponUser1 != null) {
                byCouponUser1.setIsUse(1);
                this.byCouponUserMapper.updateByPrimaryKeySelective(byCouponUser1);
            }
            if (goods.getCouponId() != null && goods.getCouponId() != 0) {
                ByCouponUser byCouponUser11 = this.byCouponUserMapper.selectByPrimaryKey(goods.getCouponId());
                byCouponUser11.setIsUse(1);
                byCouponUser11.setUseDate(new Date());
                this.byCouponUserMapper.updateByPrimaryKeySelective(byCouponUser11);
            }
            BigDecimal orderAmount = goods.getOrderAmount().setScale(2);
            BigDecimal payAmount = goods.getPayAmount();
            BigDecimal couponAmount = goods.getCouponAmount();
            BigDecimal scoreAmount = goods.getScoreAmount();
            if (scoreAmount != null && scoreAmount.compareTo(new BigDecimal(0)) != 0) {
                Boolean type = (Boolean) pay.get("type");
                if (type) {
                    Integer count = (Integer) pay.get("count");
                    /*if (scoreAmount.compareTo(new BigDecimal(count)) != 0) {
                        throw new CustomException("订单金额有误");
                    }*/
                } else {
                    /*if (scoreAmount.compareTo(new BigDecimal(0)) != 0) {
                        throw new CustomException("订单金额有误");
                    }*/
                }
            }

            BigDecimal subtract = DecimalUtil.sub(orderAmount, goods.getCouponAmount(), goods.getScoreAmount());
//            BigDecimal subtract = orderAmount.subtract(goods.getCouponAmount()).subtract(goods.getScoreAmount());
            //处理一下积分问题，如果抵扣的积分 等于 总金额 ， 至少0.01元
            if (subtract.doubleValue() == 0) {
                subtract = BigDecimal.valueOf( OrderGoodsConstant.LOW_PRICE_GOODS );
            }

            /*if (subtract.compareTo(payAmount) != 0) {
                throw new CustomException("订单金额有误");
            }*/
            /*计算优惠券*/
            if (goods.getCouponId() != null && goods.getCouponId() != 0) {
                BigDecimal aBoolean = getBoolean(goods);
                if (aBoolean.compareTo(new BigDecimal(0)) == -1) {
                    aBoolean = new BigDecimal(0);
                }
                if (aBoolean.compareTo(new BigDecimal(0)) == 1 && orderAmount.compareTo(aBoolean) != 1 && orderAmount.compareTo(new BigDecimal(0)) == 0) {
                    throw new CustomException("优惠卷金额有误");
                }
            }
            /*用户减少积分*/
            ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
            if (byPlatformSet != null && byPlatformSet.getIntegralReturn() != null && byPlatformSet.getIntegralReturn().compareTo(new BigDecimal(0)) != 0 && goods.getScoreAmount().compareTo(new BigDecimal(0)) != 0) {
                ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
                ByIntegralLog byIntegralLog = new ByIntegralLog();
                byIntegralLog.setUserId(byCustUser.getId());
                byIntegralLog.setChangeType(1);
                Integer count = (byPlatformSet.getIntegralReturn().multiply(goods.getScoreAmount())).intValue();
                byIntegralLog.setChangeNum(count);
                byIntegralLog.setChangeReason("商品购买扣除");
                byIntegralLog.setIntegralType(2);
                byIntegralLog.setChangeType(2);
                byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                byIntegralLog.setGmtCreate(new Date());
                log.info(byCustUser.getNowPoint().toString());
                if (count != 0) {
                    int i = this.byCustUserMapper.updateByNotAdd(byCustUser.getId(), count);
                    if (i < 1) {
                        throw new CustomException("积分不足");
                    }
                    this.byIntegralLogMapper.insertSelective(byIntegralLog);
                }
                log.info(byCustUser.getNowPoint() + "_______________________" + count);
            }
            String orderNo = UUIDOrder.getUUID();
            ByOrders byOrders = new ByOrders();
            byOrders.setIntegralAmount(scoreAmount);
            byOrders.setCouponAmount(couponAmount);
            byOrders.setOrderAmount(orderAmount);
            byOrders.setUserId(user.getId());
            byOrders.setOrderStatus(1);
            byOrders.setPayOrderNo( orderNo );
            byOrders.setActualAmount(payAmount);
            byOrders.setGoodsType(1);
            byOrders.setEvalType(Boolean.FALSE);
            byOrders.setGmtUpdate(new Date());
            if(!ObjectUtil.isEmpty(goods.getPayType())){
                byOrders.setPayType(goods.getPayType().equals(2)?2:1);
            }else{
                byOrders.setPayType(1);
            }
            byOrders.setIsDel(Boolean.FALSE);
            byOrders.setIsRefund(Boolean.FALSE);
            byOrders.setCouponId(goods.getCouponId());
            byOrders.setRemark(goods.getRemark());
            byOrders.setGmtCreate(new Date());
            byOrders.setOrderTime(new Date());
            byOrders.setOrderNo( orderNo );
            byOrders.setRemark(goods.getRemark());
            byOrders.setFromId(goods.getFromId());
            //订单保存到数据库
            byOrdersMapper.insertSelective(byOrders);
            goods.setOrderNo(byOrders.getOrderNo());
            goods.setId(byOrders.getId().toString());
            Object goods1 = pay.get("goods");
            //<-- 疑似新增代码！！
//            ArrayList<ByOrderGoods> orderGoodsList = new ArrayList<>();
            //疑似新增代码！！-->
            //商品详情页跳转支付
            if (goods.getType() == 1) {
                ByOrderGoods byOrderGoods = new ByOrderGoods();
                byOrderGoods.setOrderNo(byOrders.getOrderNo());
                byOrderGoods.setOrderId(byOrders.getId());

                // /* 1 普通商品 2  次卡 3  联票 5规格*/
                if (goods.getGoodsType() == 1) {
                    List<ByGoodsInfo> byGoodsInf = (List<ByGoodsInfo>) goods1;
                    ByGoodsInfo byGoodsInfo = byGoodsInf.get(0);
                    if (goods.getGoodsCount() > byGoodsInfo.getGoodsStock()) {
                        throw new CustomException("库存不足");
                    }
                    goodsService.checkGoodsStock(byGoodsInfo.getId(), goods.getGoodsCount());

                    byOrderGoods.setGoodsId(byGoodsInfo.getId());
                    byOrderGoods.setGoodsImg(byGoodsInfo.getGoodsImg());
                    byOrderGoods.setGoodsName(byGoodsInfo.getGoodsName());
                    byOrderGoods.setGoodsNo(byGoodsInfo.getGoodsNo());
                    byOrderGoods.setGoodsPrice(byGoodsInfo.getSellPrice());
                    byOrderGoods.setGoodsNum((Integer) pay.get("goodsCount"));
                    byOrderGoods.setGmtCreate(new Date());
                    byOrderGoods.setOrderStatus(0);
                    byOrderGoods.setProductId(byGoodsInfo.getId().toString());
                    byOrderGoods.setProductType("1");
                    byOrderGoods.setProductCount(goods.getGoodsCount());
                    byOrderGoods.setOrderNo(byOrders.getOrderNo());
                    byOrderGoods.setIntegralPrice(scoreAmount);
                    byOrderGoods.setCouponPrice(couponAmount);
                    //订单详情进入支付页。只会有同一个商品，可能商品数量不止一个，但是只有一个订单商品
                    this.byOrderGoodsMapper.insertSelective(byOrderGoods);
                    //<-- 新增代码
                    ByOrderGoods tempOrderGoods = ByOrderGoods.builder()
                            .id(byOrderGoods.getId())
                            .goodsPrice(byOrderGoods.getGoodsPrice())
                            .goodsNum(byOrderGoods.getGoodsNum())
                            .build();
                    //计算商品实际支付价格
                    discountOrderGoods(Arrays.asList(tempOrderGoods),byOrders.getOrderAmount().doubleValue()
                            ,byOrders.getIntegralAmount().doubleValue(),
                            byOrders.getCouponAmount().doubleValue(),
                            byOrders.getActualAmount().doubleValue());
                    //更新商品实际支付价格。
                    this.byOrderGoodsMapper.updatePayPrice(tempOrderGoods);
                    // 新增代码 -->

                    //<-- 疑似新增代码！！
//                  orderGoodsList.add(byOrderGoods);
                    //疑似新增代码！！-->

                    Example example = new Example(ByGoodsStore.class);
                    example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
                    List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                    String str = "";
                    if (byGoodsStores.size() != 0) {
                        for (ByGoodsStore byGoodsStore : byGoodsStores) {
                            str += byGoodsStore.getStoreId() + ",";
                        }
                        str = str.substring(0, str.length() - 1);
                    }

                    /*普通商品*/
                    for (int i = 0; i < byOrderGoods.getGoodsNum(); i++) {
                        WriteOffCode writeOffCode = new WriteOffCode();
                        if (str != "") {
                            writeOffCode.setStoreIds(str);
                        }
                        writeOffCode.setDetailId(byOrderGoods.getId());

                        if (!byGoodsInfo.getGoodsName().contains("【") && !StringUtils.isEmpty(byGoodsInfo.getSpecName())) {
                            writeOffCode.setWriteOffName("【" + byGoodsInfo.getGoodsName() + "】" + byGoodsInfo.getSpecName());
                        } else {
                            writeOffCode.setWriteOffName(byGoodsInfo.getGoodsName());
                        }
                        writeOffCode.setCustUserId(user.getId());
                        writeOffCode.setGoodsName(byGoodsInfo.getGoodsName());
                        writeOffCode.setSourceGoodsId(byGoodsInfo.getId());
                        writeOffCode.setSurplusNum(1);
                        writeOffCode.setTotalNum(1);
                        if(ObjectUtil.isNotEmpty(byGoodsInfo.getHasVerificationDay()) && byGoodsInfo.getHasVerificationDay()){
                            Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            writeOffCode.setExpiryDate(date);
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            // 这里是加天数
                            calendar.add(Calendar.DAY_OF_MONTH, byGoodsInfo.getVerificationDay());
                            writeOffCode.setEndDate(calendar.getTime());
                        }else{
                            writeOffCode.setExpiryDate(byGoodsInfo.getVerificationStart());
                            writeOffCode.setEndDate(byGoodsInfo.getVerificationEnd());
                        }
                        writeOffCode.setOrderNo(byOrders.getOrderNo());
                        writeOffCode.setGmtCreate(new Date());
                        writeOffCode.setType(1);
                        this.writeOffCodeMapper.insertSelective(writeOffCode);
                    }
                    /*减少库存*/
                    ByGoodsInfo byGoodsInfo1 = this.byGoodsInfoMapper.selectByPrimaryKey(byGoodsInfo.getId());
                    Integer count = byGoodsInfo1.getGoodsStock() - goods.getGoodsCount();
                    byGoodsInfo.setGoodsStock(count);
                    this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);

                    //如果选择了油菜花预充值套餐
                    // if(ObjectUtil.isNotEmpty(byGoodsInfo.getFirstVerifyBalance()) && byGoodsInfo.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                        // YchGoods query = new YchGoods();
                        // query.setId(byGoodsInfo.getPreDepositId());
                        // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                        // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                    //     memberCardService.reduceMemberBalance(user.getId(), byGoodsInfo.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                    // }
                }
                /*getGoodsType  1 普通商品 2  次卡 3  联票 5规格*/
                if (goods.getType() == 1 && goods.getGoodsType() == 3) {

                    List<BySubCardGoods> by = (List<BySubCardGoods>) goods1;
                    BySubCardGoods bySubCardGoods = by.get(0);
                    if (goods.getGoodsCount() > bySubCardGoods.getGoodsStock()) {
                        throw new CustomException("库存不足");
                    }

                    /*减少库存*/
                    Integer count = bySubCardGoods.getGoodsStock() - goods.getGoodsCount();
                    bySubCardGoods.setGoodsStock(count);
                    this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);

                    //如果选择了油菜花预充值套餐
                    // if(ObjectUtil.isNotEmpty(bySubCardGoods.getFirstVerifyBalance()) && bySubCardGoods.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                        // YchGoods query = new YchGoods();
                        // query.setId(bySubCardGoods.getPreDepositId());
                        // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                        // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                    //     memberCardService.reduceMemberBalance(user.getId(), bySubCardGoods.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                    // }

                    byOrderGoods.setGoodsId(bySubCardGoods.getId());
                    byOrderGoods.setGoodsImg(bySubCardGoods.getGoodsImg());
                    byOrderGoods.setGoodsName(bySubCardGoods.getGoodsName());
                    byOrderGoods.setGoodsPrice(bySubCardGoods.getSellPrice());
                    byOrderGoods.setGoodsNum((Integer) pay.get("goodsCount"));
                    byOrderGoods.setGmtCreate(new Date());
                    byOrderGoods.setOrderStatus(0);
                    byOrderGoods.setProductId(bySubCardGoods.getId().toString());
                    byOrderGoods.setProductType("2");
                    byOrderGoods.setIntegralPrice(scoreAmount);
                    byOrderGoods.setCouponPrice(couponAmount);
                    byOrderGoods.setProductCount(goods.getGoodsCount());
//                    ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(bySubCardGoods.getGoodsId());
                    this.byOrderGoodsMapper.insertSelective(byOrderGoods);


                    Example example = new Example(BySubGoodsStore.class);
                    example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId());
                    List<BySubGoodsStore> bySubGoodsStores = bySubGoodsStoreMapper.selectByExample(example);
                    String str = "";
                    if (bySubGoodsStores.size() != 0) {
                        for (BySubGoodsStore bySubGoodsStore : bySubGoodsStores) {
                            str += bySubGoodsStore.getStoreId() + ",";
                        }
                        str = str.substring(0, str.length() - 1);
                    }

                    /*次卡*/
                    for (int i = 0; i < byOrderGoods.getGoodsNum(); i++) {
                        WriteOffCode writeOffCode = new WriteOffCode();
                        if (str != "") {
                            writeOffCode.setStoreIds(str);
                        }
                        writeOffCode.setDetailId(byOrderGoods.getId());
                        writeOffCode.setWriteOffName(bySubCardGoods.getSubCardGoodsName());
                        writeOffCode.setCustUserId(user.getId());
                        writeOffCode.setGoodsName(bySubCardGoods.getSubCardGoodsName());
                        writeOffCode.setSourceGoodsId(bySubCardGoods.getId());
                        writeOffCode.setSurplusNum(bySubCardGoods.getSubCardGoodsNum());
                        writeOffCode.setTotalNum(bySubCardGoods.getSubCardGoodsNum());
                        if(ObjectUtil.isNotEmpty(bySubCardGoods.getHasVerificationDay()) && bySubCardGoods.getHasVerificationDay()){
                            Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            writeOffCode.setExpiryDate(date);
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            // 这里是加天数
                            calendar.add(Calendar.DAY_OF_MONTH, bySubCardGoods.getVerificationDay());
                            writeOffCode.setEndDate(calendar.getTime());
                        }else{
                            writeOffCode.setExpiryDate(bySubCardGoods.getEffectiveStart());
                            writeOffCode.setEndDate(bySubCardGoods.getEffectiveEnd());
                        }
                        writeOffCode.setOrderNo(byOrders.getOrderNo());
                        writeOffCode.setGmtCreate(new Date());
                        writeOffCode.setType(2);
                        this.writeOffCodeMapper.insertSelective(writeOffCode);
                    }
                }
                /*getGoodsType  1 普通商品 2  次卡 3  联票 5规格*/
                if (goods.getType() == 1 && goods.getGoodsType() == 2) {

                    List<ByTicketGoods> byTicketGoodlist = (List<ByTicketGoods>) goods1;
                    ByTicketGoods byTicketGoods = byTicketGoodlist.get(0);
                    if (goods.getGoodsCount() > byTicketGoods.getGoodsStock()) {
                        throw new CustomException("库存不足");
                    }
                    /*减少库存*/
                    ByTicketGoods byTicketGoods1 = this.byTicketGoodsMapper.selectByPrimaryKey(byTicketGoods.getId());
                    Integer count = byTicketGoods1.getGoodsStock() - goods.getGoodsCount();
                    byTicketGoods1.setGoodsStock(count);
                    this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods1);

                    //如果选择了油菜花预充值套餐
                    // if(ObjectUtil.isNotEmpty(byTicketGoods.getFirstVerifyBalance()) && byTicketGoods.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                        // YchGoods query = new YchGoods();
                        // query.setId(byTicketGoods.getPreDepositId());
                        // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                        // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                    //     memberCardService.reduceMemberBalance(user.getId(), byTicketGoods.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                    // }

                    byOrderGoods.setGoodsId(byTicketGoods.getId());
                    byOrderGoods.setGoodsImg(byTicketGoods.getGoodsImg());
                    byOrderGoods.setGoodsName(byTicketGoods.getGoodsName());
                    byOrderGoods.setGoodsPrice(byTicketGoods.getSellPrice());
                    byOrderGoods.setGoodsNum((Integer) pay.get("goodsCount"));
                    byOrderGoods.setGmtCreate(new Date());
                    byOrderGoods.setOrderStatus(0);
                    byOrderGoods.setProductId(byTicketGoods.getId().toString());
                    byOrderGoods.setProductType("3");
                    byOrderGoods.setProductCount(goods.getGoodsCount());
                    byOrderGoods.setIntegralPrice(scoreAmount);
                    byOrderGoods.setCouponPrice(couponAmount);
                    byOrderGoods.setProductCount(goods.getGoodsCount());
                    this.byOrderGoodsMapper.insertSelective(byOrderGoods);
                    List<ByGoodsInfo> byGoodsInfos = this.byGoodsInfoMapper.selectGoods(byTicketGoods.getId());
                    if (byGoodsInfos.size() != 0) {
                        for (ByGoodsInfo byGoodsInfo : byGoodsInfos) {

                            Example example = new Example(ByGoodsStore.class);
                            example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
                            List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                            String str = "";
                            if (byGoodsStores.size() != 0) {
                                for (ByGoodsStore byGoodsStore : byGoodsStores) {
                                    str += byGoodsStore.getStoreId() + ",";
                                }
                                str = str.substring(0, str.length() - 1);
                            }

                            for (int i = 0; i < byOrderGoods.getGoodsNum(); i++) {
                                WriteOffCode writeOffCode = new WriteOffCode();
                                if (str != "") {
                                    writeOffCode.setStoreIds(str);
                                }
                                writeOffCode.setDetailId(byOrderGoods.getId());
                                writeOffCode.setWriteOffName(byTicketGoods.getTicketGoodsName());
                                writeOffCode.setCustUserId(user.getId());
                                writeOffCode.setGoodsName(byGoodsInfo.getGoodsName());
                                writeOffCode.setSourceGoodsId(byGoodsInfo.getId());
                                writeOffCode.setSurplusNum(1);
                                writeOffCode.setTotalNum(1);
                                if(ObjectUtil.isNotEmpty(byTicketGoods.getHasVerificationDay()) && byTicketGoods.getHasVerificationDay()){
                                    Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                                    writeOffCode.setExpiryDate(date);
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(date);
                                    // 这里是加天数
                                    calendar.add(Calendar.DAY_OF_MONTH, byTicketGoods.getVerificationDay());
                                    writeOffCode.setEndDate(calendar.getTime());
                                }else{
                                    writeOffCode.setExpiryDate(byTicketGoods.getEffectiveStart());
                                    writeOffCode.setEndDate(byTicketGoods.getEffectiveEnd());
                                }
                                writeOffCode.setOrderNo(byOrders.getOrderNo());
                                writeOffCode.setGmtCreate(new Date());
                                writeOffCode.setType(1);
                                this.writeOffCodeMapper.insertSelective(writeOffCode);
                            }
                        }
                    }
                }
                ///*getGoodsType  1 普通商品 2  次卡 3  联票 5规格*/
                if (goods.getType() == 1 && goods.getGoodsType() == 10) {
                    List<ByCombinationGoods> list = (List<ByCombinationGoods>) goods1;
                    ByCombinationGoods byCombinationGoods = list.get(0);
                    if (goods.getGoodsCount() > byCombinationGoods.getGoodsStock()) {
                        throw new CustomException("库存不足");
                    }
                    byOrderGoods.setGoodsId(byCombinationGoods.getId());
                    byOrderGoods.setGoodsImg(byCombinationGoods.getGoodsImg());
                    byOrderGoods.setGoodsName(byCombinationGoods.getName());
                    byOrderGoods.setGoodsPrice(byCombinationGoods.getPrice());
                    byOrderGoods.setGoodsNum((Integer) pay.get("goodsCount"));
                    byOrderGoods.setGmtCreate(new Date());
                    byOrderGoods.setOrderStatus(0);
                    byOrderGoods.setProductId(byCombinationGoods.getId().toString());
                    byOrderGoods.setProductType("5");
                    byOrderGoods.setProductCount(goods.getGoodsCount());
                    byOrderGoods.setOrderNo(byOrders.getOrderNo());
                    byOrderGoods.setIntegralPrice(scoreAmount);
                    byOrderGoods.setCouponPrice(couponAmount);
                    this.byOrderGoodsMapper.insertSelective(byOrderGoods);
                    Example example = new Example(ByGoodsStore.class);
                    example.createCriteria().andEqualTo("goodsId", byCombinationGoods.getGoodsId());
                    List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                    String str = "";
                    if (byGoodsStores.size() != 0) {
                        for (ByGoodsStore byGoodsStore : byGoodsStores) {
                            str += byGoodsStore.getStoreId() + ",";
                        }
                        str = str.substring(0, str.length() - 1);
                    }

                    /*规格商品*/
                    for (int i = 0; i < byOrderGoods.getGoodsNum(); i++) {
                        WriteOffCode writeOffCode = new WriteOffCode();
                        if (str != "") {
                            writeOffCode.setStoreIds(str);
                        }
                        writeOffCode.setDetailId(byOrderGoods.getId());
                        ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCombinationGoods.getGoodsId());
                        writeOffCode.setWriteOffName("【" + byGoodsInfo.getGoodsName() + "】" + byCombinationGoods.getName());
                        writeOffCode.setCustUserId(user.getId());
                        writeOffCode.setGoodsName(byCombinationGoods.getName());
                        writeOffCode.setSourceGoodsId(byCombinationGoods.getId());
                        writeOffCode.setSurplusNum(1);
                        writeOffCode.setTotalNum(1);
                        if(ObjectUtil.isNotEmpty(byCombinationGoods.getHasVerificationDay()) && byCombinationGoods.getHasVerificationDay()){
                            Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                            writeOffCode.setExpiryDate(date);
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(date);
                            // 这里是加天数
                            calendar.add(Calendar.DAY_OF_MONTH, byCombinationGoods.getVerificationDay());
                            writeOffCode.setEndDate(calendar.getTime());
                        }else{
                            writeOffCode.setExpiryDate(byCombinationGoods.getVerificationStart());
                            writeOffCode.setEndDate(byCombinationGoods.getVerificationEnd());
                        }
                        writeOffCode.setOrderNo(byOrders.getOrderNo());
                        writeOffCode.setGmtCreate(new Date());
                        writeOffCode.setType(5);
                        ;
                        this.writeOffCodeMapper.insertSelective(writeOffCode);
                    }
                    /*减少库存*/
                    ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byCombinationGoods.getId());
                    Integer count = combinationGoods.getGoodsStock() - goods.getGoodsCount();
                    combinationGoods.setGoodsStock(count);
                    this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);

                    //如果选择了油菜花预充值套餐
                    // if(ObjectUtil.isNotEmpty(combinationGoods.getFirstVerifyBalance()) && combinationGoods.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                        // YchGoods query = new YchGoods();
                        // query.setId(combinationGoods.getPreDepositId());
                        // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                        // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                    //     memberCardService.reduceMemberBalance(user.getId(), combinationGoods.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                    // }
                }
            }


            //购物车支付商品
            if (goods.getType() == 2) {
                List<ByGoodsShoping> byGoodsShoping = (List<ByGoodsShoping>) pay.get("goods");
                if (goods.getScoreAmount() != null) {
                    //计算积分
                    byGoodsShoping = check(byGoodsShoping, goods.getScoreAmount());
                }
                //  byGoodsShoping = checkAmount(byGoodsShoping);
                //查询购物车中的商品，对这些商品进行付款
                if (byGoodsShoping.size() != 0) {
                    //<---新增代码
                    ArrayList<ByOrderGoods> cartOrderGoods = new ArrayList<>();
                    //新增代码--->
                    for (ByGoodsShoping b : byGoodsShoping) {
                        ByOrderGoods byOrderGoods = new ByOrderGoods();
                        byOrderGoods.setGoodsId(b.getId());
                        byOrderGoods.setGoodsImg(b.getGoodsImg());
                        byOrderGoods.setGoodsName(b.getGoodsName());
                        byOrderGoods.setGoodsPrice(new BigDecimal(b.getSellPrice()));
                        byOrderGoods.setGoodsNum((Integer) pay.get("goodsCount"));
                        byOrderGoods.setGmtCreate(new Date());
                        byOrderGoods.setOrderStatus(0);
                        byOrderGoods.setOrderNo(byOrders.getOrderNo());
                        byOrderGoods.setGoodsNum(b.getGoodsCount());
                        byOrderGoods.setGoodsId(b.getGoodsId());
                        byOrderGoods.setProductId(b.getGoodsId().toString());
                        byOrderGoods.setProductType(b.getGoodsType().toString());
                        byOrderGoods.setProductCount(b.getGoodsCount());
                        byOrderGoods.setOrderId(byOrders.getId());
                        BigDecimal multiply = new BigDecimal(b.getSellPrice()).multiply(new BigDecimal(b.getGoodsCount()));
                        log.info("订单金额" + orderAmount + "商品金额" + multiply + "优惠券" + b.getAmount() + "积分" + b.getInAmount());
                        byOrderGoods.setCouponPrice(b.getAmount());
                        byOrderGoods.setIntegralPrice(b.getInAmount());
                        //添加到数据库 原有代码
                        this.byOrderGoodsMapper.insertSelective(byOrderGoods);
                        //原有代码
                        //<--- 新增代码
                        //新增购物车的商品
                        //添加到集合中。分摊优惠的价格，计算付款时的单价
                        cartOrderGoods.add(
                                ByOrderGoods.builder()
                                        .id(byOrderGoods.getId())
                                        .goodsPrice(byOrderGoods.getGoodsPrice())
                                        .goodsNum(byOrderGoods.getGoodsNum())
                                        .build()
                        );
                        //新增代码 --->
                        if (b.getGoodsType() == 1) {
                            ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(b.getGoodsId());
                            if (b.getGoodsCount() > byGoodsInfo.getGoodsStock()) {
                                throw new CustomException("库存不足");
                            }
                            goodsService.checkGoodsStock(byGoodsInfo.getId(), b.getGoodsCount());

                            /*减少库存*/
                            Integer count = byGoodsInfo.getGoodsStock() - b.getGoodsCount();
                            byGoodsInfo.setGoodsStock(count);
                            this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);

                            //如果选择了油菜花预充值套餐
                            // if(ObjectUtil.isNotEmpty(byGoodsInfo.getFirstVerifyBalance()) && byGoodsInfo.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                                // YchGoods query = new YchGoods();
                                // query.setId(byGoodsInfo.getPreDepositId());
                                // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                                // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                            //     memberCardService.reduceMemberBalance(user.getId(), byGoodsInfo.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                            // }

                            Example example = new Example(ByGoodsStore.class);
                            example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
                            List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                            String str = "";
                            if (byGoodsStores.size() != 0) {
                                for (ByGoodsStore byGoodsStore : byGoodsStores) {
                                    str += byGoodsStore.getStoreId() + ",";
                                }
                                str = str.substring(0, str.length() - 1);
                            }

                            for (int i = 0; i < b.getGoodsCount(); i++) {
                                WriteOffCode writeOffCode = new WriteOffCode();
                                if (str != "") {
                                    writeOffCode.setStoreIds(str);
                                }
                                writeOffCode.setDetailId(byOrderGoods.getId());
                                if (!byGoodsInfo.getGoodsName().contains("【") && !StringUtils.isEmpty(byGoodsInfo.getSpecName())) {
                                    writeOffCode.setWriteOffName("【" + byGoodsInfo.getGoodsName() + "】" + byGoodsInfo.getSpecName());
                                } else {
                                    writeOffCode.setWriteOffName(byGoodsInfo.getGoodsName());
                                }
                                writeOffCode.setCustUserId(user.getId());
                                writeOffCode.setGoodsName(byGoodsInfo.getGoodsName());
                                writeOffCode.setSourceGoodsId(byGoodsInfo.getId());
                                writeOffCode.setSurplusNum(1);
                                writeOffCode.setTotalNum(1);
                                if(ObjectUtil.isNotEmpty(byGoodsInfo.getHasVerificationDay()) && byGoodsInfo.getHasVerificationDay()){
                                    Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                                    writeOffCode.setExpiryDate(date);
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(date);
                                    // 这里是加天数
                                    calendar.add(Calendar.DAY_OF_MONTH, byGoodsInfo.getVerificationDay());
                                    writeOffCode.setEndDate(calendar.getTime());
                                }else{
                                    writeOffCode.setExpiryDate(byGoodsInfo.getVerificationStart());
                                    writeOffCode.setEndDate(byGoodsInfo.getVerificationEnd());
                                }
                                writeOffCode.setOrderNo(byOrders.getOrderNo());
                                writeOffCode.setGmtCreate(new Date());
                                writeOffCode.setType(1);
                                this.writeOffCodeMapper.insertSelective(writeOffCode);
                            }
                            this.byGoodsShopingMapper.update(b.getId());
                        }
                        if (b.getGoodsType() == 2) {

                            BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                            if (b.getGoodsCount() > bySubCardGoods.getGoodsStock()) {
                                throw new CustomException("库存不足");
                            }
                            /*减少库存*/
                            BySubCardGoods bySubCardGoods1 = bySubCardGoodsMapper.selectByPrimaryKey(bySubCardGoods.getId());
                            Integer count = bySubCardGoods1.getGoodsStock() - b.getGoodsCount();
                            bySubCardGoods1.setGoodsStock(count);
                            this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods1);

                            //如果选择了油菜花预充值套餐
                            // if(ObjectUtil.isNotEmpty(bySubCardGoods1.getFirstVerifyBalance()) && bySubCardGoods1.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                                // YchGoods query = new YchGoods();
                                // query.setId(bySubCardGoods1.getPreDepositId());
                                // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                                // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                            //     memberCardService.reduceMemberBalance(user.getId(), bySubCardGoods1.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                            // }

                            Example example = new Example(BySubGoodsStore.class);
                            example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId());
                            List<BySubGoodsStore> bySubGoodsStores = bySubGoodsStoreMapper.selectByExample(example);
                            String str = "";
                            if (bySubGoodsStores.size() != 0) {
                                for (BySubGoodsStore bySubGoodsStore : bySubGoodsStores) {
                                    str += bySubGoodsStore.getStoreId() + ",";
                                }
                                str = str.substring(0, str.length() - 1);
                            }
                            for (int i = 0; i < b.getGoodsCount(); i++) {
                                WriteOffCode writeOffCode = new WriteOffCode();
                                if (str != "") {
                                    writeOffCode.setStoreIds(str);
                                }
                                writeOffCode.setDetailId(byOrderGoods.getId());
                                writeOffCode.setWriteOffName(bySubCardGoods.getSubCardGoodsName());
                                writeOffCode.setCustUserId(user.getId());
                                writeOffCode.setGoodsName(bySubCardGoods.getSubCardGoodsName());
                                writeOffCode.setSourceGoodsId(bySubCardGoods.getId());
                                writeOffCode.setSurplusNum(bySubCardGoods.getSubCardGoodsNum());
                                writeOffCode.setTotalNum(bySubCardGoods.getSubCardGoodsNum());
                                if(ObjectUtil.isNotEmpty(bySubCardGoods.getHasVerificationDay()) && bySubCardGoods.getHasVerificationDay()){
                                    Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                                    writeOffCode.setExpiryDate(date);
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(date);
                                    // 这里是加天数
                                    calendar.add(Calendar.DAY_OF_MONTH, bySubCardGoods.getVerificationDay());
                                    writeOffCode.setEndDate(calendar.getTime());
                                }else{
                                    writeOffCode.setExpiryDate(bySubCardGoods.getEffectiveStart());
                                    writeOffCode.setEndDate(bySubCardGoods.getEffectiveEnd());
                                }
                                writeOffCode.setOrderNo(byOrders.getOrderNo());
                                writeOffCode.setGmtCreate(new Date());
                                writeOffCode.setType(2);
                                this.writeOffCodeMapper.insertSelective(writeOffCode);
                            }
                            this.byGoodsShopingMapper.update(b.getId());
                        }
                        if (b.getGoodsType() == 3) {
                            ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                            if (b.getGoodsCount() > byTicketGoods.getGoodsStock()) {
                                throw new CustomException("库存不足");
                            }
                            /*减少库存*/
                            Integer count = byTicketGoods.getGoodsStock() - b.getGoodsCount();
                            byTicketGoods.setGoodsStock(count);
                            this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);

                            //如果选择了油菜花预充值套餐
                            // if(ObjectUtil.isNotEmpty(byTicketGoods.getFirstVerifyBalance()) && byTicketGoods.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                                // YchGoods query = new YchGoods();
                                // query.setId(byTicketGoods.getPreDepositId());
                                // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                                // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                            //     memberCardService.reduceMemberBalance(user.getId(), byTicketGoods.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                            // }

                            List<ByGoodsInfo> byGoodsInfos = this.byGoodsInfoMapper.selectGoods(byTicketGoods.getId());
                            if (byGoodsInfos.size() != 0) {
                                for (ByGoodsInfo byGoodsInfo : byGoodsInfos) {

                                    Example example = new Example(ByGoodsStore.class);
                                    example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
                                    List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                                    String str = "";
                                    if (byGoodsStores.size() != 0) {
                                        for (ByGoodsStore byGoodsStore : byGoodsStores) {
                                            str += byGoodsStore.getStoreId() + ",";
                                        }
                                        str = str.substring(0, str.length() - 1);
                                    }

                                    for (int i = 0; i < b.getGoodsCount(); i++) {
                                        WriteOffCode writeOffCode = new WriteOffCode();
                                        if (str != "") {
                                            writeOffCode.setStoreIds(str);
                                        }
                                        writeOffCode.setDetailId(byOrderGoods.getId());
                                        writeOffCode.setWriteOffName(byTicketGoods.getTicketGoodsName());
                                        writeOffCode.setCustUserId(user.getId());
                                        writeOffCode.setGoodsName(byGoodsInfo.getGoodsName());
                                        writeOffCode.setSourceGoodsId(byGoodsInfo.getId());
                                        writeOffCode.setSurplusNum(b.getGoodsCount());
                                        writeOffCode.setTotalNum(b.getGoodsCount());
                                        if(ObjectUtil.isNotEmpty(byTicketGoods.getHasVerificationDay()) && byTicketGoods.getHasVerificationDay()){
                                            Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                                            writeOffCode.setExpiryDate(date);
                                            Calendar calendar = Calendar.getInstance();
                                            calendar.setTime(date);
                                            // 这里是加天数
                                            calendar.add(Calendar.DAY_OF_MONTH, byTicketGoods.getVerificationDay());
                                            writeOffCode.setEndDate(calendar.getTime());
                                        }else{
                                            writeOffCode.setExpiryDate(byTicketGoods.getEffectiveStart());
                                            writeOffCode.setEndDate(byTicketGoods.getEffectiveEnd());
                                        }
                                        writeOffCode.setOrderNo(byOrders.getOrderNo());
                                        writeOffCode.setGmtCreate(new Date());
                                        writeOffCode.setType(1);
                                        this.writeOffCodeMapper.insertSelective(writeOffCode);
                                    }
                                }
                                this.byGoodsShopingMapper.update(b.getId());
                            }
                        }
                        if (b.getGoodsType() == 5) {
                            ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                            if (b.getGoodsCount() > combinationGoods.getGoodsStock()) {
                                throw new CustomException("库存不足");
                            }
                            /*减少库存*/
                            Integer count = combinationGoods.getGoodsStock() - b.getGoodsCount();
                            combinationGoods.setGoodsStock(count);
                            this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);

                            //如果选择了油菜花预充值套餐
                            // if(ObjectUtil.isNotEmpty(combinationGoods.getFirstVerifyBalance()) && combinationGoods.getFirstVerifyBalance().compareTo(BigDecimal.ZERO) > 0){
                                // YchGoods query = new YchGoods();
                                // query.setId(combinationGoods.getPreDepositId());
                                // YchGoods ychGoods = ychApiService.getGoodsDetail(query);
                                // ychApiService.createOrder(ychGoods, byOrders.getOrderNo());
                                // memberCardService.reduceMemberBalance(user.getId(), combinationGoods.getFirstVerifyBalance(), byOrders.getOrderNo(), "商品购买扣除");
                            // }

                            Example example = new Example(ByGoodsStore.class);
                            example.createCriteria().andEqualTo("goodsId", combinationGoods.getGoodsId());
                            List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
                            String str = "";
                            if (byGoodsStores.size() != 0) {
                                for (ByGoodsStore byGoodsStore : byGoodsStores) {
                                    str += byGoodsStore.getStoreId() + ",";
                                }
                                str = str.substring(0, str.length() - 1);
                            }

                            for (int i = 0; i < b.getGoodsCount(); i++) {
                                WriteOffCode writeOffCode = new WriteOffCode();
                                if (str != "") {
                                    writeOffCode.setStoreIds(str);
                                }
                                writeOffCode.setDetailId(byOrderGoods.getId());
                                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(combinationGoods.getGoodsId());
                                writeOffCode.setWriteOffName("【" + byGoodsInfo.getGoodsName() + "】" + combinationGoods.getName());
                                writeOffCode.setCustUserId(user.getId());
                                writeOffCode.setGoodsName(combinationGoods.getName());
                                writeOffCode.setSourceGoodsId(combinationGoods.getId());
                                writeOffCode.setSurplusNum(1);
                                writeOffCode.setTotalNum(1);
                                if(ObjectUtil.isNotEmpty(combinationGoods.getHasVerificationDay()) && combinationGoods.getHasVerificationDay()){
                                    Date date = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
                                    writeOffCode.setExpiryDate(date);
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(date);
                                    // 这里是加天数
                                    calendar.add(Calendar.DAY_OF_MONTH, combinationGoods.getVerificationDay());
                                    writeOffCode.setEndDate(calendar.getTime());
                                }else{
                                    writeOffCode.setExpiryDate(combinationGoods.getVerificationStart());
                                    writeOffCode.setEndDate(combinationGoods.getVerificationEnd());
                                }
                                writeOffCode.setOrderNo(byOrders.getOrderNo());
                                writeOffCode.setGmtCreate(new Date());
                                writeOffCode.setType(1);
                                this.writeOffCodeMapper.insertSelective(writeOffCode);
                            }
                            this.byGoodsShopingMapper.update(b.getId());
                        }
                    }

                    //<--- 新增代码
                    //计算每件商品实际支付价格
                    discountOrderGoods(cartOrderGoods, DecimalUtil.doubleValue(byOrders.getOrderAmount())
                            ,DecimalUtil.doubleValue(byOrders.getIntegralAmount()),
                            DecimalUtil.doubleValue(byOrders.getCouponAmount()),
                            DecimalUtil.doubleValue(byOrders.getActualAmount()));
                    //更新订单实际支付价格
                    cartOrderGoods.forEach(cartGoods->this.byOrderGoodsMapper.updatePayPrice(cartGoods));
                    //新增代码 --->
                }
            }
        }
        //未支付订单的支付
        else {
            ByOrders byOrders = this.byOrdersMapper.selectByPrimaryKey(goods.getId());
            orderGoodsTag = byOrders.getGoodsTag();
            ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
            if (byPlatformSet != null || byPlatformSet.getPayTime() != null || byPlatformSet.getPayTime() != 0) {
                Calendar instance = Calendar.getInstance();
                Calendar in = instance;
                in.setTime(new Date());
                instance.setTime(byOrders.getGmtCreate());
                instance.add(Calendar.HOUR_OF_DAY, byPlatformSet.getPayTime());
                if (in.getTime().getTime() > instance.getTime().getTime()) {
                    if (byOrders.getOrderStatus() == 1) {
                        byOrders.setOrderStatus(4);
                        byOrders.setGmtUpdate(new Date());
                        int i = this.byOrdersMapper.updateByPrimaryKeySelective(byOrders);
                        if (i != 1) {
                            if (byOrders.getIntegralAmount() != null && byOrders.getIntegralAmount().compareTo(new BigDecimal(0)) != 0) {
                                ByPlatformSet byPlatformSet1 = byPlatformSetMapper.selectByPrimaryKey(1);
                                BigDecimal bigDecimal = byPlatformSet1.getIntegralReturn().multiply(byOrders.getActualAmount());
                                BigDecimal divide = byOrders.getIntegralAmount().divide(bigDecimal, 2, BigDecimal.ROUND_HALF_EVEN);
                                ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
                                ByIntegralLog byIntegralLog = new ByIntegralLog();
                                byIntegralLog.setUserId(user.getId());
                                byIntegralLog.setChangeType(1);
                                byIntegralLog.setChangeNum(divide.intValue());
                                byIntegralLog.setChangeReason("商品退款");
                                byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                                byIntegralLog.setGmtCreate(new Date());
                                byIntegralLog.setIntegralType(1);
                                byIntegralLogMapper.insertSelective(byIntegralLog);
                                this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(), divide.intValue());

                            }
                        }
                    }
                    throw new CustomException("订单已超时");
                }

            }
            goods.setPayAmount( byOrders.getActualAmount() );
            goods.setOrderNo( byOrders.getOrderNo() );
            if(StringUtils.isEmpty(goods.getPayType())){
                byOrders.setPayType(1);
            }else{
                byOrders.setPayType(goods.getPayType().equals(2)?2:1);
            }
            byOrders.setPayOrderNo( byOrders.getOrderNo() );
            this.byOrdersMapper.updateByPrimaryKeySelective(byOrders);

        }
        if(!StringUtils.isEmpty(goods.getPayType()) && goods.getPayType().equals(2)) {
            ByOrders byOrders = this.byOrdersMapper.selectByPrimaryKey(goods.getId());
            Map<String, Object> data = new HashMap<>();
            data.put("orderId", goods.getId());
            data.put("orderNo", goods.getOrderNo());
            data.put("payAmount", goods.getPayAmount());
            String TransId = "VAL"+UUIDOrder.getUUID();
            //更新系统的支付状态
            update(goods.getOrderNo(), TransId);
            // byOrders.setPayFlowNo(TransId);
            // //更新油菜花系统余额
            // ychApiService.LeaguerPrepaidChange(byOrders);
            memberCardService.reduceMemberBalance(user.getId(), goods.getPayAmount(), byOrders.getOrderNo(), "商品购买扣除");
            return data;
        }

//        if (goods.getType() != 3 && (goods.getPayAmount().compareTo(new BigDecimal(0)) == 0 || goods.getOrderAmount().compareTo(goods.getScoreAmount()) == 0)) {
//            Map<String, Object> data = new HashMap<>();
//            data.put("type", 0);
//            update(goods.getOrderNo(), "");
//            return data;
//        }
        WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
        wechatJsPayDTO.setOrderNo(goods.getOrderNo());
        wechatJsPayDTO.setTotalFee(goods.getPayAmount());
        //wechatJsPayDTO.setTotalFee(new BigDecimal(0.01));
        wechatJsPayDTO.setNotifyUrl(wechatPayProperties.getPayNotifyUrl());
//        wechatJsPayDTO.setNotifyUrl("https://wx.byloft.net/v2/api/pay/updateOrder");
//        wechatJsPayDTO.setNotifyUrl("https://wx.byloft.net/api/pay/updateOrder");
//        wechatJsPayDTO.setNotifyUrl("http://api.baoyan.llons.com/api/pay/updateOrder");
//        wechatJsPayDTO.setNotifyUrl("https:///dengyinghui-wechat-baoyan.f.wmeimob.com/api/pay/updateOrder");
        Wechat wechat = wechatService.getApiComponent();
        wechatJsPayDTO.setOpenid(user.getWxOpenId());
        wechatJsPayDTO.setAppid(wechat.getAppId());
        wechatJsPayDTO.setIsTest(false);
        wechatPayName = writeOffCodeMapper.getPayPrdName(goods.getOrderNo());
        log.info("wechatPayName : {}", wechatPayName);

        if ( !DecimalUtil.notZeroNull(goods.getPayAmount()) && !goods.isPayAll() ){
            throw new CustomException("价格不能为0");
        }

        wechatJsPayDTO.setBody(wechatPayName.length() > 20 ? wechatPayName.substring(0, 20) : wechatPayName);

        Map<String, Object> data = new HashMap<>();

        ByGoodsInfo goodsInfo = null;
        String goodsTag = "";

        if (goods.getType() != 3){

            if(goods.getGoods() != null){

                // 遍历商品的微信优惠标签
                for (ByGoodsShoping item : goods.getGoods()){

                    Integer _goodsid = item.getGoodsId();

                    switch(item.getGoodsType()){
                        default:
                        case 1:
                            Map<String, Object> detail = goodsService.detail(_goodsid);
                            goodsInfo = (ByGoodsInfo) detail.get("byGoodsInfo");
                            if (goodsInfo != null && goodsInfo.getGoodsTag() != null){
                                goodsTag = goodsInfo.getGoodsTag();
                            }
                            break;
                        case 3:
                        case 2:
                            BySubCardGoods bySubCardGoods = (BySubCardGoods)this.goodsService.cardGoods(_goodsid).get("bySubCardGoods");
                            if (bySubCardGoods != null && bySubCardGoods.getGoodsTag() != null){
                                goodsTag = bySubCardGoods.getGoodsTag();
                            }
                            break;
                    }
                }
            }

            // 单一商品购买匹配优惠标签
            if (goods.getGoodsType() != null){
                switch(goods.getGoodsType()){
                    default:
                    case 1:
                        goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(originalGoodsId);
                        if (goodsInfo != null && goodsInfo.getGoodsTag() != null){
                            goodsTag = goodsInfo.getGoodsTag();
                        }
                        break;
                    case 3:
                    case 2:
                        BySubCardGoods bySubCardGoods = (BySubCardGoods)this.goodsService.cardGoods(Integer.valueOf(originalGoodsId)).get("bySubCardGoods");
                        if (bySubCardGoods != null && bySubCardGoods.getGoodsTag() != null){
                            goodsTag = bySubCardGoods.getGoodsTag();
                        }
                        break;
                }
            }
        }

        if(!goodsTag.equals("")){
            wechatJsPayDTO.setGoodsTag(goodsTag);

            // 检查订单里面是否有 标签，没有就帮上。
            ByOrders tmpByOrders = this.byOrdersMapper.selectByPrimaryKey(goods.getId());
            if (tmpByOrders != null && Objects.equals(tmpByOrders.getGoodsTag(), null)){
                tmpByOrders.setGoodsTag(goodsTag);
                this.byOrdersMapper.updateByPrimaryKeySelective(tmpByOrders);
            }
        }

        if (orderGoodsTag != null && !orderGoodsTag.equals("")){
            wechatJsPayDTO.setGoodsTag(orderGoodsTag);
        }

        if ( !goods.isPayAll() ){
            //单独支付的话需要拉取微信支付信息，混合支付由payAll进行拉取
            //准备微信支付的信息
            JsPayResponse jsPayResponse = wepay.prePay(wechatJsPayDTO);
            data.put("pay", jsPayResponse);
        }

        data.put("orderId", goods.getId());
        data.put("orderNo", goods.getOrderNo( ));
        data.put("payAmount", goods.getPayAmount() );
        data.put("type", 1);

        log.warn("goodstag desc:{}", wechatJsPayDTO.getGoodsTag());
        return data;
    }


    /**
     * 计算订单商品优惠后的价格
     * @param byOrderGoods 订单商品
     * @param orderAmount 订单原价
     * @param integralAmount 抵扣的积分
     * @param couponAmount 优惠券抵扣的金额
     * @param actualAmount 实收金额
     * @return
     */
    private List<ByOrderGoods> discountOrderGoods(
            List<ByOrderGoods> byOrderGoods, Double orderAmount, Double integralAmount,
            Double couponAmount,Double actualAmount
    ) {
        //如果没有使用优惠券和积分，商品单价就是商品原价
        if (DoubleUtil.blankDouble(couponAmount) && DoubleUtil.blankDouble(integralAmount)){
            byOrderGoods.forEach(orderGood -> orderGood.setPayUnitPrice(orderGood.getGoodsPrice()));
            return byOrderGoods;
        }

        //实际支付价格，单位分
        int actual = (int) (actualAmount*100);
        //商品原价，单位分
        double orderTotal = orderAmount*100;

        //actualTotal 下面计算出的实际支付价格总和，单位分
        int actualTotal = 0;

        int goodsCount = 0;
        //计算当前商品占实付金额的比例
        for (ByOrderGoods orderGood : byOrderGoods) {
            //商品原价，单位分
            double goodsPrice = orderGood.getGoodsPrice().doubleValue()*100;
            //当前商品单价占订单原价的百分比
            double scale = goodsPrice/orderTotal;
            //根据比例计算实际支付单价
            int tempUnitPrice = (int) (scale*actual);
            //设置到订单商品中去
            orderGood.setTempUnitPrice(tempUnitPrice);
            actualTotal += tempUnitPrice*orderGood.getGoodsNum();
            goodsCount += orderGood.getGoodsNum();
        }

        //判断拆分计算的价格和实付价格是不是一样
        int difference = actual - actualTotal;
        log.warn("实付价格{},计算的总价{},差价{}",actual,actualTotal,difference);

        trimOrderGoodsPrice(byOrderGoods, difference, goodsCount);

        return byOrderGoods;
    }

    /**
     * 计算订单商品优惠后的价格
     * @param tcOrderGoods 订单商品
     * @param orderAmount 订单原价
     * @param integralAmount 抵扣的积分
     * @param couponAmount 优惠券抵扣的金额
     * @param actualAmount 实收金额
     * @return
     */
    private List<TcOrderGoods> discountTcOrderGoods(
            List<TcOrderGoods> tcOrderGoods, Double orderAmount, Double integralAmount,
            Double couponAmount,Double actualAmount
    ) {


        //如果没有使用优惠券和积分，商品单价就是商品原价
        if (DoubleUtil.blankDouble(couponAmount) && DoubleUtil.blankDouble(integralAmount)){
            tcOrderGoods.forEach(orderGood -> orderGood.setUnitPrice(orderGood.getGoodsPrice()));
            return tcOrderGoods;
        }

        //实际支付价格，单位分
        int actual = (int) (actualAmount*100);
        //商品原价，单位分
        double orderTotal = orderAmount*100;

        //actualTotal 下面计算出的实际支付价格总和，单位分
        int actualTotal = 0;
        int goodsCount = 0;

        //计算当前商品占实付金额的比例
        for (TcOrderGoods orderGood : tcOrderGoods) {
            //商品原价，单位分
            double goodsPrice = orderGood.getGoodsPrice().doubleValue()*100;
            //当前商品单价占订单原价的百分比
            double scale = goodsPrice/orderTotal;
            //根据比例计算实际支付单价
            int tempUnitPrice = (int) (scale*actual);
            //设置到订单商品中去
            orderGood.setTempUnitPrice(tempUnitPrice);
            actualTotal += tempUnitPrice*orderGood.getGoodsCount();
            goodsCount += orderGood.getGoodsCount();
        }

        //判断拆分计算的价格和实付价格是不是一样
        int difference = actual - actualTotal;
        log.warn("实付价格{},计算的总价{},差价{}",actual,actualTotal,difference);

        trimTcOrderGoodsPrice(tcOrderGoods, difference, goodsCount);

        return tcOrderGoods;
    }

    /**
     * 淘潮玩方法
     * 调整淘潮玩订单商品的价格
     * @param byOrderGoods 从这些订单商品里调整
     * @param difference 一共调整这么多金额
     * @param goodsCount 商品数量
     * @return
     */
    private List<ByOrderGoods> trimOrderGoodsPrice(List<ByOrderGoods> byOrderGoods, int difference, int goodsCount){
        if (difference==0){
            return byOrderGoods;
        }

        if ( difference>0 ){
            //支付金额大于拆分计算后的价格，订单商品需要增加 实际支付单价
            //计算每个商品至少增加多少
            int minAdd = difference/goodsCount;

            //全部进行增加这个金额
            for (ByOrderGoods good : byOrderGoods) {
                int newUnitPrice = good.getTempUnitPrice()+minAdd;
                good.setTempUnitPrice(newUnitPrice);
                good.setPayUnitPrice(BigDecimal.valueOf(newUnitPrice*0.01));
                log.warn("{}:{}",good.getGoodsName(),good.getPayUnitPrice());
            }

        }else {
            //支付金额小于拆分计算后的价格，订单商品需要减少 实际支付价格
            //+1 为了保证支付金额一定大于全部可退款金额的和 最多用户会少退款 商品数量*1分的金额
            int minAdd = (difference/goodsCount)+1;

            //全部进行减少这个金额
            for (ByOrderGoods good : byOrderGoods) {
                int newUnitPrice = good.getTempUnitPrice()-minAdd;
                good.setTempUnitPrice(newUnitPrice);
                good.setPayUnitPrice(BigDecimal.valueOf(newUnitPrice*0.01));
                log.warn("{}:{}",good.getGoodsName(),good.getPayUnitPrice());
            }
        }

        return byOrderGoods;
    }

    /**
     * 淘潮玩方法
     * 调整淘潮玩订单商品的价格
     * @param tcOrderGoods 从这些订单商品里调整
     * @param difference 一共调整这么多金额
     * @param goodsCount 商品数量
     * @return
     */
    private List<TcOrderGoods> trimTcOrderGoodsPrice(List<TcOrderGoods> tcOrderGoods, int difference, int goodsCount){

        //价格没有问题，直接返回
        if (difference==0){
            for (TcOrderGoods good : tcOrderGoods) {
                int newUnitPrice = good.getTempUnitPrice();
                good.setUnitPrice( BigDecimal.valueOf(newUnitPrice*0.01) );
            }

            return tcOrderGoods;
        }

        if ( difference>0 ){
            //支付金额大于拆分计算后的价格，订单商品需要增加 实际支付单价
            //计算每个商品至少增加多少
            int minAdd = difference/goodsCount;

            //全部进行增加这个金额
            for (TcOrderGoods good : tcOrderGoods) {
                int newUnitPrice = good.getTempUnitPrice()+minAdd;
                good.setTempUnitPrice(newUnitPrice);
                good.setUnitPrice(BigDecimal.valueOf(newUnitPrice*0.01));
//                log.warn("{}:{}",good.getTcGoodsName(),good.getUnitPrice());
            }

        }else {
            //支付金额小于拆分计算后的价格，订单商品需要减少 实际支付价格
            //+1 为了保证支付金额一定大于全部可退款金额的和 最多用户会少退款 商品数量*1分的金额
            int minAdd = (difference/goodsCount)+1;

            //全部进行减少这个金额
            for (TcOrderGoods good : tcOrderGoods) {
                int newUnitPrice = good.getTempUnitPrice()-minAdd;
                good.setTempUnitPrice(newUnitPrice);
                good.setUnitPrice(BigDecimal.valueOf(newUnitPrice*0.01));
//                log.warn("{}:{}",good.getTcGoodsName(),good.getUnitPrice());
            }
        }

        return tcOrderGoods;
    }

    /**
     * 处理金额为负数
     */
    private List<ByGoodsShoping> checkAmount(List<ByGoodsShoping> shopings) {
        BigDecimal checkAmount = new BigDecimal(0);

        for (ByGoodsShoping shoping : shopings) {
            //优惠券平摊金额
            BigDecimal amount = shoping.getAmount() == null ? BigDecimal.ZERO : shoping.getAmount();
            //积分平摊金额
            BigDecimal inAmount = shoping.getInAmount() == null ? BigDecimal.ZERO : shoping.getInAmount();
            //单个商品的金额
            BigDecimal sellPrice = new BigDecimal(shoping.getSellPrice());
            //商品的数量
            BigDecimal goodsCount = new BigDecimal(shoping.getGoodsCount());
            //单笔订单的总金额
            BigDecimal sumGoods = sellPrice.multiply(goodsCount);
            if (amount.add(inAmount).compareTo(sumGoods) > 0) {
                BigDecimal subtract = amount.add(inAmount).subtract(sumGoods);


            }

        }


        /** 将所有 金额大于订单金额取出来*/
        for (ByGoodsShoping shoping : shopings) {
            if (shoping.getInAmount() != null && shoping.getAmount() != null) {
                BigDecimal totalAmount = shoping.getAmount().add(shoping.getInAmount());
                BigDecimal goodsAmount = new BigDecimal(shoping.getGoodsCount()).multiply(new BigDecimal(shoping.getSellPrice()));
                //计算总的优惠金额 是否大于优惠金额
                if (totalAmount.compareTo(goodsAmount) >= 0) {
                    //计算两者差额
                    BigDecimal subtractAmount = totalAmount.subtract(goodsAmount);
                    //递增差额
                    checkAmount = checkAmount.add(subtractAmount);
                    // 处理优惠卷和积分大于商品金额
                    BigDecimal subtract = shoping.getAmount().subtract(subtractAmount);
                    shoping.setAmount(subtract);
                }
            }
        }
        if (checkAmount.compareTo(new BigDecimal(0)) == 0) {
            return shopings;
        }
        //剩余 商品list
        for (ByGoodsShoping shoping : shopings) {
            BigDecimal totalGoodAmount = new BigDecimal(shoping.getGoodsCount()).multiply(new BigDecimal(shoping.getSellPrice()));
            BigDecimal totalAmount = shoping.getAmount().add(shoping.getInAmount());
            log.info("checkAmount : totalGoodAmount totalAmount", totalGoodAmount, totalAmount);
            if (totalAmount.compareTo(totalGoodAmount) < 0) {
                // 差价
                BigDecimal subtractAmount = totalGoodAmount.subtract(totalAmount);
                log.info("checkAmount : subtractAmount", subtractAmount);
                if (subtractAmount != null && subtractAmount.compareTo(new BigDecimal(0)) != -1 && checkAmount.compareTo(subtractAmount) > 0) {
                    // 填补差价
                    BigDecimal addAmount = shoping.getAmount().add(subtractAmount);
                    shoping.setAmount(addAmount);
                    checkAmount = checkAmount.subtract(subtractAmount);
                }
                if (subtractAmount != null && subtractAmount.compareTo(new BigDecimal(0)) != -1 && checkAmount.compareTo(subtractAmount) < 0) {
                    // 填补差价
                    BigDecimal addAmount = checkAmount;
                    shoping.setAmount(addAmount);
                    checkAmount = new BigDecimal(0);
                    break;
                }
                log.info("checkAmount : subtractAmount", subtractAmount);
            }
//            BigDecimal yuBig = totalGoodAmount.subtract(totalAmount);
//            if (checkAmount.compareTo(yuBig) > 0) {
//                checkAmount = checkAmount.subtract(yuBig);
//                shoping.setAmount(shoping.getAmount().add(yuBig));
//                continue;
//            }
//            shoping.setAmount(shoping.getAmount().add(checkAmount));
//            break;
//
        }
//        shopings.addAll(shopingsCopy);

        /**将多余的金额平摊*/
        /*if (checkAmount.compareTo(new BigDecimal(0)) == 1) {
            for (ByGoodsShoping shoping : shopings) {
                BigDecimal totalAmount = shoping.getAmount().add(shoping.getInAmount());
                BigDecimal goodsAmount = new BigDecimal(shoping.getGoodsCount()).multiply(new BigDecimal(shoping.getSellPrice()));
                if (totalAmount.compareTo(goodsAmount) == -1) {
                    BigDecimal subtractAmount = goodsAmount.subtract(totalAmount);
                    if (subtractAmount.compareTo(checkAmount) != 1) {
                        checkAmount = checkAmount.subtract(subtractAmount);
                        shoping.setAmount(subtractAmount.add(shoping.getAmount()));
                    } else {
                        shoping.setAmount(checkAmount);
                        checkAmount = new BigDecimal(0);
                    }
                }
            }
        }*/


        return shopings;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateChannel(String orderNo) {
        //记录门票支付日志
        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .orderNo(orderNo.toString())
                        .operatorType(1)
                        .logType(OrderLogType.PAY)
                        .build());

        Example example = new Example(ByOrders.class);
        example.createCriteria().andEqualTo("orderNo", orderNo);
        List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example);
        if (byOrders.size() != 0) {
            ByOrders byOrders1 = byOrders.get(0);
            byOrders1.setOrderStatus(2);
            byOrders1.setPayTime(new Date());
            this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
            Example goods = new Example(ByOrderGoods.class);
            goods.createCriteria().andEqualTo("orderId", byOrders1.getId());
            List<ByOrderGoods> byOrderGoods1 = this.byOrderGoodsMapper.selectByExample(goods);
            for (ByOrderGoods byOrderGoods : byOrderGoods1) {
                Example example1 = new Example(WriteOffCode.class);
                example1.createCriteria().andEqualTo("detailId", byOrderGoods.getId()).andEqualTo("orderType", 0);
                List<WriteOffCode> writeOffCodes = writeOffCodeMapper.selectByExample(example1);
                if (writeOffCodes.size() != 0) {
                    for (WriteOffCode writeOffCode : writeOffCodes) {
                        writeOffCode.setOrderState(0);
                        this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
                    }
                }
                byOrderGoods.setOrderNo(byOrders1.getOrderNo());
                byOrderGoods.setOrderStatus(2);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
            }
            String str = "";
            Example example1 = new Example(ByOrderGoods.class);
            example1.createCriteria().andEqualTo("orderNo", byOrders1.getOrderNo());
            List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
            for (ByOrderGoods b : byOrderGoods) {
                str += b.getGoodsName();
                /*增加销量*/
                if ("1".equals(b.getProductType())) {
                    ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(b.getGoodsId());
                    byGoodsInfo.setActualSalesNum(b.getGoodsNum() + byGoodsInfo.getActualSalesNum());

                    this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
                }
                if ("2".equals(b.getProductType())) {
                    BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    bySubCardGoods.setActualSalesNum(b.getGoodsNum() + bySubCardGoods.getActualSalesNum());
                    this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
                }
                if ("3".equals(b.getProductType())) {
                    ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    byTicketGoods.setActualSalesNum(b.getGoodsNum() + byTicketGoods.getActualSalesNum());
                    this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
                }
                if ("5".equals(b.getProductType())) {
                    ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    combinationGoods.setActualSalesNum(b.getGoodsNum() + (combinationGoods.getActualSalesNum() == null ? 0 : combinationGoods.getActualSalesNum()));
                    this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
                }
            }
            //购买成功推送消息
            Map<String, String> map = new HashMap<>();
            ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(byOrders.get(0).getUserId());
            map.put("keyword1", DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
            map.put("keyword2", str);
            map.put("keyword3", orderNo);
            map.put("keyword4", byOrders.get(0).getOrderAmount().toString());
            messageUtil.sendWxTemplateMsg(map, byCustUser.getWxOpenId(), "Lk6ArTvbCfOAFg7CklhtmY-Gj4RuDAHb123eDRqYmKQ", byOrders.get(0).getFromId(), "pages/main/Order/orderDetails/main?id=" + byOrders1.getId());
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(Object out_trade_no, Object transaction_id) {
        //记录门票支付日志
        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .orderNo(out_trade_no.toString())
                        .operatorType(1)
                        .logType(OrderLogType.PAY)
                        .build());

        Example example = new Example(ByOrders.class);
        example.createCriteria().andEqualTo("orderNo", out_trade_no);
        List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example);
        if (byOrders.size() != 0) {
            if (StringUtils.isEmpty(transaction_id)) {
                transaction_id = "";
            }
            ByOrders byOrders1 = byOrders.get(0);
            byOrders1.setOrderStatus(2);
            byOrders1.setPayFlowNo((String) transaction_id);
            byOrders1.setPayTime(new Date());
            this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
            Example goods = new Example(ByOrderGoods.class);
            goods.createCriteria().andEqualTo("orderId", byOrders1.getId());
            List<ByOrderGoods> byOrderGoods1 = this.byOrderGoodsMapper.selectByExample(goods);
            for (ByOrderGoods byOrderGoods : byOrderGoods1) {
                Example example1 = new Example(WriteOffCode.class);
                example1.createCriteria().andEqualTo("detailId", byOrderGoods.getId()).andEqualTo("orderType", 0);
                List<WriteOffCode> writeOffCodes = writeOffCodeMapper.selectByExample(example1);
                if (writeOffCodes.size() != 0) {
                    for (WriteOffCode writeOffCode : writeOffCodes) {
                        writeOffCode.setOrderState(0);
                        this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
                    }
                }
                byOrderGoods.setOrderNo(byOrders1.getOrderNo());
                byOrderGoods.setOrderStatus(2);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
            }
            String str = "";
            Example example1 = new Example(ByOrderGoods.class);
            example1.createCriteria().andEqualTo("orderNo", byOrders1.getOrderNo());
            List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
            for (ByOrderGoods b : byOrderGoods) {
                str += b.getGoodsName();
                /*增加销量*/
                if ("1".equals(b.getProductType())) {
                    ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(b.getGoodsId());
                    byGoodsInfo.setActualSalesNum(b.getGoodsNum() + byGoodsInfo.getActualSalesNum());

                    this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
                }
                if ("2".equals(b.getProductType())) {
                    BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    bySubCardGoods.setActualSalesNum(b.getGoodsNum() + bySubCardGoods.getActualSalesNum());
                    this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
                }
                if ("3".equals(b.getProductType())) {
                    ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    byTicketGoods.setActualSalesNum(b.getGoodsNum() + byTicketGoods.getActualSalesNum());
                    this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
                }
                if ("5".equals(b.getProductType())) {
                    ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(b.getGoodsId());
                    combinationGoods.setActualSalesNum(b.getGoodsNum() + (combinationGoods.getActualSalesNum() == null ? 0 : combinationGoods.getActualSalesNum()));
                    this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
                }
            }
            //购买成功推送消息
            Map<String, String> map = new HashMap<>();
            ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(byOrders.get(0).getUserId());
            map.put("keyword1", DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
            map.put("keyword2", str);
            map.put("keyword3", out_trade_no.toString());
            map.put("keyword4", byOrders.get(0).getOrderAmount().toString());
            messageUtil.sendWxTemplateMsg(map, byCustUser.getWxOpenId(), "Lk6ArTvbCfOAFg7CklhtmY-Gj4RuDAHb123eDRqYmKQ", byOrders.get(0).getFromId(), "pages/main/Order/orderDetails/main?id=" + byOrders1.getId());
        }
    }

    @Autowired
    private ByTeamOrderMapper byTeamOrderMapper;
    @Autowired
    private ByTeamMapper byTeamMapper;
    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTicketOrder(Object out_trade_no, Object transaction_id) {
        Example example = new Example(ByTeamOrder.class);
        example.createCriteria().andEqualTo("orderNo", out_trade_no);
        List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
        if (byTeamOrders.size() != 0) {
            ByTeamOrder byTeamOrder = byTeamOrders.get(0);
            if (byTeamOrder.getOrderStatus() == 2) return;
            byTeamOrder.setOrderStatus(2);
            byTeamOrder.setPayTime(new Date());
            if (StringUtils.isEmpty(transaction_id)) {
                transaction_id = "";
            }
            byTeamOrder.setPayFlowNo((String) transaction_id);
            this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
            ByTeam byTeam = this.byTeamMapper.selectByPrimaryKey(byTeamOrder.getTeamId());
            if (byTeam.getOrderStatus() == 1) {
                byTeamOrder.setOrderStatus(-1);
                this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
                refundPay(byTeamOrder.getOrderNo(), String.valueOf(System.currentTimeMillis()), byTeamOrder.getPayAmount(), byTeamOrder.getPayAmount());
                return;
            }
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeam.getTeamGoodsId());
            byTeamGoods.setStockNum((byTeamGoods.getStockNum() - 1));
            this.byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
            if (byTeamOrder.getCouponId() != null) {
                ByCouponUser byCouponUser = new ByCouponUser();
                byCouponUser.setId(byTeamOrder.getCouponId());
                byCouponUser.setIsUse(0);
                byCouponUserMapper.updateByPrimaryKeySelective(byCouponUser);
            }
            if (byTeamOrder.getCouponId() != null && byTeamOrder.getCouponId() != 0) {
                ByCouponUser byCouponUser = this.byCouponUserMapper.selectByPrimaryKey(byTeamOrder.getCouponId());
                byCouponUser.setIsUse(1);
                byCouponUser.setUseDate(new Date());
                this.byCouponUserMapper.updateByPrimaryKeySelective(byCouponUser);
            }
            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
            /*拼团季结束*/
            if (byTeam.getTeamNum() == 1) {
                byTeam.setOrderStatus(1);
                byTeamGoods.setTeamNum(byTeamGoods.getTeamNum() + 1);
                this.byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
                this.byTeamMapper.updateByPrimaryKeySelective(byTeam);
                byTeamOrder.setOrderStatus(2);
                this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
                Example example2 = new Example(ByTeamOrder.class);
                example2.createCriteria()
                        .andEqualTo("teamId", byTeam.getId())
                        .andEqualTo("orderStatus", 2);
                List<ByTeamOrder> byTeamOrders1 = this.byTeamOrderMapper.selectByExample(example2);
                if (byTeamOrders1.size() != 0) {
                    byTeamOrders1.forEach(e -> {
                        Example example1 = new Example(WriteOffCode.class);
                        example1.createCriteria().andEqualTo("detailId", e.getId()).andEqualTo("orderType", 1);
                        List<WriteOffCode> writeOffCodes = writeOffCodeMapper.selectByExample(example1);
                        if (writeOffCodes.size() != 0) {
                            for (WriteOffCode writeOffCode : writeOffCodes) {
                                writeOffCode.setOrderState(0);
                                this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
                            }
                        }

                        //拼团成功 返回用户积分
                        if (byPlatformSet.getIntegralReturn() != null && e.getPayAmount() != null) {
                            Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(e.getPayAmount()).intValue();
                            ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getUserId());
                            ByIntegralLog byIntegralLog = new ByIntegralLog();
                            byIntegralLog.setUserId(e.getUserId());
                            byIntegralLog.setChangeType(1);
                            byIntegralLog.setChangeNum(Integer.parseInt(coun.toString()));
                            byIntegralLog.setChangeReason("拼团成功");
                            byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                            byIntegralLog.setGmtCreate(new Date());
                            byIntegralLog.setIntegralType(1);
                            if (byIntegralLog.getChangeNum() > 0) {
                                byIntegralLogMapper.insertSelective(byIntegralLog);
                                this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(), Integer.parseInt(coun.toString()));
                            }
                        }
                        Map<String, String> data = new HashMap<>();
                        data.put("keyword1", byTeam.getTeamName());
                        data.put("keyword2", byTeam.getGoodsName());
                        data.put("keyword3", e.getPayAmount().toString());
                        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getUserId());
                        messageUtil.sendWxTemplateMsg(data, byCustUser.getWxOpenId(), "grj4toazHVskQ_tTubQLEJYUc221Q1lk0eeEO2cMBxw", e.getFromId(), "pages/main/Order/orderDetails/main?id=" + e.getId());
                    });
                }
            } else {
                Example example2 = new Example(ByTeamOrder.class);
                example2.createCriteria()
                        .andEqualTo("teamId", byTeam.getId())
                        .andEqualTo("orderStatus", 2);
                List<ByTeamOrder> byTeamOrders1 = this.byTeamOrderMapper.selectByExample(example2);
                if ((byTeamOrders1.size() == byTeam.getTeamNum())) {
                    byTeam.setOrderStatus(1);
                    byTeamGoods.setTeamNum(byTeamGoods.getTeamNum() + 1);
                    this.byTeamMapper.updateByPrimaryKeySelective(byTeam);
                    byTeamOrder.setOrderStatus(2);
                    this.byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
                    if (byTeamOrders1.size() != 0) {
                        byTeamOrders1.forEach(e -> {
                            Example example1 = new Example(WriteOffCode.class);
                            example1.createCriteria().andEqualTo("detailId", e.getId()).andEqualTo("orderType", 1);
                            List<WriteOffCode> writeOffCodes = writeOffCodeMapper.selectByExample(example1);
                            if (writeOffCodes.size() != 0) {
                                for (WriteOffCode writeOffCode : writeOffCodes) {
                                    writeOffCode.setOrderState(0);
                                    this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
                                }
                            }

                            //拼团成功 返回用户积分
                            if (byPlatformSet.getIntegralReturn() != null && e.getPayAmount() != null) {
                                Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(e.getPayAmount()).intValue();
                                ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getUserId());
                                ByIntegralLog byIntegralLog = new ByIntegralLog();
                                byIntegralLog.setUserId(e.getUserId());
                                byIntegralLog.setChangeType(1);
                                byIntegralLog.setChangeNum(Integer.parseInt(coun.toString()));
                                byIntegralLog.setChangeReason("拼团成功");
                                byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                                byIntegralLog.setGmtCreate(new Date());
                                byIntegralLog.setIntegralType(1);
                                if (byIntegralLog.getChangeNum() > 0) {
                                    byIntegralLogMapper.insertSelective(byIntegralLog);
                                    this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(), byIntegralLog.getChangeNum());
                                }
                            }

                            Map<String, String> data = new HashMap<>();
                            data.put("keyword1", byTeam.getTeamName());
                            data.put("keyword2", byTeam.getGoodsName());
                            data.put("keyword3", e.getPayAmount().toString());
                            ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getUserId());
                            messageUtil.sendWxTemplateMsg(data, byCustUser.getWxOpenId(), "grj4toazHVskQ_tTubQLEJYUc221Q1lk0eeEO2cMBxw", e.getFromId(), "pages/main/Order/orderDetails/main?id=" + e.getId());
                        });
                    }
                } else {
                    byTeam.setOrderStatus(0);
                    this.byTeamMapper.updateByPrimaryKeySelective(byTeam);
                    byTeamOrder.setOrderStatus(2);
                    this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
                }
            }
        }
    }

    @Override
    public BigDecimal calculation(Goods goods) {
        return this.getBoolean(goods);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void gifts(GiftsQo gifts) {
        ByCustUser user = SecurityContext.getUser();
        if (user == null || user.getId() == null) {
            throw new CustomException("请先登录再领取");
        }
        ByOrders orders = byOrdersMapper.selectByPrimaryKey(gifts.getOrderId());
        if (StringUtils.isEmpty(gifts.getRandom()) || !gifts.getRandom().equals(Md5Crypt.apr1Crypt(orders.getOrderNo(), orders.getOrderNo()))) {
            throw new CustomException("赠送人和领取人不匹配");
        }
        if (orders.getUserId().equals(gifts.getUserId())) {
            throw new CustomException("当前用户不可领取");
        }
        if (!StringUtils.isEmpty(orders.getCurrentUserId())) {
            throw new CustomException("订单已领取");
        }
        if (orders.getOrderStatus() != null && orders.getOrderStatus() == 2) {
            try {
                //修改订单被转增用户
                ByOrders byOrders = new ByOrders();
                byOrders.setId(gifts.getOrderId());
                byOrders.setCurrentUserId(gifts.getUserId());
                byOrdersMapper.updateByPrimaryKeySelective(byOrders);
                ByOrders order = byOrdersMapper.selectByPrimaryKey(gifts.getOrderId());
                //修改核销码用户
                writeOffCodeMapper.updateUserByOrdersId(gifts, order.getOrderNo());


            } catch (Exception e) {
                throw new CustomException("赠送失败");
            }
        } else {
            throw new CustomException("订单已完成或已退款");
        }
    }

    /**
     * 查询准备支付的订单详情
     *
     * @param payAllOrder
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public OrderDetailVo payAllDetail(PayAllOrder payAllOrder) {

        //普通门票
        Goods goods = payAllOrder.getGoods();


        if ( goods!=null && CollectionUtil.isNotEmpty(payAllOrder.getTcGoods()) ){
            //购物车结算时 如果有门票也有淘潮玩
            //淘潮玩的详情
            OrderDetailVo tcDetail = this.payTcDetail(payAllOrder);
            //门票商品的详情
            OrderDetailVo ticketDetail = detailMapToDetailVo(computeDetailMap(goods));

            //返回给前端的详情
            OrderDetailVo detailAll = new OrderDetailVo();

            //是否可用优惠券
            if ( tcDetail.isCouponType() || ticketDetail.isCouponType() ){
                detailAll.setCouponType( true );
                // 可以使用的优惠券
                List<ByCouponUser> concatCoupon = ListUtil.concat(tcDetail.getCoupon(), ticketDetail.getCoupon());
                detailAll.setCoupon( concatCoupon );
            }

            //商品金额
            BigDecimal goodsAmount = tcDetail.getGoodsAmount().add(ticketDetail.getGoodsAmount());
            detailAll.setGoodsAmount( goodsAmount );
            
            //是否可用积分
            if ( tcDetail.isType() || ticketDetail.isType() ){
                detailAll.setType( true );
                int fraction = ticketDetail.getFraction() + tcDetail.getFraction();
                //可用积分不能超过用户积分
                fraction = fraction > tcDetail.getUserPoint() ? tcDetail.getUserPoint() : fraction ;
                detailAll.setFraction( fraction );
                detailAll.setCount( fraction );
            }

            //商品数量
            detailAll.setGoodsCount( tcDetail.getGoodsCount()+ticketDetail.getGoodsCount() );

            //门票商品
            detailAll.setGoods( ticketDetail.getGoods() );

            //淘潮商品
            detailAll.setTcGoods( tcDetail.getTcGoods() );

            return detailAll;
        }else if ( CollectionUtil.isNotEmpty(payAllOrder.getTcGoods()) ){
            //只有淘潮玩，直接走淘潮玩的逻辑
            return this.payTcDetail(payAllOrder);
        }else if ( goods!=null ){
            Map<String, Object> detailMap = computeDetailMap(goods);
            //这段代码直接copy的原先代码
            //只有门票，直接走门票的逻辑
            return detailMapToDetailVo(detailMap);
        }else{
            //啥都没有，有问题
            throw new CustomException("您没有选中任何商品");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String,Object> computeDetailMap(Goods goods){
        //这段代码直接copy的原先代码
        if (goods.getType() == 1) {
            if (goods.getGoodsType() == 6) goods.setGoodsType(1);
            if (goods.getGoodsType() == 8) goods.setGoodsType(2);
            if (goods.getGoodsType() == 7) goods.setGoodsType(3);

        }
        if (StringUtils.isEmpty(goods.getId())) {
            throw new CustomException("至少选择一件商品");
        }
        if (goods.getType() == 2) {
            List<ByGoodsShoping> goodsList = new ArrayList<>();
            String[] split = goods.getId().split(",");
            for (int i = 0; i < split.length; i++) {
                ByGoodsShoping byGoodsShoping = this.byGoodsShopingMapper.selectByPrimaryKey(split[i]);
                goodsList.add(byGoodsShoping);
            }
            goods.setGoods(goodsList);
        }
        Map<String, Object> result = this.pay(goods);
        if (null != result.get("coupon")) {
            List<ByCouponUser> listUser = (List<ByCouponUser>) result.get("coupon");
            dealByCouponUserList(listUser);
            result.put("coupon", listUser);
            log.info("coupon listUser:{}", listUser);
        }

        return result;
    }

    /**
     * 将payMap转成PayVo
     * 为了不动以前代码才加的方法！ε=(´ο｀*)))唉
     * @return
     */
    private PayVo payMapToPayVo(Map<String,Object> payMap){
        PayVo payVo = new PayVo();

        //商家订单号，支付以及退款都使用这个订单号
        Object orderNo = payMap.get("orderNo");
        if ( orderNo!=null ){
            payVo.setOrderNo( orderNo.toString() );
        }

        //订单id
        Object orderId = payMap.get("orderId");
        if ( orderId!=null ){
            payVo.setOrderId( orderId.toString() );
        }

        //type
        Object type = payMap.get("type");
        if ( type!=null ){
            payVo.setType((Integer) type);
        }

        //支付信息
        Object pay = payMap.get("pay");
        if ( pay!=null ){
            payVo.setPay((JsPayResponse) pay);
        }

        Object payAmount = payMap.get("payAmount");
        if ( payAmount!=null ){
            payVo.setPayAmount((BigDecimal) payAmount);
        }

        return payVo;
    }

    /**
     * 将detailMap转成OrderDetailVo
     *
     * 为了不动以前代码才加的方法！ε=(´ο｀*)))唉
     * @return
     */
    private OrderDetailVo detailMapToDetailVo(Map<String,Object> detailMap){
        OrderDetailVo detailVo = new OrderDetailVo();

        Object coupon = detailMap.get("coupon");
        if ( coupon!=null ){
            detailVo.setCoupon((List<ByCouponUser>) coupon);
        }

        Object couponType = detailMap.get("couponType");
        if ( couponType!=null ){
            detailVo.setCouponType((Boolean) couponType);
        }

        Object goodsAmount = detailMap.get("goodsAmount");
        if ( goodsAmount!=null ){
            detailVo.setGoodsAmount((BigDecimal) goodsAmount);
        }

        Object count = detailMap.get("count");
        if ( count!=null ){
            detailVo.setCount((Integer) count);
        }

        Object goods = detailMap.get("goods");
        if ( goods!=null ){
            detailVo.setGoods((List<ByGoodsInfo>) goods);
        }

        Object type = detailMap.get("type");
        if (type!=null){
            detailVo.setType((Boolean) type);
        }

        Object fraction = detailMap.get("fraction");
        if (type!=null){
            detailVo.setFraction((Integer) fraction);
        }

        Object goodsCount = detailMap.get("goodsCount");
        if (goodsCount!=null){
            detailVo.setGoodsCount((Integer) goodsCount);
        }


        return detailVo;
    }

    /**
     * 淘潮玩
     */
    @Resource
    private TcGoodsShopingService tcShopingService;

    @Resource
    private WechatCouponUserService wechatCouponUserService;

    /**
     * 查看准备支付的淘潮玩订单详情
     *
     * @param payAllOrder
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OrderDetailVo payTcDetail(PayAllOrder payAllOrder) {
        //当前登录的用户信息
        ByCustUser user = SecurityContext.getUser();
        ByCustUser dbUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
        Integer nowPoint = dbUser.getNowPoint();
        OrderDetailVo detailVo = new OrderDetailVo();
        //是否可用积分，默认不让用
        detailVo.setUserPoint( nowPoint );


        //提交的淘潮商品
        List<TcGoodsShoping> tcGoods = payAllOrder.getTcGoods();
        if ( CollectionUtil.isEmpty(tcGoods) ){
            throw new CustomException("请选择商品");
        }

        //商品最多可以使用多少积分
        Integer fraction = 0;
        //商品数量
        Integer goodsCount = 0;
        //查询淘潮玩购物车对应的淘潮玩商品
        List<TcGoodsShoping> dbTcGoods = tcShopingService.findGoodsByCart(dbUser.getId(), tcGoods, payAllOrder.isJustAloneTc());

        if ( dbTcGoods.size()!=tcGoods.size() ){
            //有购物车不是当前用户的，有问题
            throw new CustomException("请重新选择商品");
        }

        detailVo.setTcGoods( dbTcGoods );

        //订单总金额
        BigDecimal goodsAmount = new BigDecimal(0);
        for (TcGoodsShoping goodsShoping : dbTcGoods) {
            //计算总金额
            TcGoods goods = goodsShoping.getTcGoods();
            //当前一件的商品 单价*数量
            BigDecimal multiply = goods.getSalePrice().multiply(BigDecimal.valueOf(goodsShoping.getGoodsCount()));
            goodsAmount = goodsAmount.add( multiply );
            goodsCount += goodsShoping.getGoodsCount();
            //库存是否足够
            if ( goodsShoping.getGoodsCount()>goods.getStock() ){
                throw new CustomException(goods.getGoodsName()+"库存不足");
            }
            //可以使用积分
            if ( goods.getIsIntegral() ){
                fraction += multiply.intValue();
            }
        }

        detailVo.setGoodsCount( goodsCount );
        detailVo.setGoodsAmount( goodsAmount );

        //可用使用的优惠券
        //查询一批淘潮玩商品可用的优惠券
        List<ByCouponUser> coupons = wechatCouponUserService.findUserTcCoupon(dbUser.getId(), dbTcGoods, goodsAmount);
        if (CollectionUtil.isNotEmpty(coupons)){
            //优惠券可用，返回的优惠券显示
            detailVo.setCoupon(coupons);
            detailVo.setCouponType(true);
        }

        //可使用{{fraction}}积分抵扣 ¥{{count}}
        //如果用户积分比 商品可抵扣积分多。以商品可抵扣积分为准
        fraction = nowPoint > fraction ? fraction : nowPoint;
        if ( fraction!=0 ){
            detailVo.setFraction(fraction);
            detailVo.setCount(fraction);
            detailVo.setType(true);
        }

        //淘潮玩的商品
//        List<TcGoods> goods = dbTcGoods.stream().map(tcCart -> tcCart.getTcGoods()).collect(Collectors.toList());

        detailVo.setTcGoods(dbTcGoods);


        return detailVo;
    }

    @Resource
    private TcOrderParentMapper orderParentMapper;


    /**
     * 同时支付淘潮玩和门票，单独支付一项请勿调用这个方法
     * @param payAllOrder
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayVo payAll(PayAllOrder payAllOrder) {

        ByCustUser user = SecurityContext.getUser();
        user = byCustUserMapper.selectByPrimaryKey(user.getId());
        Integer nowPoint = user.getNowPoint();

        //订单和优惠券不可同时使用
        Integer couponId = payAllOrder.getCouponId();
        if ( couponId!=null && DecimalUtil.notZeroNull(payAllOrder.getScoreAmount()) ){
            throw new CustomException("优惠券和积分不可同时使用");
        }

        //查看优惠券是否合法
        if ( couponId!=null ){
            ByCouponUser coupon = byCouponUserMapper.findUserCoupon(couponId, user.getId());
            Assert.isNull(coupon,"这张优惠券不能使用，请重新选择");
        }

        BigDecimal scoreAmount = payAllOrder.getScoreAmount();
        if ( scoreAmount!=null ){
            //查看积分是否足够
            Assert.lessThen( user.getNowPoint(), scoreAmount.intValue(),"您的积分不足" );
        }

        //淘潮玩的 支付详情
        OrderDetailVo tcDetail = payTcDetail(payAllOrder);
        //门票的 支付详情
        Map<String, Object> detailMap = computeDetailMap( ObjectUtil.clone(payAllOrder.getGoods()) );
        OrderDetailVo ticketDetail = detailMapToDetailVo(detailMap);

        //聚合订单
        TcOrderParent orderParent = new TcOrderParent();

        //请求支付的 原价格 和 两个详情里的原价是否相等
        BigDecimal checkAmount = tcDetail.getGoodsAmount().add(ticketDetail.getGoodsAmount());
        Assert.notEq(payAllOrder.getOrderAmount(), checkAmount, "价格不正确");

        orderParent.setOrderAmount( checkAmount );


        //用来调用门票支付逻辑的类
        PayAllOrder ticketPay = extractTicketPay(payAllOrder);
        //用来调用淘潮支付逻辑的类
        PayAllOrder tcPay = extractTcPay(payAllOrder);



        if ( DecimalUtil.notZeroNull(scoreAmount) ){
            //判断积分属于淘潮玩还是门票
            if ( tcDetail.isType() && ticketDetail.isType() ){
                int fraction = ticketDetail.getFraction() + tcDetail.getFraction();
                //淘潮和门票都可以使用积分，效验积分总数是否正确
                if ( fraction>nowPoint && nowPoint.equals(scoreAmount.intValue()) ){
                    //将积分平摊给淘潮 和 门票
                    //商品总金额
                    BigDecimal sum = ticketDetail.getGoodsAmount().add(tcDetail.getGoodsAmount());
                    //淘潮商品占总金额的比例
                    BigDecimal proportion = tcDetail.getGoodsAmount().divide(sum, 2, RoundingMode.FLOOR);

                    //淘潮积分
                    BigDecimal tcPoint = scoreAmount.multiply(proportion).setScale(0, RoundingMode.FLOOR);
                    //门票积分
                    BigDecimal ticketPoint = scoreAmount.subtract(tcPoint);

                    tcDetail.setFraction( tcPoint.intValue() );
                    ticketDetail.setFraction( ticketPoint.intValue() );

                    fraction = ticketDetail.getFraction() + tcDetail.getFraction();
                }
                Assert.notEq( fraction, payAllOrder.getScoreAmount().intValue(), "积分数量不正确" );
                //积分数量没问题
                orderParent.setIntegralAmount( BigDecimal.valueOf(fraction) );
                //积分原封不对摊给两个订单
                tcPay.setScoreAmount( BigDecimal.valueOf( tcDetail.getFraction() ) );
                ticketPay.setScoreAmount( BigDecimal.valueOf(ticketDetail.getFraction()) );
            }else if ( tcDetail.isType() ){
                //只有淘潮玩可用积分
                tcPay.setScoreAmount( payAllOrder.getScoreAmount() );
                orderParent.setIntegralAmount( payAllOrder.getScoreAmount() );
            }else if ( ticketDetail.isType() ){
                //只有淘潮玩可用积分
                ticketPay.setScoreAmount( payAllOrder.getScoreAmount() );
                orderParent.setIntegralAmount( payAllOrder.getScoreAmount() );
            }
        }

        //判断优惠券是门票的还是tc的
        if ( couponId!=null ){
            wechatCouponUserService.consumerCoupon(
                    couponId, user.getId(),
                    coupon -> {
                        ticketPay.setCouponId(couponId);
                        orderParent.setIntegralAmount(coupon.getDiscount());
                    },
                    coupon -> {
                        tcPay.setCouponId(couponId);
                        orderParent.setIntegralAmount(coupon.getDiscount());
                    });
        }



        //门票需要支付的金额
        ticketPay.setOrderAmount( ticketDetail.getGoodsAmount()  );
        ticketPay.setPayAmount( DecimalUtil.sub(ticketDetail.getGoodsAmount(), ticketPay.getScoreAmount())  );


        //实际支付金额

        BigDecimal payAmount = DecimalUtil.sub( orderParent.getOrderAmount(), orderParent.getIntegralAmount());
        orderParent.setPayAmount( payAmount );
        orderParent.setOrderParentNo( UUIDOrder.getUUID() );



        //淘潮玩 创建订单
        PayVo tcPayVo = this.payTc(tcPay);

        //门票 创建订单
        PayVo ticketPayVo = this.payOrderNew(ticketPay);

        //实际支付金额是否正确
        Assert.notEq( payAmount, tcPayVo.getPayAmount().add( ticketPayVo.getPayAmount() ),"金额不正确");

        orderParentMapper.insertSelective(orderParent);

        //改变两个订单的 商品订单号
        byOrdersMapper.updateByPrimaryKeySelective(
                ByOrders.builder()
                        .id( Integer.parseInt(ticketPayVo.getOrderId()) )
                        .payOrderNo( orderParent.getOrderParentNo() )
                        .build()
        );

        tcOrderMapper.updateByPrimaryKeySelective(
                TcOrder.builder()
                        .id( Integer.parseInt(tcPayVo.getOrderId()) )
                        .payOrderNo( orderParent.getOrderParentNo() )
                        .build()
        );

        if ( !DecimalUtil.notZeroNull( orderParent.getPayAmount() ) ){
            throw new CustomException("价格不能为0");
        }
        //准备微信支付的信息
        Wechat wechat = wechatService.getApiComponent();

        WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
        wechatJsPayDTO.setOrderNo( orderParent.getOrderParentNo() );
        wechatJsPayDTO.setTotalFee( orderParent.getPayAmount() );
        wechatJsPayDTO.setNotifyUrl( wechatPayProperties.getPayNotifyUrl() );

        wechatJsPayDTO.setOpenid( user.getWxOpenId() );
        wechatJsPayDTO.setAppid( wechat.getAppId() );
        wechatJsPayDTO.setIsTest( false );
        //商品名 需要更改
        wechatJsPayDTO.setBody("豹豹乐园淘潮玩+门票购买");
        JsPayResponse jsPayResponse = wepayService.prePay(wechatJsPayDTO);

        //响应给前端唤醒支付的类
        return PayVo.builder()
                .orderId( ticketPayVo.getOrderId() )
                .orderNo( orderParent.getOrderParentNo() )
                .payAmount( orderParent.getPayAmount() )
                .pay( jsPayResponse )
                .build();
    }

    /**
     * 现在是一起支付，从支付信息里提取门票需要的支付信息
     * @param payAll
     * @return
     */
    private PayAllOrder extractTicketPay(PayAllOrder payAll){


        return PayAllOrder.builder()
                .goods( payAll.getGoods() )
                .isPayAll( payAll.isPayAll() )
                .fromId( payAll.getFromId() )
                .type( payAll.getType() )
                .build();
    }

    /**
     * 现在是一起支付，从支付信息里提取淘潮玩需要的支付信息
     * @param payAll
     * @return
     */
    private PayAllOrder extractTcPay(PayAllOrder payAll){
        return PayAllOrder.builder()
                .deliveryMode( payAll.getDeliveryMode() )
                .isPayAll( payAll.isPayAll() )
                .address( payAll.getAddress() )
                .consignee( payAll.getConsignee() )
                .mobilePhone( payAll.getMobilePhone() )
                .tcGoods( payAll.getTcGoods() )
                .remark( payAll.getRemark() )
                .justAloneTc( payAll.isJustAloneTc() )
                .fromId( payAll.getFromId() )
                .build();
    }

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderGoodsMapper orderGoodsMapper;

    @Resource
    private ByOrderLogService orderLogService;

    /**
     * 支付门票订单，参数和返回值进行了优化
     * @param allOrder
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayVo payOrderNew(PayAllOrder allOrder){

        //门票商品
        Goods goods = allOrder.getGoods();
        goods.setPayAll( allOrder.isPayAll() );
        //如果goods 没有商品金额的话，就以PayAllOrder 的金额为准
        if ( goods.getPayAmount()==null && allOrder.getPayAmount()!=null ){
            goods.setPayAmount( allOrder.getPayAmount() );
        }
        if ( goods.getOrderAmount()==null && allOrder.getOrderAmount()!=null ){
            goods.setOrderAmount( allOrder.getOrderAmount() );
        }
        if ( goods.getScoreAmount()==null && allOrder.getScoreAmount()!=null ){
            goods.setScoreAmount( allOrder.getScoreAmount() );
        }
        if ( goods.getCouponAmount()==null && allOrder.getCouponAmount()!=null ){
            goods.setCouponAmount( allOrder.getCouponAmount() );
        }
        if ( goods.getCouponId()==null && allOrder.getCouponId()!=null ){
            goods.setCouponAmount( allOrder.getCouponAmount() );
        }
        //优惠券和积分的抵扣金额
        if ( goods.getCouponAmount()==null ){
            goods.setCouponAmount( BigDecimal.ZERO );
        }
        if ( goods.getScoreAmount()==null ){
            goods.setScoreAmount( BigDecimal.ZERO );
        }


        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        ByCustUser user = (ByCustUser) principal;
        log.info("支付pay() goods: {} 当前登录人userId：{}", goods, user.getId());
        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(user.getId());
        if (byCustUser.getIsDisable()) {
            throw new CustomException("你已被禁用，请联系管理员");
        }
        /*if (goods.getPayAmount() != null && goods.getPayAmount().compareTo(new BigDecimal(0)) != 1) {
            throw new CustomException("价格不能为0");
        }*/
        if (goods.getType() == 1) {
            if (goods.getGoodsType() == 7) {
                goods.setGoodsType(2);
            }
            if (goods.getGoodsType() == 8) {
                goods.setGoodsType(3);
            }
            if (goods.getGoodsType() == 6) {
                goods.setGoodsType(1);
            }
            if (goods.getGoodsType() == 10) {
                goods.setGoodsType(10);
            }
        }

        Map<String, Object> payMap = this.payOrder(goods);
        //记录创建订单的日志
        String orderId = payMap.get("orderId").toString();
        String orderNo = payMap.get("orderNo").toString();
        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .id(Integer.parseInt(orderId))
                        .orderNo(orderNo)
                        .operatorType(1)
                        .logType(OrderLogType.CREATE)
                        .build()
        );


        return payMapToPayVo(payMap);
    }

    @Resource
    private TcOrderService tcOrderService;

    /**
     * 根据父订单号 更新订单的状态
     * @param parentNo 支付时的订单号，参考订单表中的 pay_order_no
     *                 tc_order_parent中的order_parent_no
     * @param transactionId
     * @return
     */
    @Override
    public boolean updateByParentNo(String parentNo, String transactionId) {
        //具体的 门票订单和淘潮订单
        ByOrders byOrders = byOrdersMapper.findByPayOrderNo(parentNo);
        TcOrder tcOrder = tcOrderMapper.findByPayOrderNo(parentNo);
        YchOrder ychOrder = ychOrderMapper.findByPayOrderNo(parentNo);

        //如果一个订单都没有，有问题！
        Assert.allNull("没有查询到对应的订单", byOrders, tcOrder, ychOrder);
        // 先处理内部订单
        if (byOrders != null) {
            this.update(byOrders.getOrderNo(), transactionId);
        } else if (ychOrder != null) {
            //在处理油菜花订单
            ychApiService.updatePaid(ychOrder.getTPOrderNo(), transactionId);
        } else if (tcOrder != null) {
            tcOrderService.updatePaid(tcOrder.getOrderNo(), transactionId);
        }

        return true;
    }

    /**
     * 未支付的淘潮订单进行付款
     *
     * @param orderId
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayVo payUnpaidTc(Integer orderId) {
        ByCustUser user = SecurityContext.getUser();
        user = byCustUserMapper.selectByPrimaryKey(user.getId());
        TcOrder dbOrder = tcOrderMapper.selectByPrimaryKey( orderId );
        log.info("再次支付的订单>>>>>>"+dbOrder.toString());

        Assert.notEq(dbOrder.getStatus(),1,"该订单不是未付款状态");
        Assert.notEq(user.getId(), dbOrder.getUserId(), "这不是你的订单");

        if ( !DecimalUtil.notZeroNull( dbOrder.getActualAmount() ) ){
            throw new CustomException("价格不能为0");
        }

        WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
        wechatJsPayDTO.setOrderNo( dbOrder.getOrderNo() );
        wechatJsPayDTO.setTotalFee( dbOrder.getActualAmount() );
        wechatJsPayDTO.setNotifyUrl(wechatPayProperties.getPayNotifyUrl());
        Wechat wechat = wechatService.getApiComponent();
        wechatJsPayDTO.setOpenid(user.getWxOpenId());
        wechatJsPayDTO.setAppid(wechat.getAppId());
        wechatJsPayDTO.setIsTest( false );
        wechatJsPayDTO.setBody( "豹豹乐园淘潮玩商品" );
        JsPayResponse jsPayResponse = wepayService.prePay(wechatJsPayDTO);

        //更新支付订单号
        tcOrderMapper.updateByPrimaryKeySelective(
                TcOrder.builder()
                        .id( dbOrder.getId() )
                        .payOrderNo( dbOrder.getOrderNo() )
                        .build()
        );

        return PayVo.builder()
                .pay( jsPayResponse )
                .orderId( dbOrder.getId().toString() )
                .orderNo( dbOrder.getOrderNo() )
                .type( 1 )
                .payAmount( dbOrder.getActualAmount() )
                .build();
    }


    @Resource
    private TcWriteOffCodeMapper tcWriteOffCodeMapper;

    @Resource
    private TcWriteOffCodeService tcWriteOffCodeService;

    @Resource(name = "WxTcGoodsServiceImpl")
    private TcGoodsService tcGoodsService;

    /**
     * 淘潮玩付款
     * @param payAllOrder
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayVo payTc(PayAllOrder payAllOrder) {
        ByCustUser user = SecurityContext.getUser();
        user = byCustUserMapper.selectByPrimaryKey(user.getId());
//        ByCustUser user = ByCustUser.builder().id(100754).nowPoint(50).build();

        Assert.isNull(payAllOrder.getDeliveryMode(),"请选择配送方式");

        //订单和优惠券不可同时使用
        if ( payAllOrder.getCouponId()!=null && DecimalUtil.notZeroNull(payAllOrder.getScoreAmount()) ){
            throw new CustomException("优惠券和积分不可同时使用");
        }

        //计算当前 淘潮玩应付的金额
        OrderDetailVo orderDetail = this.payTcDetail(payAllOrder);

        BigDecimal scoreAmount = payAllOrder.getScoreAmount();
        if ( scoreAmount!=null && scoreAmount.intValue() > orderDetail.getFraction() ){

            throw new CustomException(String.format("最多可以使用%d积分,你想要使用%d积分",orderDetail.getFraction(), scoreAmount.intValue()));
        }

        //效验收货地址
        if
        ( Objects.equals(payAllOrder.getDeliveryMode(),2)
                &&
        ( StringUtils.isEmpty(payAllOrder.getAddress())
                ||
          StringUtils.isEmpty(payAllOrder.getConsignee())
                ||
          StringUtils.isEmpty(payAllOrder.getMobilePhone()) )
        ){
            throw new CustomException("请检查收货地址是否完善");
        }



        //查看该优惠券是否可用
        ByCouponUser couponUser = null;
        if ( payAllOrder.getCouponId()!=null ){
            couponUser = orderDetail.getCoupon().stream()
                    .filter(coupon -> payAllOrder.getCouponId().equals(coupon.getId()))
                    .findFirst()
                    .orElse(null);
            //该优惠券不可用
            if (couponUser==null){
                throw new CustomException("这张优惠券不适用当前订单，请刷新");
            }
        }

        //判断积分是否足够
        if ( DecimalUtil.notZeroNull(scoreAmount) && user.getNowPoint()<scoreAmount.intValue() ){
            throw new CustomException("您的积分不足");
        }


        //如果只有淘潮玩商品的话，
        int goodsCount = 0;
        //商家订单号，退款时需要使用
        String orderNo = UUIDOrder.getUUID();
        TcOrder tcOrder = TcOrder.builder()
                .orderNo( orderNo )
                .payOrderNo( orderNo )
                .gtmCreate( new Date() )
                .userId( user.getId() )
                .estimatedTime( payAllOrder.getEstimatedTime() )
                .remark( payAllOrder.getRemark() )
                .deliveryMode( payAllOrder.getDeliveryMode() )
                .status( 1 )
                .goodsCount( orderDetail.getGoodsCount() )
                .originAmount( orderDetail.getGoodsAmount() )
                .integralAmount( scoreAmount )
                .build();

        if ( Objects.equals( payAllOrder.getDeliveryMode(),2 ) ){
            tcOrder.setConsignee( payAllOrder.getConsignee() );
            tcOrder.setMobilePhone( payAllOrder.getMobilePhone() );
            tcOrder.setAddress( payAllOrder.getAddress() );
        }

        BigDecimal actualAmount = tcOrder.getOriginAmount();

        //抵扣积分金额
        if ( DecimalUtil.notZeroNull(scoreAmount) ){
            actualAmount = actualAmount.subtract( scoreAmount );
            //扣除用户对应积分
            ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
            if (byPlatformSet != null && byPlatformSet.getIntegralReturn() != null && byPlatformSet.getIntegralReturn().compareTo(new BigDecimal(0)) != 0 && payAllOrder.getScoreAmount().compareTo(new BigDecimal(0)) != 0) {
                ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(user.getId());
                ByIntegralLog byIntegralLog = new ByIntegralLog();
                byIntegralLog.setUserId(byCustUser.getId());
                byIntegralLog.setChangeType(1);
                Integer count = (byPlatformSet.getIntegralReturn().multiply(scoreAmount)).intValue();
                byIntegralLog.setChangeNum(count);
                byIntegralLog.setChangeReason("商品购买扣除");
                byIntegralLog.setIntegralType(2);
                byIntegralLog.setChangeType(2);
                byIntegralLog.setBeforeNum( byCustUser.getNowPoint() );
                byIntegralLog.setGmtCreate(new Date());
                log.info(byCustUser.getNowPoint().toString());
                if (count != 0) {
                    int i = this.byCustUserMapper.updateByNotAdd(byCustUser.getId(), count);
                    if (i < 1) {
                        throw new CustomException("积分不足");
                    }
                    this.byIntegralLogMapper.insertSelective(byIntegralLog);
                }
                log.info(byCustUser.getNowPoint() + "_______________________" + count);
            }
        }


        //抵扣优惠券金额
        if (couponUser!=null){
            tcOrder.setCouponAmount( couponUser.getDiscount() );
            actualAmount = actualAmount.subtract( couponUser.getDiscount() );
            //优惠券改为已使用
            ByCouponUser upCoupon = ByCouponUser
                    .builder()
                    .id(couponUser.getId())
                    .isUse(1)
                    .build();

            byCouponUserMapper.updateByPrimaryKeySelective(upCoupon);
        }

        tcOrder.setActualAmount( actualAmount );


        //删除购物车的淘潮玩商品
        if ( !payAllOrder.isJustAloneTc() ){
            List<Integer> collect = payAllOrder.getTcGoods().stream().map( TcGoodsShoping::getId ).collect(Collectors.toList());

            tcShopingService.deleteByIds(CartIds.builder().tcIds(collect).build());
        }

        //保存当前订单
        tcOrderMapper.insertSelective( tcOrder );

        //创建订单商品
        List<TcGoodsShoping> tcGoods = orderDetail.getTcGoods();

        //需要计算的订单商品
        ArrayList<TcOrderGoods> tcOrderGoods = new ArrayList<>();

        for (TcGoodsShoping cart : tcGoods) {
            TcGoods goods = cart.getTcGoods();

            goodsCount += cart.getGoodsCount();

            TcOrderGoods orderGoods = TcOrderGoods.builder()
                    .tcOrderId( tcOrder.getId() )
                    .tcGoodsId( cart.getTcGoods().getId() )
                    .tcGoodsName(goods.getGoodsName())
                    .tcGoodsImg(goods.getCoverImg())
                    .goodsPrice(goods.getSalePrice())
                    .goodsCount(cart.getGoodsCount())
                    .tcOrderNo(orderNo)
                    .status(1)
                    .build();

            tcOrderGoods.add(orderGoods);
        }


        //计算单价，保存订单商品
        discountTcOrderGoods(tcOrderGoods, DecimalUtil.doubleValue(tcOrder.getOriginAmount()), DecimalUtil.doubleValue(tcOrder.getIntegralAmount())
                , DecimalUtil.doubleValue(tcOrder.getCouponAmount()), DecimalUtil.doubleValue(tcOrder.getActualAmount()) );


        //保存订单商品
        for (TcOrderGoods orderGood : tcOrderGoods) {
            orderGoodsMapper.insertSelective( orderGood );
            //扣减库存!!!!!!!!
            tcGoodsService.reduceStock(orderGood.getTcGoodsId(), -orderGood.getGoodsCount());
            //到店核销的生成核销码
            if (tcOrder.getDeliveryMode().equals(1)){
                tcWriteOffCodeService.saveOffCode(orderGood, user.getId());
            }
        }




        if ( !DecimalUtil.notZeroNull(tcOrder.getActualAmount()) ){
            throw new CustomException("价格不能为0");
        }

        PayVo payVo = PayVo.builder()
                .payAmount(tcOrder.getActualAmount())
                .orderId(tcOrder.getId().toString())
                .orderNo(tcOrder.getOrderNo())
                .type(1)
                .build();

        if ( !payAllOrder.isPayAll() ){
            //单独支付的话需要拉取微信支付信息，混合支付由payAll进行拉取
            //准备微信支付的信息
            Wechat wechat = wechatService.getApiComponent();

            WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
            wechatJsPayDTO.setOrderNo(tcOrder.getPayOrderNo());
            wechatJsPayDTO.setTotalFee(tcOrder.getActualAmount());
            wechatJsPayDTO.setNotifyUrl(wechatPayProperties.getPayNotifyUrl());

            wechatJsPayDTO.setOpenid(user.getWxOpenId());
            wechatJsPayDTO.setAppid(wechat.getAppId());
            wechatJsPayDTO.setIsTest( false );
            //商品名
            wechatJsPayDTO.setBody("豹豹乐园淘潮玩商品");
            JsPayResponse jsPayResponse = wepayService.prePay(wechatJsPayDTO);

            payVo.setPay(jsPayResponse);
        }


        return payVo;
    }

    /**
     * 油菜花-充值余额
     * @param ychOrder
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PayVo payYch(YchOrder ychOrder) {
        ByCustUser user = SecurityContext.getUser();
        user = byCustUserMapper.selectByPrimaryKey(user.getId());
        PayVo payVo = PayVo.builder()
                .payAmount(BigDecimal.valueOf(ychOrder.getOrderMoney()).setScale(2, RoundingMode.HALF_UP))
                .orderId(ychOrder.getId().toString())
                .orderNo(ychOrder.getTPOrderNo())
                .type(1)
                .build();
        //准备微信支付的信息
        Wechat wechat = wechatService.getApiComponent();

        WechatJsPayDTO wechatJsPayDTO = new WechatJsPayDTO();
        wechatJsPayDTO.setOrderNo(ychOrder.getTPOrderNo());
        wechatJsPayDTO.setTotalFee(BigDecimal.valueOf(ychOrder.getOrderMoney()).setScale(2, RoundingMode.HALF_UP));
        wechatJsPayDTO.setNotifyUrl(wechatPayProperties.getPayNotifyUrl());

        wechatJsPayDTO.setOpenid(user.getWxOpenId());
        wechatJsPayDTO.setAppid(wechat.getAppId());
        wechatJsPayDTO.setIsTest( false );
        //商品名
        wechatJsPayDTO.setBody("储值卡充值");
        JsPayResponse jsPayResponse = wepayService.prePay(wechatJsPayDTO);

        payVo.setPay(jsPayResponse);
        return payVo;
    }

/**
     * 处理余额支付
     */
//    public Map<String, Object> balancePayment(PaymentRequest goods) {
//        if (goods.getPayType().equals(2)) { // 余额支付
//            ByCustUser user = SecurityContext.getUser();
//            ByOrders byOrders = this.byOrdersMapper.selectByPrimaryKey(goods.getId());
//
//            // 使用新的会员卡服务处理余额支付
//            String transId = "VAL" + UUIDOrder.getUUID();
//            try {
//                // 在本地扣减余额并创建异步同步任务
//                memberCardService.changeMemberBalance(
//                    user.getMobile(),
//                    byOrders.getPayAmount().negate(), // 负数表示扣减
//                    transId,
//                    "商品订单支付：" + byOrders.getOrderNo()
//                );
//
//                // 更新订单支付状态
//                update(goods.getOrderNo(), transId);
//                byOrders.setPayFlowNo(transId);
//
//                Map<String, Object> data = new HashMap<>();
//                data.put("orderId", goods.getId());
//                data.put("orderNo", goods.getOrderNo());
//                data.put("payAmount", goods.getPayAmount());
//
//                return data;
//            } catch (Exception e) {
//                log.error("余额支付失败", e);
//                throw new CustomException("余额支付失败：" + e.getMessage());
//            }
//        }
//
//        // 其他支付方式处理...
//    }


    private Integer getCount(Integer first, Integer second, Integer count) {
        if (first < 0) {
            first = 0;
        }
        int i = (first - first % second) / second;
        return i;
    }

    @Autowired
    private ByCouponTempMapper byCouponTempMapper;

    @Autowired
    private ByTicketGoodsMappingMapper byTicketGoodsMappingMapper;

    //计算优惠券
    public BigDecimal getBoolean(Goods goods) {
        if (goods.getCouponId() == null || goods.getCouponId() == 0) {
            return goods.getOrderAmount().subtract(goods.getScoreAmount());
        }
        //优惠券
        ByCouponUser byCouponUser = this.byCouponUserMapper.selectByPrimaryKey(goods.getCouponId());
        //优惠券详情
        ByCouponTemp byCouponTemp = this.byCouponTempMapper.selectByPrimaryKey(byCouponUser.getCouponId());
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        if (byCouponUser.getIsUse() != 0 && byCouponUser.getAuditStatus() != 1 && instance.getTime().getTime() > byCouponUser.getEndDate().getTime()) {
            throw new CustomException("优惠券已过期");
        }
        if (byCouponUser.getFull() != null || byCouponUser.getFull().compareTo(new BigDecimal(0)) != 0) {
            if (goods.getOrderAmount().compareTo(byCouponUser.getFull()) == -1) {
                throw new CustomException("此优惠卷不能使用，不满足优惠条件");
            }
        }
        if (goods.getType() == 1) {
            if (byCouponUser.getCouponType() == 1) {
                if (byCouponUser.getFull() == null || byCouponUser.getFull().compareTo(new BigDecimal(0)) == 0) {
                    return goods.getOrderAmount().subtract(byCouponUser.getDiscount()).subtract(goods.getScoreAmount());
                } else {
                    if (goods.getOrderAmount().compareTo(byCouponUser.getFull()) == -1) {
                        throw new CustomException("此优惠卷不能使用，不满足优惠条件");
                    }
                    return goods.getOrderAmount().subtract(byCouponUser.getDiscount()).subtract(goods.getScoreAmount());
                }
            }
            if (byCouponUser.getCouponType() == 2) {
                if (byCouponUser.getFull() == null || byCouponUser.getFull().compareTo(new BigDecimal(0)) == 0) {
                    return (goods.getOrderAmount().multiply(byCouponUser.getDiscount()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_EVEN)).subtract(goods.getScoreAmount());
                } else {
                    if (goods.getOrderAmount().compareTo(byCouponUser.getFull()) == -1) {
                        throw new CustomException("此优惠卷不能使用，不满足优惠条件");
                    }
                    return (goods.getOrderAmount().multiply(byCouponUser.getDiscount()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_EVEN)).subtract(goods.getScoreAmount());
                }
            }
        }

        List<Integer> integers = new ArrayList<>();
        BigDecimal dec = new BigDecimal(0);
        if (goods.getType() == 2) {
            /*可是使用优惠圈订单金额*/
            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
            if (byCouponUser.getType() == 0) {
                for (ByGoodsShoping e : goods.getGoods()) {
                    if (e.getGoodsType() == 1 && byPlatformSet.getGeneralCouponIsEnable() == 1) {
                        if (!integers.contains(e.getId())) {
                            integers.add(e.getId());
                            BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                            dec = dec.add(multiply);
                        }

                    }
                    if (e.getGoodsType() == 2) {
                        if (byPlatformSet.getSubCardCouponIsEnable() == 1) {
                            if (!integers.contains(e.getId())) {
                                integers.add(e.getId());
                                BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                dec = dec.add(multiply);
                            }

                        }

                    }
                    if (e.getGoodsType() == 3) {
                        if (byPlatformSet.getTicketCouponIsEnable() == 1) {
                            if (!integers.contains(e.getId())) {
                                integers.add(e.getId());
                                BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                dec = dec.add(multiply);
                            }

                        }
                    }
                    if (e.getGoodsType() == 5) {
                        if (byCombinationGoodsMapper.selectByPrimaryKey(e.getGoodsId()).getIsCoupon() == 1) {
                            if (!integers.contains(e.getId())) {
                                integers.add(e.getId());
                                BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                dec = dec.add(multiply);
                            }

                        }
                    }
                }
            }
            if (byCouponUser.getType() == 1) {
                for (ByGoodsShoping e : goods.getGoods()) {
                    //普通
                    if (e.getGoodsType() == 1 && byPlatformSet.getGeneralCouponIsEnable() == 1) {
                        Example example = new Example(ByGoodsClassify.class);
                        example.createCriteria().andEqualTo("goodsId", e.getGoodsId()).andEqualTo("classifyId", byCouponTemp.getTargetId());
                        List<ByGoodsClassify> byGoodsClassifies = byGoodsClassifyMapper.selectByExample(example);
                        if (byGoodsClassifies.size() != 0) {
                            if (!integers.contains(e.getId())) {
                                integers.add(e.getId());
                                BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                dec = dec.add(multiply);
                            }
                        }
                    }
                    //Modify by2019年11月7日19:57:34  注释 本次不考虑 联票 次卡使用优惠券情况
                    //次卡
                    if (e.getGoodsType() == 3) {
                        if (byPlatformSet.getSubCardCouponIsEnable() == 1) {
                            BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(e.getGoodsId());
                            Example example = new Example(BySubGoodsClassify.class);
                            example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId()).andEqualTo("classifyId", byCouponTemp.getTargetId());
                            List<BySubGoodsClassify> bySubGoodsClassifies = bySubGoodsClassifyMapper.selectByExample(example);
                            if (bySubGoodsClassifies.size() != 0) {
                                if (!integers.contains(e.getId())) {
                                    integers.add(e.getId());
                                    BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                    dec = dec.add(multiply);
                                }

                            }
                        }

                    }
                    //联票
                    if (e.getGoodsType() == 2) {
                        if (byPlatformSet.getTicketCouponIsEnable() == 1) {
                            Example example = new Example(ByTicketGoodsMapping.class);
                            example.createCriteria().andEqualTo("ticketGoodsId", e.getGoodsId());
                            List<ByTicketGoodsMapping> byTicketGoodsMappings = this.byTicketGoodsMappingMapper.selectByExample(example);
                            if (byTicketGoodsMappings != null) {
                                int i = 0;
                                for (ByTicketGoodsMapping byTicketGoodsMapping : byTicketGoodsMappings) {
                                    Example example1 = new Example(ByGoodsClassify.class);
                                    example1.createCriteria().andEqualTo("goodsId", byTicketGoodsMapping.getGoodsId()).andEqualTo("classifyId", byCouponTemp.getTargetId());
                                    List<ByGoodsClassify> byGoodsClassifies = byGoodsClassifyMapper.selectByExample(example1);
                                    if (byGoodsClassifies.size() != 0) {
                                        i = 1;
                                        continue;
                                    }
                                }
                                if (i == 1) {
                                    if (!integers.contains(e.getId())) {
                                        integers.add(e.getId());
                                        BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                        dec = dec.add(multiply);
                                    }
                                }
                            }
                        }
                    }
                    //规格
                    if (e.getGoodsType() == 5) {
                        ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(e.getGoodsId());
                        Example example = new Example(ByGoodsClassify.class);
                        example.createCriteria().andEqualTo("goodsId", combinationGoods.getGoodsId()).andEqualTo("classifyId", byCouponTemp.getTargetId());
                        List<ByGoodsClassify> byGoodsClassifies = byGoodsClassifyMapper.selectByExample(example);
                        if (byGoodsClassifies.size() != 0) {
                            if (!integers.contains(e.getId())) {
                                integers.add(e.getId());
                                BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                                dec = dec.add(multiply);
                            }
                        }
                    }
                }
            }
            if (byCouponUser.getType() == 2) {
                for (ByGoodsShoping e : goods.getGoods()) {
                    log.info("商品类型" + e.getGoodsType() + "商品id" + e.getGoodsId());
                    if (byCouponUser.getTargetId().equals(e.getGoodsId()) && byCouponUser.getSingleGoodsType() == 1) {
                        log.info("单个商品");
                        if (!integers.contains(e.getId())) {
                            integers.add(e.getId());
                            BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                            BigDecimal add = dec.add(multiply);
                            dec = add.add(dec);
                        }
                    }
                    if (byCouponUser.getTargetId().equals(e.getGoodsId()) && byCouponUser.getSingleGoodsType() == 2) {
                        log.info("单个商品");
                        if (!integers.contains(e.getId())) {
                            integers.add(e.getId());
                            BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                            BigDecimal add = dec.add(multiply);
                            dec = add.add(dec);
                        }
                    }
                    if (byCouponUser.getTargetId().equals(e.getGoodsId()) && byCouponUser.getSingleGoodsType() == 3) {
                        log.info("单个商品");
                        if (!integers.contains(e.getId())) {
                            integers.add(e.getId());
                            BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                            BigDecimal add = dec.add(multiply);
                            dec = add.add(dec);
                        }
                    }
                    if (byCouponUser.getTargetId().equals(e.getGoodsId()) && byCouponUser.getSingleGoodsType() == 5) {
                        log.info("单个商品");
                        if (!integers.contains(e.getId())) {
                            integers.add(e.getId());
                            BigDecimal multiply = new BigDecimal(e.getGoodsCount()).multiply(new BigDecimal(e.getSellPrice()));
                            BigDecimal add = dec.add(multiply);
                            dec = add.add(dec);
                        }
                    }
                }
            }
        }
        log.info("输出  可用优惠券商品数量  优惠抵用金额  可使用优惠金额" + integers.size() + "-----------" + goods.getCouponAmount() + "-------------" + dec);
        log.info("输出 getBoolean() byCouponUser:{}", byCouponUser);

        //总积分 / 可用优惠券的金额  = 占比
        BigDecimal divide1 = goods.getCouponAmount().divide(dec, 2, BigDecimal.ROUND_HALF_EVEN);

        if (byCouponUser.getCouponType() == 1 && byCouponUser.getDiscount() != null && byCouponUser.getDiscount().compareTo(new BigDecimal(0)) != 0) {
            if (byCouponUser.getFull() == null || byCouponUser.getFull().compareTo(new BigDecimal(0)) == 0) {
                // 将优惠卷的金额放入购物车
                for (Integer id : integers) {
                    for (ByGoodsShoping byGoodsShoping : goods.getGoods()) {
                        if (id.equals(byGoodsShoping.getId()) && goods.getOrderAmount() != null) {
                            BigDecimal multiply = new BigDecimal(byGoodsShoping.getGoodsCount()).multiply(new BigDecimal(byGoodsShoping.getSellPrice()));
                            BigDecimal multiply1 = multiply.multiply(divide1);
                            byGoodsShoping.setAmount(multiply1);
                            log.info("##############################处理优惠券平摊#############################shoping：" + multiply1);
                        }
                    }
                }
                //优惠券余数处理
                updateAmount(goods.getGoods(), goods.getCouponAmount());
                return byCouponUser.getDiscount();
            } else {
                BigDecimal subtract = dec.subtract(byCouponUser.getDiscount()).subtract(goods.getScoreAmount());
                if (integers.size() != 0) {
                    // 将优惠卷的金额放入购物车
                    for (Integer id : integers) {
                        for (ByGoodsShoping byGoodsShoping : goods.getGoods()) {
                            if (id.equals(byGoodsShoping.getId())) {
                                // 按比例分配 优惠卷金
                                BigDecimal multiply = new BigDecimal(byGoodsShoping.getGoodsCount()).multiply(new BigDecimal(byGoodsShoping.getSellPrice()));
                                BigDecimal multiply1 = multiply.multiply(divide1);
                                byGoodsShoping.setAmount(multiply1);
                                log.info("##############################处理优惠券平摊#############################shoping：" + multiply1);
                            }
                        }
                    }
                }
                updateAmount(goods.getGoods(), goods.getCouponAmount());
                return subtract;
            }
        }
        if (byCouponUser.getCouponType() == 2) {
            for (Integer id : integers) {
                for (ByGoodsShoping byGoodsShoping : goods.getGoods()) {
                    if (id.equals(byGoodsShoping.getId()) && goods.getOrderAmount() != null) {
                        BigDecimal multiply = new BigDecimal(byGoodsShoping.getGoodsCount()).multiply(new BigDecimal(byGoodsShoping.getSellPrice()));
                        BigDecimal multiply1 = multiply.multiply(divide1);
                        byGoodsShoping.setAmount(multiply1);
                        log.info("##############################处理优惠券平摊#############################shoping：" + multiply1);
                    }
                }

                for (ByGoodsShoping byGoodsShoping : goods.getGoods()) {
                    if (id.equals(byGoodsShoping.getId()) && goods.getOrderAmount() != null) {
                        BigDecimal multiply = new BigDecimal(byGoodsShoping.getGoodsCount()).multiply(new BigDecimal(byGoodsShoping.getSellPrice()));
                        BigDecimal multiply1 = multiply.multiply(divide1);
                        byGoodsShoping.setAmount(multiply1);
                        log.info("##############################处理优惠券平摊#############################shoping：" + multiply1);
                    }
                }
            }
            updateAmount(goods.getGoods(), goods.getCouponAmount());
            return dec.multiply(byCouponUser.getDiscount()).divide(new BigDecimal(100));
        }
        throw new CustomException("此优惠卷不能使用，不满足优惠条件");
    }

    @Resource
    private WechatProperties wechatProperties;


    ///*处理积分过多或者少*/
    private List<ByGoodsShoping> updateAmount(List<ByGoodsShoping> shoping, BigDecimal amount) {
        log.info("##############################处理优惠券余数情况#############################shoping：" + JSONObject.toJSONString(shoping));
        log.info("##############################处理优惠券余数情况##############################amount：" + amount);
        BigDecimal totalAmount = new BigDecimal(0);
//        取出多余的金额
        for (ByGoodsShoping goodsShoping : shoping) {
            totalAmount = totalAmount.add(goodsShoping.getAmount() == null ? new BigDecimal(0) : goodsShoping.getAmount());
        }

        if (totalAmount.compareTo(amount) == 0) {
            return shoping;
        }
        //订单总优惠金额-优惠券金额 = 平摊优惠金额总和
        BigDecimal subtract = amount.subtract(totalAmount);
        for (ByGoodsShoping goodsShoping : shoping) {
            if (goodsShoping.getAmount() == null) {
                continue;
            }
            //优惠券平摊金额
            BigDecimal amount1 = goodsShoping.getAmount();
            //单个商品的金额
            BigDecimal sellPrice = new BigDecimal(goodsShoping.getSellPrice());
            //商品的数量
            BigDecimal goodsCount = new BigDecimal(goodsShoping.getGoodsCount());
            //单笔订单的总金额
            BigDecimal sumGoods = sellPrice.multiply(goodsCount);
            //单项订单总金额>优惠券平摊金额  或   优惠券平摊金额+余数金额 <= 单项订单总金额
            if (sumGoods.compareTo(amount1) > 0 || (amount1.add(subtract).compareTo(amount1) <= 0)) {
                goodsShoping.setAmount(amount1.add(subtract));
                break;
            }
        }
        return shoping;
    }


    /**
     * 计算积分
     */

    @Transactional(rollbackFor = Exception.class)
    public List<ByGoodsShoping> check(List<ByGoodsShoping> shoping, BigDecimal bigDecimal) {
        log.info("##############################处理积分平摊处理入口#############################shoping：" + JSONObject.toJSONString(shoping));
        ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
        List<Integer> list = new ArrayList<>();
        //全部商品可用积分的总价格
        BigDecimal dec = new BigDecimal(0);
        for (ByGoodsShoping goodsShoping : shoping) {
            if (byPlatformSet.getGeneralIntegralIsEnable() == 1 && goodsShoping.getGoodsType() == 1) {
                list.add(goodsShoping.getId());
                BigDecimal multiply = new BigDecimal(goodsShoping.getSellPrice()).multiply(new BigDecimal(goodsShoping.getGoodsCount()));
                dec = dec.add(multiply);
            }
            //修改规格商品价格问题
            if (byPlatformSet.getGeneralIntegralIsEnable() == 1 && goodsShoping.getGoodsType() == 5) {
                list.add(goodsShoping.getId());
                BigDecimal multiply = new BigDecimal(goodsShoping.getSellPrice()).multiply(new BigDecimal(goodsShoping.getGoodsCount()));
                dec = dec.add(multiply);
            }
            if (byPlatformSet.getTicketIntegralIsEnable() == 1 && goodsShoping.getGoodsType() == 3) {
                list.add(goodsShoping.getId());
                BigDecimal multiply = new BigDecimal(goodsShoping.getSellPrice()).multiply(new BigDecimal(goodsShoping.getGoodsCount()));
                dec = dec.add(multiply);
            }
            if (byPlatformSet.getSubCardIntegralIsEnable() == 1 && goodsShoping.getGoodsType() == 2) {
                list.add(goodsShoping.getId());
                BigDecimal multiply = new BigDecimal(goodsShoping.getSellPrice()).multiply(new BigDecimal(goodsShoping.getGoodsCount()));
                dec = dec.add(multiply);
            }
        }
        if (list.size() > 0) {
            for (ByGoodsShoping goodsShoping : shoping) {
                //总积分 / 可用积分的金额  = 占比
                BigDecimal divide1 = bigDecimal.divide(dec, 2, BigDecimal.ROUND_HALF_EVEN);
                for (Integer id : list) {
                    if (id.equals(goodsShoping.getId())) {
                        BigDecimal multiply = new BigDecimal(goodsShoping.getSellPrice()).multiply(new BigDecimal(goodsShoping.getGoodsCount()));
                        //订单金额 * 占比
                        BigDecimal multiply1 = multiply.multiply(divide1);
                        goodsShoping.setInAmount(multiply1);
                        log.info("##############################处理积分平摊#############################shoping：" + multiply1);
                    }
                }
            }
        }
        /*处理积分过多或者少*/
        updateInAmountPlus(shoping, bigDecimal);
//        int i = 1/0;
        return shoping;
    }



    /*处理积分过多或者少*/
    private List<ByGoodsShoping> updateInAmountPlus(List<ByGoodsShoping> shoping, BigDecimal InAmount) {
        log.info("##############################处理积分余数情况#############################shoping：" + JSONObject.toJSONString(shoping));
        log.info("##############################处理积分券余数情况##############################amount：" + InAmount);
        BigDecimal totalAmount = new BigDecimal(0);
//        取出多余的金额
        for (ByGoodsShoping goodsShoping : shoping) {
            totalAmount = totalAmount.add(goodsShoping.getInAmount() == null ? new BigDecimal(0) : goodsShoping.getInAmount());
        }
        if (totalAmount.compareTo(InAmount) == 0) {
            return shoping;
        }
        //订单总优惠金额-积分金额 = 平摊优惠金额总和
        BigDecimal subtract = InAmount.subtract(totalAmount);
        for (ByGoodsShoping goodsShoping : shoping) {
            if (goodsShoping.getInAmount() == null) {
                continue;
            }
            //积分平摊金额
            BigDecimal amount1 = goodsShoping.getInAmount();
            //单个商品的金额
            BigDecimal sellPrice = new BigDecimal(goodsShoping.getSellPrice());
            //商品的数量
            BigDecimal goodsCount = new BigDecimal(goodsShoping.getGoodsCount());
            //单笔订单的总金额
            BigDecimal sumGoods = sellPrice.multiply(goodsCount);
            //单项订单总金额>积分平摊金额  或   积分平摊金额+余数金额 <= 单项订单总金额
            if (sumGoods.compareTo(amount1) > 0 || (amount1.add(subtract).compareTo(amount1) <= 0)) {
                goodsShoping.setInAmount(amount1.add(subtract));
                break;
            }
        }
        return shoping;
    }


    private List<ByGoodsShoping> updateInAmount(List<ByGoodsShoping> shoping, BigDecimal InAmount) {
        BigDecimal totalAmount = new BigDecimal(0);
//        取出多余的金额
        for (ByGoodsShoping goodsShoping : shoping) {
            totalAmount = totalAmount.add(goodsShoping.getInAmount() == null ? new BigDecimal(0) : goodsShoping.getInAmount());
        }

        if (totalAmount.compareTo(InAmount) == 0) {
            return shoping;
        }
        BigDecimal subtract = totalAmount.subtract(InAmount);
        if (subtract.compareTo(new BigDecimal(0)) == 1) {
            for (ByGoodsShoping goodsShoping : shoping) {
                if (goodsShoping.getAmount() != null) {
                    if (subtract.compareTo(goodsShoping.getAmount()) == 1) {
                        goodsShoping.setInAmount(new BigDecimal(0));
                        subtract = subtract.subtract(goodsShoping.getInAmount());
                    }
                    if (subtract.compareTo(goodsShoping.getAmount()) == 1) {
                        goodsShoping.setAmount(goodsShoping.getInAmount().subtract(subtract));
                        subtract = new BigDecimal(0);
                        break;
                    }
                }
            }
        }
        if (subtract.compareTo(new BigDecimal(0)) == -1) {
            subtract = subtract.multiply(new BigDecimal(-1));
            for (ByGoodsShoping goodsShoping : shoping) {
                if (goodsShoping.getInAmount() != null) {
                    goodsShoping.setInAmount(goodsShoping.getInAmount().add(subtract));
                    break;
                }
            }
        }
        return shoping;
    }

    /**
     * <AUTHOR>
     * @date 2019/8/12 11:15
     * @Description: 微信退款
     */
    private void refundPay(String orderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee) {
        me.hao0.wepay.core.Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
        RefundApplyRequest request = new RefundApplyRequest();
        request.setOutTradeNo(orderNo);
        request.setOutRefundNo(refundNo);
        request.setTotalFee(totalFee.multiply(CommonFinal.BIG_100).intValue());
        request.setRefundFee(refundFee.multiply(CommonFinal.BIG_100).intValue());
        request.setOpUserId("1");
        try {
            wepay.refund().apply(request);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }
}
