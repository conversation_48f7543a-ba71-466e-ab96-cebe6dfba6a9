spring:
  datasource:
    url: ****************************************************************************************************************************************
    username: root
    password: 123456

  redis:
    host: gmcoai.com
    port: 6379
    password: gs123456


fastboot:
  ## 本地上传配置 ##
  upload:
    configs:
      #  场景值：
      #    配置选项1:
      #    配置选项2:
      #    ……
      images:
        #上传文件目录（以 “/” 结尾）
        dir: /statics/images/
        #回显域名
        domain: shinez01.frp.dev.wmeimob.com
        #访问前缀（非 “/” 结尾）
        visit-prefix: /statics/images

  #微信配置
  wechat:
    #正式服
    appid: wx07a42f09798787bb
    secret: 7ce07eca7380a0a3862ca63c79086a0a
    #企业号corpid
    corpid:
    #是否是小程序
    is-miniprogram: true
    #授权作用域
    auth-scope:
    #第三方平台APPID
    component-appid:
    #公众号原始ID
    user-name:
    #消息加解密token
    token: 1
    #消息加解密key
    encoding-aes-key: 1
    #公众号名称
    nick-name: 依视路预约验光
    #二维码地址
    qrcode-url:
    #企业应用
    apps:
      myApp1:
        agentid:
        corpsecret:


    #多公众号配置
    multiples:
      myApp1:
        appid:
        secret:

  #微信支付配置
  wechat-pay:
    #服务商微信商户号 正式服
    server-mch-no: 1498355912
    mch-no: 1498355912
    #微信支付key
    mch-key: wx71e1536f0d6bba2a11111111111111
    #证书位置
    cert-path: apiclient_cert.p12
