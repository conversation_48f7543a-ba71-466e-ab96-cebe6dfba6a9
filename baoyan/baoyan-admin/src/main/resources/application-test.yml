spring:
  datasource:
    url: ************************************************************************************************************************************
    username: root
    password: root

  redis:
    host: 127.0.0.1
    port: 6379
    password: Weimob@baoyan123


fastboot:
  ## 本地上传配置 ##
  upload:
    configs:
      #  场景值：
      #    配置选项1:
      #    配置选项2:
      #    ……
      images:
        #上传文件目录（以 “/” 结尾）
        dir: /statics/images/
        #回显域名
        domain: shinez01.frp.dev.wmeimob.com
        #访问前缀（非 “/” 结尾）
        visit-prefix: /statics/images

  #微信配置
  wechat:
    #测试服
    appid: wx00ddb88325bf5cd5
    secret: ad096cc56acbec51ba3aaf9e889d24bb
    #企业号corpid
    corpid:
    #是否是小程序
    is-miniprogram: true
    #授权作用域
    auth-scope:
    #第三方平台APPID
    component-appid:
    #公众号原始ID
    user-name:
    #消息加解密token
    token: 1
    #消息加解密key
    encoding-aes-key: 1
    #公众号名称
    nick-name: 依视路预约验光
    #二维码地址
    qrcode-url:
    #企业应用
    apps:
      myApp1:
        agentid:
        corpsecret:


    #多公众号配置
    multiples:
      myApp1:
        appid:
        secret:

  #微信支付配置
  wechat-pay:
    #服务商微信商户号
#    server-mch-no: 1282476501
    mch-no: 1282476501
    #微信支付key
    mch-key: 6T81dIM9o3Y2Qvejk1090IpUlOImp1xl
    #证书位置
    cert-path: apiclient_cert-wpp.p12
