<?xml version="1.0" encoding="UTF-8"?>

<!--    scan:
        当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
        scanPeriod:
        设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
        debug:
        当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。-->
<configuration scan="true" scanPeriod="1000">
    <property name="custom.name" value="baoyan"/><!-- 项目名字 -->
    <property name="custom.module" value="admin"/><!-- 模塊名字 -->
    <contextName>${custom.name}-${custom.module}</contextName>

    <!-- 控制台输出 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%thread]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>    <!-- Console 输出设置 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <!-- debug文件输出 -->
    <appender name="debugLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>logs/${custom.name}-${custom.module}_debug.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyy-MM-dd}_${custom.name}-${custom.module}_debug.log</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印DEBUG日志 -->
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- info文件输出 -->
    <appender name="infoLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>logs/${custom.name}-${custom.module}_info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyy-MM-dd}_${custom.name}-${custom.module}_info.log</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印INFO日志 -->
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- error文件输出 -->
    <appender name="errorLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>logs/${custom.name}-${custom.module}_error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>logs/%d{yyyy-MM-dd}_${custom.name}-${custom.module}_error.log</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="root" level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="debugLogFile"/>
        <appender-ref ref="infoLogFile"/>
        <appender-ref ref="errorLogFile"/>
    </logger>

    <logger name="com.wmeimob.fastboot.starter.admin.mapper" addtivity="true">
        <level value="debug"/>
    </logger>

    <logger name="com.wmeimob.fastboot.baoyan.mapper" addtivity="true">
        <level value="debug"/>
    </logger>


</configuration>