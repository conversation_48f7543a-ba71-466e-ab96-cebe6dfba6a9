package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.BaseActivity;
import com.wmeimob.fastboot.baoyan.service.BaseActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName BaseActivityController
 * @Description 【活动表】控制器
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("baseactivity")
@Slf4j
public class BaseActivityController {

    @Resource
    private BaseActivityService baseActivityService;




    /**
     * 活动表分页查询
     * @param request
     * @param baseActivity
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseActivity(HttpServletRequest request, BaseActivity baseActivity){
        PageContext.startPage();
        return new PageInfo<BaseActivity>(baseActivityService.findByCondition(baseActivity));
         
    }

     /**
     * 活动表导出
     * @param request
     * @param baseActivity
     * @return
     */
    @GetMapping("/exports")
    public List<BaseActivity> queryForBaseActivityexports(HttpServletRequest request, BaseActivity 
 baseActivity){
        return  baseActivityService.findByCondition(baseActivity);
    }


    /**
     * 活动表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseActivity queryForBaseActivityById(HttpServletRequest request, @PathVariable("id") Object id){
        BaseActivity baseActivity=    baseActivityService.queryBaseActivityById(id);
        return baseActivity;
    }


    /**
     * 活动表添加
     * @param request
     * @param baseActivity
     * @return
     */
    @PostMapping("/")
    public void insertForBaseActivity(HttpServletRequest request,@RequestBody BaseActivity baseActivity){
            baseActivityService.addBaseActivity(baseActivity);    
    }


    /**
     * 活动表修改
     * @param request
     * @param baseActivity
     * @return
     */
    @PutMapping("/")
    public void updateForBaseActivity(HttpServletRequest request,@RequestBody BaseActivity baseActivity){
            baseActivityService.modifyBaseActivity(baseActivity);  
    }

    /**
     * 活动表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseActivity(HttpServletRequest request,@PathVariable("id") Object id){
            baseActivityService.removeBaseActivity(id);
    }

    /**
     * 活动上下架
     * @param baseActivity
     * @return
     */
    @PutMapping("/openClose")
    public void openClose(@RequestBody BaseActivity baseActivity){
        if(baseActivity.getId() == null){
            throw new CustomException("参数错误");
        }
        baseActivityService.modifyBaseActivity(baseActivity);
    }
}
