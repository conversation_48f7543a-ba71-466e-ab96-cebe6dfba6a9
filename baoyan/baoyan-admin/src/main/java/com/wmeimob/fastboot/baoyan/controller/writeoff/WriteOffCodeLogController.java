package com.wmeimob.fastboot.baoyan.controller.writeoff;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.annotation.ApiVersion;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeLogMapper;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.service.WriteOffCodeLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName WriteOffCodeLogController
 * @Description 【核销码记录表】控制器
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("writeoffcodelog")
@Slf4j
public class WriteOffCodeLogController {

    @Resource
    private WriteOffCodeLogService writeOffCodeLogService;

    @Resource
    private WriteOffCodeLogMapper writeOffCodeLogMapper;


    /**
     * 核销码记录表分页查询
     * @param request
     * @param writeOffCodeLog
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForWriteOffCodeLog(HttpServletRequest request, WriteOffCodeLog writeOffCodeLog){
        PageContext.startPage();
        PageInfo<WriteOffCodeLog> info = new PageInfo<>(writeOffCodeLogService.findByCondition(writeOffCodeLog));
        List<WriteOffCodeLog> list = info.getList();
        for (WriteOffCodeLog codeLog : list) {
            log.info(codeLog.toString());
        }
        return info;
         
    }

     /**
     * 核销码记录表导出
     * @param request
     * @param writeOffCodeLog
     * @return
     */
    @GetMapping("/exports")
    public List<WriteOffCodeLog> queryForWriteOffCodeLogexports(HttpServletRequest request,WriteOffCodeLog
 writeOffCodeLog){
        return  writeOffCodeLogService.findByCondition(writeOffCodeLog);
    }

    @ApiVersion(value = 2)
    @GetMapping("/v2/exports")
    public void queryWriteOffCodeStatisticsExportsV2(WriteOffCodeLog writeOffCodeLog, HttpServletRequest request, HttpServletResponse response){
        List<WriteOffCodeLog> writeOffCodeLogList =  writeOffCodeLogMapper.findByCondition(writeOffCodeLog);

        HashMap<String,String> exportFieldSets = new HashMap<>();
        exportFieldSets.put("writeOffName","商品核销码名称");
        exportFieldSets.put("goodsName","来源商品");
        exportFieldSets.put("orderNo","来源订单");
        exportFieldSets.put("actualAmount","实收金额");
        exportFieldSets.put("channelSourceName","来源渠道");
        exportFieldSets.put("endDate","有效期至");
        exportFieldSets.put("custUserName","所属用户");
        exportFieldSets.put("storeName","核销门店");
        exportFieldSets.put("writeOffNum","核销次数");
        exportFieldSets.put("staffName","核销员工");
        
        exportFieldSets.put("writeOffDate","核销时间");
        //TODO 后续可以导出自定义哪些字段进行导出
//        if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getExportFields())) {
//            exportFieldSets = Sets.newHashSet(dto.getExportFields().split(","));
//        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(String.format("核销记录导出_%s", DateUtils.format(new Date(), "yyyy-MM-dd")), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            ServletOutputStream out=response.getOutputStream();
            // 通过工具类创建writer，默认创建xls格式
            ExcelWriter writer = ExcelUtil.getWriter(true);
            // 一次性写出内容，使用默认样式，强制输出标题
            //自定义标题别名
            exportFieldSets.forEach(writer::addHeaderAlias);
            writer.setOnlyAlias(true);
            //根据exportFieldSets中 key，从writeOffCodeLogList 转换为 list
            List<Map<String, Object>> rows = writeOffCodeLogList.stream().map(item -> {
                Map<String, Object> map = new HashMap<>();
                Map<String, Object> mapItem = BeanUtil.beanToMap(item);
                exportFieldSets.forEach((key, value) -> {
                    map.put(key, mapItem.get(key));
                });
                return map;
            }).collect(Collectors.toList());
            // 获取正确的列索引
            List<String> fieldKeys = new ArrayList<>(exportFieldSets.keySet());
            int orderNoCol = fieldKeys.indexOf("orderNo");
            int amountCol = fieldKeys.indexOf("actualAmount");
            
            mergeCells(writer, rows, orderNoCol, amountCol);

            writer.write(rows);
            writer.flush(out);
            // 关闭writer，释放内存
            writer.close();
            IoUtil.close(out);
        } catch (Exception e) {
            log.info("核销记录导出数据:{}", e.getMessage());
        }
    }

     // 新增合并单元格方法
     private void mergeCells(ExcelWriter writer, List<Map<String, Object>> rows,
     int orderNoColumn, int amountColumn) {
        if (rows.isEmpty() || orderNoColumn < 0 || amountColumn < 0) return;
        boolean isMerge = false;
        int mergeStartRow = 0;
         for (int i = 0; i < rows.size(); i++) {
             String currentOrder = rows.get(i).get("orderNo").toString();
             String newOrder = "";
             if(i + 1 < rows.size()){
                 newOrder = rows.get(i + 1).get("orderNo").toString();
             }else{
                 newOrder = "";
             }

             if (newOrder.equals(currentOrder) && !isMerge) {
                 isMerge = true;
                 mergeStartRow = i;
             }

             if( (!newOrder.equals(currentOrder) && isMerge ) || (i + 1 == rows.size() && isMerge)){
                 writer.merge(mergeStartRow + 1, i + 1, orderNoColumn, orderNoColumn, null, true);
                 writer.merge(mergeStartRow + 1, i + 1, amountColumn, amountColumn, null, true);
                 isMerge = false;
             }
         }
     }


    /**
     * 核销码记录表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public WriteOffCodeLog queryForWriteOffCodeLogById(HttpServletRequest request, @PathVariable("id") Object id){
            return  writeOffCodeLogService.queryWriteOffCodeLogById(id);
    }


    /**
     * 核销码记录表添加
     * @param request
     * @param writeOffCodeLog
     * @return
     */
    @PostMapping("/")
    public void insertForWriteOffCodeLog(HttpServletRequest request,@RequestBody WriteOffCodeLog writeOffCodeLog){
            writeOffCodeLogService.addWriteOffCodeLog(writeOffCodeLog);    
    }


    /**
     * 核销码记录表修改
     * @param request
     * @param writeOffCodeLog
     * @return
     */
    @PutMapping("/")
    public void updateForWriteOffCodeLog(HttpServletRequest request,@RequestBody WriteOffCodeLog writeOffCodeLog){
            writeOffCodeLogService.modifyWriteOffCodeLog(writeOffCodeLog);  
    }

    /**
     * 核销码记录表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForWriteOffCodeLog(HttpServletRequest request,@PathVariable("id") Object id){
            writeOffCodeLogService.removeWriteOffCodeLog(id);
    }

}
