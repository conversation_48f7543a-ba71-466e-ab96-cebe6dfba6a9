package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.BaseActivityService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BaseActivityServiceImpl
 * @Description 活动表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseActivityServiceImpl implements BaseActivityService {

    @Resource
    private  BaseActivityMapper baseActivityMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;


    @Override
    public List<BaseActivity> findByCondition(BaseActivity baseActivity) {
        Example example = new Example(BaseActivity.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(baseActivity.getId())) {
            criteria.andEqualTo("id", baseActivity.getId());
        }
        if (!StringUtils.isEmpty(baseActivity.getActivityName())) {
            criteria.andLike("activityName", StringUtils.fullFuzzy(baseActivity.getActivityName()));
        }
        if (!StringUtils.isEmpty(baseActivity.getImgUrl())) {
            criteria.andLike("imgUrl", StringUtils.fullFuzzy(baseActivity.getImgUrl()));
        }
        if (!StringUtils.isEmpty(baseActivity.getJumpType())) {
            criteria.andEqualTo("jumpTypeId", baseActivity.getJumpType());
        }
        if (!StringUtils.isEmpty(baseActivity.getTarget())) {
            criteria.andLike("target", StringUtils.fullFuzzy(baseActivity.getTarget()));
        }
        if (!StringUtils.isEmpty(baseActivity.getStatus())) {
            criteria.andEqualTo("status", baseActivity.getStatus());
        }
        if (!StringUtils.isEmpty(baseActivity.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", baseActivity.getGmtCreate());
        }
        if (!StringUtils.isEmpty(baseActivity.getGmtUpdate())) {
            criteria.andEqualTo("gmtUpdate", baseActivity.getGmtUpdate());
        }
        criteria.andEqualTo("isDel", 0);
        example.orderBy("id").desc();
        List<BaseActivity> baseActivitys = baseActivityMapper.selectByExample(example);
        return baseActivitys;
    }

    @Override
    public BaseActivity queryBaseActivityById(Object id) {
        return baseActivityMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addBaseActivity(BaseActivity baseActivity) {
        baseActivity.setGmtCreate(new Date());
        //处理请求参数
        dealReqParam(baseActivity);
        //效验参数
        checkParam(baseActivity);
        baseActivityMapper.insertSelective(baseActivity);
    }

    @Override
    public void removeBaseActivity(Object id) {
        BaseActivity baseActivity = new BaseActivity();
        baseActivity.setId(Integer.parseInt(id.toString()));
        baseActivity.setIsDel(1);
        baseActivityMapper.updateByPrimaryKeySelective(baseActivity);
    }

    @Override
    public void modifyBaseActivity(BaseActivity baseActivity) {
        baseActivity.setGmtUpdate(new Date());
        //效验参数
        checkParam(baseActivity);
        //处理请求参数
        dealReqParam(baseActivity);
        baseActivityMapper.updateByPrimaryKeySelective(baseActivity);
    }

    /**
     * 效验参数
     *
     * @param baseActivity
     */
    private void checkParam(BaseActivity baseActivity) {
        if (null != baseActivity.getJumpType()) {
            if (baseActivity.getJumpType().equals(1)) {
                //判断当前分类id是否存在
                if (null == baseActivity || null == baseActivity.getClassifyId() || StringUtils.isEmpty(baseActivity.getClassifyId())) {
                    throw new CustomException("请选择分类");
                }
            }
            if (baseActivity.getJumpType().equals(2)) {
                if (null == baseActivity || null == baseActivity.getTargetImgUrl()) {
                    throw new CustomException("请上传联票列表图片");
                }
            }
            if (baseActivity.getJumpType().equals(3)) {
                if (null == baseActivity || null == baseActivity.getTargetImgUrl()) {
                    throw new CustomException("请上传次卡列表图片");
                }
            }
            if (baseActivity.getJumpType().equals(4)) {
                if (null == baseActivity || null == baseActivity.getTargetImgUrl()) {
                    throw new CustomException("请上传拼团列表图片");
                }
            }

            if (baseActivity.getJumpType().equals(5)) {
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", baseActivity.getTarget());
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    throw new CustomException("文章不存在");
                }
            }
            if (baseActivity.getJumpType().equals(6)) {
                //判断当前商品普通商品存在
                Example example = new Example(ByGoodsInfo.class);
                example.createCriteria().andEqualTo("id", baseActivity.getTarget()).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(example);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }

            }
            if (baseActivity.getJumpType().equals(7)) {
                //判断当前联票是否存在
                Example example = new Example(ByTicketGoods.class);
                example.createCriteria().andEqualTo("id", baseActivity.getTarget()).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(example);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    throw new CustomException("联票商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseActivity.getJumpType().equals(8)) {
                //判断当前次卡商品是否存在
                Example example = new Example(BySubCardGoods.class);
                example.createCriteria().andEqualTo("id", baseActivity.getTarget()).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(example);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    throw new CustomException("次卡商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseActivity.getJumpType().equals(9)) {
                //判断当前拼团商品是否存在
                Example example = new Example(ByTeamGoods.class);
                example.createCriteria().andEqualTo("id", baseActivity.getTarget()).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(example);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    throw new CustomException("拼团商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
        }

    }

    /***
     * 处理类型参数
     * @param baseActivity
     */
    private void dealReqParam(BaseActivity baseActivity) {
        if (null != baseActivity.getJumpType()) {
            String target;
            switch (baseActivity.getJumpType()) {
                case 1:
                    target = String.valueOf(baseActivity.getClassifyId());
                    break;
                case 2:
                    target = baseActivity.getTargetImgUrl();
                    break;
                case 3:
                    target = baseActivity.getTargetImgUrl();
                    break;
                case 4:
                    target = baseActivity.getTargetImgUrl();
                    break;
                default:
                    target = baseActivity.getTarget();
                    break;
            }
            baseActivity.setTarget(target);
        }

    }

}
