package com.wmeimob.fastboot.baoyan.tool.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.wmeimob.fastboot.autoconfigure.sms.aliyun.AliyunSms;
import com.wmeimob.fastboot.autoconfigure.sms.aliyun.AliyunSmsProperties;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.rest.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-26 10:14
 * @Version 1.0
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SchedulerUtil {

    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private ByIntegralLogMapper byIntegralLogMapper;
    @Resource
    private AliyunSmsProperties aliyunSmsProperties;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;

    /**
     * 取消订单
     * */
    protected void cloneOrder(ByOrders e,Integer count) {
        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getUserId());
        Integer nowPoint = byCustUser.getNowPoint();
        BigDecimal divide = e.getIntegralAmount().divide(new BigDecimal(count), 2, BigDecimal.ROUND_HALF_EVEN);
        BigDecimal add = new BigDecimal(byCustUser.getNowPoint()).add(divide);
        int i = this.byCustUserMapper.updateByPointAdd(byCustUser.getId(), divide.intValue());
        if ( i > 0){
            ByIntegralLog byIntegralLog = new ByIntegralLog();
            byIntegralLog.setUserId(byCustUser.getId());
            byIntegralLog.setChangeType(1);
            byIntegralLog.setChangeNum(add.intValue());
            byIntegralLog.setChangeReason("订单取消");
            byIntegralLog.setIntegralType(2);
            byIntegralLog.setBeforeNum(nowPoint);
            byIntegralLog.setGmtCreate(new Date());
            if (byIntegralLog.getChangeNum() > 0){
                byIntegralLogMapper.insertSelective(byIntegralLog);
            }
        }
        /*返回库存*/
        e.setOrderStatus(4);
        e.setGmtUpdate(new Date());
        this.byOrdersMapper.updateByPrimaryKeySelective(e);
        //更新 核销码状态 为已取消 状态
        Example orderExample = new Example(WriteOffCode.class);
        orderExample.createCriteria().andEqualTo("orderNo", e.getOrderNo());
        WriteOffCode writeOffCode = new WriteOffCode();
        writeOffCode.setOrderState(1);//已取消
        writeOffCodeMapper.updateByExampleSelective(writeOffCode, orderExample);

        Example example = new Example(ByOrderGoods.class);
        example.createCriteria().andEqualTo("orderNo",e.getOrderNo());
        List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example);
        if (byOrderGoods.size() != 0){
            for (ByOrderGoods goods : byOrderGoods){
                if ("1".equals(goods.getProductType())){
                    ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goods.getProductId());
                    this.byGoodsInfoMapper.updateByAddStock(byGoodsInfo.getId(),goods.getProductCount());
                }
                if ("2".equals(goods.getProductType())){
                    BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(goods.getProductId());
                    bySubCardGoods.setGoodsStock(bySubCardGoods.getGoodsStock()+goods.getProductCount());
                    this.bySubCardGoodsMapper.updateByAddStock(bySubCardGoods.getId(),goods.getProductCount());
                }
                if ("3".equals(goods.getProductType())){
                    ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(goods.getProductId());
                    this.byTicketGoodsMapper.updateByAddStock(byTicketGoods.getId(),goods.getProductCount());
                }
            }
        }
    }

    /**
    * @Description 发送短信
    * <AUTHOR>
    * @Date        2019-08-26 10:44
    * @Version    1.0
    */
    public RestResult createCode(Map<String, String> paramMap,String mobile, String template){
        try {
            SendSmsResponse resp = AliyunSms.getInstance(this.aliyunSmsProperties).sendSms(mobile, paramMap, template);
            log.info(JSONObject.toJSONString(resp));
        } catch (ClientException var5) {
            log.error(var5.getMessage(), var5);
        }
        return RestResult.success();
    }

}
