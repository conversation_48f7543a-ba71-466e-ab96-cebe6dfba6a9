package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.baoyan.vo.ByGoodsInfoVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName ByGoodsInfoServiceImpl
 * @Description 商品信息表 服务类实现
 * <AUTHOR>
 * @Date Fri Jul 12 13:41:20 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByGoodsInfoServiceImpl implements ByGoodsInfoService {

    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByGoodsStoreMapper byGoodsStoreMapper;
    @Resource
    private ByGoodsClassifyMapper byGoodsClassifyMapper;
    @Resource
    private ByRichTextMapper byRichTextMapper;
    @Resource
    private ByTicketGoodsMappingMapper byTicketGoodsMappingMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private BaseBannerMapper baseBannerMapper;
    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;
    @Resource
    private BaseActivityMapper baseActivityMapper;


    /**
     * 删除提示信息
     */
    public static final String ERR_MESSAGE = "当前商品 联票、次卡、拼团 首页推荐中有引用,暂不能删除";




    @Override
    public List<ByGoodsInfo> findByCondition(ByGoodsInfo byGoodsInfo) {
        List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.findByCondition(byGoodsInfo);
        return byGoodsInfos;
    }

    @Override
    public ByGoodsInfo queryByGoodsInfoById(Object id) {
        return byGoodsInfoMapper.selectByPrimaryKey(id);
    }

    public static long getLineNumber(String target) throws IOException {
        return target.trim().split("\n").length;
    }

    /**
     * 门票商品二维码
     */
    private static final String CODE_URL = "pages/home/<USER>/classDetails/main";
    @Resource
    private QrCodeService qrCodeService;


    @Override
    public ByGoodsInfo addByGoodsInfo(ByGoodsInfo byGoodsInfo) throws IOException {
        byGoodsInfo.setGmtCreate(new Date());
        byGoodsInfo.setGoodsNo(getGoodsNo());
        byGoodsInfoMapper.insertSelective(byGoodsInfo);

        // 生成小程序二维码
        ByGoodsInfo goodsInfo = queryByGoodsInfoById(byGoodsInfo.getId());
        if (null != goodsInfo) {
            generateQrCode(goodsInfo);
        }
        //门店新增 stores
        if (null == byGoodsInfo.getStores() || byGoodsInfo.getStores().equals("")) {
            throw new CustomException("请选择门店");
        }

        List<String> storeList = Arrays.asList(byGoodsInfo.getStores().split(","));
        if (storeList.size() > 0) {
            for (String str : storeList) {
                ByGoodsStore store = new ByGoodsStore();
                store.setGmtCreate(new Date());
                store.setStoreId(Integer.valueOf(str));
                store.setGoodsId(byGoodsInfo.getId());
                byGoodsStoreMapper.insertSelective(store);
            }
        } else {
            throw new CustomException("请选择门店");
        }

        //分类
        if (null == byGoodsInfo.getClassifyIds()) {
            throw new CustomException("请选择分类");
        }
        List<String> classfyList = Arrays.asList(byGoodsInfo.getClassifyIds().split(","));
        if (classfyList.size() > 0) {
            for (String cla : classfyList) {
                ByGoodsClassify classify = new ByGoodsClassify();
                classify.setGmtCreate(new Date());
                classify.setGoodsId(byGoodsInfo.getId());
                classify.setClassifyId(Integer.valueOf(cla));
                byGoodsClassifyMapper.insertSelective(classify);
            }
        } else {
            throw new CustomException("请选择分类");
        }
        if (null == byGoodsInfo.getRichContent() || byGoodsInfo.getRichContent().equals("")) {
            throw new CustomException("请填写商品描述");
        }
        //富文本新增
        ByRichText richText = new ByRichText();
        richText.setDataId(byGoodsInfo.getId());
        richText.setDataType(BaoYanConstant.RICH_TYPE_1);
        richText.setGmtCreate(new Date());
        richText.setContent(byGoodsInfo.getRichContent());

        byRichTextMapper.insertSelective(richText);
        return byGoodsInfo;
    }

    /**
     * 商城编号生成
     *
     * @return
     */
    private String getGoodsNo() {
        StringBuffer sb = new StringBuffer();
        sb.append("SP");
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        sb.append(sdf.format(new Date()));
        return sb.toString();
    }

    @Override
    public void removeByGoodsInfo(Integer id) {
        //效验当前商品是否在其他地方有引用
        checkGoodsQuote(id);
        ByGoodsInfo byGoodsInfo = new ByGoodsInfo();
        byGoodsInfo.setId(Integer.parseInt(id.toString()));
        byGoodsInfo.setIsDel(true);
        int delNum = byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
        if (delNum <= 0) {
            throw new CustomException("删除失败");
        }
    }

    /**
     * 效验当前商品是否在其他地方有引用
     *
     * @param id 商品id
     */
    private void checkGoodsQuote(Integer id) {
        //联票商品
        Example ticketExample = new Example(ByTicketGoodsMapping.class);
        ticketExample.createCriteria().andEqualTo("goodsId", id);
        List<ByTicketGoodsMapping> byTicketGoodsMappings = byTicketGoodsMappingMapper.selectByExample(ticketExample);
        if (null != byTicketGoodsMappings && byTicketGoodsMappings.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        //拼团商品
        Example teamGoodExample = new Example(ByTeamGoods.class);
        teamGoodExample.createCriteria().andEqualTo("goodsId", id).andEqualTo("isDel", 0);
        List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(teamGoodExample);
        if (null != byTeamGoods && byTeamGoods.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        //首页推荐位置判断是否有引用
        Example bannerExample = new Example(BaseBanner.class);
        //商品类型
        bannerExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target", id).andEqualTo("isDel", 0);
        List<BaseBanner> baseBanners = baseBannerMapper.selectByExample(bannerExample);
        if (null != baseBanners && baseBanners.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseNavigationConfExample = new Example(BaseNavigationConf.class);
        baseNavigationConfExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target", id);
        List<BaseNavigationConf> baseNavigationConfs = baseNavigationConfMapper.selectByExample(baseNavigationConfExample);
        if (null != baseNavigationConfs && baseNavigationConfs.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseActivityExample = new Example(BaseActivity.class);
        baseActivityExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target", id).andEqualTo("isDel", 0);
        List<BaseActivity> baseActivities = baseActivityMapper.selectByExample(baseActivityExample);
        if (null != baseActivities && baseActivities.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
    }

    @Override
    public void modifyByGoodsInfo(ByGoodsInfo byGoodsInfo) throws IOException {
        byGoodsInfo.setGmtModified(new Date());
        //门店
        if (null == byGoodsInfo.getStores()) {
            throw new CustomException("请选择门店");
        }
        List<String> storeList = Arrays.asList(byGoodsInfo.getStores().split(","));
        if (null != storeList && !byGoodsInfo.getStores().equals("") && storeList.size() > 0) {
            //删除原有的门店 关联
            Example example = new Example(ByGoodsStore.class);
            example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
            byGoodsStoreMapper.deleteByExample(example);
            for (String str : storeList) {
                ByGoodsStore store = new ByGoodsStore();
                store.setGmtCreate(new Date());
                store.setStoreId(Integer.valueOf(str));
                store.setGoodsId(byGoodsInfo.getId());
                byGoodsStoreMapper.insertSelective(store);
            }
        } else {
            throw new CustomException("请选择门店");
        }
        //分类
        if (null == byGoodsInfo.getClassifyIds()) {
            throw new CustomException("请选择分类");
        }
        List<String> classfyList = Arrays.asList(byGoodsInfo.getClassifyIds().split(","));
        if (null != classfyList && !byGoodsInfo.getClassifyIds().equals("") && classfyList.size() > 0) {
            //删除原有的分类关联
            Example example = new Example(ByGoodsStore.class);
            example.createCriteria().andEqualTo("goodsId", byGoodsInfo.getId());
            byGoodsClassifyMapper.deleteByExample(example);
            for (String cla : classfyList) {
                ByGoodsClassify classify = new ByGoodsClassify();
                classify.setGmtCreate(new Date());
                classify.setGoodsId(byGoodsInfo.getId());
                classify.setClassifyId(Integer.valueOf(cla));
                byGoodsClassifyMapper.insertSelective(classify);
            }
        } else {
            throw new CustomException("请选择分类");
        }
        //添加log日志记录修改
        log.info(">>>>>商品修改前 info：{}", byGoodsInfoMapper.selectByPrimaryKey(byGoodsInfo.getId()));
        byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
        log.info(">>>>>商品修改后 info：{}", byGoodsInfo);
        //富文本修改
        ByRichText richText = new ByRichText();
        richText.setDataId(byGoodsInfo.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_1);
        richText.setContent(byGoodsInfo.getRichContent());
        byRichTextMapper.updateByDataIdAndType(richText);

    }

    @Override
    public void updateShelf(ByGoodsInfo byGoodsInfo) {
        checkGoodShelfTime(byGoodsInfo);
        byGoodsInfo.setGmtModified(new Date());
        byGoodsInfo.setGmtCreate(new Date());
        byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
    }

    /**
     * 效验当前 商品上架时间是否
     *
     * @param byGoodsInfo
     */
    private void checkGoodShelfTime(ByGoodsInfo byGoodsInfo) {
        ByGoodsInfo goodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byGoodsInfo.getId());
        //上架判断 下产品核销码是否 过期
//        if (byGoodsInfo.getStatus().equals(1)) {
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(goodsInfo.getVerificationEnd());
//            if (calendar.getTime().getTime() < new Date().getTime()) {
//                throw new CustomException("当前商品不满足上架要求,请效验产品核销时间");
//            }
//        }

    }

    @Override
    public ByGoodsInfoVO queryGoodDetailInfoById(Integer id) {
        ByGoodsInfoVO vo = byGoodsInfoMapper.queryGoodDetailInfoById(id);
        return vo;
    }

    // 生成二维码 并更新数据库
    private String generateQrCode(ByGoodsInfo goodsInfo) {
        HashMap<String, Object> objectHashMap = new HashMap<>(2);
        objectHashMap.put("id", goodsInfo.getId());
        objectHashMap.put("type", 1);
        String url = qrCodeService.exportQrCode(null, objectHashMap, CODE_URL);
        if (null != url) {
            goodsInfo.setCodeImg(url);
            byGoodsInfoMapper.updateByPrimaryKeySelective(goodsInfo);
        }
        return url;
    }

    @Override
    public String queryCodeImg(Integer id, Boolean flag) {
        ByGoodsInfo byGoodsInfo = queryByGoodsInfoById(id);
        if (null == byGoodsInfo) throw new CustomException("参数不对");
        if (StringUtils.isNotBlank(byGoodsInfo.getCodeImg()) && !flag) return byGoodsInfo.getCodeImg();
        return generateQrCode(byGoodsInfo);
    }
}
