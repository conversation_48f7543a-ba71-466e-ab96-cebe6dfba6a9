package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.entity.ByRichText;
import com.wmeimob.fastboot.baoyan.entity.ByTeamGoods;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsInfoMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByRichTextMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByTeamGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.ByTeamGoodsService;
import com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTeamGoodsServiceImpl
 * @Description  拼团商品表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTeamGoodsServiceImpl implements ByTeamGoodsService {

    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByRichTextMapper byRichTextMapper;


    @Override
    public List<ByTeamGoods> findByCondition(ByTeamGoods byTeamGoods) {
        List<ByTeamGoods> byTeamGoodss = byTeamGoodsMapper.findByCondition(byTeamGoods);
        return byTeamGoodss;
    }

    @Override
    public ByTeamGoods queryByTeamGoodsById(Integer  id) {
        return byTeamGoodsMapper.queryByTeamGoodsById(id);
    }

	@Override
    public void addByTeamGoods(ByTeamGoods byTeamGoods) {
	  byTeamGoods.setGmtCreate(new Date());
	  checkGoodsInfoExitById(byTeamGoods.getGoodsId());
        byTeamGoodsMapper.insertSelective(byTeamGoods);
        //富文本
        ByRichText richText=new ByRichText();
        richText.setDataId(byTeamGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_4);
        richText.setContent(byTeamGoods.getRichContent());
        if (byTeamGoods.getTeamHour().equals(byTeamGoods.getAutoHour())){
            throw new CustomException("拼团时间和自动拼团成功时间不能一样");
        }
        byRichTextMapper.insertSelective(richText);
    }

    /**
     * 效验商品 是否存在
     */
    private void checkGoodsInfoExitById(Integer goodsId){
        if (null==goodsId) {
            throw new CustomException("请选择商品id");
        }
        //判断商品是否存在
        ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(goodsId);
        if (null==byGoodsInfo){
            throw new CustomException("商品不存在");
        }
    }
    @Override
    public void removeByTeamGoods(Object id) {
	  ByTeamGoods byTeamGoods = new ByTeamGoods();
	  byTeamGoods.setId(Integer.parseInt(id.toString()));
	  byTeamGoods.setIsDel(true);
        byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
    }

    @Override
    public void modifyByTeamGoods(ByTeamGoods byTeamGoods) {
        checkGoodsInfoExitById(byTeamGoods.getGoodsId());
        byTeamGoods.setGmtUpdate(new Date());
        byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
        ByRichText richText = new ByRichText();
        richText.setDataId(byTeamGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_4);
        richText.setContent(byTeamGoods.getRichContent());
        if (byTeamGoods.getTeamHour().equals(byTeamGoods.getAutoHour())){
            throw new CustomException("拼团时间和自动拼团成功时间不能一样");
        }
        byRichTextMapper.updateByDataIdAndType(richText);
    }
    @Override
    public  List<TeamGoodsVo> forms(ByTeamGoods byTeamGoods) {
      return   byTeamGoodsMapper.forms(byTeamGoods);
    }

    @Override
    public void updateShelf(ByTeamGoods byTeamGoods) {
        byTeamGoods.setGmtUpdate(new Date());
        byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
    }




}
