package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByStoreTable;
import com.wmeimob.fastboot.baoyan.mapper.ByStoreTableMapper;
import com.wmeimob.fastboot.baoyan.qo.StoreTableQo;
import com.wmeimob.fastboot.baoyan.vo.StoreTableVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 桌台管理
 */
@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByStoreTableServiceImpl implements ByStoreTableService {

    @Resource
    private ByStoreTableMapper byStoreTableMapper;

    @Override
    public List<StoreTableVo> queryStoreNoList(StoreTableQo storeTableQo) {
        return byStoreTableMapper.queryStoreNoList(storeTableQo);
    }

    @Override
    public Boolean addByStoreTable(ByStoreTable byStoreTable) {
        if (StringUtils.isEmpty(byStoreTable.getStoreId())) {
            throw new CustomException("门店不能为空");
        }
        if (StringUtils.isEmpty(byStoreTable.getTableNo())) {
            throw new CustomException("桌号不能为空");
        }
        if (StringUtils.isEmpty(byStoreTable.getPeopleNum())) {
            throw new CustomException("桌人数不能为空");
        }
        byStoreTable.setGmtCreate(new Date());
        return byStoreTableMapper.insertSelective(byStoreTable) > 0;
    }

}
