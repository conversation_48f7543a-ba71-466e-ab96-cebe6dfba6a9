package com.wmeimob.fastboot.baoyan.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.tool.impl.SchedulerImpl;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.exception.NotImplementedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName WriteOffCodeServiceImpl
 * @Description 核销码表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class WriteOffCodeServiceImpl implements WriteOffCodeService {

    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Resource
    private BaseStoreMapper baseStoreMapper;


    @Override
    public List<WriteOffCode> findByCondition(WriteOffCode writeOffCode) {

        List<WriteOffCode> writeOffCodes = writeOffCodeMapper.findByCondition(writeOffCode);
        if (null != writeOffCodes && writeOffCodes.size() > 0) {
            for (WriteOffCode code : writeOffCodes) {
                if (null != code.getSurplusNum() && null != code.getTotalNum()) {
                    code.setSurplusShow(code.getSurplusNum() + "/" + code.getTotalNum());
                }

                if (null != code.getStoreIds()) {
                    List<String> storeList = Arrays.asList(code.getStoreIds().split(","));
                    StringBuffer buffer = new StringBuffer();
                    for (String store : storeList) {
                        if (buffer.length() > 0) {
                            buffer.append(",");
                        }
                        BaseStore resultStore = baseStoreMapper.selectByPrimaryKey(store);
                        if (null == resultStore) {
                            continue;
                        }
                        buffer.append(resultStore.getName());
                    }
                    code.setStoreName(buffer.toString());
                }
            }
        }

        return writeOffCodes;
    }

    @Override
    public WriteOffCode queryWriteOffCodeById(Object id) {
        return writeOffCodeMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addWriteOffCode(WriteOffCode writeOffCode) {
        writeOffCode.setGmtCreate(new Date());
        writeOffCodeMapper.insertSelective(writeOffCode);
    }

    @Override
    public void removeWriteOffCode(Object id) {
        WriteOffCode writeOffCode = new WriteOffCode();
        writeOffCode.setId(Integer.parseInt(id.toString()));
        writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
    }

    @Override
    public void modifyWriteOffCode(WriteOffCode writeOffCode) {
        writeOffCode.setGmtModified(new Date());
        writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode);
    }

    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private ByTeamOrderMapper byTeamOrderMapper;
    @Resource
    private ByOrderAfterMapper byOrderAfterMapper;
    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;

    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    @Autowired
    private SchedulerImpl scheduler;
    @Resource
    private ByOrderLogService orderLogService;

    @Override
    public List<WriteOffCode> queryByDetailId(Integer detailId){
        Example example = new Example(WriteOffCode.class);
        example.createCriteria()
                .andEqualTo("detailId", detailId);

        return writeOffCodeMapper.selectByExample(example);
    }

    /**
     * 核销一个核销码
     * @param writeOffCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOff(WriteOffCode writeOffCode) {
        checkUpdateOffReqParam(writeOffCode);

        WriteOffCode writeOffCode1 = this.writeOffCodeMapper.selectByPrimaryKey(writeOffCode.getId());
        List<Integer> orderStatus = Lists.newArrayList();
        orderStatus.add(1);
        orderStatus.add(-1);
        orderStatus.add(-2);
        orderStatus.add(4);
        //查看当前订单是否待支付
        if (writeOffCode1.getOrderType().equals(0)) {
            Example queryOrder = new Example(ByOrders.class);
            queryOrder.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo()).andIn("orderStatus", orderStatus);
            List<ByOrders> byOrders = byOrdersMapper.selectByExample(queryOrder);
            if (null != byOrders && byOrders.size() > 0) {
                throw new CustomException("当前订单不能核销");
            }

        } else {
            Example queryOrder = new Example(ByTeamOrder.class);
            queryOrder.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo()).andIn("orderStatus", orderStatus);
            List<ByTeamOrder> teamOrders = byTeamOrderMapper.selectByExample(queryOrder);
            if (null != teamOrders && teamOrders.size() > 0) {
                throw new CustomException("当前订单不能核销");
            }
        }


        if (writeOffCode1 == null || writeOffCode1.getStatus() != 0) {
            throw new CustomException("已核销或已过期");
        }
        try {
            //判断核销 开始时间 是否 大于等于 当天 &&  结束时间小于等于今天
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = format.parse(format.format(new Date()));
            Date startDate = format.parse(format.format(writeOffCode1.getExpiryDate()));
            Date endDate = format.parse(format.format(writeOffCode1.getEndDate()));
            //当天 大于等于核销开始时间
            if (currentDate.compareTo(startDate) < 0) {
                throw new CustomException("暂未到核销开始时间");
            }
            //当天 >结束时间 过期
            /*if (currentDate.compareTo(endDate) == 1) {
                throw new CustomException("已核销或已过期");
            }*/

        } catch (ParseException e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }

        ByOrderGoods byOrderGoods = null;
        if (writeOffCode1.getOrderType() == 0) {
            byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
            if (byOrderGoods == null) {
                throw new CustomException("订单不存在");
            }
        }
        if (writeOffCode1.getOrderType() == 1) {
            ByTeamOrder byTeamOrder = byTeamOrderMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
            if (byTeamOrder == null) {
                throw new CustomException("订单不存在");
            }
        }
        /*判断是否完成退款*/
        Example example1 = new Example(ByOrderAfter.class);
        example1.createCriteria().andEqualTo("afterStatus", 1)
                //两表类型不一致 write_off_code   `order_type` 订单类型 0普通订单 1拼团订单',  售后表类型 普通 1拼团2
                .andEqualTo("resouceType", writeOffCode1.getOrderType().equals(BaoYanConstant.CONSTANT_ZERO)?BaoYanConstant.CONSTANT_ONE:BaoYanConstant.CONSTANT_TWO)
                .andEqualTo("detailId", writeOffCode1.getDetailId());
        List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example1);
        if (byOrderAfters.size() > 0) {
            throw new CustomException("已售后不能核销");
        }
        //ByCustUser user = SecurityContext.getUser();
        /*ByCustUser byCustUser1 = this.byCustUserMapper.selectByPrimaryKey(writeOffCode.getCustUserId());//前端傳值的核銷人
        Example queryExample = new Example(ByStoreStaff.class);
        queryExample.createCriteria().andEqualTo("staffPhone",byCustUser1.getMobile()).andEqualTo("isDel",Boolean.FALSE);
        List<ByStoreStaff> byStoreStaffs = this.byStoreStaffMapper.selectByExample(queryExample);
        if (byStoreStaffs.size() <= 0){
            throw new CustomException("无权核销");
        }*/
        //前端傳值的核銷员工id writeOffCode.getCustUserId()
        ByStoreStaff byStoreStaff = byStoreStaffMapper.selectByPrimaryKey(writeOffCode.getCustUserId());
        if (null==byStoreStaff) {
            throw new CustomException("员工不存在");
        }
        Example queryExample = new Example(ByCustUser.class);
        queryExample.createCriteria().andEqualTo("wxOpenId", byStoreStaff.getOpenId());
        List<ByCustUser> byCustUsers = byCustUserMapper.selectByExample(queryExample);
        if (CollectionUtils.isEmpty(byCustUsers)) {
            throw new CustomException("用户不存在");
        }
        //判断 是否 当前门店 员工 是否可以核销 此核销码
        if (null == writeOffCode1.getStoreIds()) {
            throw new CustomException("核销码异常");
        }
        List<String> stroeList = Arrays.asList(writeOffCode1.getStoreIds().split(","));
        if (!stroeList.contains(writeOffCode.getStoreIds())) {
            throw new CustomException("当前门店员工不能核销");
        }

        //余量是否 充足
        if (writeOffCode1.getSurplusNum() > 0) {
            writeOffCode1.setSurplusNum(writeOffCode1.getSurplusNum() - writeOffCode.getSurplusNum());
            if (writeOffCode1.getSurplusNum() == 0) {
                writeOffCode1.setStatus(1);
            }
        } else {
            throw new CustomException("余量不足");
        }
        writeOffCode1.setGmtModified(new Date());
        this.writeOffCodeMapper.updateByPrimaryKeySelective(writeOffCode1);
        WriteOffCodeLog writeOffCodeLog = new WriteOffCodeLog();
        writeOffCodeLog.setWriteOffId(writeOffCode.getId());
        writeOffCodeLog.setCustUserId(writeOffCode1.getCustUserId());
        writeOffCodeLog.setStaffId(writeOffCode.getCustUserId());//custUserId前端传来的是 选择的门店id
        writeOffCodeLog.setWriteOffNum(writeOffCode.getSurplusNum());
        writeOffCodeLog.setStoreId(Integer.parseInt(writeOffCode.getStoreIds()));
        writeOffCodeLog.setWriteOffDate(new Date());
        writeOffCodeLog.setGmtCreate(new Date());
        writeOffCodeLogMapper.insertSelective(writeOffCodeLog);

        //检查当前订单详情的核销码 如果全部退款或者全部核销，订单状态就是 已完成
        List<WriteOffCode> write1 = writeOffCodeMapper.select(WriteOffCode.builder().detailId(writeOffCode1.getDetailId()).build());
        boolean allMatch1 = write1.stream().allMatch(code -> code.getStatus() == 1 || code.getOrderState() == 1);
        if (allMatch1){
            byOrderGoodsMapper.updateByPrimaryKeySelective(
                    ByOrderGoods.builder()
                            .id(writeOffCode1.getDetailId())
                            .orderStatus(3)
                            .build()
            );
        }

        //检查当前订单的核销码 如果全部退款或者全部核销，订单状态就是 已完成
        List<WriteOffCode> write2 = writeOffCodeMapper.select(WriteOffCode.builder().orderNo(writeOffCode1.getOrderNo()).build());
        boolean allMatch2 = write2.stream().allMatch(code -> code.getStatus() == 1 || code.getOrderState() == 1);
        if (allMatch2){
            byOrdersMapper.updateStatusByOrderNo(
                    ByOrders.builder()
                            .orderNo( writeOffCode1.getOrderNo() )
                            .orderStatus( 3 )
                            .build()
            );
        }

        //记录日志
        orderLogService.addOrderLogAsync(
                ByOrderLog.builder()
                        .orderNo(writeOffCode1.getOrderNo())
                        .logType(OrderLogType.WRITE_OFF)
                        .orderGoodsId(writeOffCode1.getDetailId())
                        .build()
        );


        //check 订单是否全部核销完成 若是的话 订单变成已完成状态
        checkOrderWriteCode(writeOffCode1);

    }

    /**
     * 效验请求参数
     *
     * @param writeOffCode
     */
    private void checkUpdateOffReqParam(WriteOffCode writeOffCode) {
        if (null == writeOffCode.getStoreIds()) {
            throw new CustomException("请选择核销门店");
        }
        if (null == writeOffCode.getCustUserId()) {
            throw new CustomException("请选择核销员工");
        }
    }


    /**
     * check 订单是否全部核销完成 若是的话 订单变成已完成状态
     *
     * @param writeOffCode1
     */
    private void checkOrderWriteCode(WriteOffCode writeOffCode1) {
        //判断订单类型
        if (writeOffCode1.getOrderType().equals(0)) {
            //普通  获取核销表 所有关联当前订单 核销记录 (不包含已过期)
            Example orderExample = new Example(WriteOffCode.class);
            orderExample.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo()).andNotEqualTo("status", BaoYanConstant.CONSTANT_TWO).andEqualTo("orderState", 0);
            List<WriteOffCode> writeList = writeOffCodeMapper.selectByExample(orderExample);
            if (null != writeList && writeList.size() > 0) {
                boolean falg = false;
                for (WriteOffCode code : writeList) {
                    //效验剩余核销码 是否核销完
                    if (code.getSurplusNum().intValue() > 0) {
                        falg = true;
                    }
                }
                if (!falg) {
                    Example orderReqExample = new Example(ByOrders.class);
                    orderReqExample.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo());
                    ByOrders byOrders = new ByOrders();
                    byOrders.setOrderStatus(BaoYanConstant.CONSTANT_THREE);//已完成
                    byOrders.setGmtUpdate(new Date());
                    byOrdersMapper.updateByExampleSelective(byOrders, orderReqExample);
                    scheduler.get(writeOffCode1.getDetailId(), 1);
                }
            }
            ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
            byOrderGoods.setIsAfterSale(false);
            this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
        } else {
            WriteOffCode writeOff = this.writeOffCodeMapper.selectByPrimaryKey(writeOffCode1.getId());
            //拼团订单（拼团订单 默认一笔订单插入一条 核销码记录）
            //查询核销余量或者 是否过期  ||  或者已完成
            if (writeOff.getStatus().equals(BaoYanConstant.CONSTANT_TWO)||writeOff.getStatus().equals(BaoYanConstant.CONSTANT_ONE)) {
                //更新 当前拼团订单已完成
                Example teamExample = new Example(ByTeamOrder.class);
                teamExample.createCriteria().andEqualTo("orderNo", writeOff.getOrderNo());
                ByTeamOrder byTeamOrder = new ByTeamOrder();
                byTeamOrder.setOrderStatus(BaoYanConstant.CONSTANT_THREE);//已完成
                byTeamOrder.setGmtUpdate(new Date());
                byTeamOrderMapper.updateByExampleSelective(byTeamOrder, teamExample);
                scheduler.get(writeOffCode1.getDetailId(), 2);
                ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
                byOrderGoods.setIsAfterSale(false);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
            }
            scheduler.get(writeOffCode1.getDetailId(), 2);
        }

    }

}
