package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsStock;
import com.wmeimob.fastboot.baoyan.service.TcGoodsStockService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (TcGoodsStock)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@Slf4j
@RestController
@RequestMapping("tc/tcGoodsStock")
public class TcGoodsStockController {
    /**
     * 服务对象
     */
    @Resource
    private TcGoodsStockService tcGoodsStockService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("selectOne")
    public TcGoodsStock selectOne(Integer id) {
        return this.tcGoodsStockService.queryById(id);
    }

    @PutMapping("/")
    public Boolean insertTcGoodsStock(@RequestBody TcGoodsStock addObject){
        log.info("put => insertTcGoodsStock [入参]============={}", addObject);
        Boolean insert = tcGoodsStockService.insert(addObject);
        return insert;
    }
    @GetMapping("/")
    public PageInfo<TcGoodsStock> queryGoodsStock(TcGoodsStock queryObject){
        PageContext.startPage();
        log.info("get=> queryGoodsStock [入参]============={}", queryObject);
        return new PageInfo<>(tcGoodsStockService.queryPageList(queryObject));
    }
    @PostMapping("/")
    public Boolean updateObj(@RequestBody TcGoodsStock updateObj){
        log.info("post => updateObj [入参]============={}", updateObj);
       return tcGoodsStockService.update(updateObj);
    }

}