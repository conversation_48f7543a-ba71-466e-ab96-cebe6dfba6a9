package com.wmeimob.fastboot.baoyan.controller.order;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.baoyan.service.ByEvaluateService;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByEvaluateController
 * @Description 【评价表】控制器
 * <AUTHOR>
 * @Date Wed Jul 17 16:52:31 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byevaluate")
@Slf4j
public class ByEvaluateController {

    @Resource
    private ByEvaluateService byEvaluateService;




    /**
     * 评价表分页查询
     * @param request
     * @param byEvaluate
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByEvaluate(HttpServletRequest request, ByEvaluate byEvaluate){
        PageContext.startPage();
        return new PageInfo<ByEvaluate>(byEvaluateService.findByCondition(byEvaluate));
         
    }

     /**
     * 评价表导出
     * @param request
     * @param byEvaluate
     * @return
     */
    @GetMapping("/exports")
    public List<ByEvaluate> queryForByEvaluateexports(HttpServletRequest request, ByEvaluate 
 byEvaluate){
        return  byEvaluateService.findByCondition(byEvaluate);
    }


    /**
     * 评价表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByEvaluate queryForByEvaluateById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byEvaluateService.queryByEvaluateById(id);
    }

    /**
     * 评价表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByEvaluate(HttpServletRequest request,@PathVariable("id") Object id){
        byEvaluateService.removeByEvaluate(id);
    }

    /**
     * 评价回复
     * @param request
     * @param byEvaluate
     * @return
     */
    @PutMapping("/replyEval")
    public void replyEval(HttpServletRequest request,@RequestBody ByEvaluate byEvaluate){
        InputValidator.checkEmpty(byEvaluate.getReturnDesc(),"回复内容");
        byEvaluateService.replyEval(byEvaluate);
    }

    /**
     * 屏蔽/取消屏蔽
     * @param request
     * @param byEvaluate
     */
    @PutMapping("/shieldEval")
    public void shieldEval(HttpServletRequest request,@RequestBody ByEvaluate byEvaluate){
        byEvaluateService.modifyByEvaluate(byEvaluate);
    }

}
