package com.wmeimob.fastboot.baoyan.controller.order;

import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/11
 */
@RequestMapping("orderLog")
@RestController
public class ByOrderLogController {

    @Resource
    private ByOrderLogService orderLogService;

    @GetMapping("/queryOrderLog/{orderNo}")
    public List<ByOrderLog> queryOrderLog(@PathVariable String orderNo){
        List<ByOrderLog> list = orderLogService.queryOrderLog(orderNo);
        return list;
    }

}
