package com.wmeimob.fastboot.baoyan.controller.store;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.service.BaseStoreService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName BaseStoreController
 * @Description 【门店】控制器
 * <AUTHOR>
 * @Date 2024-04-03
 * @Version 1.0
 **/
@RestController
@RequestMapping("basestore")
@Slf4j
public class BaseStoreController {

    @Resource
    private BaseStoreService baseStoreService;


    /**
     * 门店分页查询
     */
    @GetMapping("/")
    public PageInfo queryForBaseStore(HttpServletRequest request, BaseStore baseStore) {
        PageContext.startPage();
        return new PageInfo<BaseStore>(baseStoreService.findByCondition(baseStore));

    }


    /**
     * 门店列表
     * @param baseStore 门店信息
     * @return 门店列表
     */
    @GetMapping("/storeList")
    public List<BaseStore> queryStoreList(BaseStore baseStore) {
        return baseStoreService.findByCondition(baseStore);
    }

    /**
     * 门店查询-<通过id查询>
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseStore queryForBaseStoreById(HttpServletRequest request, @PathVariable("id") Object id) {
        return baseStoreService.queryBaseStoreById(id);
    }


    /**
     * 门店添加
     *
     * @return
     */
    @PostMapping("/")
    public void insertForBaseStore(HttpServletRequest request, @RequestBody BaseStore baseStore) {
        if (StringUtils.isBlank(baseStore.getName())) {
            throw new CustomException("名字不能为空");
        }
        if (StringUtils.isBlank(baseStore.getMobile())) {
            throw new CustomException("联系方式不能为空");
        }
        if (StringUtils.isBlank(baseStore.getAddress())) {
            throw new CustomException("地址不能为空");
        }

        baseStoreService.addBaseStore(baseStore);
    }


    /**
     * 门店修改
     *
     * @param request
     * @param baseStore
     * @return
     */
    @PutMapping("/")
    public void updateForBaseStore(HttpServletRequest request, @RequestBody BaseStore baseStore) {
        baseStoreService.modifyBaseStore(baseStore);
    }

    /**
     * 门店删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseStore(HttpServletRequest request, @PathVariable("id") Object id) {
        baseStoreService.removeBaseStore(id);
    }

    /**
    * @Description 查询所有门店
    * <AUTHOR>
    * @Date        2019-10-31 14:23
    * @Version    1.0
    */
    @GetMapping("show")
    public List<BaseStore> show(Integer writeOffId){
        return baseStoreService.show(writeOffId);
    }
    /**
    * @Description 根据门店查看员工
    * <AUTHOR>
    * @Date        2019-10-31 14:26
    * @Version    1.0
    */
    @GetMapping("showUser")
    public List<ByStoreStaff> showUser(Integer id){
        return baseStoreService.showUser(id);
    }

    @GetMapping("/findStoreByTcId")
    public List<BaseStore> findStoreByTcId(Integer tcId){
        return baseStoreService.findStoreByTcId(tcId);
    }

    /**
     * 生成渠道（门店）二维码
     * @param id
     * @return
     */
    @GetMapping("/generateQrCode")
    public R generateQrCode(Integer id){
        return R.success(baseStoreService.generateQrCode(id));
    }
}
