package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByIntegralLogMapper;
import com.wmeimob.fastboot.baoyan.service.ByIntegralLogService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByIntegralLogServiceImpl
 * @Description  积分明细记录表 服务类实现
 * <AUTHOR>
 * @Date Fri Jul 05 15:35:59 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByIntegralLogServiceImpl implements ByIntegralLogService {

    @Resource
    private ByIntegralLogMapper byIntegralLogMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;


    @Override
    public List<ByIntegralLog> findByCondition(ByIntegralLog byIntegralLog) {
        List<ByIntegralLog> byIntegralLogs =byIntegralLogMapper.findByCondition(byIntegralLog);
        return byIntegralLogs;
    }

    @Override
    public ByIntegralLog queryByIntegralLogById(Object id) {
        return byIntegralLogMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByIntegralLog(ByIntegralLog byIntegralLog) {
	  byIntegralLog.setGmtCreate(new Date());
        byIntegralLogMapper.insertSelective(byIntegralLog);
    }

    @Override
    public void removeByIntegralLog(Object id) {
	  ByIntegralLog byIntegralLog = new ByIntegralLog();
	  byIntegralLog.setId(Integer.parseInt(id.toString()));
        byIntegralLogMapper.updateByPrimaryKeySelective(byIntegralLog);
    }

    @Override
    public void modifyByIntegralLog(ByIntegralLog byIntegralLog) {
		byIntegralLog.setGmtModified(new Date());
        byIntegralLogMapper.updateByPrimaryKeySelective(byIntegralLog);
    }

}
