package com.wmeimob.fastboot.baoyan.service;

import com.alibaba.fastjson.JSONArray;
import com.wmeimob.fastboot.baoyan.entity.BaseBannerType;
import com.wmeimob.fastboot.baoyan.entity.SysUserBase;
import com.wmeimob.fastboot.baoyan.mapper.BaseBannerTypeMapper;
import com.wmeimob.fastboot.baoyan.mapper.SysUserBaseMapper;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.management.common.entity.SysRole;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.management.common.handler.AuthenticateHandler;
import com.wmeimob.fastboot.management.common.mapper.SysRoleMapper;
import com.wmeimob.fastboot.management.common.mapper.SysUserMapper;
import com.wmeimob.fastboot.management.common.tools.JSONArrayTools;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName BaseBannerTypeServiceImpl
 * @Description  Banner跳转类型表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class BaseBannerTypeServiceImpl implements BaseBannerTypeService {

    @Resource
    private BaseBannerTypeMapper baseBannerTypeMapper;


    @Override
    public List<BaseBannerType> findByCondition(BaseBannerType baseBannerType) {
        Example example = new Example(BaseBannerType.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(baseBannerType.getId())){
            criteria.andEqualTo("id",baseBannerType.getId());
	  }
	  if(!StringUtils.isEmpty(baseBannerType.getJumpTypeName())){
            criteria.andLike("jumpTypeName",StringUtils.fullFuzzy(baseBannerType.getJumpTypeName()));
	  }
        example.orderBy("id").desc();
        List<BaseBannerType> baseBannerTypes = baseBannerTypeMapper.selectByExample(example);
        return baseBannerTypes;
    }

    @Override
    public BaseBannerType queryBaseBannerTypeById(Object id) {
        return baseBannerTypeMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addBaseBannerType(BaseBannerType baseBannerType) {
        baseBannerTypeMapper.insertSelective(baseBannerType);
    }

    @Override
    public void removeBaseBannerType(Object id) {
	  BaseBannerType baseBannerType = new BaseBannerType();
	  baseBannerType.setId(Integer.parseInt(id.toString()));
        baseBannerTypeMapper.updateByPrimaryKeySelective(baseBannerType);
    }

    @Override
    public void modifyBaseBannerType(BaseBannerType baseBannerType) {
        baseBannerTypeMapper.updateByPrimaryKeySelective(baseBannerType);
    }

    @Service
    public static class AuthLoginServiceImpl  implements AuthLoginService {
        @Resource
        private AuthRoleService authRoleService;
        @Resource
        private UserDetailsService userDetailsService;
        @Resource
        private SysUserBaseMapper sysUserBaseMapper;

        public SysUserBase login(SysUserBase loginUser) {
            SysUser sysUser = (SysUser) this.userDetailsService.loadUserByUsername(loginUser.getUsername());
            if (!sysUser.getPassword().equals(AuthenticateHandler.encodeUserPassowrd(loginUser.getPassword(), sysUser.getUsername()))) {
                throw new BadCredentialsException("用户名或密码错误");
            } else if (!sysUser.getIsEnabled()) {
                throw new DisabledException("用户被禁用");
            } else if (sysUser.getIsLocked()) {
                throw new LockedException("用户被锁定");
            } else {
                List<SysRole> sysRoleList = this.authRoleService.findByIdArr(sysUser.getRolesId().toJavaList(Integer.class));
                if (sysRoleList.isEmpty()) {
                    throw new BadCredentialsException("没有所属的角色");
                } else {
                    SysUserBase sysUserBase = this.sysUserBaseMapper.selectByPrimaryKey(sysUser.getId());
                    sysUserBase.setAuthorities((List) sysRoleList.stream().map(SysRole::new).collect(Collectors.toList()));
                    return sysUserBase;
                }
            }
        }
    }

    @Service
    public static class AuthRoleServiceImpl implements AuthRoleService {
        @Resource
        private SysRoleMapper sysRoleMapper;

        public SysRole findById(Integer id) {
            return (SysRole)this.sysRoleMapper.selectByPrimaryKey(id);
        }

        public List<SysRole> findByIdArr(List<Integer> roleIds) {
            if (roleIds != null && !roleIds.isEmpty()) {
                Example example = new Example(SysRole.class);
                example.createCriteria().andIn("id", roleIds);
                return this.sysRoleMapper.selectByExample(example);
            } else {
                return new ArrayList();
            }
        }

        public List<SysRole> findByCondition(SysRole condition) {
            condition.setAppsId(JSONArrayTools.toIntegerArrayNotNull(condition.getAppsId()));
            Example example = new Example(SysRole.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andLike("roleName", StringUtils.fullFuzzy(condition.getRoleName())).orLike("description", StringUtils.fullFuzzy(condition.getRoleName())).orLike("authority", StringUtils.fullFuzzy(condition.getRoleName()));
            example.and().andCondition("JSON_CONTAINS (apps_id,'" + JSONArray.toJSONString(condition.getAppsId()) + "')").orCondition("JSON_LENGTH (apps_id) = 0");
            if (condition.getInstanceId() != null) {
                example.and().andEqualTo("instanceId", condition.getInstanceId()).orEqualTo("instanceId", 0);
            }

            example.and().andEqualTo("roleType", condition.getRoleType());
            return this.sysRoleMapper.selectByExample(example);
        }

        public SysRole add(SysRole object) {
            JSONArray emptyJSONArray = new JSONArray();
            object.setGmtCreate(new Date());
            object.setPoliciesId(emptyJSONArray);
            object.setAppsId(JSONArrayTools.toIntegerArrayNotNull(object.getAppsId()));

            try {
                this.sysRoleMapper.insertSelective(object);
                return object;
            } catch (Exception var4) {
                throw new CustomException("您不能使用这个识别码");
            }
        }

        public int update(SysRole updateObject, SysRole condition) {
            Assert.notNull(condition.getId(), "id");
            Example example = new Example(SysRole.class);
            example.createCriteria().andCondition("JSON_CONTAINS (apps_id,'" + JSONArray.toJSONString(condition.getAppsId()) + "')").andEqualTo("instanceId", condition.getInstanceId()).andEqualTo("id", condition.getId());
            updateObject.setGmtModified(new Date());
            updateObject.setAppsId(JSONArrayTools.toIntegerArray(updateObject.getAppsId()));
            updateObject.setPoliciesId(JSONArrayTools.toIntegerArray(updateObject.getPoliciesId()));
            return this.sysRoleMapper.updateByExampleSelective(updateObject, example);
        }

        public int delete(Integer id, SysRole condition) {
            Assert.notNull(id, "id");
            Example example = new Example(SysRole.class);
            example.createCriteria().andEqualTo("instanceId", condition.getInstanceId()).andCondition("JSON_CONTAINS (apps_id,'" + JSONArray.toJSONString(condition.getAppsId()) + "')").andEqualTo("id", id);
            return this.sysRoleMapper.deleteByExample(example);
        }
    }
}
