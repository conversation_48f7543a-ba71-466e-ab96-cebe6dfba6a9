package com.wmeimob.fastboot.baoyan.controller.cust;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.baoyan.entity.ByIntegralLog;
import com.wmeimob.fastboot.baoyan.service.ByIntegralLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * 【积分明细记录表】控制器
 * <AUTHOR>
 * @Date 2024-06-22
 * @version 1.0
 **/
@RestController
@RequestMapping("byintegrallog")
@Slf4j
public class ByIntegralLogController {

    @Resource
    private ByIntegralLogService byIntegralLogService;




    /**
     * 积分明细记录表分页查询
     * @param request
     * @param byIntegralLog
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByIntegralLog(HttpServletRequest request, ByIntegralLog byIntegralLog){
        log.info("查询 入参 =================> {}",byIntegralLog);
        PageContext.startPage();
        return new PageInfo<ByIntegralLog>(byIntegralLogService.findByCondition(byIntegralLog));
         
    }

     /**
     * 积分明细记录表导出
     * @param request
     * @param byIntegralLog
     * @return
     */
    @GetMapping("/exports")
    public List<ByIntegralLog> queryForByIntegralLogexports(HttpServletRequest request,ByIntegralLog byIntegralLog){
        return byIntegralLogService.findByCondition(byIntegralLog);
    }
    /**
     * 积分明细记录表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByIntegralLog queryForByIntegralLogById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byIntegralLogService.queryByIntegralLogById(id);
    }

    /**
     * 用户 积分查询
     * @param cellPhone 待查询 用户手机号
     */
    @GetMapping("/user/{cellPhone}")
    public PageInfo<ByIntegralLog> queryForUser(@PathVariable("cellPhone") String cellPhone){
        ByIntegralLog byIntegralLog =  new ByIntegralLog();
        byIntegralLog.setSearchName(cellPhone);
        PageContext.startPage();
        return new PageInfo<>(byIntegralLogService.findByCondition(byIntegralLog));
    }

    /**
     * 积分明细记录表添加
     * @param request
     * @param byIntegralLog
     * @return
     */
    @PostMapping("/")
    public void insertForByIntegralLog(HttpServletRequest request,@RequestBody ByIntegralLog byIntegralLog){
            byIntegralLogService.addByIntegralLog(byIntegralLog);    
    }


    /**
     * 积分明细记录表修改
     * @param request
     * @param byIntegralLog
     * @return
     */
    @PutMapping("/")
    public void updateForByIntegralLog(HttpServletRequest request,@RequestBody ByIntegralLog byIntegralLog){
            byIntegralLogService.modifyByIntegralLog(byIntegralLog);  
    }

    /**
     * 积分明细记录表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByIntegralLog(HttpServletRequest request,@PathVariable("id") Object id){
            byIntegralLogService.removeByIntegralLog(id);
    }
}
