package com.wmeimob.fastboot.baoyan.tool;

import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeMapper;
import com.wmeimob.fastboot.baoyan.tool.impl.SchedulerImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-26 09:29
 * @Version 1.0
 */
@Component
@EnableScheduling
@Slf4j
public class Scheduler {

    @Autowired
    private SchedulerImpl scheduler;

    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;

    /**
     * @Description 订单关闭（XXXX小时自动关闭）
     * <AUTHOR>
     * @Date 2019-08-26 9:32
     * @Version 1.0
     */
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void cloneOrder() {
        log.info("查看未支付的订单开始" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.cloneOrder();
        log.info("查看未支付的订单结束" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }


    /**
     * 关闭超过两个小时没有付款的订单
     * 关闭超时的淘潮玩订单
     */
    @Scheduled(cron = "0 55 0/2 * * ? ")
    public void closeTcOrder(){
        log.info("查看超时未支付的淘潮玩订单开始" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.closeTcOrder();
        log.info("查看超时未支付的淘潮玩订单结束" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description 前一天推送
     * <AUTHOR>
     * @Date 2019-08-26 10:37
     * @Version 1.0
     */
    @Scheduled(cron = "0 0 0 1/1 * ? ")
    public void sendMsg() {
        log.info("查看未订单开始" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.sendMsg();
        log.info("查看未订单结束" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description 拼团机器人
     * <AUTHOR>
     * @Date 2019-08-26 11:43
     * @Version 1.0
     */
    @Scheduled(cron = "0 0/10 * * * ? ")
    public void payTeam() {
        log.info("查看未拼团开始" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.payTeam();
        log.info("查看未拼团结束" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description 扫描过期优惠券
     * <AUTHOR>
     * @Date 2019-08-26 11:43
     * @Version 1.0
     * 凌晨两点触发
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void checkCouponExpire() {
        log.info("扫描过期优惠券" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.checkCouponExpire();
        log.info("扫描过期优惠券" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description 扫描过期
     * <AUTHOR>
     * @Date 2019-08-26 11:43
     * @Version 1.0
     * 凌晨两点触发   0 0 2 * * ?
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void checkWriteOffCodeExpire() {
        log.info("扫描过期核销码" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.checkWriteOffCodeExpire();
        log.info("扫描过期核销码" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description check 产品核销期限过期
     * <AUTHOR>
     * @Date 2019年11月14日11:23:28
     * @Version 1.0
     * 0点触发
     */
    @Scheduled(cron = "50 * * * * ?")
    public void checkWriteOffCodeExpires() {
        log.info("扫描过期核销码数量: " + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        List<WriteOffCode> writeOffCodes = writeOffCodeMapper.checkWriteOffCodeExpire();
        log.info("过期核销码数量:" + writeOffCodes.size());
        log.info("扫描过期核销码数量" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }


    /**
     * @Description 优惠券过期提醒
     * <AUTHOR>
     * @Date 2019-08-26 11:43
     * @Version 1.0
     * 10点触发
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void couponRemindMsg() {
        log.info("扫描优惠券过期提醒" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.couponRemindMsg();
        log.info("扫描优惠券过期提醒" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

    /**
     * @Description check 产品核销期限过期
     * <AUTHOR>
     * @Date 2019年11月14日11:23:28
     * @Version 1.0
     * 0点触发
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void checkPrdWrioffEffective() {
        log.info("扫描产品核销期限过期提醒" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
        scheduler.checkPrdWrioffEffective();
        log.info("扫描产品核销期限过期提醒" + DateUtils.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"));
    }

}
