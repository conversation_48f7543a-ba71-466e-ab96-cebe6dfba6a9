package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByClassifyRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.service.ByClassifyRecommendService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (ByClassifyRecommend)表控制层
 *
 * <AUTHOR>
 * @since 2021-08-08 16:34:53
 */
@RestController
@RequestMapping("/byClassifyRecommend")
@Slf4j
public class ByClassifyRecommendController {
    /**
     * 服务对象
     */
    @Resource(name = "byClassifyRecommendService")
    private ByClassifyRecommendService byClassifyRecommendService;

    @GetMapping("/{id}")
    public ByClassifyRecommend selectOne(@PathVariable("id") Integer id) {
        log.info("get  => selectOne [入参]============={}",id);
        return this.byClassifyRecommendService.queryById(id);
    }

    @GetMapping("/")
    public PageInfo<? extends ByClassifyRecommend> queryAll(ByClassifyRecommend queryObject) {
        PageContext.startPage();
        log.info("get  => queryAll [入参]============={}",queryObject);
        return new PageInfo<>(this.byClassifyRecommendService.queryAll(queryObject));
    }

    @PutMapping("/")
    public Boolean insertObj(@RequestBody ByClassifyRecommend object){
        log.info("put  => insertObj [入参]============={}",object);
        return this.byClassifyRecommendService.insert(object);
    }

    @PostMapping("/")
    public Boolean updateObj (@RequestBody ByClassifyRecommend object)
    {
        log.info("post  => updateObj [入参]============={}",object);
        return this.byClassifyRecommendService.update(object);
    }
    @DeleteMapping("/{id}")
    public Boolean deleteById (@PathVariable("id") Integer id)
    {
        log.info("del  => deleteById [入参]============={}",id);
        return this.byClassifyRecommendService.deleteById(id);
    }

    @PostMapping("/onAndOffShelves")
    public Boolean onAndOffShelves (@RequestBody ByClassifyRecommend updateObject)
    {
        log.info("post => onAndOffShelves [入参]============={}",updateObject);
        return byClassifyRecommendService.onAndOffShelves(updateObject);
    }


}