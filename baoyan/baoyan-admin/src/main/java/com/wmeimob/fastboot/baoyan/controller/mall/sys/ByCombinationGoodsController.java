package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods;
import com.wmeimob.fastboot.baoyan.service.ByCombinationGoodsService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 商品规格管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("ByCombinationGoods")
@Slf4j
public class ByCombinationGoodsController {
    @Resource
    ByCombinationGoodsService byCombinationGoodsService;

    /**
     * 规格列表
     * @param request
     * @param byCombinationGoods
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByGoodsInfo(HttpServletRequest request, ByCombinationGoods byCombinationGoods){
        PageContext.startPage();
        return new PageInfo<ByCombinationGoods>(byCombinationGoodsService.findByCondition(byCombinationGoods));
    }

    /**
     * 编辑
     */
    @GetMapping("/{id}")
    public ByCombinationGoods select(@PathVariable("id") Integer id){
        return byCombinationGoodsService.select(id);
    }

    /**
     * 新增/修改
     */
    @PostMapping("/")
    public void insertOne(@RequestBody ByCombinationGoods byCombinationGoods){
        byCombinationGoodsService.insertOne(byCombinationGoods);
    }

    /**
     * 修改
     */
    @PutMapping("/")
    public void UpdateById(@RequestBody ByCombinationGoods byCombinationGoods){
        byCombinationGoodsService.UpdateById(byCombinationGoods);
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    public void delete(@PathVariable("id") Integer id){
        byCombinationGoodsService.deleteById(id);
    }

}
