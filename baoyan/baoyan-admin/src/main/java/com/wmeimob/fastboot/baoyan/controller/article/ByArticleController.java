package com.wmeimob.fastboot.baoyan.controller.article;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.vo.GoodsVo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByArticle;
import com.wmeimob.fastboot.baoyan.service.ByArticleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByArticleController
 * @Description 【文章表】控制器
 * <AUTHOR>
 * @Date Tue Jul 09 16:59:24 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byarticle")
@Slf4j
public class ByArticleController {

    @Resource
    private ByArticleService byArticleService;




    /**
     * 文章表分页查询
     * @param request
     * @param byArticle
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByArticle(HttpServletRequest request, ByArticle byArticle){
        PageContext.startPage();
        return new PageInfo<ByArticle>(byArticleService.findByCondition(byArticle));
         
    }

     /**
     * 文章表导出
     * @param request
     * @param byArticle
     * @return
     */
    @GetMapping("/exports")
    public List<ByArticle> queryForByArticleexports(HttpServletRequest request, ByArticle 
 byArticle){
        return  byArticleService.findByCondition(byArticle);
    }


    /**
     * 文章表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByArticle queryForByArticleById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byArticleService.queryByArticleById(id);
    }


    /**
     * 文章表添加
     * @param request
     * @param byArticle
     * @return
     */
    @PostMapping("/")
    public void insertForByArticle(HttpServletRequest request,@RequestBody ByArticle byArticle){
            byArticleService.addByArticle(byArticle);    
    }


    /**
     * 文章表修改
     * @param request
     * @param byArticle
     * @return
     */
    @PutMapping("/")
    public void updateForByArticle(HttpServletRequest request,@RequestBody ByArticle byArticle){
            byArticleService.modifyByArticle(byArticle);  
    }

    /**
     * 文章表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByArticle(HttpServletRequest request,@PathVariable("id") Integer id){
            byArticleService.deleteByArticle(id);
    }

    /**
     * 商品选择返回
     * @return
     */
    @GetMapping("/goods")
    public List<GoodsVo> getGoods(@RequestParam(required = false,value = "name") String name){
        return byArticleService.getGoods(name);
    }
}
