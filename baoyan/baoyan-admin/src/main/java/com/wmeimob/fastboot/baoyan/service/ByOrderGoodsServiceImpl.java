package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.mapper.ByOrderGoodsMapper;
import com.wmeimob.fastboot.baoyan.service.ByOrderGoodsService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByOrderGoodsServiceImpl
 * @Description  普通订单商品表 服务类实现
 * <AUTHOR>
 * @Date Tue Aug 06 17:02:57 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByOrderGoodsServiceImpl implements ByOrderGoodsService {

    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;


    @Override
    public List<ByOrderGoods> findByCondition(ByOrderGoods byOrderGoods) {
        Example example = new Example(ByOrderGoods.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byOrderGoods.getId())){
            criteria.andEqualTo("id",byOrderGoods.getId());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getOrderId())){
            criteria.andEqualTo("orderId",byOrderGoods.getOrderId());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getOrderNo())){
            criteria.andLike("orderNo",StringUtils.fullFuzzy(byOrderGoods.getOrderNo()));
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsId())){
            criteria.andEqualTo("goodsId",byOrderGoods.getGoodsId());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsImg())){
            criteria.andLike("goodsImg",StringUtils.fullFuzzy(byOrderGoods.getGoodsImg()));
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsName())){
            criteria.andLike("goodsName",StringUtils.fullFuzzy(byOrderGoods.getGoodsName()));
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsNo())){
            criteria.andLike("goodsNo",StringUtils.fullFuzzy(byOrderGoods.getGoodsNo()));
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getFirstClass())){
            criteria.andEqualTo("firstClass",byOrderGoods.getFirstClass());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsPrice())){
            criteria.andEqualTo("goodsPrice",byOrderGoods.getGoodsPrice());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGoodsNum())){
            criteria.andEqualTo("goodsNum",byOrderGoods.getGoodsNum());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getCouponPrice())){
            criteria.andEqualTo("couponPrice",byOrderGoods.getCouponPrice());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getIsAfterSale())){
            criteria.andEqualTo("isAfterSale",byOrderGoods.getIsAfterSale());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byOrderGoods.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getGmtUpdate())){
            criteria.andEqualTo("gmtUpdate",byOrderGoods.getGmtUpdate());
	  }
	  if(!StringUtils.isEmpty(byOrderGoods.getOrderStatus())){
            criteria.andEqualTo("orderStatus",byOrderGoods.getOrderStatus());
	  }
        example.orderBy("id").desc();
        List<ByOrderGoods> byOrderGoodss = byOrderGoodsMapper.selectByExample(example);
        return byOrderGoodss;
    }

    @Override
    public ByOrderGoods queryByOrderGoodsById(Object id) {
        return byOrderGoodsMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByOrderGoods(ByOrderGoods byOrderGoods) {
	  byOrderGoods.setGmtCreate(new Date());
        byOrderGoodsMapper.insertSelective(byOrderGoods);
    }

    @Override
    public void removeByOrderGoods(Object id) {
	  ByOrderGoods byOrderGoods = new ByOrderGoods();
	  byOrderGoods.setId(Integer.parseInt(id.toString()));
        byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
    }

    @Override
    public void modifyByOrderGoods(ByOrderGoods byOrderGoods) {
        byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
    }

}
