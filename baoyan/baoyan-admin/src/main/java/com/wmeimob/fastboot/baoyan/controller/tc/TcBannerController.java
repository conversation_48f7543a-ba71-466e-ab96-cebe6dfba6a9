package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcBanner;
import com.wmeimob.fastboot.baoyan.service.TcBannerService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("tc/tcBanner")
@Slf4j
public class TcBannerController {
    @Resource
    private TcBannerService tcBannerService;
    /**
     * Banner分页查询
     * @param request
     * @param tcBanner
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseBanner(HttpServletRequest request, TcBanner tcBanner){
        PageContext.startPage();
        return new PageInfo<TcBanner>(tcBannerService.findByCondition(tcBanner));
    }
    /**
     * Banner导出
     * @param request
     * @param tcBanner
     * @return
     */
    @GetMapping("/exports")
    public List<TcBanner> queryForBaseBannerexports(HttpServletRequest request, TcBanner tcBanner){
        return  tcBannerService.findByCondition(tcBanner);
    }
    /**
     * Banner查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public TcBanner queryForBaseBannerById(HttpServletRequest request, @PathVariable("id") Object id){
        return  tcBannerService.queryTcBannerById(id);
    }
    /**
     * Banner添加
     * @param request
     * @param
     * @return
     */
    @PostMapping("/")
    public void insertForBaseBanner(HttpServletRequest request, @RequestBody TcBanner tcBanner){
        tcBannerService.addTcBanner(tcBanner);
    }

    /**
     * Banner修改
     * @param request
     * @param tcBanner
     * @return
     */
    @PutMapping("/")
    public void updateForBaseBanner(HttpServletRequest request,@RequestBody TcBanner tcBanner){
        tcBannerService.modifyTcBanner(tcBanner);
    }

    /**
     * Banner删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseBanner(HttpServletRequest request,@PathVariable("id") Object id){
        tcBannerService.removeTcBanner(id);
    }

    /**
     *上下架
     * @param tcBanner
     * @return
     */
    @PutMapping("/openClose")
    public void openClose(@RequestBody TcBanner tcBanner){
        if(tcBanner.getId() == null){
            throw new CustomException("参数错误");
        }
        tcBannerService.modifyTcBanner(tcBanner);
    }
}
