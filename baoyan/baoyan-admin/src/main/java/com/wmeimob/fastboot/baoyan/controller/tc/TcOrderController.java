package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderGoodsService;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/2
 */
@RequestMapping("/tc/order")
@RestController
public class TcOrderController {

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private TcOrderService tcOrderService;

    /**
     * 查看单个订单
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public TcOrder getOne(@PathVariable Integer id){
        return tcOrderMapper.findById(id);
    }

    /**
     * 查看单个订单
     * @param id
     * @return
     */
    @GetMapping("/findOne")
    public TcOrder findOne(Integer id, String orderNo){

        if ( id==null && orderNo==null ){
            throw new CustomException("参数为空");
        }

        if ( id!=null ){
            return tcOrderMapper.findById(id);
        }else{
            TcOrder dbOrder = tcOrderMapper.findByOrderNo(orderNo);
            return tcOrderMapper.findById(dbOrder.getId());
        }
    }

    /**
     * 分页查看订单列表
     * @param tcOrder
     * @return
     */
    @GetMapping
    public PageInfo<TcOrder> list(TcOrder tcOrder){
        PageContext.startPage();

        return new PageInfo<TcOrder>(tcOrderMapper.selectByCondition(tcOrder));
    }

    /**
     * 获得导出的excel的数据
     * @param tcOrder
     * @return
     */
    @GetMapping("/exports")
    public List<TcOrder> exports(TcOrder tcOrder){

        return tcOrderMapper.selectByCondition(tcOrder);
    }

    /**
     * 发货
     * @param tcOrder
     * @return
     */
    @PutMapping("/deliver")
    public boolean deliver(@RequestBody TcOrder tcOrder){
        return tcOrderService.deliver(tcOrder);
    }

}
