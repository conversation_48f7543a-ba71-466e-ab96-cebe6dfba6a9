package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.wmeimob.fastboot.baoyan.entity.BaseAdvertising;
import com.wmeimob.fastboot.baoyan.qo.BaseAdvertisingQo;
import com.wmeimob.fastboot.baoyan.service.BaseAdvertisingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 广告图片控制
 * <AUTHOR>
 */
@RestController
@RequestMapping("base_advertising")
@Slf4j
public class BaseAdvertisingController {

    @Resource
    BaseAdvertisingService baseAdvertisingService;

    /**
     * 广告表单查询
     */
    @GetMapping("/")
    public BaseAdvertising select(){
        return baseAdvertisingService.select();
    }
    /**
     * 广告更新
     */
    @PostMapping("/")
    public void update(@RequestBody BaseAdvertisingQo qo){
        baseAdvertisingService.update(qo);
    }

}
