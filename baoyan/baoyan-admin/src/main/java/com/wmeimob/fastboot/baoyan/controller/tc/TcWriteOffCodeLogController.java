package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcOrderAfter;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeLogMapper;
import com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeMapper;
import com.wmeimob.fastboot.baoyan.service.TcWriteOffCodeService;
import com.wmeimob.fastboot.core.context.PageContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/15
 */
@RequestMapping("/tc/writeOffCodeLog")
@RestController
public class TcWriteOffCodeLogController {

    @Resource
    private TcWriteOffCodeLogMapper tcWriteOffCodeLogMapper;

    @GetMapping
    public PageInfo<TcWriteOffCodeLog> list(TcWriteOffCodeLog writeOffCodeLog){
        PageContext.startPage();
        List<TcWriteOffCodeLog> writeOffCodeLogs = tcWriteOffCodeLogMapper.findByCondition(writeOffCodeLog);
        return new PageInfo<>(writeOffCodeLogs);
    }
    /**
     * 核销记录表导出
     * @param request
     * @param writeOffCodeLog
     * @return
     */
    @GetMapping("/exports")
    public List<TcWriteOffCodeLog> queryForByOrdersexports(HttpServletRequest request, TcWriteOffCodeLog writeOffCodeLog){
        return  tcWriteOffCodeLogMapper.findByCondition(writeOffCodeLog);
    }
}
