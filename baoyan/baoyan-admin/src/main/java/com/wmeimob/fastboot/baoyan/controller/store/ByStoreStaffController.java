package com.wmeimob.fastboot.baoyan.controller.store;

import com.github.pagehelper.PageInfo;
import com.qiniu.util.StringUtils;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.service.ByStoreStaffService;
import com.wmeimob.fastboot.util.InputValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByStoreStaffController
 * @Description 【门店员工】控制器
 * <AUTHOR>
 * @Date Thu Jul 11 09:48:59 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bystorestaff")
@Slf4j
public class ByStoreStaffController {

    @Resource
    private ByStoreStaffService byStoreStaffService;


    /**
     * 门店员工分页查询
     *
     * @param request
     * @param byStoreStaff
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByStoreStaff(HttpServletRequest request, ByStoreStaff byStoreStaff) {
        if (null == byStoreStaff.getStoreId()) {
            throw new CustomException("请选择门店操作");
        }

        PageContext.startPage();
        return new PageInfo<ByStoreStaff>(byStoreStaffService.findByCondition(byStoreStaff));

    }

    /**
     * 门店员工查询-<通过id查询>
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByStoreStaff queryForByStoreStaffById(HttpServletRequest request, @PathVariable("id") Object id) {
        return byStoreStaffService.queryByStoreStaffById(id);
    }


    /**
     * 门店员工添加
     *
     * @param request
     * @param byStoreStaff
     * @return
     */
    @PostMapping("/")
    public void insertForByStoreStaff(HttpServletRequest request, @RequestBody ByStoreStaff byStoreStaff) {
        if (StringUtils.isNullOrEmpty(byStoreStaff.getStaffName())) {
            throw new CustomException("请出入员工姓名");
        }
        if (StringUtils.isNullOrEmpty(byStoreStaff.getStaffPhone())) {
            throw new CustomException("请出入员工电话");
        }
        if (null == byStoreStaff.getStoreId()) {
            throw new CustomException("请选择门店操作");
        }


        byStoreStaffService.addByStoreStaff(byStoreStaff);
    }


    /**
     * 门店员工修改
     *
     * @param request
     * @param byStoreStaff
     * @return
     */
    @PutMapping("/")
    public void updateForByStoreStaff(HttpServletRequest request, @RequestBody ByStoreStaff byStoreStaff) {
        byStoreStaffService.modifyByStoreStaff(byStoreStaff);
    }

    /**
     * 门店员工删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByStoreStaff(HttpServletRequest request, @PathVariable("id") Object id) {
        byStoreStaffService.removeByStoreStaff(id);
    }
}
