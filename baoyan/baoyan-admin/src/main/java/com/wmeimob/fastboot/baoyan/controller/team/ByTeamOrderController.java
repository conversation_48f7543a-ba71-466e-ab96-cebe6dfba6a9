package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.vo.TeamOrderVo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.baoyan.service.ByTeamOrderService;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByTeamOrderController
 * @Description 【拼团订单表】控制器
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byteamorder")
@Slf4j
public class ByTeamOrderController {

    @Resource
    private ByTeamOrderService byTeamOrderService;

    /**
     * 分页查询
     * @param vo
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForSfTeamOrder(TeamOrderVo vo){
        PageContext.startPage();
        return new PageInfo<TeamOrderVo>(byTeamOrderService.queryTeamList(vo));
    }

    /**
     * 导出
     * @param teamOrder
     * @return
     */
    @GetMapping("/exports")
    public List<TeamOrderVo> queryForSfTeamOrderexports(TeamOrderVo teamOrder){
        return  byTeamOrderService.queryTeamList(teamOrder);
    }

    /**
     * 查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public TeamOrderVo queryForSfTeamOrderById(HttpServletRequest request, @PathVariable("id") Integer id){
        return byTeamOrderService.queryTeamOrderById(id);
    }

    /**
     * 拼团管理 拼团订单
     */
    @GetMapping("/info/{id}")
    public List<TeamOrderVo> getTeamOrderInfo( @PathVariable("id")Integer id){
        return byTeamOrderService.getTeamOrderInfo(id);
    }


}
