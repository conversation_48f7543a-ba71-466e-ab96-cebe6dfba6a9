package com.wmeimob.fastboot.baoyan.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.sun.org.apache.bcel.internal.generic.IF_ACMPEQ;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.entity.TcOrderParent;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.OrderResVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.model.refund.RefundApplyRequest;
import me.hao0.wepay.model.refund.RefundApplyResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName ByOrdersServiceImpl
 * @Description 商品订单表 服务类实现
 * <AUTHOR>
 * @Date Mon Jul 15 15:41:21 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByOrdersServiceImpl implements ByOrdersService {

    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Resource
    private WepayService wepayService;
    @Resource
    private WechatProperties wechatProperties;
    @Resource
    private ByOrderLogService orderLogService;
    @Resource
    private ByOrderAfterService afterService;
    @Resource
    private TcOrderParentMapper tcOrderParentMapper;

    @Override
    public List<ByOrders> findByCondition(ByOrders byOrders) {
        List<ByOrders> byOrderss = byOrdersMapper.findByCondition(byOrders);
        return byOrderss;
    }

    @Override
    public OrderResVO queryByOrdersById(Integer id) {
        log.info("===========>执行dealResVo =======>");
        return dealResVo(id);
    }

    private OrderResVO dealResVo(Integer id) {
        log.info("===========>执行dealResVo =======>开始");
        OrderResVO vo = new OrderResVO();
        ByOrders orders = new ByOrders();
        orders.setId(id);
        List<ByOrders> byCondition = byOrdersMapper.findByCondition(orders);
        if (null != byCondition && byCondition.size() > 0) {
            for (ByOrders order : byCondition) {
                vo.setOrderStatus(order.getOrderStatus());
                vo.setOrderNo(order.getOrderNo());
                vo.setGoodsType(order.getGoodsType());
                vo.setOrderTime(order.getOrderTime());
                vo.setPayTime(order.getPayTime());
                vo.setPayType(order.getPayType());
                vo.setPayFlowNo( order.getPayFlowNo() );
                vo.setCustUserName( order.getCustUserName() );
                vo.setMobile( order.getMobile() );
                vo.setRemark( order.getRemark() );
                vo.setIsChannel(order.getIsChannel());
                vo.setChannelId( order.getChannelId());
                vo.setOrderAmount(order.getOrderAmount());
                vo.setActualAmount(order.getActualAmount());
                List<ByOrderGoods> list = byOrderGoodsMapper.selectByOrderId(order.getId());
                for (ByOrderGoods goods : list) {
                    goods.setAllRefund( afterService.isAllRefund(goods.getId()) );
                }
                vo.setList(list);
            }
        }
        log.info("===========>执行dealResVo =======>结束");
        return vo;
    }


    @Override
    public void addByOrders(ByOrders byOrders) {
        byOrdersMapper.insertSelective(byOrders);
    }

    @Override
    public void removeByOrders(Object id) {
        ByOrders byOrders = new ByOrders();
        byOrders.setId(Integer.parseInt(id.toString()));
        byOrders.setIsDel(true);
        byOrdersMapper.updateByPrimaryKeySelective(byOrders);
    }

    @Override
    public void modifyByOrders(ByOrders byOrders) {
        byOrdersMapper.updateByPrimaryKeySelective(byOrders);
    }

    @Override
    public List<ByOrders> queryOrderStatistics(ByOrders byOrders) {
        dealReqParam(byOrders);
        return byOrdersMapper.queryOrderStatistics(byOrders);
    }

    /**
     * 处理请求参数
     *
     * @param byOrders
     */
    private void dealReqParam(ByOrders byOrders) {
        if (StringUtils.isEmpty(byOrders.getStartTime()) && StringUtils.isEmpty(byOrders.getEndTime())) {
            //默认初始化当前 时间前三十天日期
            String endTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            Calendar now = Calendar.getInstance();
            now.add(Calendar.DAY_OF_MONTH, -30);
            String startTime = new SimpleDateFormat("yyyy-MM-dd").format(now.getTime());
            byOrders.setStartTime(startTime);
            byOrders.setEndTime(endTime);
        }
    }

    @Override
    public List<GoodsStatisticsVO> queryGoodsStatistics(ByOrders byOrders) {
        return byOrdersMapper.queryGoodsStatistics(byOrders);
    }
    @Override
    public List<GoodsStatisticsVO> querySubGoodsStatistics(ByOrders byOrders){
        return byOrdersMapper.querySubGoodsStatistics(byOrders);
    }
    @Override
    public List<GoodsStatisticsVO> queryTicketGoodsStatistics(ByOrders byOrders){
        return byOrdersMapper.queryTicketGoodsStatistics(byOrders);
    }

    @Override
    public Map<String, Object> getFinanceOrderStatisticsSum(ByOrders byOrders) {
        dealReqParam(byOrders);
        return byOrdersMapper.getFinanceOrderStatisticsSum(byOrders);
    }

    @Override
    public Map<String, Object> getFinanceGoodsStatisticsSum(ByOrders byOrders) {
        return byOrdersMapper.getFinanceGoodsStatisticsSum(byOrders);
    }
    @Override
    public Map<String, Object> getFinanceTicketGoodsStatisticsSum(ByOrders byOrders) {
        return byOrdersMapper.getFinanceTicketGoodsStatisticsSum(byOrders);
    }

    @Override
    public void refund(Integer id) {
        ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(id);
        if (byOrders != null && byOrders.getOrderStatus()!=null &&
                (byOrders.getOrderStatus()==2 || byOrders.getOrderStatus() == 3)){

            //修改订单状态
            byOrders.setIsRefund(true);
            byOrders.setOrderStatus(4);

            byOrdersMapper.updateByPrimaryKeySelective(byOrders);

            String refundOrderNo = byOrders.getPayOrderNo();
            BigDecimal totalFee = byOrders.getActualAmount();
            if ( refundOrderNo == null ){
                // 以前的订单， 商家订单号为null， 商家订单号就是自己的订单号
                refundOrderNo = byOrders.getOrderNo();
            }else if ( !Objects.equals(refundOrderNo, byOrders.getOrderNo()) ){
                // 如果订单号 和 商家订单号不一致。代表是混合支付，总金额需要查询 父订单
                TcOrderParent parentOrder = tcOrderParentMapper.findByOrderParentNo(refundOrderNo);
                totalFee = parentOrder.getPayAmount();
            }


            //微信退款
            refundPay(refundOrderNo ,String.valueOf(System.currentTimeMillis()), totalFee, byOrders.getActualAmount());

            //记录订单日志
            orderLogService.addOrderLogAsync(
                    ByOrderLog.builder()
                            .operatorType(2)
                            .orderNo(byOrders.getOrderNo())
                            .logType(OrderLogType.ADMIN_REFUND)
                            .build()
            );

        }

    }

    @Override
    public String refunds() {
        Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
        RefundApplyRequest request = new RefundApplyRequest();
        request.setOutTradeNo("202004837244456965");
        request.setOutRefundNo(String.valueOf(new Date().getTime()));
        BigDecimal totalFee = new BigDecimal(297);
        BigDecimal fee = new BigDecimal(198);
        request.setTotalFee(totalFee.multiply(CommonFinal.BIG_100).intValue());
        request.setRefundFee(fee.multiply(CommonFinal.BIG_100).intValue());
        request.setOpUserId("1");
        RefundApplyResponse apply = wepay.refund().apply(request);
        return JSON.toJSONString(apply);
    }

    private void refundPay(String orderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee) {
        Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
        RefundApplyRequest request = new RefundApplyRequest();
        request.setOutTradeNo(orderNo);
        request.setOutRefundNo(refundNo);
        request.setTotalFee(totalFee.multiply(CommonFinal.BIG_100).intValue());
        request.setRefundFee(refundFee.multiply(CommonFinal.BIG_100).intValue());
        request.setOpUserId("1");
        try {
            wepay.refund().apply(request);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFinanceSubGoodsStatisticsSum(ByOrders byOrders) {
        return byOrdersMapper.getFinanceSubGoodsStatisticsSum(byOrders);
    }

    @Override
    public List<ByOrders> findExportByCondition(ByOrders byOrders) {
        return byOrdersMapper.findExportByCondition(byOrders);
    }

    @Override
    public List<FinanceStatisticsVO> queryFinanceStatistics(ByOrders byOrders) {
        List<FinanceStatisticsVO> list = byOrdersMapper.queryFinanceStatistics(byOrders);
        if (!CollectionUtils.isEmpty(list)) {
            for (FinanceStatisticsVO vo : list) {
                //普通订单
                if (vo.getOrderType().equals(CommonFinal.ZERO)) {
                    //查询detailId 次卡订单
                    ByOrderGoods detailVo = byOrderGoodsMapper.selectByPrimaryKey(vo.getDetailId());
                    //6.22产品需求 可以先判断次卡的适用门店是多个，还是一个；如果是一个则显示核销门店，多个则不显示
                    //次卡 && 门店id 不为空
                    if (Integer.valueOf(detailVo.getProductType()).equals(CommonFinal.TWO) && !StringUtils.isEmpty(vo.getStoreIds())) {
                        List<String> storeIdList = Arrays.asList(vo.getStoreIds().split(""));
                        //是否大于1
                        if (storeIdList.size() == BaoYanConstant.CONSTANT_ONE.intValue()) {
                            FinanceStatisticsVO queryVo = byOrdersMapper.queryWriteStoreAndStaffByWriteId(vo.getWriteOffId());
                            if (null != queryVo) {
                                vo.setWriteOffStaff(queryVo.getWriteOffStaff());
                                vo.setWriteOffStore(queryVo.getWriteOffStore());
                                vo.setWriteOffTime(queryVo.getWriteOffTime());
                            }
                        }else{
                            //多个门店 次卡 核销内容置为空
                            vo.setWriteOffStaff(null);
                            vo.setWriteOffStore(null);
                            vo.setWriteOffTime(null);
                        }
                    }
                }

            }
        }
      /*  if (!CollectionUtils.isEmpty(list)) {
            //处理次卡 商品 均分订单实付金额 6.19 季永杰 让添加 目前产品 不兼容这种情况特殊处理
            for (FinanceStatisticsVO vo : list) {
                //只处理普通订单次卡商品 拼团不管
                if (vo.getOrderType().equals(CommonFinal.ZERO)) {
                    Example goOrderGood = new Example(ByOrderGoods.class);
                    goOrderGood.createCriteria().andEqualTo("orderId", vo.getOrderId());
                    List<ByOrderGoods> byOrderGoods = byOrderGoodsMapper.selectByExample(goOrderGood);
                    if (!CollectionUtils.isEmpty(byOrderGoods)) {
                        ByOrderGoods go = byOrderGoods.get(0);
                        //次卡
                        if (Integer.valueOf(go.getProductType()).equals(CommonFinal.TWO)) {
                            //通过订单 查询 订单表  实付金额= 总金额 -优惠券 -积分金额 商品详情 查询订单类型次卡
                            Example orderExample = new Example(ByOrders.class);
                            orderExample.createCriteria().andEqualTo("orderNo", go.getOrderNo());
                            ByOrders orders = byOrdersMapper.selectOneByExample(orderExample);
                            //实际支付金额
                            BigDecimal acu = orders.getActualAmount();
                            //查询次卡数量
                            BySubCardGoods sub = bySubCardGoodsMapper.selectByPrimaryKey(go.getGoodsId());
                            //通过订单金额/除次卡数量看是否可以整除
                            BigDecimal number = acu.divide(new BigDecimal(sub.getSubCardGoodsNum()), 10, BigDecimal.ROUND_HALF_DOWN);
                            //判断是否能整除
                            if (new BigDecimal(number.intValue()).compareTo(number) == 0) {
                                //整除
                                vo.setActualAmount(number);
                            } else {
                                //没有整除 查询核销记录中当前订单的最后一条 若是最后一条将零头补在最后一条上面
                                Example writeOffExc = new Example(WriteOffCode.class);
                                writeOffExc.createCriteria().andEqualTo("orderNo", go.getOrderNo());
                                writeOffExc.orderBy("id").desc();
                                List<WriteOffCode> writeList = writeOffCodeMapper.selectByExample(writeOffExc);
                                //倒序排序取最近的一条补充 不能整除金额
                                WriteOffCode code = writeList.get(0);
                                BigDecimal avg = acu.divide(new BigDecimal(sub.getSubCardGoodsNum()), 2, BigDecimal.ROUND_DOWN);
                                //判读当前 条是否是最后一条
                                if (code.getId().equals(vo.getWriteOffId())) {
                                    //考虑除不尽的情况 得出多了 多少 补到最后一个上面
                                    //总金额 -（平均金额*数量）=余数
                                    BigDecimal reu = acu.subtract(avg.multiply(new BigDecimal(sub.getSubCardGoodsNum())));
                                    //余数+平均金额
                                    vo.setActualAmount(avg.add(reu));

                                } else {
                                    // 正常保留 两位展示前端
                                    vo.setActualAmount(avg);
                                }

                            }

                        }

                    }
                }
            }

        }*/
        return list;
    }

}
