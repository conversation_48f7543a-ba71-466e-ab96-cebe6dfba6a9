package com.wmeimob.fastboot.baoyan.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.mzlion.easyokhttp.HttpClient;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsStore;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.mapper.BaseStoreMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsStoreMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByStoreStaffMapper;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName BaseStoreServiceImpl
 * @Description 门店 服务类实现
 * <AUTHOR>
 * @Date Wed Jul 10 11:37:09 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseStoreServiceImpl implements BaseStoreService {

    @Resource
    private BaseStoreMapper baseStoreMapper;
    @Resource
    private ByGoodsStoreMapper byGoodsStoreMapper;
    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;

    /**
     * 渠道二维码
     * TODO 还需要修改 URL
     */
    private static final String CODE_URL = "pages/channel/authMember";
    @Resource
    private QrCodeService qrCodeService;

    private final static String Url = "https://apis.map.qq.com/ws/geocoder/v1/?key=VFWBZ-FNZ33-FKG3V-YNT5J-QP5L3-JCF4H&address={address}";

    @Override
    public List<BaseStore> findByCondition(BaseStore baseStore) {
        Example example = new Example(BaseStore.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(baseStore.getMobile())) {
            criteria.andLike("mobile", StringUtils.fullFuzzy(baseStore.getMobile()));
            criteria.orLike("id", StringUtils.fullFuzzy(baseStore.getMobile()));
        }
        if(!StringUtils.isEmpty(baseStore.getName())){
            criteria.andLike("name", StringUtils.fullFuzzy(baseStore.getName()));
        }
        if(ObjectUtil.isNull(baseStore.getStoreType())){
            criteria.andEqualTo("storeType", 0);
        }else{
            criteria.andEqualTo("storeType", baseStore.getStoreType());
        }
        criteria.andEqualTo("deleteStatus", Boolean.FALSE);
        example.orderBy("id").desc();
        return baseStoreMapper.selectByExample(example);
    }

    @Override
    public BaseStore queryBaseStoreById(Object id) {
        return baseStoreMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addBaseStore(BaseStore baseStore) {
        baseStore.setGmtCreate(new Date());
        baseStore = getInt(baseStore);
        baseStoreMapper.insertSelective(baseStore);
    }

    @Override
    public void removeBaseStore(Object id) {

       /* Example example = new Example(ByGoodsStore.class);
        example.createCriteria().andEqualTo("storeId", id);*/
        //List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
        //效验 当前门店是否被 门店引用
        List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectContactGoods(id);
        if (null != byGoodsStores && byGoodsStores.size() > 0) {
            throw new CustomException("当前门店有关联商品,暂不能删除");
        }
        BaseStore baseStore = new BaseStore();
        baseStore.setGmtModified(new Date());
        baseStore.setId(Integer.parseInt(id.toString()));
        baseStore.setDeleteStatus(Boolean.TRUE);
        baseStore.setStatus(Boolean.FALSE);
        baseStoreMapper.updateByPrimaryKeySelective(baseStore);
    }

    @Override
    public void modifyBaseStore(BaseStore baseStore) {
        baseStore.setGmtModified(new Date());
        if (null != baseStore.getAddress()) {
            baseStore = getInt(baseStore);
        }
        baseStoreMapper.updateByPrimaryKeySelective(baseStore);
    }

    @Override
    public List<BaseStore> show(Integer writeOffId) {
        Example store = new Example(BaseStore.class);
        store.createCriteria()
                .andEqualTo("deleteStatus", Boolean.FALSE)
                .andEqualTo("status", Boolean.TRUE);
       return baseStoreMapper.showWriteOffStore(writeOffId);
    }

    @Override
    public List<ByStoreStaff> showUser(Integer id) {
        Example staff = new Example(ByStoreStaff.class);
        staff.createCriteria().andEqualTo("isDel", Boolean.FALSE).andEqualTo("storeId", id);
        return this.byStoreStaffMapper.selectByExample(staff);
    }

    @Override
    public List<BaseStore> findStoreByTcId(Integer tcId) {
        if (tcId!=null){
            return baseStoreMapper.findStoreByTcId(tcId);
        }else{
            return baseStoreMapper.selectAll();
        }
    }

    @Override
    public String generateQrCode(Integer id) {
        BaseStore baseStore = baseStoreMapper.selectByPrimaryKey(id);
        String url = qrCodeService.exportQrCode(null, baseStore.getId(), CODE_URL);
        if (null != url) {
            baseStore.setStoreQrcode(url);
            baseStoreMapper.updateByPrimaryKeySelective(baseStore);
        }
        return url;
    }

    private BaseStore getInt(BaseStore baseStore) {
        /*String request = HttpClient.get(Url.replace("{address}", baseStore.getAddress())).asString();
        JSONObject json = JSONObject.parseObject(request);
        if (json.getInteger("status").equals(0)) {
            JSONObject jsonObject = json.getJSONObject("result").getJSONObject("location");
            Object lng = jsonObject.get("lng");
            Object lat = jsonObject.get("lat");
            baseStore.setLatitude(lat.toString());
            baseStore.setLongitude(lng.toString());
            return baseStore;
        }
        throw new CustomException("解析失败，请确认地址是否正确");*/
        return baseStore;
    }
}
