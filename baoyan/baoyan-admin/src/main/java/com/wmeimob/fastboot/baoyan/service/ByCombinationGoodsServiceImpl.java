package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCombinationGoods;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.ByCombinationGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsInfoMapper;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 商品规格管理
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByCombinationGoodsServiceImpl implements ByCombinationGoodsService{

    @Resource
    ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Resource
    ByGoodsInfoMapper ByGoodsInfoMapper;

    @Override
    public List<ByCombinationGoods> findByCondition(ByCombinationGoods byCombinationGoods) {
        if (StringUtils.isEmpty(byCombinationGoods.getGoodsId())){
            throw new CustomException("请传入商品id");
        }else {
            return byCombinationGoodsMapper.findByCondition(byCombinationGoods);
        }
    }

    @Override
    public ByCombinationGoods select(Integer id) {
        return byCombinationGoodsMapper.selectAndGoods(id);
    }

    @Override
    public void insertOne(ByCombinationGoods byCombinationGoods) {
        //新增
        verification(byCombinationGoods);
        byCombinationGoodsMapper.insertSelective(byCombinationGoods.setGmtCreate(new Date()).setIsDel(0));

    }

    @Override
    public void UpdateById(ByCombinationGoods byCombinationGoods) {
        ByCombinationGoods combinationGoods = byCombinationGoodsMapper.selectByPrimaryKey(byCombinationGoods.getId());
        //修改
        updateVerification(byCombinationGoods,combinationGoods);
        byCombinationGoodsMapper.updateByPrimaryKeySelective(byCombinationGoods.setGmtUpdate(new Date()));
    }

    @Override
    public void deleteById(Integer id) {
        ByCombinationGoods byCombinationGoods = new ByCombinationGoods().setId(id).setIsDel(1);
        byCombinationGoodsMapper.updateByPrimaryKeySelective(byCombinationGoods);
    }



    public void updateVerification(ByCombinationGoods byCombinationGoods,ByCombinationGoods combinationGoods){
        if (byCombinationGoods.getStatus()!=null && !Status.getStatus().contains(byCombinationGoods.getStatus())){
            throw new CustomException("请设置正确上架状态");
        }
        if (byCombinationGoods.getIsCoupon()!=null && !Status.getStatus().contains(byCombinationGoods.getIsCoupon())){
            throw new CustomException("请设置正确使用优惠卷状态");
        }
        if (byCombinationGoods.getIsIntegral()!=null && !Status.getStatus().contains(byCombinationGoods.getIsIntegral())){
            throw new CustomException("请设置正确使用积分状态");
        }
//        if ((byCombinationGoods.getVerificationStart()==null && combinationGoods.getVerificationStart()==null) || (byCombinationGoods.getVerificationEnd()==null && combinationGoods.getVerificationEnd()==null)){
//            throw new CustomException("请设置核销时间");
//        }
//        //上架判断核销码是否 过期
//        if (byCombinationGoods.getStatus() != null && byCombinationGoods.getStatus() == 1) {
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(byCombinationGoods.getVerificationEnd() != null ? byCombinationGoods.getVerificationEnd() : combinationGoods.getVerificationEnd());
//            if (calendar.getTime().getTime() < System.currentTimeMillis()) {
//                throw new CustomException("当前商品不满足上架要求,请效验产品核销时间");
//            }
//        }
    }

    public void verification(ByCombinationGoods byCombinationGoods){
        if (StringUtils.isEmpty(byCombinationGoods.getGoodsId())){
            throw new CustomException("请设置商品id");
        }
        ByGoodsInfo byGoodsInfo = ByGoodsInfoMapper.selectByPrimaryKey(byCombinationGoods.getGoodsId());
        if (StringUtils.isEmpty(byGoodsInfo.getSpecName())){
            throw new CustomException("请设置商品规则名称");
        }
        if (StringUtils.isEmpty(byCombinationGoods.getName())){
            throw new CustomException("请设置规则名称");
        }
        if (byCombinationGoods.getGoodsStock()==null){
            throw new CustomException("请设置库存");
        }
        if (byCombinationGoods.getPrice()==null){
            throw new CustomException("请设置价格");
        }
        if(!byCombinationGoods.getHasVerificationDay()){
            if (byCombinationGoods.getVerificationStart()==null){
                throw new CustomException("请设置核销开始时间");
            }
            if (byCombinationGoods.getVerificationEnd()==null){
                throw new CustomException("请设置核销结束时间");
            }
        }

        if (byCombinationGoods.getStatus()==null || !Status.getStatus().contains(byCombinationGoods.getStatus())){
            throw new CustomException("请设置正确上架状态");
        }
        if (byCombinationGoods.getIsCoupon()!=null && !Status.getStatus().contains(byCombinationGoods.getIsCoupon())){
            throw new CustomException("请设置正确使用优惠卷状态");
        }
        if (byCombinationGoods.getIsIntegral()!=null && !Status.getStatus().contains(byCombinationGoods.getIsIntegral())){
            throw new CustomException("请设置正确使用积分状态");
        }
    }
}
