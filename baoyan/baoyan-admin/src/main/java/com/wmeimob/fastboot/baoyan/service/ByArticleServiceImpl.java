package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.ByArticleService;
import com.wmeimob.fastboot.baoyan.vo.GoodsVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName ByArticleServiceImpl
 * @Description  文章表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 09 16:59:24 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByArticleServiceImpl implements ByArticleService {

    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private BaseBannerMapper baseBannerMapper;
    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;
    @Resource
    private BaseActivityMapper baseActivityMapper;
    @Resource
    private ByGoodsInfoMapper goodsInfoMapper;
    @Resource
    BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    ByPlatformSetMapper byPlatformSetMapper;
    /**
     *删除提示信息
     */
    public static final String ERR_MESSAGE="当前文章 首页推荐中有引用,暂不能删除";

    @Override
    public List<ByArticle> findByCondition(ByArticle byArticle) {
        Example example = new Example(ByArticle.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byArticle.getId())){
            criteria.andEqualTo("id",byArticle.getId());
	  }
	  if(!StringUtils.isEmpty(byArticle.getArticleName())){
            criteria.andLike("articleName",StringUtils.fullFuzzy(byArticle.getArticleName()));
	  }
	  if(!StringUtils.isEmpty(byArticle.getContent())){
            criteria.andEqualTo("content",byArticle.getContent());
	  }
	  if(!StringUtils.isEmpty(byArticle.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byArticle.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byArticle.getGmtModified())){
            criteria.andEqualTo("gmtModified",byArticle.getGmtModified());
	  }
        example.orderBy("id").desc();
        List<ByArticle>  list= byArticleMapper.selectArticleList(byArticle);
        for (ByArticle article : list) {
            if (article.getType() != null && article.getGoodsId() != null){
                if (article.getType().equals(1)){
                    ByGoodsInfo byGoodsInfo = Optional.ofNullable(goodsInfoMapper.selectOne(new ByGoodsInfo().setId(article.getGoodsId()).setIsDel(false))).orElse(new ByGoodsInfo().setGoodsName("已删除"));
                    article.setGoodsName(byGoodsInfo.getGoodsName());
                }
                if (article.getType().equals(2)){
                    BySubCardGoods bySubCardGoods = Optional.ofNullable(bySubCardGoodsMapper.selectOne(new BySubCardGoods().setId(article.getGoodsId())).setIsDel(false)).orElse(new BySubCardGoods().setSubCardGoodsName("已删除"));
                    article.setGoodsName(bySubCardGoods.getSubCardGoodsName());
                }
                if (article.getType().equals(3)){
                    ByTicketGoods byTicketGoods = Optional.ofNullable(byTicketGoodsMapper.selectOne(new ByTicketGoods().setId(article.getGoodsId())).setIsDel(false)).orElse(new ByTicketGoods().setTicketGoodsName("已删除"));
                    article.setGoodsName(byTicketGoods.getTicketGoodsName());
                }
                if (article.getType().equals(4)){
                    ByTeamGoods byTeamGoods = new ByTeamGoods();
                    byTeamGoods.setId(article.getGoodsId());
                    byTeamGoods.setIsDel(false);
                    ByTeamGoods byTeamGoodsElse = new ByTeamGoods();
                    byTeamGoodsElse.setTeamName("已删除");
                    byTeamGoods = Optional.ofNullable(byTeamGoodsMapper.selectOne(byTeamGoods)).orElse(byTeamGoodsElse);
                    article.setGoodsName(byTeamGoods.getTeamName());
                }
            }
        }
        //List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
        return list;
    }

    @Override
    public ByArticle queryByArticleById(Object id) {
        return byArticleMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByArticle(ByArticle byArticle) {
        verifyArticle(byArticle);
        byArticle.setGmtCreate(new Date());
        byArticleMapper.insertSelective(byArticle);
    }

    public void verifyArticle(ByArticle byArticle){
        if (byArticle.getState() != null && !Status.getStatus().contains(byArticle.getState())){
            throw new CustomException("请设置正确滚动状态");
        }
        if (byArticle.getGoodsId() != null){
            if (byArticle.getType() == null){
                throw new CustomException("请选择商品分类（1 商品 2 次卡 3联票 4拼团）");
            }else {
                if (byArticleMapper.selectAllForGoods(byArticle) == null){
                    throw new CustomException("该商品不存在,或未上架");
                }
            }
        }
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
        if (byPlatformSet.getArticleNum() != null){
            if (byArticle.getId() != null){
                Long count = byArticleMapper.selectOtherState(byArticle.getId());
                if (count != null && count >= byPlatformSet.getArticleNum() && byArticle.getState().equals(1)){
                    throw new CustomException("不能超过商城设置滚动条数");
                }
            }else {
                List<ByArticle> articles = byArticleMapper.select(new ByArticle().setState(1));
                if (articles != null && articles.size() >= byPlatformSet.getArticleNum() && byArticle.getState().equals(1)){
                    throw new CustomException("不能超过商城设置滚动条数");
                }
            }
        }
    }

    @Override
    public void removeByArticle(Object id) {
	  ByArticle byArticle = new ByArticle();
	  byArticle.setId(Integer.parseInt(id.toString()));
        byArticleMapper.updateByPrimaryKeySelective(byArticle);
    }

    @Override
    public void modifyByArticle(ByArticle byArticle) {
        ByArticle before = byArticleMapper.selectByPrimaryKey(byArticle);
        if (byArticle.getGoodsId() != null && byArticle.getState() == null){
            byArticle.setState(before.getState());
        }else if (byArticle.getGoodsId() == null && byArticle.getState() != null){
            byArticle.setType(before.getType()).setGoodsId(before.getGoodsId());
        }
        verifyArticle(byArticle);
        byArticle.setGmtModified(new Date());
        if (byArticle.getState()==1){
            byArticleMapper.updateState();
        }
        byArticleMapper.updateByPrimaryKeySelective(byArticle);
    }
    @Override
    public void deleteByArticle(Integer id) {
        checkArticleQuote(id);
        byArticleMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<GoodsVo> getGoods(String name) {
        ByArticle byArticle = new ByArticle();
        byArticle.setGoodsName(name);
        return byArticleMapper.selectAllForGoods(byArticle);
    }

    /**
     * 效验当前商品是否在其他地方有引用
     *
     * @param id 商品id
     */
    private void checkArticleQuote(Integer id) {
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
        ByArticle byArticle = byArticleMapper.selectByPrimaryKey(id);
        if(byPlatformSet != null && byPlatformSet.getArticleStatus() != null && byPlatformSet.getArticleStatus().equals(1) && byArticle != null && byArticle.getState()!=null && byArticle.getState().equals(1)){
            throw new CustomException("当前文章正在参与滚动");
        }
        //首页推荐位置判断是否有引用
        Example bannerExample=new Example(BaseBanner.class);
        //商品类型
        bannerExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target",id );
        List<BaseBanner> baseBanners = baseBannerMapper.selectByExample(bannerExample);
        if (null!=baseBanners&&baseBanners.size()>0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseNavigationConfExample=new Example(BaseNavigationConf.class);
        baseNavigationConfExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target",id );
        List<BaseNavigationConf> baseNavigationConfs = baseNavigationConfMapper.selectByExample(baseNavigationConfExample);
        if (null!=baseNavigationConfs&&baseNavigationConfs.size()>0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseActivityExample=new Example(BaseActivity.class);
        baseActivityExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_6).andEqualTo("target",id );
        List<BaseActivity> baseActivities = baseActivityMapper.selectByExample(baseActivityExample);
        if (null!=baseActivities&&baseActivities.size()>0) {
            throw new CustomException(ERR_MESSAGE);
        }
    }

}
