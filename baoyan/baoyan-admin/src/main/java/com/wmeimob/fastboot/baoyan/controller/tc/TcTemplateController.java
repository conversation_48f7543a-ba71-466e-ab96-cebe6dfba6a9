package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.enums.JumpType;
import com.wmeimob.fastboot.baoyan.service.TcTemplateService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * (TcTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-20 19:03:42
 */
@RestController
@RequestMapping("/tc/template")
@Slf4j
public class TcTemplateController {
    /**
     * 服务对象
     */
    @Resource
    private TcTemplateService tcTemplateService;

    @GetMapping("/")
    public PageInfo<? extends TcTemplate> queryPage(TcTemplate queryObject){
        PageContext.startPage();
        log.info("get=> query [入参]============={}",queryObject);
        return new PageInfo<>(this.tcTemplateService.queryPage(queryObject));
    }
    @GetMapping("/{id}")
    public TcTemplate queryById(@PathVariable("id") Integer id){
        return tcTemplateService.queryById(id);
    }
    @PostMapping("/")
    public Boolean updateTemplate (@RequestBody TcTemplate updateObject)
    {
        log.info("post => updateTemplate [入参]============={}",updateObject);
        return tcTemplateService.update(updateObject);
    }

    @PostMapping("/onAndOffShelves")
    public Boolean onAndOffShelves (@RequestBody TcTemplate updateObject)
    {
        log.info("post => onAndOffShelves [入参]============={}",updateObject);
        return tcTemplateService.onAndOffShelves(updateObject);
    }
    @PutMapping("/")
    public Boolean addTemplate (@RequestBody TcTemplate addObject)
    {
        log.info("put => addTemplate [入参]============={}",addObject);
        return tcTemplateService.insert(addObject);
    }
    @DeleteMapping("/{id}")
    public Boolean delTemplate (@PathVariable("id") Integer id)
    {
        log.info("delete => delTemplate [入参]============={}",id);
        TcTemplate template = tcTemplateService.queryById(id);
        if (template == null) throw new CustomException("参数不对");
        if (JumpType.AMOY_PLAY_TOP.getId().equals(template.getJumpType())) throw new CustomException("淘潮玩首页 头部不支持 删除");
        return tcTemplateService.deleteById(id);
    }

    @GetMapping("/queryIsTemplateTop")
    public Boolean queryIsTemplateTop(){
        TcTemplate template = new TcTemplate();
        template.setJumpType(JumpType.AMOY_PLAY_TOP.getId());
        List<TcTemplate> tcTemplates = tcTemplateService.queryPage(template);

        log.info("tcTemplates =====================>{}",tcTemplates);

        return tcTemplates != null && !(tcTemplates.isEmpty());
    }

}
