package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcCate;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.service.TcGoodsService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import jdk.nashorn.internal.runtime.logging.Logger;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * (TcGoods)淘潮玩商品 Controller
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@RestController
@RequestMapping("tc/tcGoods")
@Slf4j
public class TcGoodsController {
    /**
     * 服务对象
     */
    @Resource
    private TcGoodsService tcGoodsService;

    @GetMapping("/")
    public PageInfo<? extends TcGoods> queryPage(TcGoods queryObject) {
        log.info("get=> queryPage [入参]============={}", queryObject);
        Page<Object> objects = PageContext.startPage();
//        Page{count=true, pageNum=1, pageSize=30, startRow=0, endRow=30, total=0, pages=0, reasonable=null, pageSizeZero=null}[]
        PageInfo<TcGoods> objectPageInfo = new PageInfo<>();
        objectPageInfo.setList(tcGoodsService.queryPage(queryObject));
        objectPageInfo.setTotal(tcGoodsService.queryAllCount(queryObject));
        objectPageInfo.setPageSize(objects.getPageSize());
        objectPageInfo.setPageNum(objects.getPageNum());
        return objectPageInfo;
    }


    @PostMapping("/templateGoods")
    public List<TcGoods> queryPagePost(@RequestBody TcGoods queryObject) {
        log.info("post=> queryPagePost [入参]============={}", queryObject);
        return tcGoodsService.queryPage(queryObject);
    }


    @GetMapping("/{id}")
    public TcGoods queryGoodsById(@PathVariable("id") Integer id) {
        TcGoods tcGoods = tcGoodsService.queryById(id);
        System.out.println(tcGoods);
        return tcGoods;
    }

    @PostMapping("/add")
    public Boolean addTcGoods(HttpServletResponse response, @RequestBody TcGoods addObject) {
        System.out.println(addObject);
        log.info("POST => addTcGoods [入参]============={}", addObject);
        return this.tcGoodsService.insert(response, addObject);
    }

    @PostMapping("/update")
    public Boolean updateTcGood(@RequestBody TcGoods object) {
        log.info("POST => updateTcGood [入参]============={}", object);
        return this.tcGoodsService.update(object);
    }

    /**
     * 上下架
     */
    @PostMapping("/onAndOffShelves")
    public Boolean onAndOffShelves(@RequestBody TcGoods object) {
        log.info("post == /onAndOffShelves [入参] =======>{}", object);
        if (null == object || null == object.getId() || null == object.getIsDeleted()) {
            throw new CustomException("参数不对 ");
        }
        TcGoods tcGoods = tcGoodsService.queryById(object.getId());
        if (null == tcGoods) {
            throw new CustomException(String.format("未找到 编号为 === >%d 商品", object.getId()));
        }
        tcGoods.setIsDeleted(!tcGoods.getIsDeleted());
        return tcGoodsService.update(tcGoods);
    }

    @DeleteMapping("/{id}")
    public Boolean deleteTcGoods(@PathVariable("id") Integer id) {
        return this.tcGoodsService.deleteById(id);
    }

    @GetMapping("/codeImg/{id}/{flag}")
    public String queryCodeImg(HttpServletResponse response, @PathVariable("id") Integer id
            , @PathVariable(value = "flag", required = false) Boolean flag)
    {
        return tcGoodsService.queryWxCodeImg(response, id, null != flag && flag);
    }

}