package com.wmeimob.fastboot.baoyan.controller.cust;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.ByChannelCust;
import com.wmeimob.fastboot.baoyan.service.ByChannelCustService;
import com.wmeimob.fastboot.baoyan.vo.ChannelCustWriteOffTotalVo;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 渠道会员
 */
@RestController
@RequestMapping("bychannelcust")
@Slf4j
public class byChannelCustController {
    @Resource
    private ByChannelCustService byChannelCustService;

    /**
     * 渠道会员分页查询
     * @param request
     * @param byChannelCust
     */
    @GetMapping("/")
    public PageInfo queryForByCustUser(HttpServletRequest request, ByChannelCust byChannelCust){
        PageContext.startPage();
        return new PageInfo<ByChannelCust>(byChannelCustService.findByCondition(byChannelCust));
    }
    /**
     * 授权用户表查询-<通过id查询>
     * @param request
     * @param id
     */
    @GetMapping("/{id}")
    public R queryById(HttpServletRequest request, @PathVariable("id") Object id){
        return R.data(byChannelCustService.queryById(id));
    }


    /**
     * 授权用户表添加
     * @param request
     * @param byChannelCust
     */
    @PostMapping("/")
    public R insertForByChannelCust(HttpServletRequest request,@RequestBody ByChannelCust byChannelCust){
        return R.data(byChannelCustService.addByChannelCust(byChannelCust));
    }


    /**
     * 用户表修改
     * @param request
     * @param byCustUser
     * @return
     */
    @PutMapping("/")
    public R updateForByChannelCust(HttpServletRequest request,@RequestBody ByChannelCust byChannelCust){
        return R.data(byChannelCustService.updateByChannelCust(byChannelCust));
    }

    /**
     * 渠道授权用户表删除
     * @param request
     * @param id
     */
    @DeleteMapping("/{id}")
    public R removeForByChannelCust(HttpServletRequest request,@PathVariable("id") Object id){
        return R.data(byChannelCustService.removeByChannelCust(id));
    }

    @PostMapping("/deleteChannelAllInfo/{id}")
    public R deleteChannelAllInfo(HttpServletRequest request,@PathVariable("id") Object id){
        return R.data(byChannelCustService.deleteChannelAllInfo(id));
    }

    @GetMapping("/exportChannelCustTotal")
    public void exportChannelCustTotal(ByChannelCust byChannelCust,HttpServletRequest request, HttpServletResponse response){
        List<ChannelCustWriteOffTotalVo> writeOffCodeLogList =  byChannelCustService.exportChannelCustTotal(byChannelCust);

        HashMap<String,String> exportFieldSets = new HashMap<>();
        exportFieldSets.put("storeName","门店名称");
        exportFieldSets.put("channelSourceName","来源渠道");
        exportFieldSets.put("goodsName","商品名称");
        exportFieldSets.put("endDate","有效日期");
        exportFieldSets.put("writeOffStoreName","核销门店");
        exportFieldSets.put("unWriteOffCount","未核销次数");
        exportFieldSets.put("writeOffCount","已核销次数");
        //TODO 后续可以导出自定义哪些字段进行导出
//        if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getExportFields())) {
//            exportFieldSets = Sets.newHashSet(dto.getExportFields().split(","));
//        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(String.format("渠道核销导出_%s", DateUtils.format(new Date(), "yyyy-MM-dd")), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            ServletOutputStream out=response.getOutputStream();
            // 通过工具类创建writer，默认创建xls格式
            ExcelWriter writer = ExcelUtil.getWriter(true);
            // 一次性写出内容，使用默认样式，强制输出标题
            //自定义标题别名
            exportFieldSets.forEach(writer::addHeaderAlias);
            writer.setOnlyAlias(true);
            //根据exportFieldSets中 key，从writeOffCodeLogList 转换为 list
            List<Map<String, Object>> rows = writeOffCodeLogList.stream().map(item -> {
                Map<String, Object> map = new HashMap<>();
                Map<String, Object> mapItem = BeanUtil.beanToMap(item);
                exportFieldSets.forEach((key, value) -> {
                    map.put(key, mapItem.get(key));
                });
                return map;
            }).collect(Collectors.toList());
            //out为OutputStream，需要写出到的目标流
            writer.write(rows);
            writer.flush(out);
            // 关闭writer，释放内存
            writer.close();
            IoUtil.close(out);

//            EasyExcel
//                    .write(response.getOutputStream(), ByOrders.class)
//                    .sheet("订单统计")
//                    .doWrite(byOrdersList);
        } catch (Exception e) {
            log.info("核销记录导出数据:{}", e.getMessage());
        }
    }
}
