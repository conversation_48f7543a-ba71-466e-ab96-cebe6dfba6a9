package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByTicketGoodsMapping;
import com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMappingMapper;
import com.wmeimob.fastboot.baoyan.service.ByTicketGoodsMappingService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTicketGoodsMappingServiceImpl
 * @Description  联票商品mapping表 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTicketGoodsMappingServiceImpl implements ByTicketGoodsMappingService {

    @Resource
    private ByTicketGoodsMappingMapper byTicketGoodsMappingMapper;


    @Override
    public List<ByTicketGoodsMapping> findByCondition(ByTicketGoodsMapping byTicketGoodsMapping) {
        Example example = new Example(ByTicketGoodsMapping.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byTicketGoodsMapping.getId())){
            criteria.andEqualTo("id",byTicketGoodsMapping.getId());
	  }
	  if(!StringUtils.isEmpty(byTicketGoodsMapping.getTicketGoodsId())){
            criteria.andEqualTo("ticketGoodsId",byTicketGoodsMapping.getTicketGoodsId());
	  }
	  if(!StringUtils.isEmpty(byTicketGoodsMapping.getGoodsId())){
            criteria.andEqualTo("goodsId",byTicketGoodsMapping.getGoodsId());
	  }
	  if(!StringUtils.isEmpty(byTicketGoodsMapping.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byTicketGoodsMapping.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byTicketGoodsMapping.getGmtUpdate())){
            criteria.andEqualTo("gmtUpdate",byTicketGoodsMapping.getGmtUpdate());
	  }
        example.orderBy("id").desc();
        List<ByTicketGoodsMapping> byTicketGoodsMappings = byTicketGoodsMappingMapper.selectByExample(example);
        return byTicketGoodsMappings;
    }

    @Override
    public ByTicketGoodsMapping queryByTicketGoodsMappingById(Object id) {
        return byTicketGoodsMappingMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByTicketGoodsMapping(ByTicketGoodsMapping byTicketGoodsMapping) {
	  byTicketGoodsMapping.setGmtCreate(new Date());
        byTicketGoodsMappingMapper.insertSelective(byTicketGoodsMapping);
    }

    @Override
    public void removeByTicketGoodsMapping(Object id) {
	  ByTicketGoodsMapping byTicketGoodsMapping = new ByTicketGoodsMapping();
	  byTicketGoodsMapping.setId(Integer.parseInt(id.toString()));
        byTicketGoodsMappingMapper.updateByPrimaryKeySelective(byTicketGoodsMapping);
    }

    @Override
    public void modifyByTicketGoodsMapping(ByTicketGoodsMapping byTicketGoodsMapping) {
        byTicketGoodsMappingMapper.updateByPrimaryKeySelective(byTicketGoodsMapping);
    }

}
