package com.wmeimob.fastboot.baoyan.task;

import com.wmeimob.fastboot.baoyan.entity.ByBalanceLog;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.YchSyncTask;
import com.wmeimob.fastboot.baoyan.mapper.ByBalanceLogMapper;
import com.wmeimob.fastboot.baoyan.mapper.YchSyncTaskMapper;
import com.wmeimob.fastboot.baoyan.service.MemberCardService;
import com.wmeimob.fastboot.baoyan.service.YchApiService;

import cn.hutool.core.util.ObjectUtil;

import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MemberCardSyncTask {
    
    @Resource
    private MemberCardService memberCardService;
    
    @Resource
    private YchSyncTaskMapper ychSyncTaskMapper;
    
    @Resource
    private ByBalanceLogMapper balanceLogMapper;

    @Resource
    private ByCustUserMapper byCustUserMapper;

    @Resource
    private YchApiService ychApiService;

    // 每5分钟同步一次油菜花同步任务
    @Scheduled(cron = "0 * * * * ?")
    public void syncYchSyncTasks() {
        log.info("开始执行油菜花同步任务");
        
        // 查询所有待同步的余额记录
        Example example = new Example(YchSyncTask.class);
        example.createCriteria()
               .andEqualTo("taskStatus", 0)
               .andLessThan("retryCount", 3);
        
        List<YchSyncTask> tasks = ychSyncTaskMapper.selectByExample(example);
        for (YchSyncTask task : tasks) {
            try {
                // 更新状态为同步中
                task.setTaskStatus(1);
                task.setUpdateTime(new Date());
                ychSyncTaskMapper.updateByPrimaryKeySelective(task);

                ByCustUser user = byCustUserMapper.selectByPrimaryKey(task.getMemberId());

                // 根据taskType执行不同的同步任务
                if (task.getTaskType() == 1) {
                    // 会员开卡
                    ychApiService.registerLeaguer(user.getMobile());
                } else if (task.getTaskType() == 2) {
                    // 余额查询
                    ychApiService.GetLeaguerValues(user.getMobile());
                } else if (task.getTaskType() == 3) {
                    // 余额变更
                    if (StringUtils.isEmpty(user.getMemberCardId())) {
                        // 会员开卡
                        ychApiService.registerLeaguer(user.getMobile());
                    }
                    // 变更余额
                    ByBalanceLog balanceLog = balanceLogMapper.selectByPrimaryKey(task.getRefId());
                    if (ObjectUtil.isNotNull(balanceLog)) {
                        float changeValue = balanceLog.getChangeBalance().floatValue();
                        if (balanceLog.getChangeType() == 2) {
                            changeValue = -1 * changeValue;
                        }
                        // 重新查询会员信息
                        user = byCustUserMapper.selectByPrimaryKey(task.getMemberId());
                        ychApiService.LeaguerPrepaidChange(changeValue, user.getMemberCardId(), balanceLog.getChangeType() == 1 ? "BonusMoney" : "UseMoney");
                    }
                }
                log.info("执行油菜花同步任务成功");
                task.setTaskStatus(2);
                task.setUpdateTime(new Date());
                ychSyncTaskMapper.updateByPrimaryKeySelective(task);
            } catch (Exception e) {

                // 更新状态为同步失败
                task.setTaskStatus(3);
                task.setRetryCount(task.getRetryCount() + 1);
                task.setErrorMsg(e.getMessage());
                task.setUpdateTime(new Date());
                ychSyncTaskMapper.updateByPrimaryKeySelective(task);
            }
        }
    }

    // 每半个小时尝试重新同步失败的记录
    @Scheduled(cron = "0 0/30 * * * ?")
    public void retryFailedSyncTasks() {
        log.info("开始尝试重新同步失败的记录");

        // 查询所有待同步的余额记录
        Example example = new Example(YchSyncTask.class);
        example.createCriteria()
               .andEqualTo("taskStatus", 3);

        List<YchSyncTask> tasks = ychSyncTaskMapper.selectByExample(example);
        for (YchSyncTask task : tasks) {
            // 更新状态为未同步
            task.setTaskStatus(0);
            ychSyncTaskMapper.updateByPrimaryKeySelective(task);
        }
    }
}
