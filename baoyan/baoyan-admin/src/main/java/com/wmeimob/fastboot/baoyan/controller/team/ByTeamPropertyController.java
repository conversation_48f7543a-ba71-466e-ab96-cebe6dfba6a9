package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTeamProperty;
import com.wmeimob.fastboot.baoyan.service.ByTeamPropertyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByTeamPropertyController
 * @Description 【预售商品属性】控制器
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byteamproperty")
@Slf4j
public class ByTeamPropertyController {

    @Resource
    private ByTeamPropertyService byTeamPropertyService;




    /**
     * 预售商品属性分页查询
     * @param request
     * @param byTeamProperty
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTeamProperty(HttpServletRequest request, ByTeamProperty byTeamProperty){
        PageContext.startPage();
        return new PageInfo<ByTeamProperty>(byTeamPropertyService.findByCondition(byTeamProperty));
         
    }

     /**
     * 预售商品属性导出
     * @param request
     * @param byTeamProperty
     * @return
     */
    @GetMapping("/exports")
    public List<ByTeamProperty> queryForByTeamPropertyexports(HttpServletRequest request, ByTeamProperty 
 byTeamProperty){
        return  byTeamPropertyService.findByCondition(byTeamProperty);
    }


    /**
     * 预售商品属性查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTeamProperty queryForByTeamPropertyById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byTeamPropertyService.queryByTeamPropertyById(id);
    }


    /**
     * 预售商品属性添加
     * @param request
     * @param byTeamProperty
     * @return
     */
    @PostMapping("/")
    public void insertForByTeamProperty(HttpServletRequest request,@RequestBody ByTeamProperty byTeamProperty){
            byTeamPropertyService.addByTeamProperty(byTeamProperty);    
    }


    /**
     * 预售商品属性修改
     * @param request
     * @param byTeamProperty
     * @return
     */
    @PutMapping("/")
    public void updateForByTeamProperty(HttpServletRequest request,@RequestBody ByTeamProperty byTeamProperty){
            byTeamPropertyService.modifyByTeamProperty(byTeamProperty);  
    }

    /**
     * 预售商品属性删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByTeamProperty(HttpServletRequest request,@PathVariable("id") Object id){
            byTeamPropertyService.removeByTeamProperty(id);
    }
}
