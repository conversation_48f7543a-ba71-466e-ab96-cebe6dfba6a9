package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.BaseListImg;
import com.wmeimob.fastboot.baoyan.qo.BaseListImgQo;
import com.wmeimob.fastboot.baoyan.service.BaseManyImgService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 多图片控制
 * <AUTHOR>
 */
@RestController
@RequestMapping("list_img")
@Slf4j
public class BaseManyImgController {
    @Resource
    BaseManyImgService baseManyImgService;

    /**
     * 多图设置列表分页查询
     */
    @GetMapping("/")
    public PageInfo list(BaseListImg baseListImg){
        PageContext.startPage();
        return new PageInfo<BaseListImg>(baseManyImgService.findByCondition(baseListImg));
    }


    /**
     * 新增/修改多图设置
     */
    @PostMapping("/")
    public void insertOrUpdate(@RequestBody BaseListImgQo qo){
        baseManyImgService.insertOrUpdate(qo);
    }


    /**
     * 修改
     */
    @PutMapping("/")
    public void update(@RequestBody BaseListImgQo qo){
        baseManyImgService.updateById(qo);
    }

    /**
     * 编辑查看
     */
    @GetMapping("/{id}")
    public BaseListImg select(@PathVariable("id") Integer id){
        return baseManyImgService.select(id);
    }


    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    public void removeForBaseActivity(@PathVariable("id") Integer id){
        baseManyImgService.deleteById(id);
    }
}
