package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsStore;
import com.wmeimob.fastboot.baoyan.service.ByGoodsStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByGoodsStoreController
 * @Description 【商品门店关系表】控制器
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bygoodsstore")
@Slf4j
public class ByGoodsStoreController {

    @Resource
    private ByGoodsStoreService byGoodsStoreService;




    /**
     * 商品门店关系表分页查询
     * @param request
     * @param byGoodsStore
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByGoodsStore(HttpServletRequest request, ByGoodsStore byGoodsStore){
        PageContext.startPage();
        return new PageInfo<ByGoodsStore>(byGoodsStoreService.findByCondition(byGoodsStore));
         
    }

     /**
     * 商品门店关系表导出
     * @param request
     * @param byGoodsStore
     * @return
     */
    @GetMapping("/exports")
    public List<ByGoodsStore> queryForByGoodsStoreexports(HttpServletRequest request, ByGoodsStore 
 byGoodsStore){
        return  byGoodsStoreService.findByCondition(byGoodsStore);
    }


    /**
     * 商品门店关系表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByGoodsStore queryForByGoodsStoreById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byGoodsStoreService.queryByGoodsStoreById(id);
    }


    /**
     * 商品门店关系表添加
     * @param request
     * @param byGoodsStore
     * @return
     */
    @PostMapping("/")
    public void insertForByGoodsStore(HttpServletRequest request,@RequestBody ByGoodsStore byGoodsStore){
            byGoodsStoreService.addByGoodsStore(byGoodsStore);    
    }


    /**
     * 商品门店关系表修改
     * @param request
     * @param byGoodsStore
     * @return
     */
    @PutMapping("/")
    public void updateForByGoodsStore(HttpServletRequest request,@RequestBody ByGoodsStore byGoodsStore){
            byGoodsStoreService.modifyByGoodsStore(byGoodsStore);  
    }

    /**
     * 商品门店关系表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByGoodsStore(HttpServletRequest request,@PathVariable("id") Object id){
            byGoodsStoreService.removeByGoodsStore(id);
    }
}
