package com.wmeimob.fastboot.baoyan.controller.store;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByStoreTable;
import com.wmeimob.fastboot.baoyan.qo.StoreTableQo;
import com.wmeimob.fastboot.baoyan.service.ByStoreTableService;
import com.wmeimob.fastboot.baoyan.vo.StoreTableVo;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 桌台管理
 * <AUTHOR>
 * @Date 2024-04-06
 */
@RestController
@RequestMapping("storetable")
@Slf4j
public class ByStoreTableController {
    @Resource
    private ByStoreTableService byStoreTableService;

    /**
     * 门店列表，如果有桌台则返回和桌台数量
     *
     * @param storeTableQo 根据所属门店和桌号查询
     */
    @GetMapping("/queryStoreNoList")
    public PageInfo queryStoreNoList(StoreTableQo storeTableQo) {
        PageContext.startPage();
        return new PageInfo<StoreTableVo>(byStoreTableService.queryStoreNoList(storeTableQo));
    }

    /**
     * 添加桌台
     */
    @PostMapping("/")
    public String storeTable(@RequestBody ByStoreTable byStoreTable, HttpServletRequest request) {
        return byStoreTableService.addByStoreTable(byStoreTable) ? "添加成功" : "添加失败";
    }
}
