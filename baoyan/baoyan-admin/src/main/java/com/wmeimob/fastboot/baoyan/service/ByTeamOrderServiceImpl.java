package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByTeamOrder;
import com.wmeimob.fastboot.baoyan.mapper.ByTeamOrderMapper;
import com.wmeimob.fastboot.baoyan.service.ByTeamOrderService;
import com.wmeimob.fastboot.baoyan.vo.TeamOrderVo;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTeamOrderServiceImpl
 * @Description  拼团订单表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTeamOrderServiceImpl implements ByTeamOrderService {

    @Resource
    private ByTeamOrderMapper byTeamOrderMapper;


    @Override
    public List<ByTeamOrder> findByCondition(ByTeamOrder byTeamOrder) {
        Example example = new Example(ByTeamOrder.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byTeamOrder.getId())){
            criteria.andEqualTo("id",byTeamOrder.getId());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getTeamId())){
            criteria.andEqualTo("teamId",byTeamOrder.getTeamId());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getTeamGoodsId())){
            criteria.andEqualTo("teamGoodsId",byTeamOrder.getTeamGoodsId());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getUserId())){
            criteria.andEqualTo("userId",byTeamOrder.getUserId());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getOrderNo())){
            criteria.andLike("orderNo",StringUtils.fullFuzzy(byTeamOrder.getOrderNo()));
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getPayType())){
            criteria.andEqualTo("payType",byTeamOrder.getPayType());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getOrderAmount())){
            criteria.andEqualTo("orderAmount",byTeamOrder.getOrderAmount());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getPayAmount())){
            criteria.andEqualTo("payAmount",byTeamOrder.getPayAmount());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getNum())){
            criteria.andEqualTo("num",byTeamOrder.getNum());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getOrderRemark())){
            criteria.andLike("orderRemark",StringUtils.fullFuzzy(byTeamOrder.getOrderRemark()));
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getOrderStatus())){
            criteria.andEqualTo("orderStatus",byTeamOrder.getOrderStatus());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getIsTeam())){
            criteria.andEqualTo("isTeam",byTeamOrder.getIsTeam());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getIsAfter())){
            criteria.andEqualTo("isAfter",byTeamOrder.getIsAfter());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getPayTime())){
            criteria.andEqualTo("payTime",byTeamOrder.getPayTime());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getCancelTime())){
            criteria.andEqualTo("cancelTime",byTeamOrder.getCancelTime());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byTeamOrder.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getGmtUpdate())){
            criteria.andEqualTo("gmtUpdate",byTeamOrder.getGmtUpdate());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getIsDel())){
            criteria.andEqualTo("isDel",byTeamOrder.getIsDel());
	  }
	  if(!StringUtils.isEmpty(byTeamOrder.getPoint())){
            criteria.andEqualTo("point",byTeamOrder.getPoint());
	  }
	  criteria.andEqualTo("isDel",false);
        example.orderBy("id").desc();
        List<ByTeamOrder> byTeamOrders = byTeamOrderMapper.selectByExample(example);
        return byTeamOrders;
    }

    @Override
    public List<TeamOrderVo>  queryTeamList(TeamOrderVo vo) {
		return byTeamOrderMapper.queryTeamList(vo);
    }
    @Override
    public TeamOrderVo queryTeamOrderById(Integer id) {
		TeamOrderVo vo = byTeamOrderMapper.getTeamOrderInfoById(id);


		return vo;
    }
	@Override
	public List<TeamOrderVo> getTeamOrderInfo(Integer id) {
		return byTeamOrderMapper.getTeamOrderInfo(id);
	}




}
