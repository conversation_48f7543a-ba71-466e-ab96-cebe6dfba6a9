package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByRichText;
import com.wmeimob.fastboot.baoyan.mapper.ByRichTextMapper;
import com.wmeimob.fastboot.baoyan.service.ByRichTextService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByRichTextServiceImpl
 * @Description  富文本 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 30 09:01:33 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByRichTextServiceImpl implements ByRichTextService {

    @Resource
    private ByRichTextMapper byRichTextMapper;


    @Override
    public List<ByRichText> findByCondition(ByRichText byRichText) {
        Example example = new Example(ByRichText.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byRichText.getId())){
            criteria.andEqualTo("id",byRichText.getId());
	  }
	  if(!StringUtils.isEmpty(byRichText.getContent())){
            criteria.andEqualTo("content",byRichText.getContent());
	  }
	  if(!StringUtils.isEmpty(byRichText.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byRichText.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byRichText.getGmtModified())){
            criteria.andEqualTo("gmtModified",byRichText.getGmtModified());
	  }
        example.orderBy("id").desc();
        List<ByRichText> byRichTexts = byRichTextMapper.selectByExample(example);
        return byRichTexts;
    }

    @Override
    public ByRichText queryByRichTextById(Object id) {
        return byRichTextMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByRichText(ByRichText byRichText) {
	  byRichText.setGmtCreate(new Date());
        byRichTextMapper.insertSelective(byRichText);
    }

    @Override
    public void removeByRichText(Object id) {
	  ByRichText byRichText = new ByRichText();
	  byRichText.setId(Integer.parseInt(id.toString()));
        byRichTextMapper.updateByPrimaryKeySelective(byRichText);
    }

    @Override
    public void modifyByRichText(ByRichText byRichText) {
		byRichText.setGmtModified(new Date());
        byRichTextMapper.updateByPrimaryKeySelective(byRichText);
    }

}
