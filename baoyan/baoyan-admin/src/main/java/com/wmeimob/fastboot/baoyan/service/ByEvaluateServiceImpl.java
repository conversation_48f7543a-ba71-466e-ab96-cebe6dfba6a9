package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByEvaluate;
import com.wmeimob.fastboot.baoyan.mapper.ByEvaluateMapper;
import com.wmeimob.fastboot.baoyan.service.ByEvaluateService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByEvaluateServiceImpl
 * @Description  评价表 服务类实现
 * <AUTHOR>
 * @Date Wed Jul 17 16:52:31 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByEvaluateServiceImpl implements ByEvaluateService {

    @Resource
    private ByEvaluateMapper byEvaluateMapper;


    @Override
    public List<ByEvaluate> findByCondition(ByEvaluate byEvaluate) {

        List<ByEvaluate> byEvaluates = byEvaluateMapper.findEvalListByCondition(byEvaluate);
        return byEvaluates;
    }

    @Override
    public ByEvaluate queryByEvaluateById(Object id) {
        return byEvaluateMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByEvaluate(ByEvaluate byEvaluate) {
	  byEvaluate.setGmtCreate(new Date());
        byEvaluateMapper.insertSelective(byEvaluate);
    }

    @Override
    public void removeByEvaluate(Object id) {
	  ByEvaluate byEvaluate = new ByEvaluate();
	  byEvaluate.setId(Integer.parseInt(id.toString()));
	  byEvaluate.setIsDel(true);
        byEvaluateMapper.updateByPrimaryKeySelective(byEvaluate);
    }

    @Override
    public void modifyByEvaluate(ByEvaluate byEvaluate) {
		byEvaluate.setGmtModified(new Date());
        byEvaluateMapper.updateByPrimaryKeySelective(byEvaluate);
    }

    @Override
    public void replyEval(ByEvaluate byEvaluate) {
        ByEvaluate byEvaluate1 = byEvaluateMapper.selectByPrimaryKey(byEvaluate.getId());
        if(null == byEvaluate1){
            throw new CustomException("评价信息异常");
        }
        byEvaluate.setGmtModified(new Date());
        int i = byEvaluateMapper.updateByPrimaryKeySelective(byEvaluate);
        if(i<1){
            throw new CustomException("回复失败");
        }
    }

}
