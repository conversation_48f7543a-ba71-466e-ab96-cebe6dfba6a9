package com.wmeimob.fastboot.baoyan.controller.tc;


import com.wmeimob.fastboot.baoyan.entity.TcLabel;
import com.wmeimob.fastboot.baoyan.service.TcLabelService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * (TcLabel)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-20 14:08:58
 */
@RestController
@RequestMapping("/tc/label")
public class TcLabelController {
    /**
     * 服务对象
     */
    @Resource
    private TcLabelService tcLabelService;

    /**
     * 通过主键查询单条数据
     * @param id 主键
     * @return 单条数据
     */

    @GetMapping("/{id}")
    public TcLabel selectOne(@PathVariable("id") Integer id) {
        return this.tcLabelService.queryById(id);
    }

    @GetMapping("/")
    public List<TcLabel> queryAll(TcLabel tcLabel) {
        return this.tcLabelService.queryAll(tcLabel);
    }

}