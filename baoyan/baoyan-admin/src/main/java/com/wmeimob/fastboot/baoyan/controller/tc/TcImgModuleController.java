package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcGoods;
import com.wmeimob.fastboot.baoyan.entity.TcImgModule;
import com.wmeimob.fastboot.baoyan.service.TcImgModuleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (TcImgModule)表控制层
 *
 * <AUTHOR>
 * @since 2021-09-02 21:22:18
 */
@RestController
@RequestMapping("tc/tcImgModule")
public class TcImgModuleController {
    /**
     * 服务对象
     */
    @Resource(name = "tcImgModuleService")
    private TcImgModuleService tcImgModuleService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    public TcImgModule selectOne(@PathVariable("id") Integer id) {
        return this.tcImgModuleService.queryById(id);
    }
    @GetMapping("/")
    public PageInfo<TcImgModule> selectAll(TcImgModule queryAll) {
        return new PageInfo<>(this.tcImgModuleService.queryAll(queryAll));
    }

    @PostMapping("/")
    public Boolean addImgModule(@RequestBody TcImgModule object) {
        return this.tcImgModuleService.insert(object);
    }

    @PutMapping("/")
    public Boolean update(@RequestBody TcImgModule object) {
        return this.tcImgModuleService.update(object);
    }

    @PostMapping("/onAndOffShelves")
    public Boolean onAndOffShelves(@RequestBody TcImgModule object){
        return this.tcImgModuleService.onAndOffShelves(object);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Integer id) {
        return this.tcImgModuleService.deleteById(id);
    }


}