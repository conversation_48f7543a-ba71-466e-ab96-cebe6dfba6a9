package com.wmeimob.fastboot.baoyan.controller.ych;

import com.wmeimob.fastboot.baoyan.entity.YchGoods;
import com.wmeimob.fastboot.baoyan.entity.YchOrder;
import com.wmeimob.fastboot.baoyan.service.YchApiService;
import com.wmeimob.fastboot.baoyan.utils.R;
import com.wmeimob.fastboot.baoyan.utils.UUIDOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 油菜花渠道订单
 */
@RestController
@RequestMapping("ych/order")
@Slf4j
public class OrderController {
    @Resource
    private YchApiService ychApiService;

    @RequestMapping("/createOrder")
    public R createOrder(Integer goodsId, String phone,String remark){
        YchGoods query = new YchGoods();
        query.setId(goodsId);
        YchGoods ychGoods = ychApiService.getGoodsDetail(query);
        String orderNo = "HTCZ" + UUIDOrder.getUUID();
        ychApiService.createOrder(ychGoods,orderNo,phone,remark);
        ychApiService.updatePaid(orderNo, orderNo);
        return R.ok();
    }

    /**
     * 订单退回
     * @param TPOrderNo
     * @return
     */
    @RequestMapping("/OrderReturn")
    public R OrderReturn(String TPOrderNo){
        return R.ok(ychApiService.orderReturn(TPOrderNo));
    }
}
