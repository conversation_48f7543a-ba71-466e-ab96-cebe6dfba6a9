package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.BaseBannerService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BaseBannerServiceImpl
 * @Description Banner 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseBannerServiceImpl implements BaseBannerService {

    @Resource
    private BaseBannerMapper baseBannerMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;

    @Override
    public List<BaseBanner> findByCondition(BaseBanner baseBanner) {
        Example example = new Example(BaseBanner.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(baseBanner.getId())) {
            criteria.andEqualTo("id", baseBanner.getId());
        }
        if (!StringUtils.isEmpty(baseBanner.getImgUrl())) {
            criteria.andLike("imgUrl", StringUtils.fullFuzzy(baseBanner.getImgUrl()));
        }
        if (!StringUtils.isEmpty(baseBanner.getStatus())) {
            criteria.andEqualTo("status", baseBanner.getStatus());
        }
        if (!StringUtils.isEmpty(baseBanner.getJumpType())) {
            criteria.andEqualTo("jumpTypeId", baseBanner.getJumpType());
        }
        if (!StringUtils.isEmpty(baseBanner.getTarget())) {
            criteria.andLike("target", StringUtils.fullFuzzy(baseBanner.getTarget()));
        }
        if (!StringUtils.isEmpty(baseBanner.getSort())) {
            criteria.andEqualTo("sort", baseBanner.getSort());
        }
        if (!StringUtils.isEmpty(baseBanner.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", baseBanner.getGmtCreate());
        }
        if (!StringUtils.isEmpty(baseBanner.getGmtUpdate())) {
            criteria.andEqualTo("gmtUpdate", baseBanner.getGmtUpdate());
        }
        criteria.andEqualTo("isDel", 0);
        example.orderBy("id").desc();
        List<BaseBanner> baseBanners = baseBannerMapper.selectByExample(example);
        return baseBanners;
    }

    @Override
    public BaseBanner queryBaseBannerById(Object id) {
        return baseBannerMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addBaseBanner(BaseBanner baseBanner) {
        baseBanner.setGmtCreate(new Date());
        //效验参数
        checkParam(baseBanner);
        //处理请求参数
        dealReqParam(baseBanner);
        baseBannerMapper.insertSelective(baseBanner);
    }

    /**
     * 效验参数
     *
     * @param baseBanner
     */
    private void checkParam(BaseBanner baseBanner) {
        if (null != baseBanner.getJumpType()) {
            if (baseBanner.getJumpType().equals(1)) {
                //判断当前分类id是否存在
                if (null == baseBanner || null == baseBanner.getClassifyId()||StringUtils.isEmpty(baseBanner.getClassifyId())) {
                    throw new CustomException("请选择分类");
                }
            }
            if (baseBanner.getJumpType().equals(2)) {
                //判断当前分类id是否存在
                if (null == baseBanner || null == baseBanner.getTargetImgUrl()) {
                    throw new CustomException("请上传联票列表图片");
                }
            }
            if (baseBanner.getJumpType().equals(3)) {
                //判断当前分类id是否存在
                if (null == baseBanner || null == baseBanner.getTargetImgUrl()) {
                    throw new CustomException("请上传次卡列表图片");
                }
            }
            if (baseBanner.getJumpType().equals(4)) {
                //判断当前分类id是否存在
                if (null == baseBanner || null == baseBanner.getTargetImgUrl()) {
                    throw new CustomException("请上传拼团列表图片");
                }
            }

            if (baseBanner.getJumpType().equals(5)) {
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", baseBanner.getTarget());
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    throw new CustomException("文章不存在");
                }
            }
            if (baseBanner.getJumpType().equals(6)) {
                //判断当前商品普通商品存在
                Example example = new Example(ByGoodsInfo.class);
                example.createCriteria().andEqualTo("id", baseBanner.getTarget()).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(example);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseBanner.getJumpType().equals(7)) {
                //判断当前联票是否存在
                Example example = new Example(ByTicketGoods.class);
                example.createCriteria().andEqualTo("id", baseBanner.getTarget()).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(example);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    throw new CustomException("联票商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseBanner.getJumpType().equals(8)) {
                //判断当前次卡商品是否存在
                Example example = new Example(BySubCardGoods.class);
                example.createCriteria().andEqualTo("id", baseBanner.getTarget()).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(example);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    throw new CustomException("次卡商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseBanner.getJumpType().equals(9)) {
                //判断当前拼团商品是否存在
                Example example = new Example(ByTeamGoods.class);
                example.createCriteria().andEqualTo("id", baseBanner.getTarget()).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(example);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    throw new CustomException("拼团商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
        }

    }

    /***
     * 处理类型参数
     * @param baseBanner
     */
    private void dealReqParam(BaseBanner baseBanner) {
        if (null != baseBanner.getJumpType()) {
            String target;
            switch (baseBanner.getJumpType()) {
                case 1:
                    target = String.valueOf(baseBanner.getClassifyId());
                    break;
                case 2:
                    target = baseBanner.getTargetImgUrl();
                    break;
                case 3:
                    target = baseBanner.getTargetImgUrl();
                    break;
                case 4:
                    target = baseBanner.getTargetImgUrl();
                    break;
                default:
                    target = baseBanner.getTarget();
                    break;
            }
            baseBanner.setTarget(target);
        }

    }

    @Override
    public void removeBaseBanner(Object id) {
        BaseBanner baseBanner = new BaseBanner();
        baseBanner.setId(Integer.parseInt(id.toString()));
        baseBanner.setIsDel(1);
        baseBannerMapper.updateByPrimaryKeySelective(baseBanner);
    }

    @Override
    public void modifyBaseBanner(BaseBanner baseBanner) {
        baseBanner.setGmtUpdate(new Date());
        checkParam(baseBanner);
        dealReqParam(baseBanner);
        baseBannerMapper.updateByPrimaryKeySelective(baseBanner);
    }

}
