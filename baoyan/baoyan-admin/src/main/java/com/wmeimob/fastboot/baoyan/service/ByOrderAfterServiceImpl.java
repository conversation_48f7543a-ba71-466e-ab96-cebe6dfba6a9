package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.tool.impl.SchedulerImpl;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.utils.ObjectUtil;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.model.refund.RefundApplyRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName ByOrderAfterServiceImpl
 * @Description 订单售后 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByOrderAfterServiceImpl implements ByOrderAfterService {

    @Resource
    private ByOrderAfterMapper byOrderAfterMapper;
    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private ByTeamOrderMapper byTeamOrderMapper;
    @Resource
    private ByOrderLogService orderLogService;


    @Override
    public List<ByOrderAfter> findByCondition(ByOrderAfter byOrderAfter) {

        List<ByOrderAfter> byOrderAfters = byOrderAfterMapper.queryAfterByCondition(byOrderAfter);
        return byOrderAfters;
    }

    /**
     * 根据id查询售后
     *
     * @param id
     * @return
     */
    @Override
    public ByOrderAfter findById(Integer id) {
        return byOrderAfterMapper.selectByPrimaryKey(id);
    }

    @Override
    public ByOrderAfter queryByOrderAfterById(Object id) {
        return byOrderAfterMapper.selectByPrimaryKey(id);
    }

    @Autowired
    private ByPlatformSetMapper byPlatformSetMapper;
    @Autowired
    private ByCustUserMapper byCustUserMapper;
    @Autowired
    private ByIntegralLogMapper byIntegralLogMapper;
    @Autowired
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Autowired
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    ByCombinationGoodsMapper byCombinationGoodsMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    @Autowired
    private SchedulerImpl scheduler;
    @Resource
    private TcOrderParentMapper tcOrderParentMapper;

    @Override
    public void auditAfterOrder(ByOrderAfter byOrderAfter) {
        //查询当前售后单
        ByOrderAfter oldAfter = byOrderAfterMapper.selectByPrimaryKey(byOrderAfter.getId());
        if (!oldAfter.getAfterStatus().equals(0)) {
            throw new CustomException("订单不在退款中");
        }

//        1、工作人员扫码时：已申请售后的订单，工作人员扫码时提醒：该订单已申请售后，不允许核销
//        2、售后订单：购买普通商品允许退款其中单个商品，但是次卡商品存在核销情况不允许退款
        Example exampleWriteOffCode = new Example(WriteOffCode.class);
        Example.Criteria criteria = exampleWriteOffCode.createCriteria();
        criteria.andEqualTo("orderNo", oldAfter.getOrderNo());
        List<WriteOffCode> writeOffCodeList = writeOffCodeMapper.selectByExample(exampleWriteOffCode);
        if(writeOffCodeList != null && !writeOffCodeList.isEmpty()) {
            boolean flag = false;
            for (WriteOffCode code : writeOffCodeList) {
                //是否被使用了
                if (code.getSurplusNum() < code.getTotalNum() && code.getType() == 2) {
                    //核销 被使用
                    flag = true;
                }
                if (flag) {
                    throw new CustomException("次卡商品已经核销部分，无法退款！");
                }
            }
        }

        byOrderAfter.setOrderNo(oldAfter.getOrderNo());
        byOrderAfter.setDetailId(oldAfter.getDetailId());
        byOrderAfter.setOrderGoodsId(oldAfter.getOrderGoodsId());
        byOrderAfter.setResouceType(oldAfter.getResouceType());

        /*驳回*/
        if (byOrderAfter.getAfterStatus().equals(CommonFinal.TWO)) {
            if (oldAfter.getResouceType() == 2) {
                Example example = new Example(ByTeamOrder.class);
                example.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo());
                List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
                if (byTeamOrders.size() == 0) {
                    throw new CustomException("拼团订单不存在");
                }
                ByTeamOrder byTeamOrder = byTeamOrders.get(0);
                byTeamOrder.setIsAfter(Boolean.FALSE);
                byTeamOrder.setGmtUpdate(new Date());
                this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
            } else {
                ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
                if (byOrderGoods == null) {
                    throw new CustomException("订单不存在");
                }
                byOrderGoods.setIsAfterSale(Boolean.FALSE);
                byOrderGoods.setGmtUpdate(new Date());
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
            }
            byOrderAfter.setAfterStatus(2);
            byOrderAfter.setGmtUpdate(new Date());
            this.byOrderAfterMapper.updateByPrimaryKeySelective(byOrderAfter);
        }
        if (byOrderAfter.getAfterStatus().equals(CommonFinal.ONE)) {
            //效验商品是否可以售后
            checkGoodsIsAfter(byOrderAfter);
            byOrderAfter.setAfterStatus(1);
            this.byOrderAfterMapper.updateByPrimaryKeySelective(byOrderAfter);
           /* OrderAfterDetailVO orderAfterDetailVO = queryAfterByOrderIdAndGoodsId(oldAfter);
            if (orderAfterDetailVO.getAfterAmount().compareTo(oldAfter.getAfterAmount()) != 0) {
                throw new CustomException("金额不一致，不能退款");
            }*/
            if (oldAfter.getResouceType() == 2) {
                Example example = new Example(ByTeamOrder.class);
                example.createCriteria().andEqualTo("orderNo", oldAfter.getOrderNo());
                List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
                if (byTeamOrders.size() == 0) {
                    throw new CustomException("拼团订单不存在");
                }
                ByTeamOrder byTeamOrder = byTeamOrders.get(0);
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date());
                refundPay(oldAfter.getOrderNo(), String.valueOf(instance.getTime().getTime()), byTeamOrder.getPayAmount(),oldAfter.getAfterAmount());
//                refundPay(oldAfter.getOrderNo(), String.valueOf(instance.getTime().getTime()), new BigDecimal(0.01), new BigDecimal(0.01));
                Example example1 = new Example(WriteOffCode.class);
                example1.createCriteria().andEqualTo("detailId", byOrderAfter.getDetailId());
                this.writeOffCodeMapper.deleteByExample(example1);
                byTeamOrder.setOrderCloseType(2);
                byTeamOrder.setOrderStatus(4);
                this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
            }
            else {
                Example example = new Example(ByOrders.class);
                example.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo());
                List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example);
                if (byOrders.size() == 0) {
                    throw new CustomException("订单不存在");
                }
                ByOrders byOrders1 = byOrders.get(0);
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date());
                // refundPay(oldAfter.getOrderNo(),String.valueOf(instance.getTime().getTime()),byOrders1.getActualAmount(),oldAfter.getAfterAmount());
                if (oldAfter.getAfterAmount() != null && oldAfter.getAfterAmount().compareTo(new BigDecimal(0)) != 0) {
                  //  refundPay(oldAfter.getOrderNo(), String.valueOf(instance.getTime().getTime()), new BigDecimal(0.01), new BigDecimal(0.01));
                    String refundNo = byOrders1.getPayOrderNo();
                    if(refundNo==null){
                        refundNo = byOrders1.getOrderNo();
                    }
                    try {
                        refundPay(refundNo,String.valueOf(instance.getTime().getTime()),byOrders1.getActualAmount(),oldAfter.getAfterAmount());
                    }catch (Exception e) {
                        if (e.getMessage().equals("[INVALID_REQUEST]订单已全额退款")) {
                            //继续往下走
                            log.info("微信商户已全额退款，请联系技术重置状态！订单号：" + byOrders1.getOrderNo());
//                            throw new CustomException("微信商户已全额退款，请联系技术重置状态！");
                        } else {
                            throw new CustomException(e.getMessage());
                        }
                    }
                }
                ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);

                /*查看订单*/
                Example example1 = new Example(ByOrderGoods.class);
                example1.createCriteria()
                        //订单号和退款的订单号必须匹配
                        .andEqualTo("orderNo", byOrderAfter.getOrderNo())
                        //商品id和 订单商品id（商品详情） 一致？
                        .andEqualTo("goodsId", byOrderAfter.getOrderGoodsId())
                        .andEqualTo("id", oldAfter.getDetailId());
                List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
                if (byOrderGoods.isEmpty()) {
                    throw new CustomException("订单不存在");
                }
                /*返回库存*/
                ByOrderGoods byOrderGoods2 = byOrderGoods.get(0);
                if (byOrderGoods2.getProductType().equals("1")) {
                    ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                    byGoodsInfo.setGoodsStock(oldAfter.getGoodsNum() + byGoodsInfo.getGoodsStock());
                    this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
                    int i = this.writeOffCodeMapper.updateWiff(oldAfter.getDetailId(), 0);
                    byGoodsInfo.setActualSalesNum(oldAfter.getGoodsNum() + byGoodsInfo.getActualSalesNum());
                    this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
                }
                if (byOrderGoods2.getProductType().equals("2")) {
                    BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                    bySubCardGoods.setGoodsStock(oldAfter.getGoodsNum() + bySubCardGoods.getGoodsStock());
                    this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
                    this.writeOffCodeMapper.updateWiff(oldAfter.getDetailId(), 0);
                    bySubCardGoods.setActualSalesNum(oldAfter.getGoodsNum() + bySubCardGoods.getActualSalesNum());
                    this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
                }
                if (byOrderGoods2.getProductType().equals("3")) {
                    ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                    byTicketGoods.setGoodsStock(oldAfter.getGoodsNum() + byTicketGoods.getGoodsStock());
                    this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
                    this.writeOffCodeMapper.updateWiff(oldAfter.getDetailId(), 0);
                    byTicketGoods.setActualSalesNum(oldAfter.getGoodsNum() + byTicketGoods.getActualSalesNum());
                    this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
                }
                if (byOrderGoods2.getProductType().equals("5")) {
                    ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                    combinationGoods.setGoodsStock(oldAfter.getGoodsNum() + combinationGoods.getGoodsStock());
                    this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
                    int i = this.writeOffCodeMapper.updateWiff(oldAfter.getDetailId(), 0);
                    combinationGoods.setActualSalesNum(oldAfter.getGoodsNum() + combinationGoods.getActualSalesNum());
                    this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
                }

                if (oldAfter.getGoodsNum().equals(byOrderGoods2.getGoodsNum())) {
                    byOrderGoods2.setIsAfterSale(false);
                    int i = this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
                    //查询订单关联 是否包含售后订单
                    Example example11 = new Example(ByOrderGoods.class);
                    example11.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo()).andEqualTo("isAfterSale", true);
                    List<ByOrderGoods> byOrderGoods1 = this.byOrderGoodsMapper.selectByExample(example11);
                    //判断 订单下是否还有有未关联 售后订单
                    if (CollectionUtils.isEmpty(byOrderGoods1) || byOrderGoods1.size() == 0) {
                        //效验未完成
                        if (oldAfter.getResouceType().equals(2)) {
                            //拼团订单 直接售后通过变成 已关闭
                            byOrders1.setOrderStatus(4);
                        } else {
                            //普通订单 需要判断核销情况
                            Example orderExample = new Example(WriteOffCode.class);
                            orderExample.createCriteria().andEqualTo("orderNo", byOrderAfter.getOrderNo()).andEqualTo("orderState", 0).andEqualTo("orderType", oldAfter.getResouceType().equals(1) ? 0 : 1);
                            int resultNum = writeOffCodeMapper.selectCountByExample(orderExample);
                            if (resultNum == 0) {
                                //关联订单 查询无 售后订单  变成已关闭
                                log.debug("关联订单 查询无 售后订单  变成已关闭 resultNum:{}", resultNum);
                                byOrders1.setOrderStatus(4);
                                byOrders1.setGmtUpdate(new Date());
                                this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                            } else {
                                log.info("进入 变更订单方法------------------------------------");
                                WriteOffCode code = new WriteOffCode();
                                code.setOrderNo(byOrderAfter.getOrderNo());
                                code.setDetailId(oldAfter.getDetailId());
                                code.setOrderType(oldAfter.getResouceType().equals(1) ? 0 : 1);
                                //效验订单 是否被核销完成 若是 则更新状态未 已完成  否则原状态
                                log.info("auditAfterOrder（）  WriteOffCode：{}", code);
                                checkOrderWriteCode(code);
                            }

                        }
//                        byOrders1.setOrderCloseType(2);
//                        byOrders1.setGmtUpdate(new Date());
//                        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                    }
                    byOrderGoods2.setIsAfterSale(false);
                    this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
                }
            }
        }
        byOrderAfter.setAuditTime(new Date());
        byOrderAfter.setGmtUpdate(new Date());
        int i = byOrderAfterMapper.updateByPrimaryKeySelective(byOrderAfter);
        if (i < 1) {
            throw new CustomException("审核操作失败");
        }

    }

    /**
     * 查询该订单可以退多少件，退多少钱，后台管理员退款专用！！
     * @param byOrderAfter
     * @return
     */
    @Override
    public OrderAfterDetailVO queryAfterByOrderId(ByOrderAfter byOrderAfter) {
        if (byOrderAfter.getOrderGoodsId() == null || byOrderAfter.getResouceType() == null){
            throw new CustomException("参数错误");
        }

        /*普通商品*/
        OrderAfterDetailVO orderAfterDetailVO = new OrderAfterDetailVO();
        if (byOrderAfter.getResouceType() == 1) {
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                    .andEqualTo("orderType", 0)
                    .andEqualTo("orderState", 0);
            //没有核销码就代表没有可售后
            List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
//            if (writeOffCodes.size() == 0) throw new CustomException("商品正在售后中");
            if (CollectionUtils.isEmpty(writeOffCodes)){
                throw new CustomException("暂无商品可售后");
            }
            //返回出去的订单售后
            orderAfterDetailVO.setResouceType(1);
//            orderAfterDetailVO.setResouceType(byOrderAfter.getResouceType());
            //查询订单商品
            ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getOrderGoodsId());
            orderAfterDetailVO.setProductType(byOrderAfter.getProductType());
            orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getGoodsId());
            //设置商品单价
            orderAfterDetailVO.setGoodsPrice(byOrderGoods.getGoodsPrice());
            //查询订单
            ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(byOrderGoods.getOrderId());
            if(ObjectUtil.isNotEmpty(byOrders.getIsChannel()) && byOrders.getIsChannel()==1){
                throw new CustomException("渠道订单不支持售后退款");
            }
            if (byOrders == null || byOrders.getActualAmount().compareTo(new BigDecimal(0)) == 0) {
                throw new CustomException("该商品不能售后");
            }


            Integer goodsCount = byOrderGoods.getGoodsNum();
            //已经售后过的商品数量
            Example exampleAfter = new Example(ByOrderAfter.class);
            exampleAfter.createCriteria()
                    .andEqualTo("detailId",byOrderAfter.getDetailId())
                    .andEqualTo("afterStatus",1);
            List<ByOrderAfter> afters = byOrderAfterMapper.selectByExample(exampleAfter);
            for (ByOrderAfter after : afters) {
                if (after.getGoodsNum()!=null){
                    goodsCount -= after.getGoodsNum();
                }
            }


            if(goodsCount <= 0) {
                throw new CustomException("可退商品数量异常,不可退款");
            }


            orderAfterDetailVO.setDetailId(byOrderGoods.getId());
            if ("1".equals(byOrderGoods.getProductType())) {
                Integer orderCount = byOrderGoods.getGoodsNum();
//                byOrderGoods.setGoodsNum(goodsCount);
                //金额比
                BigDecimal proportion = new BigDecimal(orderCount).divide(new BigDecimal(orderCount),2,BigDecimal.ROUND_HALF_DOWN);
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(goodsCount));
                BigDecimal proportionCoupon = byOrderGoods.getCouponPrice().multiply(proportion);
                BigDecimal subtract = multiply.subtract(proportionCoupon);
                BigDecimal proportIntegral = byOrderGoods.getIntegralPrice().multiply(proportion);
                BigDecimal subtract1 = subtract.subtract(proportIntegral);
                BigDecimal divide = subtract1;
                orderAfterDetailVO.setAfterAmount(divide);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(goodsCount);
                orderAfterDetailVO.setOrderNo(byOrderGoods.getOrderNo());
            }
            if (!"1".equals(byOrderGoods.getProductType())) {
//                byOrderGoods.setGoodsNum(goodsCount);
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(goodsCount));
                BigDecimal subtract = multiply.subtract((byOrderGoods.getCouponPrice().add(byOrderGoods.getIntegralPrice())));
                orderAfterDetailVO.setAfterAmount(subtract);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(goodsCount);
                orderAfterDetailVO.setOrderNo(byOrderGoods.getOrderNo());

            }
        }
        if (byOrderAfter.getResouceType() == 2) {
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getOrderGoodsId())
                    .andNotEqualTo("status", 0)
                    .andEqualTo("orderType", 1)
                    .andEqualTo("orderState", 0);
            List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
            if (writeOffCodes.size() != 0) throw new CustomException("商品已核销，不可通过售后申请");

            orderAfterDetailVO.setProductType(4);
            Example example11 = new Example(ByOrderAfter.class);
            example11.createCriteria().andEqualTo("detailId", byOrderAfter.getOrderGoodsId()).andEqualTo("resouceType", 2).andEqualTo("afterStatus", 0);
            List<ByOrderAfter> byOrderAfters1 = this.byOrderAfterMapper.selectByExample(example11);
            if (byOrderAfters1.size() != 0) throw new CustomException("商品正在售后中,不可售后");


            ByTeamOrder byTeamOrder = byTeamOrderMapper.selectByPrimaryKey(byOrderAfter.getOrderId());
            if (byTeamOrder.getPayAmount().compareTo(new BigDecimal(0)) == 0) {
                throw new CustomException("该商品不能售后");
            }
            orderAfterDetailVO.setAfterAmount(byTeamOrder.getPayAmount());
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
            orderAfterDetailVO.setGoodsImg(byTeamGoods.getGoodsImg());
            orderAfterDetailVO.setGoodsName(byTeamGoods.getTeamName());
            orderAfterDetailVO.setGoodsNum(writeOffCodes.size());
            orderAfterDetailVO.setOrderGoodsId(byTeamGoods.getId());
            orderAfterDetailVO.setOrderNo(byTeamOrder.getOrderNo());
            orderAfterDetailVO.setResouceType(byOrderAfter.getResouceType());
            orderAfterDetailVO.setGoodsNum(1);
        }
        //判断当前商品是否可以售后
        return orderAfterDetailVO;
    }

    /**
     * 根据核销卡进行退款
     * @param ids
     * @return
     */
    @Override
    public boolean returnByWrite(List<Integer> ids, Integer resouceType){
        //查询当前提交的id，有没有已经退款过的
        Example codeExample = new Example(WriteOffCode.class);
        codeExample.createCriteria()
                .andIn("id", ids);

        List<WriteOffCode> offCodes = writeOffCodeMapper.selectByExample(codeExample);
        Assert.isEmpty(offCodes, "没有对应的核销码");

        //如果有核销卡已经退过，不让退款

        offCodes.forEach( code -> Assert.eq(code.getOrderState(),1,"已经退款过了") );

        WriteOffCode tempCode = offCodes.get(0);
        Integer detailId = tempCode.getDetailId();
        String orderNo = tempCode.getOrderNo();
        int hopeRefund = ids.size();
        //查询是不是全部退款过
        boolean allRefund = this.isAllRefund( detailId );
        if (allRefund){
            throw new CustomException("已经全部退款过了");
        }


        //再次查询订单退款金额，查询还可以退多少件，每件多少钱
        ByOrderAfter tempAfter = ByOrderAfter.builder().orderGoodsId(detailId).resouceType(resouceType).detailId(detailId).build();
        OrderAfterDetailVO orderAfter = this.queryAfterByOrderId( tempAfter );
        orderAfter.setGoodsNum( hopeRefund );
        orderAfter.setAfterAmount( orderAfter.getGoodsPrice().multiply( BigDecimal.valueOf(hopeRefund) ) );
        orderAfter.setResouceType( resouceType );


        //查询当前订单有没有售后单
        Example exampleAfter = new Example(ByOrderAfter.class);
        //查询申请售后中的订单
        exampleAfter.createCriteria()
                .andEqualTo("detailId", detailId)
                .andEqualTo("afterStatus",0);
        List<ByOrderAfter> afters = byOrderAfterMapper.selectByExample(exampleAfter);
        if (!CollectionUtils.isEmpty(afters)){
            //有售后单，更新申请中的售后单
            log.info("--------------售后单个数"+afters.size());
            afters.forEach(
                    byOrderAfter -> byOrderAfterMapper.updateByPrimaryKeySelective(
                            ByOrderAfter
                                    .builder()
                                    .id(byOrderAfter.getId())
                                    .afterStatus(2)
                                    .auditTime(new Date())
                                    .refuseReason("管理员已经手动退款")
                                    .build()
                    )
            );
        }

        //退款的信息
        String refundOrderNo = null;
        String refundNo = null;
        BigDecimal totalFee = null;
        BigDecimal refundFee = null;

        //开始退款
        //检查商品是否可以退款
//        checkGoodsIsAfter(orderAfter);
        //新增一条已通过的售后记录
        byOrderAfterMapper.insert(
                ByOrderAfter
                        .builder()
                        .detailId( detailId )
                        .afterStatus(1)
                        .goodsNum(hopeRefund)
                        .resouceType( resouceType )
                        .afterReason("管理员主动进行退款")
                        .build()
        );
        if ( orderAfter.getResouceType() == 2) {
            Example example = new Example(ByTeamOrder.class);
            example.createCriteria().andEqualTo("orderNo", orderNo);
            List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
            if (byTeamOrders.size() == 0) {
                throw new CustomException("拼团订单不存在");
            }
            ByTeamOrder byTeamOrder = byTeamOrders.get(0);
            Calendar instance = Calendar.getInstance();
            instance.setTime(new Date());
            refundOrderNo = orderNo;
            refundNo = String.valueOf(instance.getTime().getTime());
            totalFee = byTeamOrder.getPayAmount();
            refundFee = orderAfter.getAfterAmount();


//            refundPay(orderAfter.getOrderNo(),String.valueOf(instance.getTime().getTime()),byTeamOrder.getPayAmount(),orderAfter.getAfterAmount());
//                refundPay(oldAfter.getOrderNo(), String.valueOf(instance.getTime().getTime()), new BigDecimal(0.01), new BigDecimal(0.01));
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria().andEqualTo("detailId", orderAfter.getDetailId());
            this.writeOffCodeMapper.deleteByExample(example1);
            byTeamOrder.setOrderCloseType(2);
            byTeamOrder.setOrderStatus(4);
            this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
        }
        else {
            Example example = new Example(ByOrders.class);
            example.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo());
            List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example);
            //查询对应的订单是否存在
            if (byOrders.size() == 0) {
                throw new CustomException("订单不存在");
            }
            ByOrders byOrders1 = byOrders.get(0);
            //退款金额不为0才进行退款
            if (orderAfter.getAfterAmount() != null && orderAfter.getAfterAmount().compareTo(new BigDecimal(0)) != 0) {
//                refundPay(orderAfter.getOrderNo(),String.valueOf(System.currentTimeMillis()),byOrders1.getActualAmount(), orderAfter.getAfterAmount());

                //获得商家订单号
                refundOrderNo = byOrders1.getPayOrderNo();
                totalFee = byOrders1.getActualAmount();

                if ( refundOrderNo==null ){
                    //以前的订单，payOrderNo是空，订单号就是商家订单号
                    refundOrderNo = byOrders1.getOrderNo();
                }else if ( !Objects.equals(refundOrderNo, byOrders1.getOrderNo()) ){
                    //如果订单号 和 商家订单号不一致。代表有父订单，是和淘潮一起混合支付的
                    //查询父订单的金额
                    TcOrderParent parentOrder = tcOrderParentMapper.findByOrderParentNo(refundOrderNo);
                    totalFee = parentOrder.getPayAmount();
                }
                refundNo = String.valueOf(System.currentTimeMillis());

                refundFee = orderAfter.getAfterAmount();
            }

            /*查看订单*/
            Example example1 = new Example(ByOrderGoods.class);
            example1.createCriteria()
                    //订单号和退款的订单号必须匹配
                    .andEqualTo("orderNo", orderAfter.getOrderNo())
                    .andEqualTo("id", orderAfter.getDetailId());
            List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
            if (byOrderGoods.size() == 0) {
                throw new CustomException("订单不存在");
            }
            /*返回库存*/
            ByOrderGoods byOrderGoods2 = byOrderGoods.get(0);
            //普通商品
            if (byOrderGoods2.getProductType().equals("1")) {
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                byGoodsInfo.setGoodsStock(orderAfter.getGoodsNum() + byGoodsInfo.getGoodsStock());
                this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);

//                int i = this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffByIds(orderAfter.getDetailId(), 0, ids);
                byGoodsInfo.setActualSalesNum(orderAfter.getGoodsNum() + byGoodsInfo.getActualSalesNum());
                this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
            }
            if (byOrderGoods2.getProductType().equals("2")) {
                BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                bySubCardGoods.setGoodsStock(orderAfter.getGoodsNum() + bySubCardGoods.getGoodsStock());
                this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
//                this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffByIds(orderAfter.getDetailId(), 0, ids);
                bySubCardGoods.setActualSalesNum(orderAfter.getGoodsNum() + bySubCardGoods.getActualSalesNum());
                this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
            }
            if (byOrderGoods2.getProductType().equals("3")) {
                ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                byTicketGoods.setGoodsStock(orderAfter.getGoodsNum() + byTicketGoods.getGoodsStock());
                this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
//                this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffByIds(orderAfter.getDetailId(), 0, ids);
                byTicketGoods.setActualSalesNum(orderAfter.getGoodsNum() + byTicketGoods.getActualSalesNum());
                this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
            }
            if (byOrderGoods2.getProductType().equals("5")) {
                ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                combinationGoods.setGoodsStock(orderAfter.getGoodsNum() + combinationGoods.getGoodsStock());
                this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
//                int i = this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffByIds(orderAfter.getDetailId(), 0, ids);
                combinationGoods.setActualSalesNum(orderAfter.getGoodsNum() + combinationGoods.getActualSalesNum());
                this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
            }


            //查询已经同意的售后，需要的是一共退款了多少个
            List<ByOrderAfter> refundAfters = byOrderAfterMapper.select(
                    ByOrderAfter.builder()
                            .detailId(orderAfter.getDetailId())
                            .afterStatus(1)
                            .build()
            );

            int realRefundCount = 0;
            for (ByOrderAfter after : refundAfters) {
                realRefundCount += after.getGoodsNum();
            }


            //是否全部退款
            if ( Objects.equals(realRefundCount, byOrderGoods2.getGoodsNum()) ) {
                byOrderGoods2.setIsAfterSale(false);
                int i = this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
                //查询订单关联 是否包含售后订单
                Example example11 = new Example(ByOrderGoods.class);
                example11.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo()).andEqualTo("isAfterSale", true);
                List<ByOrderGoods> byOrderGoods1 = this.byOrderGoodsMapper.selectByExample(example11);
                //判断 订单下是否还有有未关联 售后订单
                if (CollectionUtils.isEmpty(byOrderGoods1) || byOrderGoods1.size() == 0) {
                    //当前订单里没有可售后的订单了
                    //效验未完成
                    if (orderAfter.getResouceType().equals(2)) {
                        //拼团订单 直接售后通过变成 已关闭
                        byOrders1.setOrderStatus(4);
                    } else {
                        //普通订单 需要判断核销情况
                        Example orderExample = new Example(WriteOffCode.class);
                        orderExample.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo())
                                //未核销
                                .andEqualTo("orderState", 0)
                                //普通订单
                                .andEqualTo("orderType", orderAfter.getResouceType().equals(1) ? 0 : 1);
                        int resultNum = writeOffCodeMapper.selectCountByExample(orderExample);
                        //如果没有未核销订单
                        if (resultNum == 0) {
                            //关联订单 查询无 售后订单  变成已关闭
                            log.debug("关联订单 查询无 售后订单  变成已关闭 resultNum:{}", resultNum);
                            byOrders1.setOrderStatus(4);
                            byOrders1.setGmtUpdate(new Date());
                            this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                        } else {
                            log.info("进入 变更订单方法------------------------------------");
                            WriteOffCode code = new WriteOffCode();
                            code.setOrderNo(orderAfter.getOrderNo());
                            code.setDetailId(orderAfter.getDetailId());
                            code.setOrderType(orderAfter.getResouceType().equals(1) ? 0 : 1);
                            //效验订单 是否被核销完成 若是 则更新状态未 已完成  否则原状态
                            log.info("auditAfterOrder（）  WriteOffCode：{}", code);
                            checkOrderWriteCode(code);
                        }

                    }
//                        byOrders1.setOrderCloseType(2);
//                        byOrders1.setGmtUpdate(new Date());
//                        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                }
                byOrderGoods2.setIsAfterSale(false);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
            }
        }


        refundPay(refundOrderNo, refundNo, totalFee, refundFee);

        //记录日志
        ByOrderLog orderLog = ByOrderLog.builder()
                .operatorType(2)
                .logType(OrderLogType.ADMIN_REFUND)
                .operatorCount( hopeRefund )
                .orderGoodsId( detailId )
                .build();
        orderLogService.addOrderLogAsync(orderLog);

        return true;
    }


    /**
     * 退款逻辑
     * @param paramOrderAfter
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnSingeOrder(ByOrderAfter paramOrderAfter) {
        Integer hopeRefund = paramOrderAfter.getGoodsNum();
        //再次查询订单退款金额
        OrderAfterDetailVO orderAfter = this.queryAfterByOrderId(paramOrderAfter);
        orderAfter.setGoodsNum( hopeRefund );
        orderAfter.setAfterAmount( orderAfter.getGoodsPrice().multiply( BigDecimal.valueOf(hopeRefund) ) );
//        orderAfter.setDetailId(orderAfter.getDetailId());
        orderAfter.setResouceType(paramOrderAfter.getResouceType());

        //查询是不是全部退款过
        boolean allRefund = this.isAllRefund(paramOrderAfter.getDetailId());
        if (allRefund){
            throw new CustomException("已经全部退款过了");
        }

        //查询当前订单有没有售后单
        log.info(paramOrderAfter.toString());
        Example exampleAfter = new Example(ByOrderAfter.class);
        //查询申请售后中的订单
        exampleAfter.createCriteria()
                .andEqualTo("detailId",paramOrderAfter.getDetailId())
                .andEqualTo("afterStatus",0);
        List<ByOrderAfter> afters = byOrderAfterMapper.selectByExample(exampleAfter);
        if (!CollectionUtils.isEmpty(afters)){
            //有售后单，更新申请中的售后单
            log.info("--------------售后单个数"+afters.size());
            afters.forEach(
                    byOrderAfter -> byOrderAfterMapper.updateByPrimaryKeySelective(
                            ByOrderAfter
                                    .builder()
                                    .id(byOrderAfter.getId())
                                    .afterStatus(2)
                                    .auditTime(new Date())
                                    .refuseReason("管理员已经手动退款")
                                    .build()
                    )
            );
        }

        //退款的信息
        String refundOrderNo = null;
        String refundNo = null;
        BigDecimal totalFee = null;
        BigDecimal refundFee = null;

        //开始退款
        //检查商品是否可以退款
//        checkGoodsIsAfter(orderAfter);
        //新增一条已通过的售后记录
        byOrderAfterMapper.insert(
                ByOrderAfter
                        .builder()
                        .detailId(orderAfter.getDetailId())
                        .afterStatus(1)
                        .goodsNum(hopeRefund)
                        .resouceType(orderAfter.getResouceType())
                        .afterReason("管理员主动进行退款")
                        .build()
        );

        if (orderAfter.getResouceType() == 2) {
            Example example = new Example(ByTeamOrder.class);
            example.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo());
            List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example);
            if (byTeamOrders.size() == 0) {
                throw new CustomException("拼团订单不存在");
            }
            ByTeamOrder byTeamOrder = byTeamOrders.get(0);
            Calendar instance = Calendar.getInstance();
            instance.setTime(new Date());
            refundOrderNo = orderAfter.getOrderNo();
            refundNo = String.valueOf(instance.getTime().getTime());
            totalFee = byTeamOrder.getPayAmount();
            refundFee = orderAfter.getAfterAmount();


//            refundPay(orderAfter.getOrderNo(),String.valueOf(instance.getTime().getTime()),byTeamOrder.getPayAmount(),orderAfter.getAfterAmount());
//                refundPay(oldAfter.getOrderNo(), String.valueOf(instance.getTime().getTime()), new BigDecimal(0.01), new BigDecimal(0.01));
            Example example1 = new Example(WriteOffCode.class);
            example1.createCriteria().andEqualTo("detailId", orderAfter.getDetailId());
            this.writeOffCodeMapper.deleteByExample(example1);
            byTeamOrder.setOrderCloseType(2);
            byTeamOrder.setOrderStatus(4);
            this.byTeamOrderMapper.updateByPrimaryKeySelective(byTeamOrder);
        }
        else {
            Example example = new Example(ByOrders.class);
            example.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo());
            List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(example);
            //查询对应的订单是否存在
            if (byOrders.size() == 0) {
                throw new CustomException("订单不存在");
            }
            ByOrders byOrders1 = byOrders.get(0);
            //退款金额不为0才进行退款
            if (orderAfter.getAfterAmount() != null && orderAfter.getAfterAmount().compareTo(new BigDecimal(0)) != 0) {
//                refundPay(orderAfter.getOrderNo(),String.valueOf(System.currentTimeMillis()),byOrders1.getActualAmount(), orderAfter.getAfterAmount());

                //获得商家订单号
                refundOrderNo = byOrders1.getPayOrderNo();
                totalFee = byOrders1.getActualAmount();

                if ( refundOrderNo==null ){
                    //以前的订单，payOrderNo是空，订单号就是商家订单号
                    refundOrderNo = byOrders1.getOrderNo();
                }else if ( !Objects.equals(refundOrderNo, byOrders1.getOrderNo()) ){
                    //如果订单号 和 商家订单号不一致。代表有父订单，是和淘潮一起混合支付的
                    //查询父订单的金额
                    TcOrderParent parentOrder = tcOrderParentMapper.findByOrderParentNo(refundOrderNo);
                    totalFee = parentOrder.getPayAmount();
                }
                refundNo = String.valueOf(System.currentTimeMillis());

                refundFee = orderAfter.getAfterAmount();
            }

            /*查看订单*/
            Example example1 = new Example(ByOrderGoods.class);
            example1.createCriteria()
                    //订单号和退款的订单号必须匹配
                    .andEqualTo("orderNo", orderAfter.getOrderNo())
                    .andEqualTo("id", orderAfter.getDetailId());
            List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);
            if (byOrderGoods.size() == 0) {
                throw new CustomException("订单不存在");
            }
            /*返回库存*/
            ByOrderGoods byOrderGoods2 = byOrderGoods.get(0);
            //普通商品
            if (byOrderGoods2.getProductType().equals("1")) {
                ByGoodsInfo byGoodsInfo = this.byGoodsInfoMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                byGoodsInfo.setGoodsStock(orderAfter.getGoodsNum() + byGoodsInfo.getGoodsStock());
                this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);

//                int i = this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffLimit(orderAfter.getDetailId(), 0, hopeRefund);
                byGoodsInfo.setActualSalesNum(orderAfter.getGoodsNum() + byGoodsInfo.getActualSalesNum());
                this.byGoodsInfoMapper.updateByPrimaryKeySelective(byGoodsInfo);
            }
            if (byOrderGoods2.getProductType().equals("2")) {
                BySubCardGoods bySubCardGoods = this.bySubCardGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                bySubCardGoods.setGoodsStock(orderAfter.getGoodsNum() + bySubCardGoods.getGoodsStock());
                this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
//                this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffLimit(orderAfter.getDetailId(), 0, hopeRefund);
                bySubCardGoods.setActualSalesNum(orderAfter.getGoodsNum() + bySubCardGoods.getActualSalesNum());
                this.bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
            }
            if (byOrderGoods2.getProductType().equals("3")) {
                ByTicketGoods byTicketGoods = this.byTicketGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                byTicketGoods.setGoodsStock(orderAfter.getGoodsNum() + byTicketGoods.getGoodsStock());
                this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
//                this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffLimit(orderAfter.getDetailId(), 0, hopeRefund);
                byTicketGoods.setActualSalesNum(orderAfter.getGoodsNum() + byTicketGoods.getActualSalesNum());
                this.byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
            }
            if (byOrderGoods2.getProductType().equals("5")) {
                ByCombinationGoods combinationGoods = this.byCombinationGoodsMapper.selectByPrimaryKey(byOrderGoods2.getProductId());
                combinationGoods.setGoodsStock(orderAfter.getGoodsNum() + combinationGoods.getGoodsStock());
                this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
//                int i = this.writeOffCodeMapper.updateWiff(orderAfter.getDetailId(), 0);
                this.writeOffCodeMapper.updateWiffLimit(orderAfter.getDetailId(), 0, hopeRefund);
                combinationGoods.setActualSalesNum(orderAfter.getGoodsNum() + combinationGoods.getActualSalesNum());
                this.byCombinationGoodsMapper.updateByPrimaryKeySelective(combinationGoods);
            }


            //查询已经同意的售后，需要的是一共退款了多少个
            List<ByOrderAfter> refundAfters = byOrderAfterMapper.select(
                    ByOrderAfter.builder()
                            .detailId(orderAfter.getDetailId())
                            .afterStatus(1)
                            .build()
            );

            int realRefundCount = 0;
            for (ByOrderAfter after : refundAfters) {
                realRefundCount += after.getGoodsNum();
            }


            //是否全部退款
            if ( Objects.equals(realRefundCount, byOrderGoods2.getGoodsNum()) ) {
                byOrderGoods2.setIsAfterSale(false);
                int i = this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
                //查询订单关联 是否包含售后订单
                Example example11 = new Example(ByOrderGoods.class);
                example11.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo()).andEqualTo("isAfterSale", true);
                List<ByOrderGoods> byOrderGoods1 = this.byOrderGoodsMapper.selectByExample(example11);
                //判断 订单下是否还有有未关联 售后订单
                if (CollectionUtils.isEmpty(byOrderGoods1) || byOrderGoods1.size() == 0) {
                    //当前订单里没有可售后的订单了
                    //效验未完成
                    if (orderAfter.getResouceType().equals(2)) {
                        //拼团订单 直接售后通过变成 已关闭
                        byOrders1.setOrderStatus(4);
                    } else {
                        //普通订单 需要判断核销情况
                        Example orderExample = new Example(WriteOffCode.class);
                        orderExample.createCriteria().andEqualTo("orderNo", orderAfter.getOrderNo())
                                //未核销
                                .andEqualTo("orderState", 0)
                                //普通订单
                                .andEqualTo("orderType", orderAfter.getResouceType().equals(1) ? 0 : 1);
                        int resultNum = writeOffCodeMapper.selectCountByExample(orderExample);
                        //如果没有未核销订单
                        if (resultNum == 0) {
                            //关联订单 查询无 售后订单  变成已关闭
                            log.debug("关联订单 查询无 售后订单  变成已关闭 resultNum:{}", resultNum);
                            byOrders1.setOrderStatus(4);
                            byOrders1.setGmtUpdate(new Date());
                            this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                        } else {
                            log.info("进入 变更订单方法------------------------------------");
                            WriteOffCode code = new WriteOffCode();
                            code.setOrderNo(orderAfter.getOrderNo());
                            code.setDetailId(orderAfter.getDetailId());
                            code.setOrderType(orderAfter.getResouceType().equals(1) ? 0 : 1);
                            //效验订单 是否被核销完成 若是 则更新状态未 已完成  否则原状态
                            log.info("auditAfterOrder（）  WriteOffCode：{}", code);
                            checkOrderWriteCode(code);
                        }

                    }
//                        byOrders1.setOrderCloseType(2);
//                        byOrders1.setGmtUpdate(new Date());
//                        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders1);
                }
                byOrderGoods2.setIsAfterSale(false);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods2);
            }
        }


        refundPay(refundOrderNo, refundNo, totalFee, refundFee);

        return true;
    }

    /**
     * 查询某个订单是否全部退款
     * @param detailId
     * @return
     */
    @Override
    public boolean isAllRefund(Integer detailId) {
        ByOrderGoods orderGoods = byOrderGoodsMapper.selectByPrimaryKey(detailId);

        Example example = new Example(ByOrderAfter.class);
        example.createCriteria()
                .andEqualTo("detailId",detailId)
                .andEqualTo("afterStatus",1);
        List<ByOrderAfter> afters = byOrderAfterMapper.selectByExample(example);

        Integer count = 0;
        for (ByOrderAfter after : afters) {
            count += after.getGoodsNum();
        }

        //售后同意商品数量和订单商品数量一样，代表全部退款
        return orderGoods.getGoodsNum().compareTo(count) == 0;
    }


    /**
     * 效验商品是否可以售后
     *
     * @param byOrderAfter
     */
    private void checkGoodsIsAfter(ByOrderAfter byOrderAfter) {
        log.info("checkGoodsIsAfter()  getDetailId:{}" + byOrderAfter.getDetailId());
        log.info("checkGoodsIsAfter()  byOrderAfter:{}", byOrderAfter);
        ByOrderGoods goods = byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
        log.info("checkGoodsIsAfter()  goods.getProductType():{}", goods.getProductType());
        log.info("checkGoodsIsAfter()  check():{}", Integer.valueOf(goods.getProductType()).equals(BaoYanConstant.CONSTANT_TWO));
        //判断  商品属于哪种类型商品 (次卡商品 除外)
        if (Integer.valueOf(goods.getProductType()).equals(BaoYanConstant.CONSTANT_TWO)) {
            log.info("checkGoodsIsAfter()  次卡 ByOrderGoods():{}", goods);
            Example writeOffCodeExample = new Example(WriteOffCode.class);
            writeOffCodeExample.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getDetailId())
                    .andEqualTo("status", 0)
                    //当前位置 区分次卡情况 是否消耗完
                    .andEqualTo("orderType", byOrderAfter.getResouceType() == 1 ? 0 : 1);
            List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(writeOffCodeExample);
            if (!CollectionUtils.isEmpty(writeOffCodes)) {
                boolean flag = false;
                for (WriteOffCode code : writeOffCodes) {
                    //是否被使用了
                    if (code.getSurplusNum().intValue() < code.getTotalNum().intValue()) {
                        //核销 被使用
                        flag = true;
                    }
                    log.info("codeFlag()  codeFlag:{}", flag);
                }
                log.info("checkGoodsIsAfter() flag:{}",flag);
                if (flag) {
                    throw new CustomException("已使用商品,不可售后");
                }
            }
        } else {
            log.info("checkGoodsIsAfter()  普通商品 联票 ByOrderGoods():{}", goods);

            //判断商品是否 被使用核销
            Example writeOffCodeExample = new Example(WriteOffCode.class);
            writeOffCodeExample.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getDetailId())
                    .andEqualTo("status", 0)
                    //当前位置 区分次卡情况 是否消耗完
                    .andEqualTo("orderType", byOrderAfter.getResouceType() == 1 ? 0 : 1);
            List<WriteOffCode> writeOffCode = this.writeOffCodeMapper.selectByExample(writeOffCodeExample);
            Example writeOffCodeExample2 = new Example(WriteOffCode.class);
            writeOffCodeExample2.createCriteria()
                    .andEqualTo("detailId", byOrderAfter.getDetailId())
                    //当前位置 区分次卡情况 是否消耗完
                    .andEqualTo("orderType", byOrderAfter.getResouceType() == 1 ? 0 : 1)
                    .andEqualTo("isExpire", 1);
            List<WriteOffCode> writeOffCode2 = this.writeOffCodeMapper.selectByExample(writeOffCodeExample2);
            if (CollectionUtils.isEmpty(writeOffCode) && CollectionUtils.isEmpty(writeOffCode2)){
                throw new CustomException("已使用商品,不可售后");
            }
        }
    }

    @Autowired
    private WriteOffCodeMapper writeOffCodeMapper;
    @Autowired
    private ByTeamGoodsMapper byTeamGoodsMapper;



    public OrderAfterDetailVO queryAfterByOrderIdAndGoodsId(ByOrderAfter byOrderAfter) {
        ByCustUser user = this.byCustUserMapper.selectByPrimaryKey(byOrderAfter.getUserId());
        if (byOrderAfter.getOrderGoodsId() == null || byOrderAfter.getResouceType() == null){
            throw new CustomException("参数错误");
        }
       /* Example example = new Example(ByOrderAfter.class);
        example.createCriteria().andEqualTo("orderGoodsId", byOrderAfter.getDetailId())
                .andNotEqualTo("afterStatus", 2)
                .andEqualTo("resouceType", byOrderAfter.getResouceType())
                .andEqualTo("userId", user.getId());
        List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example);
        if (byOrderAfters.size() != 0) throw new CustomException("不能重复申请售后");*/
        Example example1 = new Example(WriteOffCode.class);
        example1.createCriteria()
                .andEqualTo("detailId", byOrderAfter.getDetailId())
                .andEqualTo("custUserId", user.getId())
                .andEqualTo("status", 0)
                .andEqualTo("orderType", byOrderAfter.getResouceType() == 1 ? 0 : 1);
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example1);
        if (writeOffCodes.size() == 0) throw new CustomException("该商品已使用完成，不可进行售后");
        /*普通商品*/
        OrderAfterDetailVO orderAfterDetailVO = new OrderAfterDetailVO();
        if (byOrderAfter.getResouceType() == 1) {
            orderAfterDetailVO.setResouceType(1);
            ByOrderGoods byOrderGoods = byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
            if (!"3".equals(byOrderGoods.getProductType())) {
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(byOrderGoods.getGoodsNum()));
                BigDecimal subtract = multiply.subtract(byOrderGoods.getCouponPrice());
                BigDecimal subtract1 = subtract.subtract(byOrderGoods.getIntegralPrice());
                //当前逻辑 候补 坑啊  总金额 -积分  -优惠券
              /*  BigDecimal divide = subtract1.multiply(new BigDecimal(writeOffCodes.size())).divide(new BigDecimal(byOrderGoods.getGoodsNum()), 2, BigDecimal.ROUND_HALF_DOWN);*/
                orderAfterDetailVO.setAfterAmount(subtract1);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(writeOffCodes.size());
            }
            if ("3".equals(byOrderGoods.getProductType())) {
                Integer count = this.writeOffCodeMapper.selectList(byOrderAfter.getDetailId());
                Integer integer = this.writeOffCodeMapper.selectCountList(byOrderAfter.getDetailId());
                if (integer.equals(byOrderGoods.getGoodsNum())) {
                    throw new CustomException("已核销完成不能退款");
                }
                //当前逻辑 候补 坑啊  总金额 -积分  -优惠券
                BigDecimal multiply = byOrderGoods.getGoodsPrice().multiply(new BigDecimal(byOrderGoods.getGoodsNum()));
                BigDecimal subtract = multiply.subtract(byOrderGoods.getCouponPrice());
                BigDecimal subtract1 = subtract.subtract(byOrderGoods.getIntegralPrice());
               /* BigDecimal divide = subtract1.multiply(new BigDecimal(count)).divide(new BigDecimal(byOrderGoods.getGoodsNum()), 2, BigDecimal.ROUND_HALF_DOWN);*/
                orderAfterDetailVO.setAfterAmount(subtract1);
                orderAfterDetailVO.setGoodsImg(byOrderGoods.getGoodsImg());
                orderAfterDetailVO.setGoodsName(byOrderGoods.getGoodsName());
                orderAfterDetailVO.setOrderGoodsId(byOrderGoods.getId());
                orderAfterDetailVO.setGoodsNum(count.intValue());
            }
        }
        if (byOrderAfter.getResouceType() == 2) {
            ByTeamOrder byTeamOrder = byTeamOrderMapper.selectByPrimaryKey(byOrderAfter.getDetailId());
            orderAfterDetailVO.setAfterAmount(byTeamOrder.getPayAmount());
            ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(byTeamOrder.getTeamGoodsId());
            orderAfterDetailVO.setGoodsImg(byTeamGoods.getGoodsImg());
            orderAfterDetailVO.setGoodsName(byTeamGoods.getTeamName());
            orderAfterDetailVO.setGoodsNum(writeOffCodes.size());
        }
        //判断当前商品是否可以售后
        return orderAfterDetailVO;
    }

    @Resource
    private WechatProperties wechatProperties;
    @Resource
    private WepayService wepayService;

    /**
     * <AUTHOR>
     * @date 2019/8/12 11:15
     * @Description: 微信退款
     */
    @Override
    public void refundPay(String orderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee) {
        Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
        RefundApplyRequest request = new RefundApplyRequest();
        request.setOutTradeNo(orderNo);
        request.setOutRefundNo(refundNo);
        request.setTotalFee( totalFee.multiply(CommonFinal.BIG_100).intValue() );
        request.setRefundFee( refundFee.multiply(CommonFinal.BIG_100).intValue() );
        request.setOpUserId("1");
        try {
            wepay.refund().apply(request);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    private void get(ByOrderAfter byOrderAfter) {
        ByOrderAfter byOrderAfter1 = byOrderAfterMapper.selectByPrimaryKey(byOrderAfter.getId());
        if (byOrderAfter1.getResouceType() != 2) {
            ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(byOrderAfter1.getDetailId());
            ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(byOrderGoods.getOrderId());
            if (byOrders.getIntegralAmount().compareTo(new BigDecimal(0)) == 0) return;
            Example example2 = new Example(ByOrderGoods.class);
            example2.createCriteria().andEqualTo("orderId", byOrders.getId());
            List<ByOrderGoods> byOrderGoodss = this.byOrderGoodsMapper.selectByExample(example2);

            ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
            if (byOrderGoodss.size() != 0 && byPlatformSet != null && byPlatformSet.getIntegralReturn() != null && byPlatformSet.getIntegralReturn().compareTo(new BigDecimal(0)) != 0 && byOrders.getIntegralAmount().compareTo(new BigDecimal(0)) != 0) {
                BigDecimal bigDecimal = new BigDecimal(0);
                for (ByOrderGoods byOrderGood : byOrderGoodss) {
                    BigDecimal multiply = byOrderGood.getIntegralPrice().multiply(new BigDecimal(byOrderGood.getGoodsNum()));
                    BigDecimal subtract = multiply.subtract(byOrderGood.getIntegralPrice());
                    bigDecimal = bigDecimal.add(subtract);
                    Example example = new Example(WriteOffCode.class);
                    example.createCriteria().andEqualTo("detailId", byOrderGood.getId()).andEqualTo("status", 0);
                    List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
                    if (writeOffCodes.size() != 0) {
                        continue;
                    }
                }
            }
        } else {
            ByTeamOrder byOrders1 = this.byTeamOrderMapper.selectByPrimaryKey(byOrderAfter1.getDetailId());
            if (byOrders1.getIntegralAmount() != null && byOrders1.getIntegralAmount().compareTo(new BigDecimal(0)) != 0) {
                ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
                if (byOrders1.getIntegralAmount() != null && byPlatformSet.getCommentIntegral() != null && byPlatformSet.getCommentIntegral().equals(0) == false) {
                    Integer coun = byOrders1.getIntegralAmount().multiply(byPlatformSet.getIntegralReturn()).intValue();
                    ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(byOrders1.getUserId());
                    ByIntegralLog byIntegralLog = new ByIntegralLog();
                    byIntegralLog.setUserId(byOrders1.getUserId());
                    byIntegralLog.setChangeType(1);
                    byIntegralLog.setChangeNum(coun);
                    byIntegralLog.setChangeReason("拼团");
                    byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                    byIntegralLog.setGmtCreate(new Date());
                    byIntegralLog.setIntegralType(1);
                    if (byIntegralLog.getChangeNum() > 0) {
                        byIntegralLogMapper.insertSelective(byIntegralLog);
                    }
                    this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(),coun);
                    this.byIntegralLogMapper.insertSelective(byIntegralLog);
                }
            }
        }
    }


    /**
     * check 订单是否全部核销完成 若是的话 订单变成已完成状态
     *
     * @param writeOffCode1
     */
    private void checkOrderWriteCode(WriteOffCode writeOffCode1) {
        //判断订单类型
        if (writeOffCode1.getOrderType().equals(0)) {
            //普通  获取核销表 所有关联当前订单 核销记录 (不包含已过期)
            Example orderExample = new Example(WriteOffCode.class);
            orderExample.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo()).andNotEqualTo("status", BaoYanConstant.CONSTANT_TWO).andEqualTo("orderState", 0);
            List<WriteOffCode> writeList = writeOffCodeMapper.selectByExample(orderExample);
            if (null != writeList && writeList.size() > 0) {
                boolean falg = false;
                for (WriteOffCode code : writeList) {
                    //效验剩余核销码 是否核销完
                    if (code.getSurplusNum() > 0) {
                        falg = true;
                    }
                }
                if (!falg) {
                    Example orderReqExample = new Example(ByOrders.class);
                    orderReqExample.createCriteria().andEqualTo("orderNo", writeOffCode1.getOrderNo());
                    ByOrders byOrders = new ByOrders();
                    byOrders.setOrderStatus(BaoYanConstant.CONSTANT_THREE);//已完成
                    byOrders.setGmtUpdate(new Date());
                    byOrders.setOrderCloseType(2);
                    byOrders.setGmtUpdate(new Date());
                    byOrdersMapper.updateByExampleSelective(byOrders, orderReqExample);
                    scheduler.get(writeOffCode1.getDetailId(), 1);
                }
            }
            ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
            byOrderGoods.setIsAfterSale(false);
            this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
        } else {
            WriteOffCode writeOff = this.writeOffCodeMapper.selectByPrimaryKey(writeOffCode1.getId());
            //拼团订单（拼团订单 默认一笔订单插入一条 核销码记录）
            //查询核销余量或者 是否过期
            if (writeOff.getStatus().equals(BaoYanConstant.CONSTANT_TWO)) {
                //更新 当前拼团订单已完成
                Example teamExample = new Example(ByTeamOrder.class);
                teamExample.createCriteria().andEqualTo("orderNo", writeOff.getOrderNo());
                ByTeamOrder byTeamOrder = new ByTeamOrder();
                byTeamOrder.setOrderStatus(BaoYanConstant.CONSTANT_THREE);//已完成
                byTeamOrderMapper.updateByExampleSelective(byTeamOrder, teamExample);
                scheduler.get(writeOffCode1.getDetailId(), 2);
                ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(writeOffCode1.getDetailId());
                byOrderGoods.setIsAfterSale(false);
                this.byOrderGoodsMapper.updateByPrimaryKeySelective(byOrderGoods);
            }
            scheduler.get(writeOffCode1.getDetailId(), 2);
        }

    }


}
