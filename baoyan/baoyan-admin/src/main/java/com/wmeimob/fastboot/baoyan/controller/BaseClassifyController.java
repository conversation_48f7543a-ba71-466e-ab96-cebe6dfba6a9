package com.wmeimob.fastboot.baoyan.controller;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.BaseClassify;
import com.wmeimob.fastboot.baoyan.service.BaseClassifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName BaseClassifyController
 * @Description 【分类表】控制器
 * <AUTHOR>
 * @Date Fri Jul 12 10:13:14 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("baseclassify")
@Slf4j
public class BaseClassifyController {

    @Resource
    private BaseClassifyService baseClassifyService;




    /**
     * 分类表分页查询
     * @param request
     * @param baseClassify
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseClassify(HttpServletRequest request, BaseClassify baseClassify){
        PageContext.startPage();
        return new PageInfo<BaseClassify>(baseClassifyService.findByCondition(baseClassify));
         
    }

     /**
     * 分类表导出
     * @param request
     * @param baseClassify
     * @return
     */
    @GetMapping("/exports")
    public List<BaseClassify> queryForBaseClassifyexports(HttpServletRequest request, BaseClassify baseClassify){
        return  baseClassifyService.findByCondition(baseClassify);
    }


    /**
     * 分类表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseClassify queryForBaseClassifyById(HttpServletRequest request, @PathVariable("id") Object id){
            return  baseClassifyService.queryBaseClassifyById(id);
    }


    /**
     * 分类表添加
     * @param request
     * @param baseClassify
     * @return
     */
    @PostMapping("/")
    public void insertForBaseClassify(HttpServletRequest request,@RequestBody BaseClassify baseClassify){
            baseClassifyService.addBaseClassify(baseClassify);    
    }


    /**
     * 分类表修改
     * @param request
     * @param baseClassify
     * @return
     */
    @PutMapping("/")
    public void updateForBaseClassify(HttpServletRequest request,@RequestBody BaseClassify baseClassify){
            baseClassifyService.modifyBaseClassify(baseClassify);  
    }

    /**
     * 分类表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseClassify(HttpServletRequest request,@PathVariable("id") Integer id){
            baseClassifyService.removeBaseClassify(id);
    }

    /**
     * 修改分类状态
     */
    @PutMapping("/state")
    public void updateState(HttpServletRequest request,@RequestBody BaseClassify baseClassify){
        baseClassifyService.updateState(baseClassify);
    }

}
