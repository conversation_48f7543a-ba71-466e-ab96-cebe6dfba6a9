package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.qo.BaseAdvertisingQo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 广告图片控制
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseAdvertisingServiceImpl implements BaseAdvertisingService {
    @Resource
    private BaseAdvertisingMapper baseAdvertisingMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByCouponMapper byCouponMapper;
    @Override
    public BaseAdvertising select() {
        return Optional.ofNullable(baseAdvertisingMapper.selectByPrimaryKey(1)).orElse(new BaseAdvertising()) ;
    }
    @Override
    public void update(BaseAdvertisingQo qo) {
        //效验参数
        checkParam(qo);
        //处理请求参数
        dealReqParam(qo);
        BaseAdvertising baseAdvertising = baseAdvertisingMapper.selectByPrimaryKey(1);
        if (qo.getStartTime() != null && qo.getEndTime() != null && qo.getEndTime().before(qo.getStartTime())){
            throw new CustomException("开始时间不能大于结束时间");
        }
        if (baseAdvertising != null){
            //修改
            if (StringUtils.isEmpty(qo.getImgUrl())){
                throw new CustomException("请上传广告图片");
            }
            qo.setGmtUpdate(new Date()).setId(1);
            baseAdvertisingMapper.updateByPrimaryKeySelective(qo);
        }else {
            //新增
            if (StringUtils.isEmpty(qo.getImgUrl())){
                throw new CustomException("请上传广告图片");
            }
            if (qo.getStartTime() == null || qo.getEndTime() == null){
                throw new CustomException("请上传在线时间");
            }
            if (qo.getStatus() == null || !Status.getStatus().contains(qo.getStatus())){
                throw new CustomException("请设置正确广告状态");
            }
            qo.setGmtCreate(new Date()).setId(1);
            baseAdvertisingMapper.insertSelective(qo);
        }
    }

    /***
     * 处理类型参数
     * @param baseAdvertising
     */
    private void dealReqParam(BaseAdvertisingQo baseAdvertising) {
        if (null != baseAdvertising.getJumpType()) {
            String target;
            switch (baseAdvertising.getJumpType()) {
                case 1:
                    target = String.valueOf(baseAdvertising.getClassifyId());
                    break;
                case 2:
                    target = baseAdvertising.getTargetImgUrl();
                    break;
                case 3:
                    target = baseAdvertising.getTargetImgUrl();
                    break;
                case 4:
                    target = baseAdvertising.getTargetImgUrl();
                    break;
                default:
                    target = baseAdvertising.getTarget();
                    break;
            }
            baseAdvertising.setTarget(target);
        }

    }



    /**
     * 效验参数
     *
     * @param baseAdvertising
     */
    private void checkParam(BaseAdvertisingQo baseAdvertising) {
        if (null != baseAdvertising.getJumpType()) {
            if (baseAdvertising.getJumpType().equals(1)) {
                //判断当前分类id是否存在
                if (null == baseAdvertising || null == baseAdvertising.getClassifyId() || StringUtils.isEmpty(baseAdvertising.getClassifyId())) {
                    throw new CustomException("请选择分类");
                }
            }
            if (baseAdvertising.getJumpType().equals(2)) {
                if (null == baseAdvertising || null == baseAdvertising.getTargetImgUrl()) {
                    throw new CustomException("请上传联票列表图片");
                }
            }
            if (baseAdvertising.getJumpType().equals(3)) {
                if (null == baseAdvertising || null == baseAdvertising.getTargetImgUrl()) {
                    throw new CustomException("请上传次卡列表图片");
                }
            }
            if (baseAdvertising.getJumpType().equals(4)) {
                if (null == baseAdvertising || null == baseAdvertising.getTargetImgUrl()) {
                    throw new CustomException("请上传拼团列表图片");
                }
            }

            if (baseAdvertising.getJumpType().equals(5)) {
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", baseAdvertising.getTarget());
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    throw new CustomException("文章不存在");
                }
            }
            if (baseAdvertising.getJumpType().equals(6)) {
                //判断当前商品普通商品存在
                Example example = new Example(ByGoodsInfo.class);
                example.createCriteria().andEqualTo("id", baseAdvertising.getTarget()).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(example);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }

            }
            if (baseAdvertising.getJumpType().equals(7)) {
                //判断当前联票是否存在
                Example example = new Example(ByTicketGoods.class);
                example.createCriteria().andEqualTo("id", baseAdvertising.getTarget()).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(example);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    throw new CustomException("联票商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseAdvertising.getJumpType().equals(8)) {
                //判断当前次卡商品是否存在
                Example example = new Example(BySubCardGoods.class);
                example.createCriteria().andEqualTo("id", baseAdvertising.getTarget()).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(example);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    throw new CustomException("次卡商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseAdvertising.getJumpType().equals(9)) {
                //判断当前拼团商品是否存在
                Example example = new Example(ByTeamGoods.class);
                example.createCriteria().andEqualTo("id", baseAdvertising.getTarget()).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(example);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    throw new CustomException("拼团商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            //优惠卷
            if(baseAdvertising.getJumpType().equals(10)){
                if (StringUtils.isEmpty(baseAdvertising.getTarget())) {
                    throw new CustomException("请选择优惠卷");
                }else {
                    //是否启用
                    ByCoupon byCoupon = byCouponMapper.selectByState(baseAdvertising.getTarget());
                    if (byCoupon == null){
                        throw new CustomException("优惠卷不存在");
                    }else {
                        if (!byCoupon.getState()){
                            throw new CustomException("优惠卷未上架");
                        }
                    }
                }
            }
        }

    }
}
