package com.wmeimob.fastboot.baoyan.controller.common;

import cn.hutool.json.JSONObject;
import com.wmeimob.fastboot.autoconfigure.oss.AliyunOssProperties;
import com.wmeimob.fastboot.baoyan.annotation.ApiVersion;
import com.wmeimob.fastboot.baoyan.utils.common.AliOOSUtil;
import org.apache.commons.io.IOUtils;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.service.BaseStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @title: 上传文件
 * @projectName baoyan
 * @description: CommonController
 * @date 2019/7/11 14:42
 */
@RestController
@RequestMapping("common")
@Slf4j
public class ByCommonController {
    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private AliyunOssProperties aliyunOssProperties;

    @GetMapping("/downloadImg")
    public void downloadImg(@RequestParam(value = "id") String id, HttpServletResponse response) throws IOException {
        //从阿里云 rds 下载多个图片并打包成 zip 返回给前端

        String fileName = "商品二维码.zip";
        response.setContentType("application/octet-stream; charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        ServletOutputStream outputStream = response.getOutputStream();

        BaseStore baseStore = baseStoreService.queryBaseStoreById(id);
        OutputStream os = null;


        try {
            File tempFile = File.createTempFile(baseStore.getName(), ".png");
            tempFile.deleteOnExit();
            URL url = new URL(baseStore.getStoreQrcode());
            InputStream inputStream = url.openStream();
            BufferedImage image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);

                // 创建缓冲区
//                byte[] buffer = new byte[4096];
//                int bytesRead;
//
//                // 读取并写入临时文件
//                while ((bytesRead = inputStream.read(buffer)) != -1) {
//                    outputStream.write(buffer, 0, bytesRead);
//                }
            }
            // 关闭Servlet输出流
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (os != null) {
                os.flush();
                os.close();
            }
        }
    }

    /**
     * 从阿里云 rds 下载多个图片并打包成 zip 返回给前端
     */
    @PostMapping("/downloadGoodsImgZip")
    public void downloadGoodsImgZip(@RequestBody List<ByGoodsInfo> byGoodsInfoList, HttpServletRequest request, HttpServletResponse response) {
        //从阿里云 rds 下载多个图片并打包成 zip 返回给前端
        try {
            String zipName = "商品二维码.zip";
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + zipName);
            ServletOutputStream outputStream = response.getOutputStream();

            ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);
            for (ByGoodsInfo byGoodsInfo : byGoodsInfoList) {

                File tempFile = File.createTempFile(byGoodsInfo.getGoodsName(), ".png");
                tempFile.deleteOnExit();
                URL url = new URL(byGoodsInfo.getCodeImg());
                try (InputStream inputStream = url.openStream();
                     FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
                ) {
                    // 创建缓冲区
                    byte[] buffer = new byte[4096];
                    int bytesRead;

                    // 读取并写入临时文件
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }

                    FileInputStream fileInputStream = new FileInputStream(tempFile);

                    ZipEntry zipEntry = new ZipEntry(tempFile.getName());
                    zipOutputStream.putNextEntry(zipEntry);

                    IOUtils.copy(fileInputStream, zipOutputStream);

                    fileInputStream.close();
                }
            }
            // 关闭ZIP输出流
            zipOutputStream.close();
            // 关闭Servlet输出流
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传文件到阿里云
     */
    @ApiVersion(value = 2)
    @PostMapping("/v2/upload")
    public Map<String, Object> upload(@RequestParam(value="file") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String name = "img/" + UUID.randomUUID().toString();
        AliOOSUtil.uploadFileByStream(aliyunOssProperties, file.getInputStream(), name);
        String fileUrl = "https://" + aliyunOssProperties.getBucket() + "." + aliyunOssProperties.getEndpoint() + "/" + name;

        Map<String, Object> result = new HashMap<>();
        result.put("state", "SUCCESS");
        result.put("url", fileUrl);
        result.put("title", file.getOriginalFilename());
        result.put("original", file.getOriginalFilename());

        return result;
    }


    @ApiVersion(value =2)
    @GetMapping("/v2/upload")
    public Map<String, Object> uploadAction(@RequestParam(value="action") String action){
        Map<String, Object> config = new HashMap<>();
        config.put("imageActionName", "uploadimage"); // 执行上传图片的action名称
        config.put("imageFieldName", "file"); // 提交的图片表单名称
        config.put("imageMaxSize", 2048000); // 上传大小限制，单位B
        config.put("imageAllowFiles", new String[]{".png", ".jpg", ".jpeg", ".gif", ".bmp"}); // 上传图片格式显示
        config.put("imageCompressEnable", true); // 是否压缩图片,默认是true
        config.put("imageCompressBorder", 1600); // 图片压缩最长边限制
        config.put("imageInsertAlign", "none"); // 插入的图片浮动方式
        config.put("imageUrlPrefix", ""); // 图片访问路径前缀
//        config.put("imagePathFormat", "/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}"); // 上传保存路径
        return config;
    }
}
