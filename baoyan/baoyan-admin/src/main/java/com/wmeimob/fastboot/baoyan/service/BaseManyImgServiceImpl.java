package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.qo.BaseListImgQo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseManyImgServiceImpl implements BaseManyImgService {
    @Resource
    BaseManyImgMapper baseManyImgMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByCouponMapper byCouponMapper;

    @Override
    public List<BaseListImg> findByCondition(BaseListImg baseListImg) {
        Example example = new Example(BaseActivity.class);
        example.orderBy("id").desc();
        List<BaseListImg> baseListImgs = baseManyImgMapper.selectByExample(example);
        return baseListImgs;
    }



    @Override
    public void insertOrUpdate(BaseListImgQo qo) {
        //效验参数
        checkParam(qo);
        //处理请求参数
        dealReqParam(qo);
        if (qo.getId() != null){
            //修改
            qo.setGmtUpdate(new Date());
            if (qo.getStatus() == null || !Status.getStatus().contains(qo.getStatus())){
                throw new CustomException("请设置正确上下架状态");
            }
            //最多显示4条
            if (qo.getStatus() != null && qo.getStatus().equals(Status.on.getCode())){
                Integer on = baseManyImgMapper.selectCount(new BaseListImg().setStatus(Status.on.getCode()));
                if (on != null && on >= 4){
                    throw new CustomException("首页多图最多显示4条");
                }
            }
            baseManyImgMapper.updateByPrimaryKeySelective(qo);
        }else {
            //新增
            if (qo.getStatus() != null && !qo.getStatus().equals(Status.off.getCode())){
                throw new CustomException("新增请勿设置上架状态");
            }
            if (StringUtils.isEmpty(qo.getTitle())){
                throw new CustomException("请设置标题");
            }
            if (StringUtils.isEmpty(qo.getImgUrl())){
                throw new CustomException("请设置封面");
            }
            if (StringUtils.isEmpty(qo.getSort())){
                throw new CustomException("请设置排序");
            }
            if (StringUtils.isEmpty(qo.getLayout())){
                throw new CustomException("请设置排版方式");
            }
            qo.setGmtCreate(new Date()).setStatus(Status.off.getCode());
            baseManyImgMapper.insertSelective(qo);
        }
    }

    @Override
    public void updateById(BaseListImgQo qo) {
        //效验参数
        checkParam(qo);
        //处理请求参数
        dealReqParam(qo);
        //修改
        qo.setGmtUpdate(new Date());
        if (qo.getStatus() == null || !Status.getStatus().contains(qo.getStatus())){
            throw new CustomException("请设置正确上下架状态");
        }
        //最多显示4条
        if (qo.getStatus() != null && qo.getStatus().equals(Status.on.getCode())){
            Integer on = baseManyImgMapper.selectCount(new BaseListImg().setStatus(Status.on.getCode()));
            BaseListImg baseListImg = baseManyImgMapper.selectByPrimaryKey(qo.getId());
            if (on !=null && baseListImg.getStatus() != null && baseListImg.getStatus().equals(Status.on.getCode())){
                on = on - 1;
            }
            if (on != null && on >= 4){
                throw new CustomException("首页多图最多显示4条");
            }
        }
        baseManyImgMapper.updateByPrimaryKeySelective(qo);
    }

    @Override
    public void deleteById(Integer id) {
        BaseListImg baseListImg = baseManyImgMapper.selectByPrimaryKey(id);
        if (baseListImg != null){
            if (baseListImg.getStatus().equals(1)){
                throw new CustomException("上架状态不能删除");
            }else {
                baseManyImgMapper.deleteByPrimaryKey(id);
            }
        }
    }

    @Override
    public BaseListImg select(Integer id) {
        return baseManyImgMapper.selectByPrimaryKey(id);
    }

    /***
     * 处理类型参数
     * @param qo
     */
    private void dealReqParam(BaseListImgQo qo) {
        if (null != qo.getJumpType()) {
            String target;
            switch (qo.getJumpType()) {
                case 1:
                    target = String.valueOf(qo.getClassifyId());
                    break;
                case 2:
                    target = qo.getTargetImgUrl();
                    break;
                case 3:
                    target = qo.getTargetImgUrl();
                    break;
                case 4:
                    target = qo.getTargetImgUrl();
                    break;
                default:
                    target = qo.getTarget();
                    break;
            }
            qo.setTarget(target);
        }

    }


    /**
     * 效验参数
     *
     * @param qo
     */
    private void checkParam(BaseListImgQo qo) {
        if (null != qo.getJumpType()) {
            if (qo.getJumpType().equals(1)) {
                //判断当前分类id是否存在
                if (null == qo || null == qo.getClassifyId() || StringUtils.isEmpty(qo.getClassifyId())) {
                    throw new CustomException("请选择分类");
                }
            }
            if (qo.getJumpType().equals(2)) {
                if (null == qo || null == qo.getTargetImgUrl()) {
                    throw new CustomException("请上传联票列表图片");
                }
            }
            if (qo.getJumpType().equals(3)) {
                if (null == qo || null == qo.getTargetImgUrl()) {
                    throw new CustomException("请上传次卡列表图片");
                }
            }
            if (qo.getJumpType().equals(4)) {
                if (null == qo || null == qo.getTargetImgUrl()) {
                    throw new CustomException("请上传拼团列表图片");
                }
            }

            if (qo.getJumpType().equals(5)) {
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", qo.getTarget());
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    throw new CustomException("文章不存在");
                }
            }
            if (qo.getJumpType().equals(6)) {
                //判断当前商品普通商品存在
                Example example = new Example(ByGoodsInfo.class);
                example.createCriteria().andEqualTo("id", qo.getTarget()).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(example);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }

            }
            if (qo.getJumpType().equals(7)) {
                //判断当前联票是否存在
                Example example = new Example(ByTicketGoods.class);
                example.createCriteria().andEqualTo("id", qo.getTarget()).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(example);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    throw new CustomException("联票商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (qo.getJumpType().equals(8)) {
                //判断当前次卡商品是否存在
                Example example = new Example(BySubCardGoods.class);
                example.createCriteria().andEqualTo("id", qo.getTarget()).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(example);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    throw new CustomException("次卡商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (qo.getJumpType().equals(9)) {
                //判断当前拼团商品是否存在
                Example example = new Example(ByTeamGoods.class);
                example.createCriteria().andEqualTo("id", qo.getTarget()).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(example);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    throw new CustomException("拼团商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            //优惠卷
            if(qo.getJumpType().equals(10)){
                if (StringUtils.isEmpty(qo.getTarget())) {
                    throw new CustomException("请选择优惠卷");
                }else {
                    //是否启用
                    ByCoupon byCoupon = byCouponMapper.selectByState(qo.getTarget());
                    if (byCoupon==null){
                        throw new CustomException("该优惠卷未启用");
                    }
                }
            }
        }

    }
}
