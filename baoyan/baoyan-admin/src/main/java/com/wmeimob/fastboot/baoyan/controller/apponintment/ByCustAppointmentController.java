package com.wmeimob.fastboot.baoyan.controller.apponintment;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.service.ByCustAppointmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByCustAppointmentController
 * @Description 【客户预约表】控制器
 * <AUTHOR>
 * @Date Tue Jul 09 14:34:33 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bycustappointment")
@Slf4j
public class ByCustAppointmentController {

    @Resource
    private ByCustAppointmentService byCustAppointmentService;




    /**
     * 客户预约表分页查询
     * @param request
     * @param byCustAppointment
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByCustAppointment(HttpServletRequest request, ByCustAppointment byCustAppointment){
        PageContext.startPage();
        return new PageInfo<ByCustAppointmentVO>(byCustAppointmentService.queryAppoinmentList(byCustAppointment));
         
    }

     /**
     * 客户预约表导出
     * @param request
     * @param byCustAppointment
     * @return
     */
    @GetMapping("/exports")
    public List<ByCustAppointmentVO> queryForByCustAppointmentexports(HttpServletRequest request, ByCustAppointment
      byCustAppointment){
        return  byCustAppointmentService.queryAppoinmentList(byCustAppointment);
    }


    /**
     * 客户预约表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCustAppointment queryForByCustAppointmentById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byCustAppointmentService.queryByCustAppointmentById(id);
    }


    /**
     * 客户预约表添加
     * @param request
     * @param byCustAppointment
     * @return
     */
    @PostMapping("/")
    public void insertForByCustAppointment(HttpServletRequest request,@RequestBody ByCustAppointment byCustAppointment){
            byCustAppointmentService.addByCustAppointment(byCustAppointment);    
    }


    /**
     * 客户预约表修改
     * @param request
     * @param byCustAppointment
     * @return
     */
    @PutMapping("/")
    public void updateForByCustAppointment(HttpServletRequest request,@RequestBody ByCustAppointment byCustAppointment){
            byCustAppointmentService.modifyByCustAppointment(byCustAppointment);  
    }

    /**
     * 客户预约表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByCustAppointment(HttpServletRequest request,@PathVariable("id") Object id){
            byCustAppointmentService.removeByCustAppointment(id);
    }
    /**
     * 取消预约
     * @param request
     * @return
     */
    @PutMapping("/cancelAppointment/")
    public  RestResult cancelAppointment(HttpServletRequest request,@RequestBody ByCustAppointment byCustAppointment){
       return byCustAppointmentService.cancelAppointment(byCustAppointment.getId());
    }


}
