package com.wmeimob.fastboot.baoyan.service;

import com.sun.org.apache.bcel.internal.generic.IF_ACMPEQ;
import com.wmeimob.fastboot.baoyan.entity.ByArticle;
import com.wmeimob.fastboot.baoyan.entity.ByPlatformSet;
import com.wmeimob.fastboot.baoyan.enums.Status;
import com.wmeimob.fastboot.baoyan.mapper.ByArticleMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByPlatformSetMapper;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByPlatformSetServiceImpl
 * @Description 平台设置表 服务类实现
 * <AUTHOR>
 * @Date Mon Jul 22 17:18:53 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByPlatformSetServiceImpl implements ByPlatformSetService {

    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private ByArticleMapper byArticleMapper;


    @Override
    public List<ByPlatformSet> findByCondition(ByPlatformSet byPlatformSet) {
        Example example = new Example(ByPlatformSet.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byPlatformSet.getId())) {
            criteria.andEqualTo("id", byPlatformSet.getId());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getPayTime())) {
            criteria.andEqualTo("payTime", byPlatformSet.getPayTime());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getIntegralReturn())) {
            criteria.andEqualTo("integralReturn", byPlatformSet.getIntegralReturn());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getCommentIntegral())) {
            criteria.andEqualTo("commentIntegral", byPlatformSet.getCommentIntegral());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getIntegrationDeduction())) {
            criteria.andEqualTo("integrationDeduction", byPlatformSet.getIntegrationDeduction());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getTeamIntegralIsEnable())) {
            criteria.andEqualTo("teamIntegralIsEnable", byPlatformSet.getTeamIntegralIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getTicketIntegralIsEnable())) {
            criteria.andEqualTo("ticketIntegralIsEnable", byPlatformSet.getTicketIntegralIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getSubCardIntegralIsEnable())) {
            criteria.andEqualTo("subCardIntegralIsEnable", byPlatformSet.getSubCardIntegralIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getTeamCouponIsEnable())) {
            criteria.andEqualTo("teamCouponIsEnable", byPlatformSet.getTeamCouponIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getTicketCouponIsEnable())) {
            criteria.andEqualTo("ticketCouponIsEnable", byPlatformSet.getTicketCouponIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getSubCardCouponIsEnable())) {
            criteria.andEqualTo("subCardCouponIsEnable", byPlatformSet.getSubCardCouponIsEnable());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getCouponInvalid())) {
            criteria.andEqualTo("couponInvalid", byPlatformSet.getCouponInvalid());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getBirthdayCouponId())) {
            criteria.andEqualTo("birthdayCouponId", byPlatformSet.getBirthdayCouponId());
        }
        if (!StringUtils.isEmpty(byPlatformSet.getPerfectInfoCouponId())) {
            criteria.andEqualTo("perfectInfoCouponId", byPlatformSet.getPerfectInfoCouponId());
        }
        example.orderBy("id").desc();
        List<ByPlatformSet> byPlatformSets = byPlatformSetMapper.selectByExample(example);
        return byPlatformSets;
    }

    @Override
    public ByPlatformSet queryByPlatformSetById(Object id) {
        return byPlatformSetMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addByPlatformSet(ByPlatformSet byPlatformSet) {
        byPlatformSetMapper.insertSelective(byPlatformSet);
    }

    @Override
    public void removeByPlatformSet(Object id) {
        ByPlatformSet byPlatformSet = new ByPlatformSet();
        byPlatformSet.setId(Integer.parseInt(id.toString()));
        byPlatformSetMapper.updateByPrimaryKeySelective(byPlatformSet);
    }

    @Override
    public void modifyByPlatformSet(ByPlatformSet byPlatformSet) {
        byPlatformSet.setId(1);
        byPlatformSet.setGmtModified(new Date());
        if (byPlatformSet.getArticleNum() != null){
            if (byPlatformSet.getArticleNum() < 1){
                throw new CustomException("文章个数最小为1");
            }
            //文章滚动条数
            List<ByArticle> byArticles = byArticleMapper.select(new ByArticle().setState(Status.on.getCode()));
            if (byArticles != null && byPlatformSet.getArticleNum() < byArticles.size()){
                throw new CustomException("设置文章数量小于文章开起滚动条数");
            }
        }
        if (byPlatformSet.getArticleStatus() != null && !Status.getStatus().contains(byPlatformSet.getArticleStatus())){
            throw new CustomException("请设置正确状态");
        }
        if (null != byPlatformSet.getChoiceType()) {
            if (2 == byPlatformSet.getChoiceType()) {
                byPlatformSetMapper.updatePerfectCoupon(byPlatformSet);
            } else {
                byPlatformSetMapper.updateBirthdayCoupon(byPlatformSet);
            }
        } else {
            byPlatformSetMapper.updateByPrimaryKeySelective(byPlatformSet);
        }
    }

}
