package com.wmeimob.fastboot.baoyan.controller.tc;


import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcRecommend;
import com.wmeimob.fastboot.baoyan.entity.TcTemplate;
import com.wmeimob.fastboot.baoyan.service.TcRecommendService;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (TcRecommend)表控制层
 * <AUTHOR>
 * @since 2021-07-20 19:02:47
 */
@RestController
@RequestMapping("/tc/recommend")
@Slf4j
public class TcRecommendController {
    /**
     * 服务对象
     */
    @Resource
    private TcRecommendService tcRecommendService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    public TcRecommend selectOne(@PathVariable("id") Integer id) {
        log.info("get  => selectOne [入参]============={}",id);
        return this.tcRecommendService.queryById(id);
    }

    @GetMapping("/")
    public PageInfo<? extends TcRecommend> queryAll(TcRecommend queryObject) {
        PageContext.startPage();
        log.info("get  => queryAll [入参]============={}",queryObject);
        return new PageInfo<>(this.tcRecommendService.queryAll(queryObject));
    }

    @PutMapping("/")
    public Boolean insertObj(@RequestBody TcRecommend object){
        log.info("put  => insertObj [入参]============={}",object);
        return this.tcRecommendService.insert(object);
    }

    @PostMapping("/")
    public Boolean updateObj (@RequestBody TcRecommend object)
    {
        log.info("post  => updateObj [入参]============={}",object);
        return this.tcRecommendService.update(object);
    }
    @DeleteMapping("/{id}")
    public Boolean deleteById (@PathVariable("id") Integer id)
    {
        log.info("del  => deleteById [入参]============={}",id);
        return this.tcRecommendService.deleteById(id);
    }

    @PostMapping("/onAndOffShelves")
    public Boolean onAndOffShelves (@RequestBody TcRecommend updateObject)
    {
        log.info("post => onAndOffShelves [入参]============={}",updateObject);
        return tcRecommendService.onAndOffShelves(updateObject);
    }


}
