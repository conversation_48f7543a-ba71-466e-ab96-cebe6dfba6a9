package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.BaseBannerType;
import com.wmeimob.fastboot.baoyan.service.BaseBannerTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName BaseBannerTypeController
 * @Description 【Banner跳转类型表】控制器
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("basebannertype")
@Slf4j
public class BaseBannerTypeController {

    @Resource
    private BaseBannerTypeService baseBannerTypeService;




    /**
     * Banner跳转类型表分页查询
     * @param request
     * @param baseBannerType
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseBannerType(HttpServletRequest request, BaseBannerType baseBannerType){
        PageContext.startPage();
        return new PageInfo<BaseBannerType>(baseBannerTypeService.findByCondition(baseBannerType));
         
    }

     /**
     * Banner跳转类型表导出
     * @param request
     * @param baseBannerType
     * @return
     */
    @GetMapping("/exports")
    public List<BaseBannerType> queryForBaseBannerTypeexports(HttpServletRequest request, BaseBannerType 
 baseBannerType){
        return  baseBannerTypeService.findByCondition(baseBannerType);
    }


    /**
     * Banner跳转类型表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseBannerType queryForBaseBannerTypeById(HttpServletRequest request, @PathVariable("id") Object id){
            return  baseBannerTypeService.queryBaseBannerTypeById(id);
    }


    /**
     * Banner跳转类型表添加
     * @param request
     * @param baseBannerType
     * @return
     */
    @PostMapping("/")
    public void insertForBaseBannerType(HttpServletRequest request,@RequestBody BaseBannerType baseBannerType){
            baseBannerTypeService.addBaseBannerType(baseBannerType);    
    }


    /**
     * Banner跳转类型表修改
     * @param request
     * @param baseBannerType
     * @return
     */
    @PutMapping("/")
    public void updateForBaseBannerType(HttpServletRequest request,@RequestBody BaseBannerType baseBannerType){
            baseBannerTypeService.modifyBaseBannerType(baseBannerType);  
    }

    /**
     * Banner跳转类型表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseBannerType(HttpServletRequest request,@PathVariable("id") Object id){
            baseBannerTypeService.removeBaseBannerType(id);
    }
}
