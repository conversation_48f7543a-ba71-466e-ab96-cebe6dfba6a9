package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.baoyan.service.ByGoodsInfoService;
import com.wmeimob.fastboot.baoyan.vo.ByGoodsInfoVO;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @ClassName ByGoodsInfoController
 * @Description 【商品信息表】控制器
 * <AUTHOR>
 * @Date Fri Jul 12 13:41:20 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bygoodsinfo")
@Slf4j
public class ByGoodsInfoController {

    @Resource
    private ByGoodsInfoService byGoodsInfoService;

    private final String DATE_FORMAT = "yyy-MM-dd HH:mm:ss";


    /**
     * 商品信息表分页查询
     *
     * @param request
     * @param byGoodsInfo
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByGoodsInfo(HttpServletRequest request, ByGoodsInfo byGoodsInfo) {
        PageContext.startPage();
        return new PageInfo<ByGoodsInfo>(byGoodsInfoService.findByCondition(byGoodsInfo));
    }

    /**
     * 商品信息表导出
     *
     * @param request
     * @param byGoodsInfo
     * @return
     */
    @GetMapping("/exports")
    public List<ByGoodsInfo> queryForByGoodsInfoexports(HttpServletRequest request, ByGoodsInfo
            byGoodsInfo) {
        return byGoodsInfoService.findByCondition(byGoodsInfo);
    }


    /**
     * 商品信息表查询-<通过id查询>
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByGoodsInfoVO queryForByGoodsInfoById(HttpServletRequest request, @PathVariable("id") Integer id) {
        return byGoodsInfoService.queryGoodDetailInfoById(id);
    }


    /**
     * 商品信息表添加
     *
     * @param request
     * @param byGoodsInfo
     * @return
     */
    @PostMapping("/")
    public ByGoodsInfo insertForByGoodsInfo(HttpServletRequest request, @RequestBody ByGoodsInfo byGoodsInfo) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);

        log.info("新增普通商品 时间 time：{}商品信息byGoodsInfo：{}", sdf.format(new Date()), byGoodsInfo);
        return byGoodsInfoService.addByGoodsInfo(byGoodsInfo);
    }

    /**
     * 商品信息表上下架修改p
     *
     * @param request
     * @param byGoodsInfo
     * @return
     */
    @PutMapping("/updateShelf")
    public void updateShelf(HttpServletRequest request, @RequestBody ByGoodsInfo byGoodsInfo) {
        byGoodsInfoService.updateShelf(byGoodsInfo);
    }

    /**
     * 修改
     *
     * @param request
     * @param byGoodsInfo
     * @return
     */
    @PutMapping("/")
    public void updateForByGoodsInfo(HttpServletRequest request, @RequestBody ByGoodsInfo byGoodsInfo) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        log.info("修改普通商品 时间 time：{}商品信息byGoodsInfo：{}", sdf.format(new Date()), byGoodsInfo);
        byGoodsInfoService.modifyByGoodsInfo(byGoodsInfo);
    }

    /**
     * 商品信息表删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByGoodsInfo(HttpServletRequest request, @PathVariable("id") Integer id) {
        byGoodsInfoService.removeByGoodsInfo(id);
    }

    /**
     * 查看二维码
     */
    @GetMapping("codeImg/{id}/{flag}")
    public String  queryCodeImg(@PathVariable("id") Integer id, @PathVariable(value = "flag", required = false) Boolean flag) {
         return byGoodsInfoService.queryCodeImg(id, null != flag && flag);
    }
}
