package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.baoyan.mapper.ByTeamMapper;
import com.wmeimob.fastboot.baoyan.service.ByTeamService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTeamServiceImpl
 * @Description  拼团管理 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTeamServiceImpl implements ByTeamService {

    @Resource
    private ByTeamMapper byTeamMapper;


    @Override
    public List<ByTeam> findByCondition(ByTeam byTeam) {
        List<ByTeam> byTeams = byTeamMapper.findByCondition(byTeam);
        return byTeams;
    }

    @Override
    public ByTeam queryByTeamById(Object id) {
        return byTeamMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByTeam(ByTeam byTeam) {
	  byTeam.setGmtCreate(new Date());
        byTeamMapper.insertSelective(byTeam);
    }

    @Override
    public void removeByTeam(Object id) {
	  ByTeam byTeam = new ByTeam();
	  byTeam.setId(Integer.parseInt(id.toString()));
	  byTeam.setIsDel(true);
        byTeamMapper.updateByPrimaryKeySelective(byTeam);
    }

    @Override
    public void modifyByTeam(ByTeam byTeam) {
        byTeamMapper.updateByPrimaryKeySelective(byTeam);
    }

}
