package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByTeamProperty;
import com.wmeimob.fastboot.baoyan.mapper.ByTeamPropertyMapper;
import com.wmeimob.fastboot.baoyan.service.ByTeamPropertyService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTeamPropertyServiceImpl
 * @Description  预售商品属性 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTeamPropertyServiceImpl implements ByTeamPropertyService {

    @Resource
    private ByTeamPropertyMapper byTeamPropertyMapper;


    @Override
    public List<ByTeamProperty> findByCondition(ByTeamProperty byTeamProperty) {
        Example example = new Example(ByTeamProperty.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byTeamProperty.getId())){
            criteria.andEqualTo("id",byTeamProperty.getId());
	  }
	  if(!StringUtils.isEmpty(byTeamProperty.getTeamId())){
            criteria.andEqualTo("teamId",byTeamProperty.getTeamId());
	  }
	  if(!StringUtils.isEmpty(byTeamProperty.getPropertyId())){
            criteria.andEqualTo("propertyId",byTeamProperty.getPropertyId());
	  }
	  if(!StringUtils.isEmpty(byTeamProperty.getPropertyVal())){
            criteria.andLike("propertyVal",StringUtils.fullFuzzy(byTeamProperty.getPropertyVal()));
	  }
	  if(!StringUtils.isEmpty(byTeamProperty.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byTeamProperty.getGmtCreate());
	  }
        example.orderBy("id").desc();
        List<ByTeamProperty> byTeamPropertys = byTeamPropertyMapper.selectByExample(example);
        return byTeamPropertys;
    }

    @Override
    public ByTeamProperty queryByTeamPropertyById(Object id) {
        return byTeamPropertyMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByTeamProperty(ByTeamProperty byTeamProperty) {
	  byTeamProperty.setGmtCreate(new Date());
        byTeamPropertyMapper.insertSelective(byTeamProperty);
    }

    @Override
    public void removeByTeamProperty(Object id) {
	  ByTeamProperty byTeamProperty = new ByTeamProperty();
	  byTeamProperty.setId(Integer.parseInt(id.toString()));
        byTeamPropertyMapper.updateByPrimaryKeySelective(byTeamProperty);
    }

    @Override
    public void modifyByTeamProperty(ByTeamProperty byTeamProperty) {
        byTeamPropertyMapper.updateByPrimaryKeySelective(byTeamProperty);
    }

}
