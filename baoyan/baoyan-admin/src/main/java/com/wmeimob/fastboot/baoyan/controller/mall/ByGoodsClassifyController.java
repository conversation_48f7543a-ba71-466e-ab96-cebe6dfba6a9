package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify;
import com.wmeimob.fastboot.baoyan.service.ByGoodsClassifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByGoodsClassifyController
 * @Description 【商品类型关联表】控制器
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bygoodsclassify")
@Slf4j
public class ByGoodsClassifyController {

    @Resource
    private ByGoodsClassifyService byGoodsClassifyService;




    /**
     * 商品类型关联表分页查询
     * @param request
     * @param byGoodsClassify
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByGoodsClassify(HttpServletRequest request, ByGoodsClassify byGoodsClassify){
        PageContext.startPage();
        return new PageInfo<ByGoodsClassify>(byGoodsClassifyService.findByCondition(byGoodsClassify));
         
    }

     /**
     * 商品类型关联表导出
     * @param request
     * @param byGoodsClassify
     * @return
     */
    @GetMapping("/exports")
    public List<ByGoodsClassify> queryForByGoodsClassifyexports(HttpServletRequest request, ByGoodsClassify 
   byGoodsClassify){
        return  byGoodsClassifyService.findByCondition(byGoodsClassify);
    }


    /**
     * 商品类型关联表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByGoodsClassify queryForByGoodsClassifyById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byGoodsClassifyService.queryByGoodsClassifyById(id);
    }


    /**
     * 商品类型关联表添加
     * @param request
     * @param byGoodsClassify
     * @return
     */
    @PostMapping("/")
    public void insertForByGoodsClassify(HttpServletRequest request,@RequestBody ByGoodsClassify byGoodsClassify){
            byGoodsClassifyService.addByGoodsClassify(byGoodsClassify);    
    }


    /**
     * 商品类型关联表修改
     * @param request
     * @param byGoodsClassify
     * @return
     */
    @PutMapping("/")
    public void updateForByGoodsClassify(HttpServletRequest request,@RequestBody ByGoodsClassify byGoodsClassify){
            byGoodsClassifyService.modifyByGoodsClassify(byGoodsClassify);  
    }

    /**
     * 商品类型关联表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByGoodsClassify(HttpServletRequest request,@PathVariable("id") Object id){
            byGoodsClassifyService.removeByGoodsClassify(id);
    }
}
