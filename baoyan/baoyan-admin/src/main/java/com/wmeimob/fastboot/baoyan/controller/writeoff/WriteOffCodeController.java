package com.wmeimob.fastboot.baoyan.controller.writeoff;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import com.wmeimob.fastboot.baoyan.service.WriteOffCodeService;
import com.wmeimob.fastboot.baoyan.tool.impl.SchedulerUtil;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.common.sms.SmsSendHandler;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.RandomCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.util.*;


/**
 * @ClassName WriteOffCodeController
 * @Description 【核销码表】控制器
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("writeoffcode")
@Slf4j
public class WriteOffCodeController {

    @Resource
    private WriteOffCodeService writeOffCodeService;

    @Resource
    private ByOrderLogService orderLogService;

    @GetMapping("/queryByDetailId/{detailId}")
    public PageInfo<WriteOffCode> queryByDetailId(@PathVariable Integer detailId){
        PageContext.startPage();

        return new PageInfo(writeOffCodeService.queryByDetailId(detailId));
    }


    /**
     * 核销码表分页查询
     *
     * @param request
     * @param writeOffCode
     * @return
     */
    @GetMapping("/")

    public PageInfo queryForWriteOffCode(HttpServletRequest request, WriteOffCode writeOffCode) {
        log.info("入参 get ==========> {}", writeOffCode);
        PageContext.startPage();
        Integer dateSum = writeOffCode.getDateSum();
        if (null != dateSum) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, dateSum);
            writeOffCode.setExpiryDate(calendar.getTime());
        }
        return new PageInfo<WriteOffCode>(writeOffCodeService.findByCondition(writeOffCode));


    }

    @Resource
    private SchedulerUtil schedulerUtil;

    /**
     * 核销群发短信
     *
     * @param writeOffCodes 需要群发
     * @return
     */
    @PostMapping("/sendMessage")
    public Boolean sendMessage(@RequestBody WriteOffCode[] writeOffCodes) {
//        log.info("writeOffCodes ==========>{}", writeOffCodes.length);

        if (writeOffCodes == null || writeOffCodes.length == 0) return false;
//            WriteOffCode writeOffCode = writeOffCodes[0];
        for (WriteOffCode writeOffCode : writeOffCodes) {
            Map<String, String> map = new HashMap<>(4);
            map.put("name", writeOffCode.getCustUserName());
            map.put("goodname", writeOffCode.getGoodsName());
            long l = writeOffCode.getEndDate().getTime() - System.currentTimeMillis();
            map.put("date", Integer.valueOf((int) Math.floor(l / 1000 / 60 / 60 / 24)).toString());
            schedulerUtil.createCode(map, writeOffCode.getMobile(), "refuse");
        }
        return true;
    }

    @PostMapping("/queryForWriteOff")

    public PageInfo postQueryForWriteOffCode(@RequestBody WriteOffCode writeOffCode) {
        if (writeOffCode.getDateSum() != null) {
            writeOffCode.setStatus(0);
        }

        log.info("入参 post ==========> {}", writeOffCode);
        PageContext.startPage();
        return new PageInfo<WriteOffCode>(writeOffCodeService.findByCondition(writeOffCode));
    }


    /**
     * 核销码表导出
     *
     * @param request
     * @param writeOffCode
     * @return
     */
    @GetMapping("/exports")

    public List<WriteOffCode> queryForWriteOffCodeexports(HttpServletRequest request, WriteOffCode
            writeOffCode) {
        return writeOffCodeService.findByCondition(writeOffCode);
    }


    /**
     * 核销码表查询-<通过id查询>
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")

    public WriteOffCode queryForWriteOffCodeById(HttpServletRequest request, @PathVariable("id") Object id) {
        return writeOffCodeService.queryWriteOffCodeById(id);
    }


    /**
     * 核销码表添加
     *
     * @param request
     * @param writeOffCode
     * @return
     */
    @PostMapping("/")

    public void insertForWriteOffCode(HttpServletRequest request, @RequestBody WriteOffCode writeOffCode) {
        writeOffCodeService.addWriteOffCode(writeOffCode);
    }


    /**
     * 核销码表修改
     *
     * @param request
     * @param writeOffCode
     * @return
     */
    @PutMapping("/")

    public void updateForWriteOffCode(HttpServletRequest request, @RequestBody WriteOffCode writeOffCode) {
        writeOffCodeService.modifyWriteOffCode(writeOffCode);
    }

    /**
     * 核销码表删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")

    public void removeForWriteOffCode(HttpServletRequest request, @PathVariable("id") Object id) {
        writeOffCodeService.removeWriteOffCode(id);

    }

    /**
     * 核销
     *
     * @param request
     * @param writeOffCode
     */
    @PutMapping("update")

    public void update(HttpServletRequest request, @RequestBody WriteOffCode writeOffCode) {

        writeOffCodeService.updateOff(writeOffCode);


    }

    public static void main(String[] args) throws ParseException {


        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = format.parse(format.format(new Date()));
        Date endDate = format.parse("2020-07-03");
        if (currentDate.compareTo(endDate) == 1) {
            System.err.println("1111111");
        }
        if (currentDate.compareTo(endDate) == -1) {
            System.err.println("222");
        }
        if (currentDate.compareTo(endDate) == 0) {
            System.err.println("333");
        }
    }
}
