package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.BaseActivity;
import com.wmeimob.fastboot.baoyan.entity.BaseBanner;
import com.wmeimob.fastboot.baoyan.service.BaseBannerService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName BaseBannerController
 * @Description 【Banner】控制器
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("basebanner")
@Slf4j
public class BaseBannerController {

    @Resource
    private BaseBannerService baseBannerService;




    /**
     * Banner分页查询
     * @param request
     * @param baseBanner
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseBanner(HttpServletRequest request, BaseBanner baseBanner){
        PageContext.startPage();
        return new PageInfo<BaseBanner>(baseBannerService.findByCondition(baseBanner));
         
    }

     /**
     * Banner导出
     * @param request
     * @param baseBanner
     * @return
     */
    @GetMapping("/exports")
    public List<BaseBanner> queryForBaseBannerexports(HttpServletRequest request, BaseBanner 
   baseBanner){
        return  baseBannerService.findByCondition(baseBanner);
    }


    /**
     * Banner查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseBanner queryForBaseBannerById(HttpServletRequest request, @PathVariable("id") Object id){
            return  baseBannerService.queryBaseBannerById(id);
    }


    /**
     * Banner添加
     * @param request
     * @param baseBanner
     * @return
     */
    @PostMapping("/")
    public void insertForBaseBanner(HttpServletRequest request,@RequestBody BaseBanner baseBanner){
            baseBannerService.addBaseBanner(baseBanner);    
    }


    /**
     * Banner修改
     * @param request
     * @param baseBanner
     * @return
     */
    @PutMapping("/")
    public void updateForBaseBanner(HttpServletRequest request,@RequestBody BaseBanner baseBanner){
            baseBannerService.modifyBaseBanner(baseBanner);  
    }

    /**
     * Banner删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseBanner(HttpServletRequest request,@PathVariable("id") Object id){
            baseBannerService.removeBaseBanner(id);
    }
    /**
     * 活动上下架
     * @param baseBanner
     * @return
     */
    @PutMapping("/openClose")
    public void openClose(@RequestBody BaseBanner baseBanner){
        if(baseBanner.getId() == null){
            throw new CustomException("参数错误");
        }
        baseBannerService.modifyBaseBanner(baseBanner);
    }
}
