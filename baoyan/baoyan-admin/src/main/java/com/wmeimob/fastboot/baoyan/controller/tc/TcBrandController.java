package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.service.TcBrandService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 淘潮玩品牌表(TcBrand)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@RestController
@RequestMapping("tc/tcBrand")
@Slf4j
public class TcBrandController {
    /**
     * 服务对象
     */
    @Resource
    private TcBrandService tcBrandService;

    @GetMapping("/")
    public PageInfo<? extends TcBrand> queryPage(TcBrand tcBrand){
        PageContext.startPage();
        log.info("get=> query [入参]============={}",tcBrand);
        return new PageInfo<>(this.tcBrandService.queryPage(tcBrand));
    }
    @GetMapping("/{id}")
    public TcBrand queryByIdTcBrand(@PathVariable Integer id){
        return  tcBrandService.queryById(id);
    }


    @PostMapping("/addTcBrand")
    public Boolean addTcBrand(@RequestBody TcBrand tcBrand){
        log.info("POST => addTcBrand [入参]============={}",tcBrand);
       return this.tcBrandService.insert(tcBrand);
    }

    @PostMapping("/updateTcBrand")
    public Boolean updateTcBrand(@RequestBody TcBrand tcBrand){
        log.info("POST => updateTcBrand [入参]============={}",tcBrand);
        return this.tcBrandService.update(tcBrand);
    }

    @DeleteMapping("/{id}")
    public Boolean delete(@PathVariable("id") Integer id){
        log.info("delete => id [入参]============={}",id);
        return tcBrandService.deleteById(id);
    }

}