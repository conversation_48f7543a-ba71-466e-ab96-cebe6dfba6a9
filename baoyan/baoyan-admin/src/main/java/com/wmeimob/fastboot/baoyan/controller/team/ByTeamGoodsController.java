package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByTeamGoods;
import com.wmeimob.fastboot.baoyan.service.ByTeamGoodsService;
import com.wmeimob.fastboot.baoyan.vo.TeamGoodsVo;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName ByTeamGoodsController
 * @Description 【拼团商品表】控制器
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byteamgoods")
@Slf4j
public class ByTeamGoodsController {

    @Resource
    private ByTeamGoodsService byTeamGoodsService;



    /**
     * 拼团商品表分页查询
     * @param request
     * @param byTeamGoods
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTeamGoods(HttpServletRequest request, ByTeamGoods byTeamGoods){
        PageContext.startPage();
        return new PageInfo<ByTeamGoods>(byTeamGoodsService.findByCondition(byTeamGoods));
         
    }
    /**
     * 拼团商品表分页查询
     * @param request
     * @param byTeamGoods
     * @return
     */
    @GetMapping("/exports")
    public List<ByTeamGoods> queryForByTeamGoodsExports(HttpServletRequest request, ByTeamGoods byTeamGoods){
        return byTeamGoodsService.findByCondition(byTeamGoods);

    }

    /**
     * 新增拼团商品
     * @param request
     * @param byTeamGoods
     */
    @PostMapping("/")
    public void addByTeamGoods(HttpServletRequest request, @RequestBody ByTeamGoods byTeamGoods){
         byTeamGoodsService.addByTeamGoods(byTeamGoods);
    }
    /**
     * 修改拼团商品
     * @param request
     * @param byTeamGoods
     */
    @PutMapping("/")
    public void updateByTeamGoods(HttpServletRequest request, @RequestBody ByTeamGoods byTeamGoods){
         byTeamGoodsService.modifyByTeamGoods(byTeamGoods);
    }


    /**
     * 拼团商品表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTeamGoods queryForByTeamGoodsById(HttpServletRequest request, @PathVariable("id") Integer id){
            return  byTeamGoodsService.queryByTeamGoodsById(id);
    }
    /**
     * 拼团统计
     * @param teamGoods
     * @return
     */
    @GetMapping("/forms/")
    public PageInfo forms(ByTeamGoods teamGoods){
        PageContext.startPage();
        return new PageInfo<TeamGoodsVo>(byTeamGoodsService.forms(teamGoods));
    }

    /**
     * 拼团商品表导出
     * @param request
     * @param byTeamGoods
     * @return
     */
    @GetMapping("/forms/exports")
    public List<TeamGoodsVo> queryForByTeamGoodsexports(HttpServletRequest request, ByTeamGoods
            byTeamGoods){
        return  byTeamGoodsService.forms(byTeamGoods);
    }


    /**
     * 拼团商品上下架修改
     * @param request
     * @param byTeamGoods
     * @return
     */
    @PutMapping("/updateShelf")
    public void updateShelf(HttpServletRequest request,@RequestBody ByTeamGoods byTeamGoods){
        byTeamGoodsService.updateShelf(byTeamGoods);
    }

    /**
     * 商品信息表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeByTeamGoods(HttpServletRequest request,@PathVariable("id") Object id){
        byTeamGoodsService.removeByTeamGoods(id);
    }

}
