package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTicketGoodsMapping;
import com.wmeimob.fastboot.baoyan.service.ByTicketGoodsMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByTicketGoodsMappingController
 * @Description 【联票商品mapping表】控制器
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byticketgoodsmapping")
@Slf4j
public class ByTicketGoodsMappingController {

    @Resource
    private ByTicketGoodsMappingService byTicketGoodsMappingService;




    /**
     * 联票商品mapping表分页查询
     * @param request
     * @param byTicketGoodsMapping
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTicketGoodsMapping(HttpServletRequest request, ByTicketGoodsMapping byTicketGoodsMapping){
        PageContext.startPage();
        return new PageInfo<ByTicketGoodsMapping>(byTicketGoodsMappingService.findByCondition(byTicketGoodsMapping));
         
    }

     /**
     * 联票商品mapping表导出
     * @param request
     * @param byTicketGoodsMapping
     * @return
     */
    @GetMapping("/exports")
    public List<ByTicketGoodsMapping> queryForByTicketGoodsMappingexports(HttpServletRequest request, ByTicketGoodsMapping 
 byTicketGoodsMapping){
        return  byTicketGoodsMappingService.findByCondition(byTicketGoodsMapping);
    }


    /**
     * 联票商品mapping表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTicketGoodsMapping queryForByTicketGoodsMappingById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byTicketGoodsMappingService.queryByTicketGoodsMappingById(id);
    }


    /**
     * 联票商品mapping表添加
     * @param request
     * @param byTicketGoodsMapping
     * @return
     */
    @PostMapping("/")
    public void insertForByTicketGoodsMapping(HttpServletRequest request,@RequestBody ByTicketGoodsMapping byTicketGoodsMapping){
            byTicketGoodsMappingService.addByTicketGoodsMapping(byTicketGoodsMapping);    
    }


    /**
     * 联票商品mapping表修改
     * @param request
     * @param byTicketGoodsMapping
     * @return
     */
    @PutMapping("/")
    public void updateForByTicketGoodsMapping(HttpServletRequest request,@RequestBody ByTicketGoodsMapping byTicketGoodsMapping){
            byTicketGoodsMappingService.modifyByTicketGoodsMapping(byTicketGoodsMapping);  
    }

    /**
     * 联票商品mapping表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByTicketGoodsMapping(HttpServletRequest request,@PathVariable("id") Object id){
            byTicketGoodsMappingService.removeByTicketGoodsMapping(id);
    }
}
