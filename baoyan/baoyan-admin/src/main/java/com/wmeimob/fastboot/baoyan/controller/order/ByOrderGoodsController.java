package com.wmeimob.fastboot.baoyan.controller.order;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByOrderGoods;
import com.wmeimob.fastboot.baoyan.service.ByOrderGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByOrderGoodsController
 * @Description 【普通订单商品表】控制器
 * <AUTHOR>
 * @Date Tue Aug 06 17:02:57 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byordergoods")
@Slf4j
public class ByOrderGoodsController {

    @Resource
    private ByOrderGoodsService byOrderGoodsService;




    /**
     * 普通订单商品表分页查询
     * @param request
     * @param byOrderGoods
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByOrderGoods(HttpServletRequest request, ByOrderGoods byOrderGoods){
        PageContext.startPage();
        return new PageInfo<ByOrderGoods>(byOrderGoodsService.findByCondition(byOrderGoods));
         
    }

     /**
     * 普通订单商品表导出
     * @param request
     * @param byOrderGoods
     * @return
     */
    @GetMapping("/exports")
    public List<ByOrderGoods> queryForByOrderGoodsexports(HttpServletRequest request, ByOrderGoods 
 byOrderGoods){
        return  byOrderGoodsService.findByCondition(byOrderGoods);
    }


    /**
     * 普通订单商品表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByOrderGoods queryForByOrderGoodsById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byOrderGoodsService.queryByOrderGoodsById(id);
    }


    /**
     * 普通订单商品表添加
     * @param request
     * @param byOrderGoods
     * @return
     */
    @PostMapping("/")
    public void insertForByOrderGoods(HttpServletRequest request,@RequestBody ByOrderGoods byOrderGoods){
            byOrderGoodsService.addByOrderGoods(byOrderGoods);    
    }


    /**
     * 普通订单商品表修改
     * @param request
     * @param byOrderGoods
     * @return
     */
    @PutMapping("/")
    public void updateForByOrderGoods(HttpServletRequest request,@RequestBody ByOrderGoods byOrderGoods){
            byOrderGoodsService.modifyByOrderGoods(byOrderGoods);  
    }

    /**
     * 普通订单商品表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByOrderGoods(HttpServletRequest request,@PathVariable("id") Object id){
            byOrderGoodsService.removeByOrderGoods(id);
    }
}
