package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BySubCardGoodsServiceImpl
 * @Description  次卡商品表 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 25 10:13:01 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class BySubCardGoodsServiceImpl implements BySubCardGoodsService {

    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByRichTextMapper byRichTextMapper;
    @Resource
    BySubGoodsStoreMapper bySubGoodsStoreMapper;
    @Resource
    BySubGoodsClassifyMapper bySubGoodsClassifyMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;


    @Override
    public List<BySubCardGoods> findByCondition(BySubCardGoods bySubCardGoods) {
        List<BySubCardGoods> bySubCardGoodss = bySubCardGoodsMapper.findByCondition(bySubCardGoods);
        return bySubCardGoodss;
    }

    @Override
    public BySubCardGoods queryBySubCardGoodsById(Integer id) {
        BySubCardGoods bySubCardGoods = bySubCardGoodsMapper.queryBySubCardGoodsById(id);
        return bySubCardGoods;
    }

    public static long getLineNumber(String target) throws IOException {
        return target.trim().split("\n").length;
    }
	@Override
    public void addBySubCardGoods(BySubCardGoods bySubCardGoods)  {
        //判断数量是否 大于0
        if (bySubCardGoods.getSubCardGoodsNum().intValue()<=0) {
            throw new CustomException("包含数量必须大于0");
        }
	    bySubCardGoods.setGmtCreate(new Date());
        bySubCardGoods.setGoodsNo(getGoodsNo());
        bySubCardGoodsMapper.insertSelective(bySubCardGoods);
        //门店新增 stores
        if (null == bySubCardGoods.getStores()||bySubCardGoods.getStores().equals("")) {
            throw new CustomException("请选择门店");
        }
        List<String> storeList = Arrays.asList(bySubCardGoods.getStores().split(","));
        if (storeList.size() > 0) {
            for (String str : storeList) {
                BySubGoodsStore store = new BySubGoodsStore();
                store.setGmtCreate(new Date());
                store.setStoreId(Integer.valueOf(str));
                store.setSubGoodsId(bySubCardGoods.getId());
                bySubGoodsStoreMapper.insertSelective(store);
            }
        } else {
            throw new CustomException("请选择门店");
        }
        //分类
        if (null == bySubCardGoods.getClassifyIds()) {
            throw new CustomException("请选择分类");
        }
        List<String> classfyList = Arrays.asList(bySubCardGoods.getClassifyIds().split(","));
        if (classfyList.size() > 0) {
            for (String cla : classfyList) {
                BySubGoodsClassify classify = new BySubGoodsClassify();
                classify.setGmtCreate(new Date());
                classify.setSubGoodsId(bySubCardGoods.getId());
                classify.setClassifyId(Integer.valueOf(cla));
                bySubGoodsClassifyMapper.insertSelective(classify);
            }
        } else {
            throw new CustomException("请选择分类");
        }
        if (null == bySubCardGoods.getRichContent() || bySubCardGoods.getRichContent().equals("")) {
            throw new CustomException("请填写商品描述");
        }
        //富文本
        ByRichText richText=new ByRichText();
        richText.setDataId(bySubCardGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_2);
        richText.setContent(bySubCardGoods.getRichContent());
        byRichTextMapper.insertSelective(richText);
    }

    /**
     * 商城编号生成
     * @return
     */
    private String getGoodsNo(){
        StringBuffer sb=new StringBuffer();
        sb.append("SP");
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        sb.append(sdf.format(new Date()));
        return sb.toString();
    }


    @Override
    public void removeBySubCardGoods(Object id) {
	  BySubCardGoods bySubCardGoods = new BySubCardGoods();
	  bySubCardGoods.setId(Integer.parseInt(id.toString()));
	  bySubCardGoods.setIsDel(true);
	  bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
    }

    @Override
    public void modifyBySubCardGoods(BySubCardGoods bySubCardGoods) throws IOException {
        if (bySubCardGoods.getSubCardGoodsNum().intValue()<=0) {
            throw new CustomException("包含数量必须大于0");
        }
        bySubCardGoods.setGmtUpdate(new Date());
        //门店
        if (null == bySubCardGoods.getStores()) {
            throw new CustomException("请选择门店");
        }
        List<String> storeList = Arrays.asList(bySubCardGoods.getStores().split(","));
        if (null != storeList && !bySubCardGoods.getStores().equals("") && storeList.size() > 0) {
            //删除原有的门店 关联
            Example example = new Example(BySubGoodsStore.class);
            example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId());
            bySubGoodsStoreMapper.deleteByExample(example);
            for (String str : storeList) {
                BySubGoodsStore store = new BySubGoodsStore();
                store.setGmtCreate(new Date());
                store.setStoreId(Integer.valueOf(str));
                store.setSubGoodsId(bySubCardGoods.getId());
                bySubGoodsStoreMapper.insertSelective(store);
            }
        } else {
            throw new CustomException("请选择门店");
        }
        //分类
        if (null == bySubCardGoods.getClassifyIds()) {
            throw new CustomException("请选择分类");
        }
        List<String> classfyList = Arrays.asList(bySubCardGoods.getClassifyIds().split(","));
        if (null != classfyList && !bySubCardGoods.getClassifyIds().equals("") && classfyList.size() > 0) {
            //删除原有的分类关联
            Example example = new Example(BySubGoodsClassify.class);
            example.createCriteria().andEqualTo("subGoodsId", bySubCardGoods.getId());
            bySubGoodsClassifyMapper.deleteByExample(example);
            for (String cla : classfyList) {
                BySubGoodsClassify classify = new BySubGoodsClassify();
                classify.setGmtCreate(new Date());
                classify.setSubGoodsId(bySubCardGoods.getId());
                classify.setClassifyId(Integer.valueOf(cla));
                bySubGoodsClassifyMapper.insertSelective(classify);
            }
        } else {
            throw new CustomException("请选择分类");
        }
        log.info("》》》编辑次卡商品信息 修改前info：{}",bySubCardGoodsMapper.selectByPrimaryKey(bySubCardGoods.getId()));
        bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
        log.info("《《《编辑次卡商品信息 修改后info：{}",bySubCardGoods);
        //富文本
        ByRichText richText=new ByRichText();
        richText.setDataId(bySubCardGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_2);
        richText.setContent(bySubCardGoods.getRichContent());
        byRichTextMapper.updateByDataIdAndType(richText);
    }
    @Override
    public void updateShelf(BySubCardGoods bySubCardGoods) {
        BySubCardGoods goodsInfo = bySubCardGoodsMapper.selectByPrimaryKey(bySubCardGoods.getId());
        //上架判断 下产品核销码是否 过期
//        if (bySubCardGoods.getStatus().equals(1)) {
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(goodsInfo.getEffectiveEnd());
//            if (calendar.getTime().getTime() < new Date().getTime()) {
//                throw new CustomException("当前商品不满足上架要求,请效验产品核销时间");
//            }
//        }
        bySubCardGoods.setGmtUpdate(new Date());
        bySubCardGoodsMapper.updateByPrimaryKeySelective(bySubCardGoods);
    }

}
