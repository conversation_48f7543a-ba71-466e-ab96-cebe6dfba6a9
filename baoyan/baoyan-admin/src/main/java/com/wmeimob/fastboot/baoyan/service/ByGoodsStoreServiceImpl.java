package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsStore;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsStoreMapper;
import com.wmeimob.fastboot.baoyan.service.ByGoodsStoreService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByGoodsStoreServiceImpl
 * @Description  商品门店关系表 服务类实现
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByGoodsStoreServiceImpl implements ByGoodsStoreService {

    @Resource
    private ByGoodsStoreMapper byGoodsStoreMapper;


    @Override
    public List<ByGoodsStore> findByCondition(ByGoodsStore byGoodsStore) {
        Example example = new Example(ByGoodsStore.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byGoodsStore.getId())){
            criteria.andEqualTo("id",byGoodsStore.getId());
	  }
	  if(!StringUtils.isEmpty(byGoodsStore.getGoodsId())){
            criteria.andEqualTo("goodsId",byGoodsStore.getGoodsId());
	  }
	  if(!StringUtils.isEmpty(byGoodsStore.getStoreId())){
            criteria.andEqualTo("storeId",byGoodsStore.getStoreId());
	  }
	  if(!StringUtils.isEmpty(byGoodsStore.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byGoodsStore.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byGoodsStore.getGmtModified())){
            criteria.andEqualTo("gmtModified",byGoodsStore.getGmtModified());
	  }
        example.orderBy("id").desc();
        List<ByGoodsStore> byGoodsStores = byGoodsStoreMapper.selectByExample(example);
        return byGoodsStores;
    }

    @Override
    public ByGoodsStore queryByGoodsStoreById(Object id) {
        return byGoodsStoreMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByGoodsStore(ByGoodsStore byGoodsStore) {
	  byGoodsStore.setGmtCreate(new Date());
        byGoodsStoreMapper.insertSelective(byGoodsStore);
    }

    @Override
    public void removeByGoodsStore(Object id) {
	  ByGoodsStore byGoodsStore = new ByGoodsStore();
	  byGoodsStore.setId(Integer.parseInt(id.toString()));
        byGoodsStoreMapper.updateByPrimaryKeySelective(byGoodsStore);
    }

    @Override
    public void modifyByGoodsStore(ByGoodsStore byGoodsStore) {
		byGoodsStore.setGmtModified(new Date());
        byGoodsStoreMapper.updateByPrimaryKeySelective(byGoodsStore);
    }

}
