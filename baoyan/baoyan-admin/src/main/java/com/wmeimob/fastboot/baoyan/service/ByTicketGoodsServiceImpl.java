package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsInfoMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByRichTextMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByTicketGoodsMappingMapper;
import com.wmeimob.fastboot.baoyan.service.ByTicketGoodsService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTicketGoodsServiceImpl
 * @Description 联票商品表 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByTicketGoodsServiceImpl implements ByTicketGoodsService {

    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTicketGoodsMappingMapper byTicketGoodsMappingMapper;
    @Resource
    private ByRichTextMapper byRichTextMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;


    @Override
    public List<ByTicketGoods> findByCondition(ByTicketGoods byTicketGoods) {
        List<ByTicketGoods> byTicketGoodss = byTicketGoodsMapper.findByCondition(byTicketGoods);
        if (null != byTicketGoodss && byTicketGoodss.size() > 0) {
            for (ByTicketGoods goods : byTicketGoodss) {
                Example example = new Example(ByTicketGoods.class);
                byTicketGoodsMappingMapper.selectByExample(example);
            }
        }

        return byTicketGoodss;
    }

    @Override
    public ByTicketGoods queryByTicketGoodsById(Integer id) {
        return byTicketGoodsMapper.queryByTicketGoodsById(id);
    }


    @Override
    public void addByTicketGoods(ByTicketGoods byTicketGoods) {
        checkTicketGoodsParam(byTicketGoods);
        byTicketGoods.setGmtCreate(new Date());
        byTicketGoodsMapper.insertSelective(byTicketGoods);
        //联票关联 商品新增
        inserMapping(byTicketGoods);
        //富文本
        ByRichText richText = new ByRichText();
        richText.setDataId(byTicketGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_3);
        richText.setContent(byTicketGoods.getRichContent());
        byRichTextMapper.insertSelective(richText);
    }

    /**
     * 效验 联票商品信息数据
     *
     * @param byTicketGoods
     */
    private void checkTicketGoodsParam(ByTicketGoods byTicketGoods) {
        if (StringUtils.isEmpty(byTicketGoods.getTicketGoodsId())) {
            throw new CustomException("请选择包含商品");
        }
        //查询 填写商品是否存在 不存在过滤掉不存在商品
        List<String> goodList = Arrays.asList(byTicketGoods.getTicketGoodsId().split(","));
        if (null == goodList || goodList.size() <= 0) {
            throw new CustomException("请选择包含商品");
        }else{
            for (String goodStr:goodList){
                ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(Integer.valueOf(goodStr));
                if (null==byGoodsInfo){
                    throw new CustomException("商品ID："+goodStr+"不存在");
                }
            }
        }
        byTicketGoods.setTicketGoodList(goodList);
    }

    @Override
    @Transactional
    public void removeByTicketGoods(Object id) {
        //判断id是否存在
        ByTicketGoods resultByticket = byTicketGoodsMapper.selectByPrimaryKey(id);
        if (null == resultByticket) {
            throw new CustomException("联票商品不存在");
        }
        ByTicketGoods byTicketGoods = new ByTicketGoods();
        byTicketGoods.setId(Integer.parseInt(id.toString()));
        byTicketGoods.setIsDel(true);
        int delNum = byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
        if (delNum <= 0) {
            throw new CustomException("删除失败");
        }
        //删除原有商品映射关联
        Example delExample = new Example(ByTicketGoodsMapping.class);
        delExample.createCriteria().andEqualTo("ticketGoodsId", id);
        int delMapingNum = byTicketGoodsMappingMapper.deleteByExample(delExample);
        if (delMapingNum <= 0) {
            throw new CustomException("删除失败");
        }
    }

    /**
     * 联票关联商品新增
     *
     * @param byTicketGoods
     */
    private void inserMapping(ByTicketGoods byTicketGoods) {
        //联票关联 商品新增
        for (String goodId : byTicketGoods.getTicketGoodList()) {
            ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(Integer.valueOf(goodId));
            if (null == byGoodsInfo) {
                continue;
            }
            ByTicketGoodsMapping mapping = new ByTicketGoodsMapping();
            mapping.setGmtCreate(new Date());
            mapping.setGoodsId(Integer.valueOf(goodId));
            mapping.setTicketGoodsId(byTicketGoods.getId());
            byTicketGoodsMappingMapper.insertSelective(mapping);
        }
    }

    @Override
    public void modifyByTicketGoods(ByTicketGoods byTicketGoods) {
        checkTicketGoodsParam(byTicketGoods);
        byTicketGoods.setGmtUpdate(new Date());
        log.info("》》》编辑联票商品信息 修改前info：{}",byTicketGoodsMappingMapper.selectByPrimaryKey(byTicketGoods.getId()));
        byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
        log.info("《《《编辑联票商品信息 修改后info：{}",byTicketGoods);
        //删除原有商品关联
        Example example = new Example(ByTicketGoodsMapping.class);
        example.createCriteria().andEqualTo("ticketGoodsId", byTicketGoods.getId());
        byTicketGoodsMappingMapper.deleteByExample(example);
        //联票商品新增
        inserMapping(byTicketGoods);
        ByRichText richText = new ByRichText();
        richText.setDataId(byTicketGoods.getId());
        richText.setGmtModified(new Date());
        richText.setDataType(BaoYanConstant.RICH_TYPE_3);
        richText.setContent(byTicketGoods.getRichContent());
        byRichTextMapper.updateByDataIdAndType(richText);
    }

    @Override
    public void updateShelf(ByTicketGoods byTicketGoods) {
        ByTicketGoods goodsInfo = byTicketGoodsMapper.selectByPrimaryKey(byTicketGoods.getId());
        //上架判断 下产品核销码是否 过期
//        if (byTicketGoods.getStatus()) {
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(goodsInfo.getEffectiveEnd());
//            if (calendar.getTime().getTime() < new Date().getTime()) {
//                throw new CustomException("当前商品不满足上架要求,请效验产品核销时间");
//            }
//        }
        byTicketGoodsMapper.updateByPrimaryKeySelective(byTicketGoods);
    }

}
