package com.wmeimob.fastboot.baoyan.service;

import com.google.common.collect.Maps;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName ByCouponTempServiceImpl
 * @Description 优惠券模板 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 17:55:47 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByCouponTempServiceImpl implements ByCouponTempService {

    @Resource
    private ByCouponTempMapper byCouponTempMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByCouponMapper byCouponMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private TcGoodsMapper tcGoodsMapper;


    @Override
    public List<ByCouponTemp> findByCondition(ByCouponTemp byCouponTemp) {
        return byCouponTempMapper.getCouponTempList(byCouponTemp);
    }

    @Override
    public ByCouponTemp queryByCouponTempById(Object id) {
        ByCouponTemp byCouponTemp = byCouponTempMapper.selectByPrimaryKey(id);
        if (byCouponTemp.getType()==5){
            TcGoods tcGoods = tcGoodsMapper.queryById( byCouponTemp.getTargetId() );
            byCouponTemp.setTcGoodsCate( tcGoods.getCateId() );
        }

        return byCouponTemp;
    }


    @Override
    public void addByCouponTemp(ByCouponTemp byCouponTemp) {
        dealByCouponTempParam(byCouponTemp);
        byCouponTemp.setGmtCreate(new Date());
        byCouponTempMapper.insertSelective(byCouponTemp);
    }

    @Override
    public void removeByCouponTemp(Object id) {
        checkCoupon(id);
        ByCouponTemp byCouponTemp = new ByCouponTemp();
        byCouponTemp.setId(Integer.parseInt(id.toString()));
        byCouponTemp.setIsDel(true);
        byCouponTempMapper.updateByPrimaryKeySelective(byCouponTemp);
    }

    /**
     * 效验 优惠卷模板是否可以删除
     *
     * @param id
     */
    private void checkCoupon(Object id) {
        Example example = new Example(ByCoupon.class);
        example.createCriteria().andEqualTo("tempId", id).andEqualTo("isDel", 0);
        List<ByCoupon> byCoupons = byCouponMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(byCoupons)) {
            throw new CustomException("优惠券专区正在使用此优惠券，请先在优惠券专区删除后再次尝试");
        }
    }

    @Override
    public void modifyByCouponTemp(ByCouponTemp byCouponTemp) {
        dealByCouponTempParam(byCouponTemp);
        byCouponTemp.setGmtUpdate(new Date());
        byCouponTempMapper.updateByPrimaryKeySelective(byCouponTemp);
    }

    public static void main(String args[]) throws ParseException {
        /*String line = "99";
        String pattern = "^([1-9]\\d|\\d|1)$";
        //创建Pattern对象
        Pattern p = Pattern.compile(pattern);
        //创建Matcher对象
        Matcher m = p.matcher(line);
        System.out.println(m.find());*/

        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
       // String endDateStr = format.format("2019-11-08");
        Date endDate = format.parse("2019-11-08");
        //效验当前时间
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        Calendar current = Calendar.getInstance();
//        String nowStr = format.format(new Date());
//        Date now = format.parse(nowStr);
//        current.setTime(now);
        System.err.println("------------:"+current.getTime());
        System.err.println("------------:"+endCalendar.getTime());
        System.err.println("------------:"+current.before(endCalendar));
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");

        Date date1 = format1.parse("2019-11-08");
        Date date2 = format1.parse("2019-11-08");

        int compareTo = date1.compareTo(date2);
        System.err.println("compareTo------------:"+compareTo);
        String startTime=new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        System.out.println("当日 时间："+startTime);
        Calendar now = Calendar.getInstance();
        now.add(Calendar.DAY_OF_MONTH, -30);
        String endDate2 = new SimpleDateFormat("yyyy-MM-dd").format(now.getTime());
        System.out.println("-----------------30天:"+endDate2);

    }

    /**
     * 处理新增编辑参数
     *
     * @param byCouponTemp
     */
    private void dealByCouponTempParam(ByCouponTemp byCouponTemp) {
        if (byCouponTemp.getCouponType().equals(2)) {
            if (byCouponTemp.getDiscount().compareTo(new BigDecimal(0)) == 0) {
                throw new CustomException("折扣卷范围 1-99 整数");
            }
            //匹配是否是1-99
            String pattern = "^([1-9]\\d|\\d|1)$";
            Pattern p = Pattern.compile(pattern);
            Matcher m = p.matcher(String.valueOf(byCouponTemp.getDiscount()));
            //折扣卷
            if (!m.find()) {
                throw new CustomException("折扣卷范围 1-99 整数");
            }
        }
        //判断有效期类型是否是天数
        if (byCouponTemp.getEffectiveType().equals(2)) {
            //计算时间 选择天数 增加
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + byCouponTemp.getDayNum());//让日期加天数 
            byCouponTemp.setStartDate(new Date());
            byCouponTemp.setEndDate(calendar.getTime());
        }
        //1是分类id 2分类id 3单独商品
        if (byCouponTemp.getType().equals(1)) {
            byCouponTemp.setTargetId(byCouponTemp.getClassifyId());
        } else if (byCouponTemp.getType().equals(2)) {
            if (null == byCouponTemp.getTargetId()) {
                throw new CustomException("请填写商品");
            } else {
                switch (byCouponTemp.getSingleGoodsType()) {
                    //普通商品  次卡商品 联票商品
                    case 1:
                        //判断商品是否存在
                        ByGoodsInfo byGoodsInfo = byGoodsInfoMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                        if (null == byGoodsInfo) {
                            throw new CustomException("商品不存在");
                        }
                        break;
                    case 2:
                        BySubCardGoods bySubCardcGoods= bySubCardGoodsMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                        if (null == bySubCardcGoods) {
                            throw new CustomException("商品不存在");
                        }
                        break;
                    case 3:
                        ByTicketGoods byTicketGoods= byTicketGoodsMapper.selectByPrimaryKey(byCouponTemp.getTargetId());
                        if (null == byTicketGoods) {
                            throw new CustomException("商品不存在");
                        }

                }

            }
            byCouponTemp.setTargetId(byCouponTemp.getTargetId());
            byCouponTemp.setSingleGoodsType(byCouponTemp.getSingleGoodsType());
        }else if ( byCouponTemp.getType().equals(5) ){
            //淘潮玩商品可用
            byCouponTemp.setTargetId( byCouponTemp.getTcGoodsId() );
        } else if (byCouponTemp.getType().equals(0) || byCouponTemp.getType().equals(6) || byCouponTemp.getType().equals(7)){
            //默认全部商品
            byCouponTemp.setTargetId(0);
        }else{
            throw new CustomException("优惠券使用范围不合法");        }
    }

}
