package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByOrderAfter;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByIntegralLogMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByOrderAfterMapper;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByCustUserServiceImpl
 * @Description 用户表 服务类实现
 * <AUTHOR>
 * @Date Fri Jul 05 13:51:52 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByCustUserServiceImpl implements ByCustUserService {

    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private ByIntegralLogMapper integralLogMapper;


    @Override
    public List<ByCustUser> findByCondition(ByCustUser byCustUser) {
        Example example = new Example(ByCustUser.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byCustUser.getId())) {
            criteria.andLike("id", StringUtils.fullFuzzy(String.valueOf(byCustUser.getId())));
        }
        if (!StringUtils.isEmpty(byCustUser.getMobile())) {
            criteria.andLike("mobile", StringUtils.fullFuzzy(byCustUser.getMobile()));
        }
        example.orderBy("id").desc();
        List<ByCustUser> byCustUsers = byCustUserMapper.selectByExample(example);
        return byCustUsers;
    }

    @Override
    public ByCustUser queryByCustUserById(Object id) {
        return byCustUserMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addByCustUser(ByCustUser byCustUser) {
        byCustUser.setGmtCreate(new Date());
        byCustUserMapper.insertSelective(byCustUser);
    }

    @Override
    public void removeByCustUser(Object id) {
        ByCustUser byCustUser = new ByCustUser();
        byCustUser.setId(Integer.parseInt(id.toString()));
        byCustUserMapper.updateByPrimaryKeySelective(byCustUser);
    }

    @Override
    public void modifyByCustUser(ByCustUser byCustUser) {
        byCustUser.setGmtModified(new Date());
        byCustUserMapper.updateByPrimaryKeySelective(byCustUser);
    }

    @Override
    public void updateIntegral(ByCustUser sfUserInfo) {
        if (sfUserInfo.getType() == 1) {
            if (null==sfUserInfo.getReason()) {
                sfUserInfo.setReason("后台新增");
            }
            byCustUserMapper.updateUserIntegralAdd(sfUserInfo.getId(), sfUserInfo.getChangeNum());
        } else {
            if (null==sfUserInfo.getReason()) {
                sfUserInfo.setReason("后台减少");
            }
            ByCustUser user = byCustUserMapper.selectByPrimaryKey(sfUserInfo.getId());
            //判断 结果是否减少时候结果是否满足大于0
            if (user.getNowPoint() - sfUserInfo.getChangeNum() < 0) {
                throw new CustomException("请输入正确的变更数量");
            }
            byCustUserMapper.updateUserIntegralAdd(sfUserInfo.getId(), -sfUserInfo.getChangeNum());
        }
        SysUser sysUser = (SysUser) SecurityContext.getUser();
        integralLogMapper.addIntegralLog(sfUserInfo.getId(), sfUserInfo.getType(), sfUserInfo.getChangeNum(), sfUserInfo.getNowPoint(), sfUserInfo.getReason(), sysUser.getUsername());
    }

    @Resource
    private ByOrderAfterMapper byOrderAfterMapper;
    @Override
    public void updateRed(Integer id) {
        ByOrderAfter byOrderAfter = this.byOrderAfterMapper.selectByPrimaryKey(id);
        ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(byOrderAfter.getUserId());
        if (byCustUser != null && byCustUser.getShowRedCount() == 0){
            byCustUser.setShowRedCount(byCustUser.getShowRedCount()+1);
            byCustUserMapper.updateByPrimaryKeySelective(byCustUser);
        }
    }

    @Override
    public Boolean  updateByCustUserInformation(ByCustUser byCustUser){

        if (null == byCustUser || null == byCustUser.getId()) throw new CustomException("请求数据异常 ");
        int i = byCustUserMapper.updateByCustUserInformation(byCustUser);
        return i>0;
    }

}
