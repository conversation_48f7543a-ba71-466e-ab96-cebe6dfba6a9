package com.wmeimob.fastboot.baoyan.controller.mall.sys;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.BaseNavigationConf;
import com.wmeimob.fastboot.baoyan.entity.ByTicketGoods;
import com.wmeimob.fastboot.baoyan.service.BaseNavigationConfService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName BaseNavigationConfController
 * @Description 【导航设置】控制器
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("basenavigationconf")
@Slf4j
public class BaseNavigationConfController {

    @Resource
    private BaseNavigationConfService baseNavigationConfService;


    /**
     * 导航设置分页查询
     *
     * @param request
     * @param baseNavigationConf
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForBaseNavigationConf(HttpServletRequest request, BaseNavigationConf baseNavigationConf) {
        PageContext.startPage();
        return new PageInfo<BaseNavigationConf>(baseNavigationConfService.findByCondition(baseNavigationConf));

    }

    /**
     * 导航设置导出
     *
     * @param request
     * @param baseNavigationConf
     * @return
     */
    @GetMapping("/exports")
    public List<BaseNavigationConf> queryForBaseNavigationConfexports(HttpServletRequest request, BaseNavigationConf
            baseNavigationConf) {
        return baseNavigationConfService.findByCondition(baseNavigationConf);
    }


    /**
     * 导航设置查询-<通过id查询>
     *
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BaseNavigationConf queryForBaseNavigationConfById(HttpServletRequest request, @PathVariable("id") Object id) {
        return baseNavigationConfService.queryBaseNavigationConfById(id);
    }


    /**
     * 导航设置添加
     *
     * @param request
     * @param baseNavigationConf
     * @return
     */
    @PostMapping("/")
    public void insertForBaseNavigationConf(HttpServletRequest request, @RequestBody BaseNavigationConf baseNavigationConf) {
        baseNavigationConfService.addBaseNavigationConf(baseNavigationConf);
    }


    /**
     * 导航设置修改
     *
     * @param request
     * @param baseNavigationConf
     * @return
     */
    @PutMapping("/")
    public void updateForBaseNavigationConf(HttpServletRequest request, @RequestBody BaseNavigationConf baseNavigationConf) {
        baseNavigationConfService.modifyBaseNavigationConf(baseNavigationConf);
    }

    /**
     * 导航设置删除
     *
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBaseNavigationConf(HttpServletRequest request, @PathVariable("id") Object id) {
        baseNavigationConfService.removeBaseNavigationConf(id);
    }

    /**
     * 上下架修改
     * @param request
     * @param baseNavigationConf
     * @return
     */
    @PutMapping("/updateShelf")
    public void updateShelf(HttpServletRequest request,@RequestBody BaseNavigationConf baseNavigationConf){
        baseNavigationConfService.updateShelf(baseNavigationConf);
    }
}
