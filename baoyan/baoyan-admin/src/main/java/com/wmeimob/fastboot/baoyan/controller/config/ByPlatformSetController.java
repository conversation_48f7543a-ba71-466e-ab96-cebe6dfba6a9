package com.wmeimob.fastboot.baoyan.controller.config;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByPlatformSet;
import com.wmeimob.fastboot.baoyan.service.ByPlatformSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByPlatformSetController
 * @Description 【平台设置表】控制器
 * <AUTHOR>
 * @Date Mon Jul 22 17:18:53 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byplatformset")
@Slf4j
public class ByPlatformSetController {

    @Resource
    private ByPlatformSetService byPlatformSetService;




    /**
     * 平台设置表分页查询
     * @param request
     * @param byPlatformSet
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByPlatformSet(HttpServletRequest request, ByPlatformSet byPlatformSet){
        PageContext.startPage();
        return new PageInfo<ByPlatformSet>(byPlatformSetService.findByCondition(byPlatformSet));

    }


    /**
     * 平台设置表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByPlatformSet queryForByPlatformSetById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byPlatformSetService.queryByPlatformSetById(id);
    }


    /**
     * 平台设置表添加
     * @param request
     * @param byPlatformSet
     * @return
     */
    @PostMapping("/")
    public void insertForByPlatformSet(HttpServletRequest request,@RequestBody ByPlatformSet byPlatformSet){
            byPlatformSetService.addByPlatformSet(byPlatformSet);    
    }


    /**
     * 平台设置表修改
     * @param request
     * @param byPlatformSet
     * @return
     */
    @PutMapping("/")
    public void updateForByPlatformSet(HttpServletRequest request,@RequestBody ByPlatformSet byPlatformSet){
            byPlatformSetService.modifyByPlatformSet(byPlatformSet);  
    }

    /**
     * 平台设置表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByPlatformSet(HttpServletRequest request,@PathVariable("id") Object id){
            byPlatformSetService.removeByPlatformSet(id);
    }
}
