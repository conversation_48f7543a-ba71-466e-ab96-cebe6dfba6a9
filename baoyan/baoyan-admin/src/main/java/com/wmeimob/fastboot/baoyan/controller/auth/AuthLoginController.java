package com.wmeimob.fastboot.baoyan.controller.auth;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.wmeimob.fastboot.baoyan.annotation.ApiVersion;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.SysUserBase;
import com.wmeimob.fastboot.baoyan.service.AuthLoginService;
import com.wmeimob.fastboot.core.context.RequestAttributeContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.JsonWebToken;
import com.wmeimob.fastboot.starter.security.JwtAuthenticationFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

@RestController("AuthLoginController")
@RequestMapping
@Slf4j
public class AuthLoginController {
    @Resource
    private AuthLoginService authLoginService;
    @Resource
    private JwtAuthenticationFilter filter;
    @Resource
    private JsonWebToken jsonWebToken;

    @ApiVersion(value = 2)
    @PostMapping({"/v2/login"})
    @SaIgnore
    public R login(@RequestBody SysUserBase loginUser) {
        SysUser login = this.authLoginService.login(loginUser);
        HttpServletResponse response = RequestAttributeContext.getResponse();
//        StpUtil.login(login.getId());
        String token = this.filter.getJsonWebTokenHandler().generateToken(login);
        HashMap<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("user", login);
        return R.data(map);
    }
}
