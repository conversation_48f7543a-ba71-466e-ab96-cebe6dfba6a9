package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.baoyan.entity.ByPlatformSet;
import com.wmeimob.fastboot.baoyan.mapper.ByCouponMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByCouponTempMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByPlatformSetMapper;
import com.wmeimob.fastboot.baoyan.service.ByCouponService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByCouponServiceImpl
 * @Description 优惠券 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 17:55:47 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByCouponServiceImpl implements ByCouponService {

    @Resource
    private ByCouponMapper byCouponMapper;
    @Resource
    private ByCouponTempMapper couponTempMapper;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;


    @Override
    public List<ByCoupon> findByCondition(ByCoupon byCoupon) {
        return byCouponMapper.getCouponList(byCoupon);
    }

    @Override
    public ByCoupon queryByCouponById(Object id) {
        return byCouponMapper.selectByPrimaryKey(id);
    }

    @Override
    public void removeByCoupon(Integer id) {
        //效验优惠券是否在其他地方有引用(商城设置)
        checkCoupon(id);
        ByCoupon byCoupon = new ByCoupon();
        byCoupon.setId(Integer.parseInt(id.toString()));
        byCoupon.setIsDel(true);
        byCouponMapper.updateByPrimaryKeySelective(byCoupon);
    }

    /**
     * 效验优惠券是否在其他地方有引用
     * @param id
     */
    private void checkCoupon(Integer id) {
        Example example=new Example(ByPlatformSet.class);
        example.createCriteria().andEqualTo("birthdayCouponId", id);
        List<ByPlatformSet> birthdayCouponList = byPlatformSetMapper.selectByExample(example);
        if (null!=birthdayCouponList&&birthdayCouponList.size()>0) {
            throw new CustomException("当前优惠券在首页设置引用，暂不能删除");
        }
        example.clear();
        example.createCriteria().andEqualTo("perfectInfoCouponId", id);
        List<ByPlatformSet> perfectInfoCouponList = byPlatformSetMapper.selectByExample(example);
        if (null!=perfectInfoCouponList&&perfectInfoCouponList.size()>0) {
            throw new CustomException("当前优惠券在首页设置引用，暂不能删除");
        }
    }

    @Override
    public void modifyCoupon(ByCoupon coupon) {
        coupon.setTargetId(couponTempMapper.selectByPrimaryKey(coupon.getTempId()).getTargetId());
        coupon.setGmtUpdate(new Date());
        byCouponMapper.updateByPrimaryKeySelective(coupon);
    }

    @Override
    public void addCoupon(ByCoupon coupon) {
        coupon.setGmtCreate(new Date());
        coupon.setTaked(0);
        coupon.setIsDel(Boolean.FALSE);
        coupon.setTargetId(couponTempMapper.selectByPrimaryKey(coupon.getTempId()).getTargetId());
        byCouponMapper.insertSelective(coupon);
    }
    @Override
    public void updateByPrimaryKeyeSlective(ByCoupon coupon) {
        coupon.setGmtUpdate(new Date());
        byCouponMapper.updateByPrimaryKeySelective(coupon);
    }
}
