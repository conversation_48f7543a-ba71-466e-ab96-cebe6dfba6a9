package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.YchGoods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class YchGoodsServiceImpl implements YchGoodsService {

    @Resource
    private YchApiService ychApiService;

    @Override
    public List<YchGoods> list(Integer goodsType) {
        return ychApiService.getGoodsList(goodsType, true);
    }
}
