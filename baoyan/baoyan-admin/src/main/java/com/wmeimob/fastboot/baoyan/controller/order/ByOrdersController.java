package com.wmeimob.fastboot.baoyan.controller.order;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.mapper.ByOrdersMapper;
import com.wmeimob.fastboot.baoyan.service.ByOrderLogService;
import com.wmeimob.fastboot.baoyan.service.ByOrdersService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.vo.OrderResVO;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @ClassName ByOrdersController
 * @Description 【商品订单表】控制器
 * <AUTHOR>
 * @Date Mon Jul 15 15:41:21 CST 2019
 * @version1.0
 **/



@RestController
@RequestMapping("byorders")
@Slf4j
public class ByOrdersController {

    @Resource
    private ByOrdersService byOrdersService;

    @Resource
    private ByOrderLogService orderLogService;

    @Resource
    private ByOrdersMapper byOrdersMapper;

    /**
     * 商品订单表分页查询
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByOrders(HttpServletRequest request, ByOrders byOrders){
//        log.info("===========>入参 =======>{}",byOrders);
        PageContext.startPage();
        return new PageInfo<ByOrders>(byOrdersService.findByCondition(byOrders));

    }

     /**
     * 商品订单表导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/exports")
    public List<ByOrders> queryForByOrdersexports(HttpServletRequest request, ByOrders
 byOrders){
        //modify by 2019 10.18
        return  byOrdersService.findExportByCondition(byOrders);
    }

    /**
     * 商品订单表导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/v2/exports")
    public void queryForByOrdersexportsV2(HttpServletRequest request, HttpServletResponse response, ByOrders
            byOrders){
        List<ByOrders> byOrdersList =  byOrdersService.findExportByCondition(byOrders);
        for (ByOrders orderItem : byOrdersList){
            if(orderItem.getOrderStatus().equals(1)){
                orderItem.setOrderStatusName("待付款");
            }else if(orderItem.getOrderStatus().equals(2)){
                orderItem.setOrderStatusName("已付款");
            }else if(orderItem.getOrderStatus().equals(3)){
                orderItem.setOrderStatusName("已完成");
            }else if(orderItem.getOrderStatus().equals(4)){
                orderItem.setOrderStatusName("已退款");
           }else if(orderItem.getOrderStatus().equals(-1)){
                orderItem.setOrderStatusName("已取消");
            }
        }

        HashMap<String,String> exportFieldSets = new HashMap<>();
        exportFieldSets.put("orderNo","订单编号");
        exportFieldSets.put("orderAmount","商品总额");
        exportFieldSets.put("integralAmount","积分抵扣");
        exportFieldSets.put("couponAmount","优惠券抵扣");
        exportFieldSets.put("actualAmount","订单金额");
        exportFieldSets.put("custUserName","下单人");
        exportFieldSets.put("goodsName","商品名称");
        exportFieldSets.put("mobile","手机号");
        exportFieldSets.put("orderTime","下单时间");
        exportFieldSets.put("orderStatusName","状态");
        //TODO 后续可以导出自定义哪些字段进行导出
//        if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getExportFields())) {
//            exportFieldSets = Sets.newHashSet(dto.getExportFields().split(","));
//        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(String.format("订单管理导出_%s", DateUtils.format(new Date(), "yyyy-MM-dd")), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            ServletOutputStream out=response.getOutputStream();
            // 通过工具类创建writer，默认创建xls格式
            ExcelWriter writer = ExcelUtil.getWriter();
            // 一次性写出内容，使用默认样式，强制输出标题
            //自定义标题别名
            exportFieldSets.forEach(writer::addHeaderAlias);
            //其他不在标题中的字段去掉
            writer.setOnlyAlias(true);
//            writer.addHeaderAlias("name", "姓名");
            //out为OutputStream，需要写出到的目标流
            List<Map<String, Object>> rows = byOrdersList.stream()
                .map(order -> {
                    Map<String, Object> row = new HashMap<>();
                    row.put("orderNo", order.getOrderNo());
                    row.put("orderAmount", order.getOrderAmount());
                    row.put("integralAmount", order.getIntegralAmount());
                    row.put("couponAmount", order.getCouponAmount());
                    row.put("actualAmount", order.getActualAmount());
                    row.put("custUserName", order.getCustUserName());
                    row.put("goodsName", order.getGoodsName());
                    row.put("mobile", order.getMobile());
                    row.put("orderTime", order.getOrderTime());
                    row.put("orderStatusName", order.getOrderStatusName());
                    return row;
                })
                .collect(Collectors.toList());
            writer.write(rows);
            writer.flush(out);
            // 关闭writer，释放内存
            writer.close();
            IoUtil.close(out);

//            EasyExcel
//                    .write(response.getOutputStream(), ByOrders.class)
//                    .sheet("订单统计")
//                    .doWrite(byOrdersList);
        } catch (Exception e) {
            log.info("订单管理:{}", e.getMessage());
        }
    }

    /**
     * 商品订单表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public OrderResVO queryForByOrdersById(HttpServletRequest request, @PathVariable("id") Integer id){
        OrderResVO resVO = byOrdersService.queryByOrdersById(id);
        return  resVO;
    }

    @GetMapping("/findOne")
    public OrderResVO findOne(Integer id, String orderNo){
        log.info("===========>入参 =======>id:{},orderNo:{}",id,orderNo);
        if ( id==null && orderNo==null ){
            throw new CustomException("参数为空");
        }

        if ( id!=null ){
            log.info("===========>执行queryByOrdersById =======>");
            return byOrdersService.queryByOrdersById(id);
        }else{
            log.info("===========>执行findByOrderNo =======>");
            ByOrders dbOrder = byOrdersMapper.findByOrderNo(orderNo);
            return byOrdersService.queryByOrdersById(dbOrder.getId());
        }

    }


    /**
     * 商品订单表添加
     * @param request
     * @param byOrders
     * @return
     */
    @PostMapping("/")
    public void insertForByOrders(HttpServletRequest request,@RequestBody ByOrders byOrders){
            byOrdersService.addByOrders(byOrders);
    }


    /**
     * 商品订单表修改
     * @param request
     * @param byOrders
     * @return
     */
    @PutMapping("/")
    public void updateForByOrders(HttpServletRequest request,@RequestBody ByOrders byOrders){
            byOrdersService.modifyByOrders(byOrders);
    }

    /**
     * 商品订单表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByOrders(HttpServletRequest request,@PathVariable("id") Object id){
            byOrdersService.removeByOrders(id);
    }
    /**
     * 退款
     */
    @GetMapping("/refund/{id}")
    public void refund(@PathVariable("id") Integer id){
        byOrdersService.refund(id);
    }

    @GetMapping("/refunds")
    public String refunds(){
        return byOrdersService.refunds();
    }



}
