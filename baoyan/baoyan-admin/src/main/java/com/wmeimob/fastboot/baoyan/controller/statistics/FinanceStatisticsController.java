package com.wmeimob.fastboot.baoyan.controller.statistics;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.service.ByOrdersService;
import com.wmeimob.fastboot.baoyan.vo.FinanceStatisticsVO;
import com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: goodsStatistics
 * @projectName baoyan
 * @description: 财务统计
 * @date 2019/7/15 15:37
 */
@RestController
@RequestMapping("financeStatistics")
@Slf4j
public class FinanceStatisticsController {
    @Resource
    private ByOrdersService byOrdersService;
    /**
     * 财务统计
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/")
    public PageInfo queryFinanceStatistics(HttpServletRequest request, ByOrders byOrders){
        PageContext.startPage();
        return new PageInfo<FinanceStatisticsVO>(byOrdersService.queryFinanceStatistics(byOrders));

    }

    /**
     * 财务统计导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/exports")
    public List<FinanceStatisticsVO> queryFinanceStatisticsExports(HttpServletRequest request, ByOrders byOrders){
        return byOrdersService.queryFinanceStatistics(byOrders);
    }
}
