package com.wmeimob.fastboot.baoyan.tool.impl;

import com.mzlion.core.date.DateUtils;
import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.TcOrderService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.starter.wechat.service.WepayService;
import lombok.extern.slf4j.Slf4j;
import me.hao0.wepay.core.Wepay;
import me.hao0.wepay.model.refund.RefundApplyRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> :Deng.YH
 * @Description:
 * @date 2019-08-26 09:30
 * @Version 1.0
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class SchedulerImpl {
    private final static Logger logger = LoggerFactory.getLogger(SchedulerImpl.class);
    @Resource
    private ByOrdersMapper byOrdersMapper;
    @Resource
    private ByPlatformSetMapper byPlatformSetMapper;
    @Resource
    private SchedulerUtil schedulerUtil;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;
    @Resource
    private ByOrderGoodsMapper byOrderGoodsMapper;
    @Resource
    private ByTeamMapper byTeamMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private ByTeamOrderMapper byTeamOrderMapper;
    @Resource
    private ByTeamRobotMapper byTeamRobotMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;
    @Resource
    private MessageUtil messageUtil;
    @Resource
    private ByIntegralLogMapper byIntegralLogMapper;
    @Resource
    private ByCouponMapper byCouponMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ByOrderAfterMapper byOrderAfterMapper;
    @Autowired
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Autowired
    private BySubCardGoodsMapper bySubCardGoodsMapper;

    @Autowired
    private ByCouponUserMapper byCouponUserMapper;


    public void cloneOrder() {
        // 插叙系统
        ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
        // 查询待支付时间是否为空 或者为0
        if (byPlatformSet != null && byPlatformSet.getPayTime() != null && byPlatformSet.getPayTime() != 0) {
            // 查询待支付订单
            Example example = new Example(ByOrders.class);
            example.createCriteria().
                    andEqualTo("orderStatus", 1);
            List<ByOrders> byOrderGoods = this.byOrdersMapper.selectByExample(example);
            // 判断查询结果是否大于0
            if (byOrderGoods.size() > 0) {
                // 循环遍历所有订单
                byOrderGoods.forEach(e -> {
                    // 订单创建时间 加上  支付超时时间
                    Calendar instance = Calendar.getInstance();
                    instance.setTime(e.getGmtCreate());
                    instance.add(Calendar.HOUR_OF_DAY, byPlatformSet.getPayTime());
                    // 获取当前时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date());
                    // 当前时间与支付结束时间对比
                    if (calendar.getTime().getTime() > instance.getTime().getTime()) {
                        // -1订单取消1-待付款,2-已付款,3-已完成,4-已关闭
                        e.setOrderCloseType(1);
                        e.setOrderStatus(4);
                        e.setCancelTime(new Date());
                        e.setGmtUpdate(new Date());
                        // 跟新订单
                        int i = this.byOrdersMapper.updateByPrimaryKeySelective(e);
                        // 跟新成功 返回积分
                        if (i > 0) {
                            // 返回用户积分
                            if (e.getIntegralAmount() != null && new BigDecimal(0).compareTo(e.getIntegralAmount()) != 0
                                    && byPlatformSet.getIntegrationDeduction() != null && byPlatformSet.getIntegrationDeduction() != 0) {
                                // 取消订单 返回积分
                                schedulerUtil.cloneOrder(e, byPlatformSet.getIntegrationDeduction());
                            }
                        }
                    }
                });
            }
        }

    }



    public void sendMsg() {
        /*查看优惠券*/
        Example example2 = new Example(ByCouponUser.class);
        example2.createCriteria().andEqualTo("isUse", 0);
        List<ByCouponUser> byCouponUsers = this.byCouponUserMapper.selectByExample(example2);
        if (byCouponUsers.size() > 0) {
            // 遍历所有优惠券
            for (ByCouponUser b : byCouponUsers) {
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date());
                // 优惠券结束时间和当前时间对比
                if (instance.getTime().getTime() > b.getEndDate().getTime()) {
                    b.setIsUse(2);
                    this.byCouponUserMapper.updateByPrimaryKeySelective(b);
                }
            }
        }
        // 获取当前时间
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        // 获取前七天
        instance.add(Calendar.DATE, 7);
        // 格式化时间
        String s = DateUtils.formatDate(instance.getTime(), "yyyy-MM-dd");
        Example example = new Example(WriteOffCode.class);
        example.createCriteria()
                .andNotEqualTo("surplusNum", 0);
        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
        if (writeOffCodes.size() > 0) {
            writeOffCodes.forEach(e -> {
                String s1 = DateUtils.formatDate(e.getExpiryDate(), "yyyy-MM-dd");
                if (s.equals(s1)) {

                    ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(e.getCustUserId());

                    Example example1 = new Example(ByOrderGoods.class);
                    example1.createCriteria()
                            .andEqualTo("orderNo", e.getOrderNo())
                            .andEqualTo("isAfterSale", Boolean.FALSE)
                            .andEqualTo("goodsId", e.getSourceGoodsId());
                    List<ByOrderGoods> byOrderGoods = this.byOrderGoodsMapper.selectByExample(example1);

                    if (byOrderGoods.size() != 0) {
                        if (byCustUser != null) {
                            Map<String, String> map = new HashMap<>(3);
                            map.put("name", byCustUser.getParentName());
                            map.put("goodname", e.getGoodsName());
                            map.put("date", "7");
                            schedulerUtil.createCode(map, byCustUser.getMobile(), "refuse");
                        }
                    }
                }
            });
        }
    }

    /**
     * 拼团机器人
     */
    public void payTeam() {
        List<ByTeam> byTeams = this.byTeamMapper.selectByExampleLIst();

        if (byTeams.size() != 0) {
            for (ByTeam e : byTeams) {
                ByTeamGoods byTeamGoods = this.byTeamGoodsMapper.selectByPrimaryKey(e.getTeamGoodsId());
                if (byTeamGoods != null && !byTeamGoods.getIsDel() && byTeamGoods.getAutoHour() != null
                        && byTeamGoods.getAutoHour() != 0 && byTeamGoods.getTeamHour() != null && byTeamGoods.getTeamHour() > byTeamGoods.getAutoHour()) {
                    Calendar instance1 = Calendar.getInstance();
                    instance1.setTime(e.getGmtCreate());
                    instance1.add(Calendar.HOUR, byTeamGoods.getAutoHour().intValue());
                    if (System.currentTimeMillis() - instance1.getTime().getTime() >= 0) {
                        List<ByTeamRobot> byTeamRobots = this.byTeamRobotMapper.selectAll();
                        if (byTeamRobots.size() >= e.getLessNum()) {
                            e.setOrderStatus(1);
                            e.setEndDate(new Date());
                            int i = this.byTeamMapper.updateByPrimaryKeySelective(e);
                            if (i != 0) {
                                Example example1 = new Example(ByTeamOrder.class);
                                example1.createCriteria().andEqualTo("teamId", e.getId()).andEqualTo("orderStatus", 2);
                                List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example1);
                                for (int j = 0; j < e.getLessNum(); j++) {
                                    ByTeamOrder byTeamOrder = new ByTeamOrder();
                                    byTeamOrder.setTeamId(e.getId());
                                    byTeamOrder.setTeamGoodsId(e.getTeamGoodsId());
                                    byTeamOrder.setUserId(0);
                                    byTeamOrder.setOrderNo(UUIDOrder.getUUID());
                                    byTeamOrder.setPayType(1);
                                    byTeamOrder.setOrderAmount(new BigDecimal(0));
                                    byTeamOrder.setNum(1);
                                    byTeamOrder.setOrderStatus(-1);
                                    byTeamOrder.setIsTeam(false);
                                    byTeamOrder.setIsAfter(Boolean.FALSE);
                                    byTeamOrder.setGmtCreate(new Date());
                                    byTeamOrder.setIsDel(Boolean.FALSE);
                                    byTeamOrder.setOrderTime(new Date());
                                    byTeamOrder.setUserName(byTeamRobots.get(j).getObotNickName());
                                    byTeamOrder.setUserImg(byTeamRobots.get(j).getObotImg());
                                    byTeamOrder.setGmtUpdate(new Date());
                                    byTeamOrder.setGmtCreate(new Date());
                                    byTeamOrder.setPayTime(new Date());
                                    byTeamOrder.setOrderStatus(1);
                                    byTeamOrder.setOrderState(1);//拼团机器人订单
                                    byTeamOrderMapper.insertSelective(byTeamOrder);
                                }
                                if (byTeamOrders.size() != 0) {
                                    byTeamOrders.forEach(data -> {
                                        Map<String, String> dat = new HashMap<>();
                                        dat.put("keyword1", byTeamGoods.getTeamName());
                                        dat.put("keyword2", e.getGoodsName());
                                        dat.put("keyword3", e.getTeamPrice().toString());
                                        messageUtil.sendWxTemplateMsg(dat, byCustUserMapper.selectByPrimaryKey(data.getUserId()).getWxOpenId(), "grj4toazHVskQ_tTubQLEJYUc221Q1lk0eeEO2cMBxw", data.getFromId(), null);
                                    });
                                }
                            }
                        }
                    }
                }
                if (byTeamGoods != null && !byTeamGoods.getIsDel() && byTeamGoods.getTeamHour() != null
                        && byTeamGoods.getTeamHour() != 0 && byTeamGoods.getTeamHour() != null && e.getOrderStatus() == 0) {
                    Calendar instance1 = Calendar.getInstance();
                    Calendar instance = instance1;
                    instance.setTime(e.getGmtCreate());
                    instance.add(Calendar.HOUR, byTeamGoods.getTeamHour());
                    instance1.setTime(new Date());
                    if (System.currentTimeMillis() - e.getEndDate().getTime() >= 0) {
                        e.setOrderStatus(2);
                        e.setEndDate(new Date());
                        int i = this.byTeamMapper.updateByPrimaryKeySelective(e);
                        if (i != 0) {
                            Example example1 = new Example(ByTeamOrder.class);
                            example1.createCriteria().andEqualTo("teamId", e.getId());
                            List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(example1);
                            byTeamOrders.forEach(data -> {
                                try {
                                   // refundPay(data.getOrderNo(), String.valueOf(instance1.getTime().getTime()), new BigDecimal(0.01), new BigDecimal(0.01));
                                    refundPay(data.getOrderNo(), String.valueOf(instance1.getTime().getTime()), data.getPayAmount(), data.getPayAmount());
                                    ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(data.getUserId());
                                    ByPlatformSet byPlatformSet = byPlatformSetMapper.selectByPrimaryKey(1);
                                    byTeamGoods.setStockNum(byTeamGoods.getSortNum() + 1);
                                    this.byTeamGoodsMapper.updateByPrimaryKeySelective(byTeamGoods);
                                    if (byPlatformSet != null || data.getIntegralAmount() != null || new BigDecimal(0).compareTo(data.getIntegralAmount()) != 0) {
                                        BigDecimal divide = data.getIntegralAmount().multiply(new BigDecimal(byPlatformSet.getIntegrationDeduction()));
                                        Integer d = divide.intValue();
                                        ByIntegralLog byIntegralLog = new ByIntegralLog();
                                        byIntegralLog.setUserId(data.getUserId());
                                        byIntegralLog.setChangeType(2);
                                        byIntegralLog.setChangeNum(d);
                                        byIntegralLog.setChangeReason("商品购退款");
                                        byIntegralLog.setIntegralType(2);
                                        byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                                        byIntegralLog.setGmtCreate(new Date());
                                        this.byTeamGoodsMapper.updateId(data.getTeamGoodsId());
                                        this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(),d);
                                        if (byIntegralLog.getChangeNum() > 0) {
                                            byIntegralLogMapper.insertSelective(byIntegralLog);
                                        }
                                    }
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }
                            });

                        }
                    }
                }
            }
            ;
        }


    }

    @Resource
    private WechatProperties wechatProperties;
    @Resource
    private WepayService wepayService;

    /**
     * <AUTHOR>
     * @date 2019/8/12 11:15
     * @Description: 微信退款
     */
    private void refundPay(String orderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee) {
        Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
        RefundApplyRequest request = new RefundApplyRequest();
        request.setOutTradeNo(orderNo);
        request.setOutRefundNo(refundNo);
        request.setTotalFee(totalFee.multiply(CommonFinal.BIG_100).intValue());
        request.setRefundFee(refundFee.multiply(CommonFinal.BIG_100).intValue());
        request.setOpUserId("1");
        try {
            wepay.refund().apply(request);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
    }

    /**
     * 扫描过期优惠券
     */
    public void checkCouponExpire() {
        //查询用户过期优惠卷
        List<ByCouponUser> userList = byCouponUserMapper.checkCouponUserExpire();
        if (userList.size() > 0) {
            for (ByCouponUser user : userList) {
                user.setIsUse(BaoYanConstant.IS_USE_2);
                byCouponUserMapper.updateByPrimaryKeySelective(user);
            }
        }

    }

    public static void main(String[] args) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.HOUR, 1);

    }

    /**
     * 扫描过期核销码
     */
    public void checkWriteOffCodeExpire() {
        List<WriteOffCode> writeOffCodes = writeOffCodeMapper.checkWriteOffCodeExpire();
        if (writeOffCodes.size() > 0) {
            for (WriteOffCode code : writeOffCodes) {
                if (code.getSurplusNum().equals(code.getTotalNum()) && code.getStatus() == 0){
                    code.setIsExpire(1);
                }
                code.setStatus(BaoYanConstant.CONSTANT_TWO);//过期状态
                code.setSurplusNum(0);
                writeOffCodeMapper.updateByPrimaryKeySelective(code);
                //查询订单下 的核销码是否全被核销 （包含过期）若是全部被核销 包含获取则订单状态变成 已完成
                //根据订单编号 订单类型 核销码状态（未核销）   查询订单下是否还存在未核销的核销码
                Example example = new Example(WriteOffCode.class);
                example.createCriteria().andEqualTo("orderNo", code.getOrderNo()).
                        andEqualTo("orderType", code.getOrderType()).andEqualTo("status", 0);
                List<WriteOffCode> queryList = writeOffCodeMapper.selectByExample(example);
                if (null == queryList || queryList.size() <= 0) {
                    //当前订单下 核销码全部被核销完 或者过期 更新订单状态 已完成.
                    if (code.getOrderType().equals(0)) {
                        //普通订单
                        Example orderExample = new Example(ByOrders.class);
                        orderExample.createCriteria().andEqualTo("orderNo", code.getOrderNo());
                        ByOrders orders = new ByOrders();
                        orders.setOrderStatus(3);//已完成
                        orders.setGmtUpdate(new Date());
                        List<ByOrders> byOrders = this.byOrdersMapper.selectByExample(orderExample);
                        if (byOrders.size() != 0) {
                            ByOrders byOrders1 = byOrders.get(0);
                            if (byOrders1.getOrderStatus() >= 2 ) {
                                byOrdersMapper.updateByExampleSelective(orders, orderExample);
                            }
                        }
                        get(code.getDetailId(), 1);
                    }
                    if (code.getOrderType().equals(1)) {
                        //拼团订单
                        Example teamOrderExample = new Example(ByTeamOrder.class);
                        teamOrderExample.createCriteria().andEqualTo("orderNo", code.getOrderNo());
                        ByTeamOrder teamOrders = new ByTeamOrder();
                        teamOrders.setOrderStatus(3);//已完成
                        List<ByTeamOrder> byTeamOrders = this.byTeamOrderMapper.selectByExample(teamOrderExample);
                        if (byTeamOrders.size() != 0) {
                            ByTeamOrder byTeamOrder = byTeamOrders.get(0);
                            if (byTeamOrder.getOrderStatus() >= 2) {
                                ByTeam byTeam = this.byTeamMapper.selectByPrimaryKey(byTeamOrder.getTeamId());
                                if (byTeam != null && byTeam.getOrderStatus() == 2) {
                                    byTeamOrderMapper.updateByExampleSelective(teamOrders, teamOrderExample);
                                }
                            }

                        }
                        get(code.getDetailId(), 2);
                    }
                }
            }
        }
    }

    /**
     * @Description 优惠券过期提醒
     * <AUTHOR>
     * @Date 2019年9月11日10:07:08
     * @Version 1.0
     * 10点触发
     */
    public void couponRemindMsg() {
        ByPlatformSet set = byPlatformSetMapper.selectByPrimaryKey(1);
        if (null != set && null != set.getCouponInvalid()) {
            ByCouponUser byCouponUser = new ByCouponUser();
            byCouponUser.setAuditStatus(1);
            byCouponUser.setIsUse(0);
            List<ByCouponUser> couponGetList = byCouponUserMapper.getCouponGetList(byCouponUser);
            if (null != couponGetList && couponGetList.size() > 0) {
                for (ByCouponUser user : couponGetList) {
                    //处理判断 对应是否过期
                    Calendar currentInstance = Calendar.getInstance();
                    currentInstance.setTime(new Date());
                    currentInstance.add(Calendar.DATE, set.getCouponInvalid());
                    Calendar endCalendar = Calendar.getInstance();
                    endCalendar.setTime(user.getEndDate());
                    //当前根据配置增加时间 是否大于优惠卷end 使用时间
                    if (currentInstance.getTime().getTime() > endCalendar.getTime().getTime()) {
                        ByCustUser custUser = byCustUserMapper.selectByPrimaryKey(user.getUserId());
                        Map<String, String> map = new HashMap<>(3);
                        map.put("name", custUser.getNickName());
                        map.put("coupon", user.getName());
                        Calendar dayCurrent = Calendar.getInstance();
                        //计算剩余多少天过期
                        dayCurrent.setTime(new Date());
                        //结束到期时间-当前时间 =剩余过期时间
                        int dateNum = calcTime(endCalendar, dayCurrent);
                        map.put("date", String.valueOf(dateNum));
                        String existKey = stringRedisTemplate.opsForValue().get(custUser.getId() + ":" + user.getCouponId());
                        //判断之前 是否有推送过这个 优惠券提醒
                        if (null == existKey && dateNum == 7) {
                            schedulerUtil.createCode(map, custUser.getMobile(), "coupon");
                            //设置缓存时间  缓存key 内容
                            stringRedisTemplate.opsForValue().set(custUser.getId() + ":" + user.getCouponId(), custUser.getId() + ":" + user.getCouponId(), 365, TimeUnit.DAYS);
                        }
                    }
                }
            }

        } else {
            logger.debug("couponRemindMsg() ByPlatformSet:", set);
        }
    }

    /**
     * 计算两个时间相差几天
     *
     * @param currentInstance
     * @param endCalendar
     * @return
     */
    private static int calcTime(Calendar currentInstance, Calendar endCalendar) {
        int days = (int) ((currentInstance.getTime().getTime() - endCalendar.getTime().getTime()) / (1000 * 3600 * 24));
        return days;
    }

    public void get(Integer id, Integer type) {
        if (type == 1) {
            ByOrderGoods byOrderGoods = this.byOrderGoodsMapper.selectByPrimaryKey(id);
            ByOrders byOrders = byOrdersMapper.selectByPrimaryKey(byOrderGoods.getOrderId());
            if (byOrders.getActualAmount() != null && byOrders.getActualAmount().compareTo(new BigDecimal(0)) != 0) {
                Example example2 = new Example(ByOrderGoods.class);
                example2.createCriteria().andEqualTo("orderId", byOrders.getId());
                List<ByOrderGoods> byOrderGoodss = this.byOrderGoodsMapper.selectByExample(example2);

                ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);

                if (byOrderGoodss.size() != 0 && byPlatformSet != null && byPlatformSet.getIntegralReturn() != null && byPlatformSet.getIntegralReturn().compareTo(new BigDecimal(0)) != 0 && byOrders.getActualAmount().compareTo(new BigDecimal(0)) != 0) {
                    BigDecimal bigDecimal = new BigDecimal(0);
                    for (ByOrderGoods byOrderGood : byOrderGoodss) {
                        Example example1 = new Example(ByOrderAfter.class);
                        example1.createCriteria().andEqualTo("afterStatus", 1).andEqualTo("detailId", byOrderGood.getId()).andEqualTo("resouceType", 1);
                        List<ByOrderAfter> byOrderAfters = this.byOrderAfterMapper.selectByExample(example1);
                        if (byOrderAfters.size() != 0) {
                            continue;
                        } else {
                            BigDecimal multiply1 = byOrderGood.getGoodsPrice().multiply(new BigDecimal(byOrderGood.getGoodsNum()));
                            BigDecimal subtract = multiply1.subtract(byOrderGood.getIntegralPrice()).subtract(byOrderGood.getCouponPrice());
                            bigDecimal = bigDecimal.add(subtract);
                            log.info("结果 -------》：" + bigDecimal.toString() + "________________________________________________________");
                        }

                        Example example = new Example(WriteOffCode.class);
                        //查询 订单详情 状态 为0 状态state 1
                        example.createCriteria().andEqualTo("detailId", byOrderGood.getId()).andEqualTo("status", 0).andEqualTo("orderState", 0);
                        List<WriteOffCode> writeOffCodes = this.writeOffCodeMapper.selectByExample(example);
                        if (writeOffCodes.size() != 0) {
                            return;
                        }
                    }
                    if (bigDecimal.compareTo(new BigDecimal(0)) != 0) {
                        log.info(bigDecimal.toString());
                        ByCustUser byCustUser = this.byCustUserMapper.selectByPrimaryKey(byOrders.getUserId());
                        ByIntegralLog byIntegralLog = new ByIntegralLog();
                        byIntegralLog.setUserId(byCustUser.getId());
                        byIntegralLog.setChangeType(1);
                        Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(bigDecimal).intValue();
                        logger.info("get() coun:{}", coun);
                        byIntegralLog.setChangeNum(coun);
                        byIntegralLog.setChangeReason("凌晨记录---商品购买赠送");
                        byIntegralLog.setIntegralType(2);
                        byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                        byIntegralLog.setGmtCreate(new Date());
                        logger.info("get() newPoint:{}", byCustUser.getNowPoint() + coun);
//                        this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(),coun);
                        if (byIntegralLog.getChangeNum() > 0) {
//                            byIntegralLogMapper.insertSelective(byIntegralLog);
                        }
                        byOrders.setUseTime(new Date());
                        this.byOrdersMapper.updateByPrimaryKeySelective(byOrders);
                    }
                }
            }
        } else {
            //拼团订单
            ByTeamOrder byOrders1 = this.byTeamOrderMapper.selectByPrimaryKey(id);
            if (byOrders1 != null && byOrders1.getIntegralAmount() != null && byOrders1.getIntegralAmount().compareTo(new BigDecimal(0)) != 0) {
                ByPlatformSet byPlatformSet = this.byPlatformSetMapper.selectByPrimaryKey(1);
                if (byOrders1.getIntegralAmount() != null && byPlatformSet.getCommentIntegral() != null && byPlatformSet.getCommentIntegral().equals(0) == false) {
                    Integer coun = (byPlatformSet.getIntegralReturn().divide(new BigDecimal(100))).multiply(byOrders1.getPayAmount()).intValue();
                    ByCustUser byCustUser = byCustUserMapper.selectByPrimaryKey(byOrders1.getUserId());
                    ByIntegralLog byIntegralLog = new ByIntegralLog();
                    byIntegralLog.setUserId(byOrders1.getUserId());
                    byIntegralLog.setChangeType(1);
                    byIntegralLog.setChangeNum(Integer.parseInt(coun.toString()));
                    byIntegralLog.setChangeReason("拼团");
                    byIntegralLog.setBeforeNum(byCustUser.getNowPoint());
                    byIntegralLog.setGmtCreate(new Date());
                    byIntegralLog.setIntegralType(1);
                    if (byIntegralLog.getChangeNum() > 0) {
                        byIntegralLogMapper.insertSelective(byIntegralLog);
                    }
                    this.byCustUserMapper.updateAddUserPoint(byCustUser.getId(),byIntegralLog.getChangeNum());
                    if (byIntegralLog.getChangeNum() > 0) {
                        byIntegralLogMapper.insertSelective(byIntegralLog);
                    }
                }
                byOrders1.setUseTime(new Date());
                byTeamOrderMapper.updateByPrimaryKeySelective(byOrders1);
            }

        }
    }

    /**
     * check 产品核销期限过期 自动下架
     */
    public void checkPrdWrioffEffective() {
        //普通商品
        Example goodExample = new Example(ByGoodsInfo.class);
        goodExample.createCriteria().andEqualTo("status", 1).andEqualTo("isDel", 0);
        List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(goodExample);
        if (!CollectionUtils.isEmpty(byGoodsInfos)) {
            Calendar calendar = Calendar.getInstance();
            for (ByGoodsInfo goodsInfo : byGoodsInfos) {
                calendar.setTime(goodsInfo.getVerificationEnd());
                //核销结束时间 《当前时间 （已过期）
                if (calendar.getTime().getTime() < new Date().getTime()) {
                    goodsInfo.setGmtModified(new Date());
                    goodsInfo.setStatus(0);
                    byGoodsInfoMapper.updateByPrimaryKeySelective(goodsInfo);
                }
            }
        }
        //联票商品
        Example ticketExample = new Example(ByTicketGoods.class);
        ticketExample.createCriteria().andEqualTo("status", 1).andEqualTo("isDel", 0);
        List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(ticketExample);
        if (!CollectionUtils.isEmpty(byTicketGoods)) {
            Calendar calendar = Calendar.getInstance();
            for (ByTicketGoods ticketGoods : byTicketGoods) {
                calendar.setTime(ticketGoods.getEffectiveEnd());
                if (calendar.getTime().getTime() < new Date().getTime()) {
                    ticketGoods.setGmtUpdate(new Date());
                    ticketGoods.setStatus(false);
                    byTicketGoodsMapper.updateByPrimaryKeySelective(ticketGoods);
                }
            }
        }
        //次卡商品
        Example subExample = new Example(BySubCardGoods.class);
        subExample.createCriteria().andEqualTo("status", 1).andEqualTo("isDel", 0);
        List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(subExample);
        if (!CollectionUtils.isEmpty(bySubCardGoods)) {
            Calendar calendar = Calendar.getInstance();
            for (BySubCardGoods subCardGoods : bySubCardGoods) {
                calendar.setTime(subCardGoods.getEffectiveEnd());
                if (calendar.getTime().getTime() < new Date().getTime()) {
                    subCardGoods.setGmtUpdate(new Date());
                    subCardGoods.setStatus(0);
                    bySubCardGoodsMapper.updateByPrimaryKeySelective(subCardGoods);
                }
            }
        }
    }

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private TcOrderService tcOrderService;

    /**
     * 关闭两小时还没有支付的淘潮玩订单
     */
    public void closeTcOrder() {
        Calendar now = Calendar.getInstance();
        now.add(Calendar.HOUR_OF_DAY,-2);

        //查询过期未支付的订单
        Example example = new Example(TcOrder.class);
        example.createCriteria()
                .andLessThan("gtmCreate",now.getTime())
                .andEqualTo("status",1);
        List<TcOrder> tcOrders = tcOrderMapper.selectByExample(example);

        for (TcOrder order : tcOrders) {
            //订单状态更新状态为 已取消
            TcOrder tcOrder = TcOrder.builder()
                    .id(order.getId())
                    .status(-1)
                    .build();

            tcOrderMapper.updateByPrimaryKeySelective(tcOrder);

            //订单详情 状态更新为已取消
            tcOrderGoodsMapper.updateStatus(order.getId(), -1);

            tcOrderService.cancelOrder(order.getId());
        }

    }
}
