package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeLogMapper;
import com.wmeimob.fastboot.baoyan.service.WriteOffCodeLogService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName WriteOffCodeLogServiceImpl
 * @Description  核销码记录表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 23 13:40:35 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class WriteOffCodeLogServiceImpl implements WriteOffCodeLogService {

    @Resource
    private WriteOffCodeLogMapper writeOffCodeLogMapper;


    @Override
    public List<WriteOffCodeLog> findByCondition(WriteOffCodeLog writeOffCodeLog) {

        List<WriteOffCodeLog> writeOffCodeLogs = writeOffCodeLogMapper.findByCondition(writeOffCodeLog);
        return writeOffCodeLogs;
    }

    @Override
    public WriteOffCodeLog queryWriteOffCodeLogById(Object id) {
        return writeOffCodeLogMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addWriteOffCodeLog(WriteOffCodeLog writeOffCodeLog) {
	  writeOffCodeLog.setGmtCreate(new Date());
        writeOffCodeLogMapper.insertSelective(writeOffCodeLog);
    }

    @Override
    public void removeWriteOffCodeLog(Object id) {
	  WriteOffCodeLog writeOffCodeLog = new WriteOffCodeLog();
	  writeOffCodeLog.setId(Integer.parseInt(id.toString()));
        writeOffCodeLogMapper.updateByPrimaryKeySelective(writeOffCodeLog);
    }

    @Override
    public void modifyWriteOffCodeLog(WriteOffCodeLog writeOffCodeLog) {
		writeOffCodeLog.setGmtModified(new Date());
        writeOffCodeLogMapper.updateByPrimaryKeySelective(writeOffCodeLog);
    }

}
