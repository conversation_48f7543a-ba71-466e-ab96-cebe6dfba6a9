package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTeam;
import com.wmeimob.fastboot.baoyan.service.ByTeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByTeamController
 * @Description 【拼团管理】控制器
 * <AUTHOR>
 * @Date Tue Jul 16 15:59:56 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byteam")
@Slf4j
public class ByTeamController {

    @Resource
    private ByTeamService byTeamService;




    /**
     * 拼团管理分页查询
     * @param request
     * @param byTeam
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTeam(HttpServletRequest request, ByTeam byTeam){
        PageContext.startPage();
        return new PageInfo<ByTeam>(byTeamService.findByCondition(byTeam));
         
    }

     /**
     * 拼团管理导出
     * @param request
     * @param byTeam
     * @return
     */
    @GetMapping("/exports")
    public List<ByTeam> queryForByTeamexports(HttpServletRequest request, ByTeam 
        byTeam){
        return  byTeamService.findByCondition(byTeam);
    }


    /**
     * 拼团管理查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTeam queryForByTeamById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byTeamService.queryByTeamById(id);
    }


    /**
     * 拼团管理添加
     * @param request
     * @param byTeam
     * @return
     */
    @PostMapping("/")
    public void insertForByTeam(HttpServletRequest request,@RequestBody ByTeam byTeam){
            byTeamService.addByTeam(byTeam);    
    }


    /**
     * 拼团管理修改
     * @param request
     * @param byTeam
     * @return
     */
    @PutMapping("/")
    public void updateForByTeam(HttpServletRequest request,@RequestBody ByTeam byTeam){
            byTeamService.modifyByTeam(byTeam);  
    }

    /**
     * 拼团管理删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByTeam(HttpServletRequest request,@PathVariable("id") Object id){
            byTeamService.removeByTeam(id);
    }
}
