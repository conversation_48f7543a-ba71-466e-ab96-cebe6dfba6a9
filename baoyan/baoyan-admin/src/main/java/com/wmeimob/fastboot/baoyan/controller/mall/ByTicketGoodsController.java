package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTicketGoods;
import com.wmeimob.fastboot.baoyan.service.ByTicketGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @ClassName ByTicketGoodsController
 * @Description 【联票商品表】控制器
 * <AUTHOR>
 * @Date Thu Jul 25 13:46:23 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byticketgoods")
@Slf4j
public class ByTicketGoodsController {

    @Resource
    private ByTicketGoodsService byTicketGoodsService;



    private final String DATE_FORMAT="yyy-MM-dd HH:mm:ss";
    /**
     * 联票商品表分页查询
     * @param request
     * @param byTicketGoods
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTicketGoods(HttpServletRequest request, ByTicketGoods byTicketGoods){
        PageContext.startPage();
        return new PageInfo<ByTicketGoods>(byTicketGoodsService.findByCondition(byTicketGoods));
         
    }

     /**
     * 联票商品表导出
     * @param request
     * @param byTicketGoods
     * @return
     */
    @GetMapping("/exports")
    public List<ByTicketGoods> queryForByTicketGoodsexports(HttpServletRequest request, ByTicketGoods 
 byTicketGoods){
        return  byTicketGoodsService.findByCondition(byTicketGoods);
    }


    /**
     * 联票商品表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTicketGoods queryForByTicketGoodsById(HttpServletRequest request, @PathVariable("id") Integer id){
            return  byTicketGoodsService.queryByTicketGoodsById(id);
    }


    /**
     * 联票商品表添加
     * @param request
     * @param byTicketGoods
     * @return
     */
    @PostMapping("/")
    public void insertForByTicketGoods(HttpServletRequest request,@RequestBody ByTicketGoods byTicketGoods){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        log.info("联票商品表添加 时间 time：{} 联票商品信息byTicketGoods：{}", sdf.format(new Date()),byTicketGoods);
            byTicketGoodsService.addByTicketGoods(byTicketGoods);    
    }


    /**
     * 联票商品表修改
     * @param request
     * @param byTicketGoods
     * @return
     */
    @PutMapping("/")
    public void updateForByTicketGoods(HttpServletRequest request,@RequestBody ByTicketGoods byTicketGoods){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        log.info("联票商品表修改 时间 time：{} 联票商品信息byTicketGoods：{}", sdf.format(new Date()),byTicketGoods);
            byTicketGoodsService.modifyByTicketGoods(byTicketGoods);  
    }

    /**
     * 联票商品表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByTicketGoods(HttpServletRequest request,@PathVariable("id") Object id){
            byTicketGoodsService.removeByTicketGoods(id);
    }

    /**
     * 商品信息表上下架修改
     * @param request
     * @param byTicketGoods
     * @return
     */
    @PutMapping("/updateShelf")
    public void updateShelf(HttpServletRequest request,@RequestBody ByTicketGoods byTicketGoods){
        byTicketGoodsService.updateShelf(byTicketGoods);
    }
}
