package com.wmeimob.fastboot.baoyan.controller.mall;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.BaseStore;
import com.wmeimob.fastboot.baoyan.entity.ByGoodsInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.BySubCardGoods;
import com.wmeimob.fastboot.baoyan.service.BySubCardGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @ClassName BySubCardGoodsController
 * @Description 【次卡商品表】控制器
 * <AUTHOR>
 * @Date Thu Jul 25 10:13:01 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bysubcardgoods")
@Slf4j
public class BySubCardGoodsController {

    @Resource
    private BySubCardGoodsService bySubCardGoodsService;
    private final String DATE_FORMAT="yyy-MM-dd HH:mm:ss";



    /**
     * 次卡商品表分页查询
     * @param request
     * @param bySubCardGoods
     * @return
     */
    @GetMapping("/")
    public PageInfo
    queryForBySubCardGoods(HttpServletRequest request, BySubCardGoods bySubCardGoods){
        PageContext.startPage();
        return new PageInfo<BySubCardGoods>(bySubCardGoodsService.findByCondition(bySubCardGoods));
    }

    /**
     * 次卡商品表列表
     */
    @GetMapping("/list")
    public R queryStoreList(BySubCardGoods bySubCardGoods) {
        return R.data(bySubCardGoodsService.findByCondition(bySubCardGoods));
    }

     /**
     * 次卡商品表导出
     * @param request
     * @param bySubCardGoods
     * @return
     */
    @GetMapping("/exports")
    public List<BySubCardGoods> queryForBySubCardGoodsexports(HttpServletRequest request, BySubCardGoods 
   bySubCardGoods){
        return  bySubCardGoodsService.findByCondition(bySubCardGoods);
    }


    /**
     * 次卡商品表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public BySubCardGoods queryForBySubCardGoodsById(HttpServletRequest request, @PathVariable("id") Integer id){
        return  bySubCardGoodsService.queryBySubCardGoodsById(id);
    }


    /**
     * 次卡商品表添加
     * @param request
     * @param bySubCardGoods
     * @return
     */
    @PostMapping("/")
    public void insertForBySubCardGoods(HttpServletRequest request,@RequestBody BySubCardGoods bySubCardGoods) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        log.info("次卡商品表添加 时间 time：{} 次卡商品信息bySubCardGoods：{}", sdf.format(new Date()),bySubCardGoods);
        bySubCardGoodsService.addBySubCardGoods(bySubCardGoods);
    }


    /**
     * 次卡商品表修改
     * @param request
     * @param bySubCardGoods
     * @return
     */
    @PutMapping("/")
    public void updateForBySubCardGoods(HttpServletRequest request,@RequestBody BySubCardGoods bySubCardGoods) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        log.info("次卡商品表修改 时间 time：{} 次卡商品信息bySubCardGoods：{}", sdf.format(new Date()),bySubCardGoods);
        bySubCardGoodsService.modifyBySubCardGoods(bySubCardGoods);
    }

    /**
     * 次卡商品表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForBySubCardGoods(HttpServletRequest request,@PathVariable("id") Object id){
            bySubCardGoodsService.removeBySubCardGoods(id);
    }
    /**
     * 次卡商品  上下架修改
     * @param request
     * @param bySubCardGoods
     * @return
     */
    @PutMapping("/updateShelf")
    public void updateShelf(HttpServletRequest request,@RequestBody BySubCardGoods bySubCardGoods){
        bySubCardGoodsService.updateShelf(bySubCardGoods);
    }
}
