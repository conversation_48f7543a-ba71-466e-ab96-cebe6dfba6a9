package com.wmeimob.fastboot.baoyan.service;

import cn.hutool.core.collection.CollectionUtil;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.baoyan.utils.DecimalUtil;
import com.wmeimob.fastboot.beetl.core.util.ConnectionUtil;
import com.wmeimob.fastboot.core.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/8
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TcOrderAfterServiceImpl implements TcOrderAfterService {

    @Resource
    private TcOrderAfterMapper tcOrderAfterMapper;

    @Resource
    private TcOrderGoodsMapper tcOrderGoodsMapper;

    @Resource
    private ByOrderAfterService byOrderAfterService;

    @Resource
    private TcOrderMapper tcOrderMapper;

    @Resource
    private TcOrderParentMapper tcOrderParentMapper;

    @Resource
    private TcOrderService tcOrderService;

    @Resource
    private TcWriteOffCodeMapper tcWriteOffCodeMapper;


    @Override
    public boolean rejectAfter(TcOrderAfter tcOrderAfter) {
        //根据id查询
        Integer id = tcOrderAfter.getId();
        TcOrderAfter dbAfter = tcOrderAfterMapper.selectByPrimaryKey(id);
        //判断状态
        if (dbAfter.getStatus() == 3 || dbAfter.getStatus() == 4) {
            throw new CustomException("已同意 | 已拒绝 售后无法拒绝");
        }

        //改变原订单状态
        Integer detailId = dbAfter.getDetailId();
        tcOrderGoodsMapper.updateByPrimaryKeySelective(
                TcOrderGoods.builder()
                        .id(detailId)
                        .canAfter(true)
                        .build()
        );


        //更新售后状态
        tcOrderAfterMapper.updateByPrimaryKeySelective(
                TcOrderAfter.builder()
                        .id(tcOrderAfter.getId())
                        .handleTime( new Date() )
                        .rejectMsg(tcOrderAfter.getRejectMsg())
                        .status(4)
                        .build()
        );

        return true;
    }

    /**
     * 管理员 退款一个订单商品
     *
     * @param orderGoodsId
     * @return
     */
    @Override
    public boolean refundSingeOrder(Integer orderGoodsId, Integer hopeRefund) {
        //查询订单商品状态
        TcOrderGoods dbOrderGoods = tcOrderGoodsMapper.selectByPrimaryKey(orderGoodsId);

        //实付单价是 0
        if ( DecimalUtil.zeroNull(dbOrderGoods.getUnitPrice()) ){
            throw new CustomException("金额太少，不予退款");
        }

        //检查订单状态
        Assert.eq(dbOrderGoods.getStatus(), 5, "已退款订单无法退款");

        //检查数量是否足够继续退款
        if ( dbOrderGoods.getGoodsCount()-dbOrderGoods.getRefundCount() < hopeRefund ){
            throw new CustomException("商品总数量："+dbOrderGoods.getGoodsCount()+"个，已经退款"+dbOrderGoods.getRefundCount()
            +"个，不能再退款"+hopeRefund+"个");
        }

        TcOrderGoods orderGoods = TcOrderGoods.builder()
                .id(orderGoodsId)
                .refundCount( dbOrderGoods.getRefundCount()+hopeRefund )
                .build();

        //如果全部退款，订单状态改为 已退款
        if ( orderGoods.getRefundCount().equals(dbOrderGoods.getGoodsCount()) ){
            orderGoods.setCanAfter(false);
            orderGoods.setStatus(5);
        }

        //查询订单
        TcOrder dbOrder = tcOrderMapper.findByOrderNo(dbOrderGoods.getTcOrderNo());

        //如果是 到店自提订单
        //让核销码变为 已退款
        //注意！！如果以后淘潮玩不是一物一码， 这里需要修改
        if ( dbOrder.getDeliveryMode()==1 && !Objects.equals(orderGoods.getStatus(),5) ){
            tcWriteOffCodeMapper.updateRefund(dbOrderGoods.getId(), hopeRefund);
            //没有核销码是 待使用状态。 代表全部使用完毕了，订单状态改为已完成
            Example example = new Example(TcWriteOffCode.class);
            example.createCriteria()
                    .andEqualTo("status",0)
                    .andEqualTo("detailId", dbOrderGoods.getId());

            List<TcWriteOffCode> writeOffCodes = tcWriteOffCodeMapper.selectByExample(example);
            if ( CollectionUtil.isEmpty(writeOffCodes) ){
//                orderGoods.setCanAfter(false);
                orderGoods.setStatus(4);
            }
        }


        //修改订单 退款过的数量
        //修改订单商品状态
        tcOrderGoodsMapper.updateByPrimaryKeySelective(orderGoods);



        //退钱
        BigDecimal totalFee = dbOrder.getActualAmount();
        //判断 支付订单号 和 当前订单号是不是同一个
        //不是同一个 代表混合支付，商家支付单号不一样
        if (!Objects.equals(dbOrder.getOrderNo(), dbOrder.getPayOrderNo())) {
            //混合支付
            TcOrderParent parentOrder = tcOrderParentMapper.findByOrderParentNo(dbOrder.getPayOrderNo());
            totalFee = parentOrder.getPayAmount();
        }

        //检查订单，如果全部都是已退款，订单状态变为已退款
        boolean allRefund = tcOrderService.allRefund(dbOrder.getId());

        //如果没有全部退款，查看其他订单是否是已完成
        if (!allRefund) {
            tcOrderService.allComplete(dbOrder.getOrderNo());
        }

        BigDecimal refundFee = dbOrderGoods.getUnitPrice().multiply(BigDecimal.valueOf(hopeRefund));

        //调用微信退款
        byOrderAfterService.refundPay(dbOrder.getPayOrderNo(), String.valueOf(System.currentTimeMillis()), totalFee, refundFee);

        return true;
    }

    /**
     * 同意售后并退款
     * @param id 售后订单id
     * @return
     */
    @Override
    public boolean audit(Integer id) {
        //判断售后申请单状态是否正常
        TcOrderAfter dbAfter = tcOrderAfterMapper.selectByPrimaryKey(id);

        Integer afterType = dbAfter.getAfterType();
        if (Objects.equals(afterType, 1)) {
            //仅退款订单 申请中就可以退款
            Assert.notEq(dbAfter.getStatus(), 1, "仅退款售后申请中的状态才能同意");
        } else if (Objects.equals(afterType, 2)) {
            //退款退货订单 必须寄出
            Assert.isNull(dbAfter.getLogisticsNo(), "退货退款订单必须用户寄出才能同意");
        } else {
            throw new CustomException("退款类型不正确");
        }


        Integer detailId = dbAfter.getDetailId();
        //查询订单商品状态
        TcOrderGoods dbOrderGoods = tcOrderGoodsMapper.selectByPrimaryKey(detailId);
        Assert.eq(dbOrderGoods.getStatus(), 5, "已退款订单无法退款");

        //查看申请数量是否 超过可退款数量
        if ( dbAfter.getRefundCount() > (dbOrderGoods.getGoodsCount()-dbOrderGoods.getRefundCount()) ){
            throw new CustomException("商品总数量："+dbOrderGoods.getGoodsCount()+"个，已经退款"+dbOrderGoods.getRefundCount()
                    +"个，不能再退款"+dbAfter.getRefundCount()+"个");
        }

        //修改订单商品状态
        TcOrderGoods orderGoods = TcOrderGoods.builder()
                .id(detailId)
                .refundCount( dbOrderGoods.getRefundCount() + dbAfter.getRefundCount() )
                .status(5)
                .build();

        //同意售后
        TcOrderAfter after = TcOrderAfter.builder()
                .id(id)
                .handleTime(new Date())
                .status(3)
                .build();

        tcOrderAfterMapper.updateByPrimaryKeySelective(after);

        tcOrderGoodsMapper.updateByPrimaryKeySelective(orderGoods);

        //退钱
        TcOrder dbOrder = tcOrderMapper.findByOrderNo(dbAfter.getOrderNo());

        BigDecimal totalFee = dbOrder.getActualAmount();
        //判断 支付订单号 和 当前订单号是不是同一个
        //不是同一个 代表混合支付，商家支付单号不一样
        if (!Objects.equals(dbOrder.getOrderNo(), dbOrder.getPayOrderNo())) {
            //混合支付
            TcOrderParent parentOrder = tcOrderParentMapper.findByOrderParentNo(dbOrder.getPayOrderNo());
            totalFee = parentOrder.getPayAmount();
        }

        //检查订单，如果全部都是已退款，订单状态变为已完成
        boolean allRefund = tcOrderService.allRefund(dbOrder.getId());

        if (!allRefund) {
            tcOrderService.allComplete(dbOrder.getOrderNo());
        }

        //调用微信退款
        byOrderAfterService.refundPay(dbOrder.getPayOrderNo(), String.valueOf(System.currentTimeMillis()), totalFee, dbAfter.getRefundPrice());

        return true;
    }


    /**
     * 管理员指定用户退货的地址
     *
     * @param tcOrderAfter
     * @return
     */
    @Override
    public boolean appointAddress(TcOrderAfter tcOrderAfter) {
        Integer id = tcOrderAfter.getId();
        String addr = tcOrderAfter.getRefundAddr();
        String consignee = tcOrderAfter.getRefundConsignee();
        String phone = tcOrderAfter.getRefundPhone();

        Assert.isNull("收货信息不全", addr, consignee, phone);

        TcOrderAfter dbAfter = tcOrderAfterMapper.selectByPrimaryKey(id);
        Assert.notEq(dbAfter.getAfterType(), 2, "只有【退货退款】才需要填写退货地址");
        Assert.notEq(dbAfter.getStatus(), 1, "订单状态不正确");

        TcOrderAfter after = TcOrderAfter.builder()
                .id(id)
                .refundAddr(addr)
                .refundConsignee(consignee)
                .refundPhone(phone)
                .handleTime(new Date())
                .status(2)
                .build();

        int update = tcOrderAfterMapper.updateByPrimaryKeySelective(after);

        return update > 0;
    }
}
