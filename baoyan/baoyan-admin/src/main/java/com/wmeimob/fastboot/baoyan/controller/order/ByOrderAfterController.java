package com.wmeimob.fastboot.baoyan.controller.order;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.enums.OrderLogType;
import com.wmeimob.fastboot.baoyan.service.*;
import com.wmeimob.fastboot.baoyan.vo.OrderAfterDetailVO;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


/**
 * @ClassName ByOrderAfterController
 * @Description 【订单售后】控制器
 * <AUTHOR>
 * @Date Tue Jul 16 13:48:35 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byorderafter")
@Slf4j
public class ByOrderAfterController {

    @Resource
    private ByOrderAfterService byOrderAfterService;

    @Resource
    private WriteOffCodeLogService writeOffCodeLogService;

    @Resource
    private WriteOffCodeService writeOffCodeService;

    @Resource
    private ByCustUserService byCustUserService;

    @Resource
    private ByOrderLogService orderLogService;


    /**
     * 根据核销卡进行退款
     * @param refund
     * @return
     */
    @PostMapping("/returnByWrite")
    public boolean returnByWrite(@RequestBody RefundVo refund){
        byOrderAfterService.returnByWrite(refund.getIds(), refund.getResouceType());
        return true;
    }

    /**
     * 对单个订单进行退款
     * @return
     */
    @PostMapping("/returnSingeOrder")
    public Boolean returnSingeOrder(@RequestBody ByOrderAfter byOrderAfter){
        if (byOrderAfter.getGoodsNum()>1){
            throw new RuntimeException("一次只能退款一个");
        }
        //调用service完成订单退款
        if (byOrderAfter.getOrderGoodsId()==null || byOrderAfter.getOrderId()==null){
            throw new CustomException("参数不对");
        }

        //进行退款逻辑
        boolean flag = byOrderAfterService.returnSingeOrder(byOrderAfter);

        //记录日志
        ByOrderLog orderLog = ByOrderLog.builder()
                .operatorType(2)
                .logType(OrderLogType.ADMIN_REFUND)
                .operatorCount(byOrderAfter.getGoodsNum())
                .orderGoodsId(byOrderAfter.getDetailId())
                .build();
        orderLogService.addOrderLogAsync(orderLog);


        return flag;
    }

    /**
     * 查看售后详情 回显
     * @param request
     * @param byOrderAfter
     * @return
     */
    @GetMapping("/getAfterDetail")
    public OrderAfterDetailVO queryAfterByOrderIdAndGoodsId(HttpServletRequest request, ByOrderAfter byOrderAfter){
        log.info(byOrderAfter.toString());
        byOrderAfter.setDetailId( byOrderAfter.getOrderGoodsId() );
        return  byOrderAfterService.queryAfterByOrderId(byOrderAfter);
    }


    /**
     * 订单售后分页查询
     * @param request
     * @param byOrderAfter
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByOrderAfter(HttpServletRequest request, ByOrderAfter byOrderAfter){
        PageContext.startPage();
        return new PageInfo<ByOrderAfter>(byOrderAfterService.findByCondition(byOrderAfter));
         
    }

     /**
     * 订单售后导出
     * @param request
     * @param byOrderAfter
     * @return
     */
    @GetMapping("/exports")
    public List<ByOrderAfter> queryForByOrderAfterexports(HttpServletRequest request, ByOrderAfter 
    byOrderAfter){
        return  byOrderAfterService.findByCondition(byOrderAfter);
    }


    /**
     * 订单售后查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByOrderAfter queryForByOrderAfterById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byOrderAfterService.queryByOrderAfterById(id);
    }
    /**
     * 审核订单
     * @param request
     * @param byOrderAfter
     * @return
     */
    @PutMapping("/auditAfter")
    public void auditAfter(HttpServletRequest request,@RequestBody ByOrderAfter byOrderAfter){
        if(null == byOrderAfter.getId()){
            throw new CustomException("请选择售后订单");
        }
        if(null == byOrderAfter.getAfterStatus()){
            throw new CustomException("请选择处理类型");
        }

        if(byOrderAfter.getAfterStatus().equals(CommonFinal.TWO)){
            InputValidator.checkEmpty(byOrderAfter.getRefuseReason(),"拒绝原因");
        }
        //后台管理如果同意售后，传递的参数是id after_status（更改后的状态）
        byOrderAfterService.auditAfterOrder(byOrderAfter);


        //查询对应的售后
        ByOrderAfter dbAfter = byOrderAfterService.findById(byOrderAfter.getId());
        ByOrderLog orderLog = ByOrderLog.builder()
                .operatorType(2)
                .orderGoodsId(dbAfter.getDetailId())
                .operatorCount(dbAfter.getGoodsNum())
                .build();

        if (CommonFinal.ONE.equals(byOrderAfter.getAfterStatus())){
            //通过售后
            orderLog.setLogType(OrderLogType.AGREE_AFTER);
        }else if (CommonFinal.TWO.equals(byOrderAfter.getAfterStatus())){
            //拒绝售后
            orderLog.setLogType(OrderLogType.REFUSE_AFTER);
        }
        //记录售后信息
        orderLogService.addOrderLogAsync(orderLog);

        //更新角标
        this.byCustUserService.updateRed(byOrderAfter.getId());
    }

}
