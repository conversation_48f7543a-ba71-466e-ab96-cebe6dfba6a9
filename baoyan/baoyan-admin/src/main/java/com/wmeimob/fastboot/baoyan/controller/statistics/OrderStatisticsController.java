package com.wmeimob.fastboot.baoyan.controller.statistics;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import com.wmeimob.fastboot.baoyan.annotation.ApiVersion;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.service.ByOrdersService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @title: OrderStatistics
 * @projectName baoyan
 * @description: 订单统计
 * @date 2019/7/15 15:37
 */
@RestController
@RequestMapping("orderStatistics")
@Slf4j
public class OrderStatisticsController {
    @Resource
    private ByOrdersService byOrdersService;
    /**
     * 订单统计
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/")
    public PageInfo queryOrderStatistics(HttpServletRequest request, ByOrders byOrders){
        log.info("OrderStatisticsController->queryOrderStatistics 入参================>{}",byOrders);
        PageContext.startPage();
        return new PageInfo<ByOrders>(byOrdersService.queryOrderStatistics(byOrders));

    }

    /**
     * 订单统计导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/exports")
    public List<ByOrders> queryOrderStatisticsExports(HttpServletRequest request, ByOrders byOrders){
        return byOrdersService.queryOrderStatistics(byOrders);
    }

    @ApiVersion(value = 2)
    @GetMapping("/v2/exports")
    public void queryOrderStatisticsExportsV2(ByOrders byOrders,HttpServletRequest request, HttpServletResponse response){
        List<ByOrders> byOrdersList =  byOrdersService.queryOrderStatistics(byOrders);

        HashMap<String,String> exportFieldSets = new HashMap<>();
        exportFieldSets.put("orderNo","订单编号");
        exportFieldSets.put("custUserName","下单人");
        exportFieldSets.put("mobile","手机号");
        exportFieldSets.put("orderTime","下单时间");
        exportFieldSets.put("orderAmount","商品总额");
        exportFieldSets.put("couponAmount","优惠券抵扣");
        exportFieldSets.put("integralAmount","积分抵扣");
        exportFieldSets.put("actualAmount","实收金额");
        exportFieldSets.put("writeOffNum","核销次数");
        //TODO 后续可以导出自定义哪些字段进行导出
//        if (Objects.nonNull(dto) && StringUtils.isNotEmpty(dto.getExportFields())) {
//            exportFieldSets = Sets.newHashSet(dto.getExportFields().split(","));
//        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(String.format("订单统计导出_%s", DateUtils.format(new Date(), "yyyy-MM-dd")), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            ServletOutputStream out=response.getOutputStream();
            // 通过工具类创建writer，默认创建xls格式
            ExcelWriter writer = ExcelUtil.getWriter();
            // 一次性写出内容，使用默认样式，强制输出标题
            //自定义标题别名
            exportFieldSets.forEach(writer::addHeaderAlias);
//            writer.addHeaderAlias("name", "姓名");
            //out为OutputStream，需要写出到的目标流
            writer.write(byOrdersList);
            writer.flush(out);
            // 关闭writer，释放内存
            writer.close();
            IoUtil.close(out);

//            EasyExcel
//                    .write(response.getOutputStream(), ByOrders.class)
//                    .sheet("订单统计")
//                    .doWrite(byOrdersList);
        } catch (Exception e) {
            log.info("订单统计导出数据:{}", e.getMessage());
        }
    }

    /**
     * 订单统计 表头
     * @param byOrders
     * @return
     */
    @GetMapping("/finance/sum")
    public Map<String,Object> financeSum(ByOrders byOrders){
        if (byOrders.getEndTime().equals("")&&byOrders.getStartTime().equals("")) {
            byOrders.setStartTime(null);
            byOrders.setEndTime(null);
        }
        return byOrdersService.getFinanceOrderStatisticsSum(byOrders);
    }

}
