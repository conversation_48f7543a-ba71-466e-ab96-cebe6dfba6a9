package com.wmeimob.fastboot.baoyan.controller.cust;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.service.ByCustUserService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName ByCustUserController
 * @Description 【用户表】控制器
 * <AUTHOR>
 * @Date Fri Jul 05 13:51:52 CST 2019
 * @version 1.0
 **/
@RestController
@RequestMapping("bycustuser")
@Slf4j
public class ByCustUserController {

    @Resource
    private ByCustUserService byCustUserService;


    /**
     * 修改用户和小孩资料
     * @param byCustUser 待修改的 用户数据
     * @return true 成功 or false 失败
     */

    /**
     * 用户表用户修改
     * @param
     * @param
     * @return
     */
    @PostMapping("/updateUser")
    public boolean updateByCustUser(@RequestBody ByCustUser byCustUser){
        log.info("[请求参数]==========>\t{}",byCustUser);
        Boolean aBoolean = byCustUserService.updateByCustUserInformation(byCustUser);
        log.info("[响应结果]==========>\t{}",aBoolean);
        return aBoolean;
    }

    /**
     * 用户表分页查询
     * @param request
     * @param byCustUser
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByCustUser(HttpServletRequest request, ByCustUser byCustUser){
        PageContext.startPage();
        return new PageInfo<ByCustUser>(byCustUserService.findByCondition(byCustUser));
         
    }

     /**
     * 用户表导出
     * @param request
     * @param byCustUser
     * @return
     */
    @RequestMapping("/exports")
    public List<ByCustUser> queryForByCustUserexports(HttpServletRequest request,ByCustUser byCustUser){

        return  byCustUserService.findByCondition(byCustUser);
    }


    /**
     * 用户表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCustUser queryForByCustUserById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byCustUserService.queryByCustUserById(id);
    }


    /**
     * 用户表添加
     * @param request
     * @param byCustUser
     * @return
     */
    @PostMapping("/")
    public void insertForByCustUser(HttpServletRequest request,@RequestBody ByCustUser byCustUser){
            byCustUserService.addByCustUser(byCustUser);    
    }


    /**
     * 用户表修改
     * @param request
     * @param byCustUser
     * @return
     */
    @PutMapping("/")
    public void updateForByCustUser(HttpServletRequest request,@RequestBody ByCustUser byCustUser){
            byCustUserService.modifyByCustUser(byCustUser);  
    }
    /**
     * 变更积分
     * @param request
     * @return
     */
    @PutMapping("/updateIntegral")
    public void updateIntegral(HttpServletRequest request,@RequestBody ByCustUser sfUserInfo){
        if(sfUserInfo.getId() == null){
            throw new CustomException("用户信息错误");
        }
        if(sfUserInfo.getType() == null){
            throw new CustomException("请选择变更类型");
        }
        if(sfUserInfo.getChangeNum() == null){
            throw new CustomException("请填写变更数量");
        }
        byCustUserService.updateIntegral(sfUserInfo);
    }

    /**
     * 用户表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByCustUser(HttpServletRequest request,@PathVariable("id") Object id){
            byCustUserService.removeByCustUser(id);
    }
    /**
     * 用户禁用 解禁
     * @param userInfo
     * @return
     */
    @PutMapping("/openClose")
    public void openClose(@RequestBody ByCustUser userInfo){
        if(userInfo.getId() == null){
            throw new CustomException("参数错误");
        }
        byCustUserService.modifyByCustUser(userInfo);
    }
}
