package com.wmeimob.fastboot.baoyan.controller.tc;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcWriteOffCode;
import com.wmeimob.fastboot.baoyan.mapper.TcWriteOffCodeMapper;
import com.wmeimob.fastboot.baoyan.service.TcWriteOffCodeService;
import com.wmeimob.fastboot.baoyan.vo.BatchWrite;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/15
 */
@RequestMapping("/tc/writeOffCode")
@RestController
public class TcWriteOffCodeController {

    @Resource
    private TcWriteOffCodeMapper tcWriteOffCodeMapper;

    @Resource
    private TcWriteOffCodeService tcWriteOffCodeService;

    @GetMapping
    public PageInfo<TcWriteOffCode> findAll(TcWriteOffCode writeOffCode){
        PageContext.startPage();
        List<TcWriteOffCode> writeOffCodes = tcWriteOffCodeMapper.findByCondition(writeOffCode);
        return new PageInfo<>(writeOffCodes);
    }

    /**
     * 核销某个核销码
     * @return
     */
    @PutMapping
    public boolean writeOff(@RequestBody TcWriteOffCode writeOffCode){
        UserDetails user = SecurityContext.getUser();
        //SysUser


        return tcWriteOffCodeService.writeOff(writeOffCode, writeOffCode.getStaffId(), writeOffCode.getStoreIds());
    }

    /**
     * 批量核销
     * @return
     */
    @PutMapping("/batchWrite")
    public Object batchWrite(@RequestBody BatchWrite batchWrite){
        List<TcWriteOffCode> writeOffCodes = batchWrite.getWriteOffCodes();
        if (CollectionUtil.isEmpty(writeOffCodes)){
            throw new CustomException("您还没有选中");
        }

        UserDetails user = SecurityContext.getUser();
        int failCount = 0;

        StringBuilder builder = new StringBuilder();
        for (TcWriteOffCode offCode : writeOffCodes) {
            try {
                tcWriteOffCodeService.writeOff(offCode, batchWrite.getStaffId(), batchWrite.getStoreIds());
            } catch (Exception e) {
                failCount++;
                builder.append("订单号:")
                        .append(offCode.getOrderNo())
                        .append(",")
                        .append(e.getMessage())
                        .append("。</br>");
            }
        }

        /*builder.insert(0,"批量核销成功"+(writeOffCodes.size()-failCount)+
                "个</br>失败原因：</br>");*/

        HashMap<String, Object> result = new HashMap<>(2);
        result.put("flag",true);
        result.put("successCount", writeOffCodes.size()-failCount);
        result.put("message",builder.toString());

        return result;
    }

}
