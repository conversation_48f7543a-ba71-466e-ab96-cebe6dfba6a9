package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustAppointment;
import com.wmeimob.fastboot.baoyan.mapper.ByCustAppointmentMapper;
import com.wmeimob.fastboot.baoyan.service.ByCustAppointmentService;
import com.wmeimob.fastboot.baoyan.vo.ByCustAppointmentVO;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByCustAppointmentServiceImpl
 * @Description  客户预约表 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 09 14:34:33 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByCustAppointmentServiceImpl implements ByCustAppointmentService {

    @Resource
    private ByCustAppointmentMapper byCustAppointmentMapper;


    @Override
    public List<ByCustAppointment> findByCondition(ByCustAppointment byCustAppointment) {
        Example example = new Example(ByCustAppointment.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byCustAppointment.getId())){
            criteria.andEqualTo("id",byCustAppointment.getId());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getApponintmentType())){
            criteria.andEqualTo("apponintmentType",byCustAppointment.getApponintmentType());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getApponintmentDate())){
            criteria.andEqualTo("apponintmentDate",byCustAppointment.getApponintmentDate());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getApponintmentNum())){
            criteria.andEqualTo("apponintmentNum",byCustAppointment.getApponintmentNum());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getTicketChannel())){
            criteria.andEqualTo("ticketChannel",byCustAppointment.getTicketChannel());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getCustUserId())){
            criteria.andEqualTo("custUserId",byCustAppointment.getCustUserId());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getAddress())){
            criteria.andLike("address",StringUtils.fullFuzzy(byCustAppointment.getAddress()));
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getStoreId())){
            criteria.andEqualTo("storeId",byCustAppointment.getStoreId());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byCustAppointment.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getGmtModified())){
            criteria.andEqualTo("gmtModified",byCustAppointment.getGmtModified());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getIsDel())){
            criteria.andEqualTo("isDel",byCustAppointment.getIsDel());
	  }
	  if(!StringUtils.isEmpty(byCustAppointment.getApponintmentStatus())){
            criteria.andEqualTo("apponintmentStatus",byCustAppointment.getApponintmentStatus());
	  }
	  criteria.andEqualTo("isDel",false);
        example.orderBy("id").desc();
        List<ByCustAppointment> byCustAppointments = byCustAppointmentMapper.selectByExample(example);
        return byCustAppointments;
    }

    @Override
    public ByCustAppointment queryByCustAppointmentById(Object id) {
        return byCustAppointmentMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByCustAppointment(ByCustAppointment byCustAppointment) {
	  byCustAppointment.setGmtCreate(new Date());
        byCustAppointmentMapper.insertSelective(byCustAppointment);
    }

    @Override
    public void removeByCustAppointment(Object id) {
	  ByCustAppointment byCustAppointment = new ByCustAppointment();
	  byCustAppointment.setId(Integer.parseInt(id.toString()));
	  byCustAppointment.setIsDel(true);
        byCustAppointmentMapper.updateByPrimaryKeySelective(byCustAppointment);
    }

    @Override
    public void modifyByCustAppointment(ByCustAppointment byCustAppointment) {
		byCustAppointment.setGmtModified(new Date());
        byCustAppointmentMapper.updateByPrimaryKeySelective(byCustAppointment);
    }
    @Override
    public  List<ByCustAppointmentVO> queryAppoinmentList(ByCustAppointment byCustAppointment) {
        return byCustAppointmentMapper.queryAppoinmentList(byCustAppointment);
    }
    @Override
    public RestResult cancelAppointment(Integer id) {
        ByCustAppointment resultApp = byCustAppointmentMapper.selectByPrimaryKey(id);
        if (null==resultApp) {
            throw new CustomException("当前预约记录不存在");
        }
        if (!resultApp.getApponintmentStatus().equals(0)) {
            throw new CustomException("当前预约状态已改变");
        }
        ByCustAppointment appointment=new ByCustAppointment();
        appointment.setId(id);
        appointment.setApponintmentStatus(1);
        int num=byCustAppointmentMapper.updateByPrimaryKeySelective(appointment);
        return num>0? RestResult.success():RestResult.fail("修改失败");
    }

}
