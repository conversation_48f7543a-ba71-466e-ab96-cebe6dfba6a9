package com.wmeimob.fastboot.baoyan.controller.statistics;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.service.ByOrdersService;
import com.wmeimob.fastboot.baoyan.vo.GoodsStatisticsVO;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: goodsStatistics
 * @projectName baoyan
 * @description: 商品统计
 * @date 2019/7/15 15:37
 */
@RestController
@RequestMapping("goodsStatistics")
@Slf4j
public class GoodsStatisticsController {
    @Resource
    private ByOrdersService byOrdersService;
    /**
     * 商品统计
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/")
    public PageInfo queryGoodsStatistics(HttpServletRequest request, ByOrders byOrders){
        PageContext.startPage();
        return new PageInfo<GoodsStatisticsVO>(byOrdersService.queryGoodsStatistics(byOrders));

    }

    /**
     * 商品统计导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/exports")
    public List<GoodsStatisticsVO> queryGoodsStatisticsExports(HttpServletRequest request, ByOrders byOrders){
        return byOrdersService.queryGoodsStatistics(byOrders);
    }

    /**
     * 商品统计 表头
     * @param byOrders
     * @return
     */
    @GetMapping("/finance/sum")
    public Map<String,Object> financeSum(ByOrders byOrders){
        return byOrdersService.getFinanceGoodsStatisticsSum(byOrders);
    }

    /**
     * 次卡商品统计
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/querySubGoodsStatistics")
    public PageInfo querySubGoodsStatistics(HttpServletRequest request, ByOrders byOrders){
        PageContext.startPage();
        return new PageInfo<GoodsStatisticsVO>(byOrdersService.querySubGoodsStatistics(byOrders));
    }

    /**
     * 次卡商品统计导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/querySubGoodsStatisticsExports")
    public List<GoodsStatisticsVO> querySubGoodsStatisticsExports(HttpServletRequest request, ByOrders byOrders){
        return byOrdersService.querySubGoodsStatistics(byOrders);
    }
    /**
     * 次卡商品统计 表头
     * @param byOrders
     * @return
     */
    @GetMapping("/subFinance/sum")
    public Map<String,Object> subFinanceSum(ByOrders byOrders){
        return byOrdersService.getFinanceSubGoodsStatisticsSum(byOrders);
    }

    /**
     * 联票商品统计
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/queryTicketGoodsStatistics")
    public PageInfo queryTicketGoodsStatistics(HttpServletRequest request, ByOrders byOrders){
        PageContext.startPage();
        return new PageInfo<GoodsStatisticsVO>(byOrdersService.queryTicketGoodsStatistics(byOrders));
    }

    /**
     * 联票商品统计导出
     * @param request
     * @param byOrders
     * @return
     */
    @GetMapping("/queryTicketGoodsStatisticsExports")
    public List<GoodsStatisticsVO> queryTicketGoodsStatisticsExports(HttpServletRequest request, ByOrders byOrders){
        return byOrdersService.queryTicketGoodsStatistics(byOrders);
    }
    /**
     * 联票商品统计 表头
     * @param byOrders
     * @return
     */
    @GetMapping("/ticketFinance/sum")
    public Map<String,Object> ticketFinance(ByOrders byOrders){
        return byOrdersService.getFinanceTicketGoodsStatisticsSum(byOrders);
    }

}
