package com.wmeimob.fastboot.baoyan.controller.team;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByTeamRobot;
import com.wmeimob.fastboot.baoyan.service.ByTeamRobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByTeamRobotController
 * @Description 【拼团机器人】控制器
 * <AUTHOR>
 * @Date Thu Jul 11 11:18:31 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("byteamrobot")
@Slf4j
public class ByTeamRobotController {

    @Autowired
    private ByTeamRobotService byTeamRobotService;




    /**
     * 拼团机器人分页查询
     * @param request
     * @param byTeamRobot
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByTeamRobot(HttpServletRequest request, ByTeamRobot byTeamRobot){
        PageContext.startPage();
        return new PageInfo<ByTeamRobot>(byTeamRobotService.findByCondition(byTeamRobot));
         
    }

     /**
     * 拼团机器人导出
     * @param request
     * @param byTeamRobot
     * @return
     */
    @GetMapping("/exports")
    public List<ByTeamRobot> queryForByTeamRobotexports(HttpServletRequest request, ByTeamRobot 
 byTeamRobot){
        return  byTeamRobotService.findByCondition(byTeamRobot);
    }


    /**
     * 拼团机器人查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByTeamRobot queryForByTeamRobotById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byTeamRobotService.queryByTeamRobotById(id);
    }


    /**
     * 拼团机器人添加
     * @param request
     * @param byTeamRobot
     * @return
     */
    @PostMapping("/")
    public void insertForByTeamRobot(HttpServletRequest request,@RequestBody ByTeamRobot byTeamRobot){
            byTeamRobotService.addByTeamRobot(byTeamRobot);    
    }


    /**
     * 拼团机器人修改
     * @param request
     * @param byTeamRobot
     * @return
     */
    @PutMapping("/")
    public void updateForByTeamRobot(HttpServletRequest request,@RequestBody ByTeamRobot byTeamRobot){
            byTeamRobotService.modifyByTeamRobot(byTeamRobot);  
    }

    /**
     * 拼团机器人删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByTeamRobot(HttpServletRequest request,@PathVariable("id") Object id){
            byTeamRobotService.removeByTeamRobot(id);
    }
}
