package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.ByStoreStaff;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByStoreStaffMapper;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByStoreStaffServiceImpl
 * @Description 门店员工 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 09:48:59 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByStoreStaffServiceImpl implements ByStoreStaffService {

    @Resource
    private ByStoreStaffMapper byStoreStaffMapper;
    @Resource
    private ByCustUserMapper byCustUserMapper;


    @Override
    public List<ByStoreStaff> findByCondition(ByStoreStaff byStoreStaff) {
        Example example = new Example(ByStoreStaff.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byStoreStaff.getStoreId())) {
            criteria.andEqualTo("storeId", byStoreStaff.getStoreId());
        }
        if (!StringUtils.isEmpty(byStoreStaff.getId())) {
            criteria.andEqualTo("id", byStoreStaff.getId());
        }
        criteria.andEqualTo("isDel", false);

        Example.Criteria and = example.and();
        if (!StringUtils.isEmpty(byStoreStaff.getStaffName())) {
            and.andLike("staffName", StringUtils.fullFuzzy(byStoreStaff.getStaffName()));
            and.orLike("staffPhone", StringUtils.fullFuzzy(byStoreStaff.getStaffName()));
        }

        example.orderBy("id").desc();
        List<ByStoreStaff> byStoreStaffs = byStoreStaffMapper.selectByExample(example);
        return byStoreStaffs;
    }

    @Override
    public ByStoreStaff queryByStoreStaffById(Object id) {
        return byStoreStaffMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addByStoreStaff(ByStoreStaff byStoreStaff) {
        Example storeStaffExample=new Example(ByStoreStaff.class);
        storeStaffExample.createCriteria().andEqualTo("isDel", false).andEqualTo("staffPhone", byStoreStaff.getStaffPhone());
        List<ByStoreStaff> byStoreStaffs = byStoreStaffMapper.selectByExample(storeStaffExample);
        if (byStoreStaffs.size()>0) throw new CustomException("当前员工已存在");
        byStoreStaff.setGmtCreate(new Date());
        int num=byStoreStaffMapper.insertSelective(byStoreStaff);
        if (num<=0) {
            throw new CustomException("新增员工失败");
        }
        //判断员工是否在用户表中存在 绑定员工关系
        Example custUserExample=new Example(ByCustUser.class);
        custUserExample.createCriteria().andEqualTo("mobile", byStoreStaff.getStaffPhone());
        List<ByCustUser> byCustUsers = byCustUserMapper.selectByExample(custUserExample);
        if (byCustUsers.size()>0) {
            //绑定员工关系更新openId
            ByCustUser user=byCustUsers.get(0);
            byStoreStaff.setOpenId(user.getWxOpenId());
            byStoreStaffMapper.updateByPrimaryKeySelective(byStoreStaff);
        }

    }

    @Override
    public void removeByStoreStaff(Object id) {
        ByStoreStaff byStoreStaff = new ByStoreStaff();
        byStoreStaff.setId(Long.parseLong(id.toString()));
        byStoreStaff.setIsDel(true);
        byStoreStaffMapper.updateByPrimaryKeySelective(byStoreStaff);
    }

    @Override
    public void modifyByStoreStaff(ByStoreStaff byStoreStaff) {
        byStoreStaff.setGmtModified(new Date());
        byStoreStaffMapper.updateByPrimaryKeySelective(byStoreStaff);
    }

}
