package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByChannelCust;
import com.wmeimob.fastboot.baoyan.entity.ByOrders;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCode;
import com.wmeimob.fastboot.baoyan.entity.WriteOffCodeLog;
import com.wmeimob.fastboot.baoyan.mapper.ByChannelCustMapper;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeLogMapper;
import com.wmeimob.fastboot.baoyan.mapper.WriteOffCodeMapper;
import com.wmeimob.fastboot.baoyan.vo.ChannelCustWriteOffTotalVo;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class ByChannelCustServiceImpl implements ByChannelCustService {
    @Resource
    private ByChannelCustMapper byChannelCustMapper;
    @Resource
    private WriteOffCodeLogService writeOffCodeLogService;
    @Resource
    private WriteOffCodeLogMapper writeOffCodeLogMapper;
    @Resource
    private WriteOffCodeService writeOffCodeService;
    @Resource
    private WriteOffCodeMapper writeOffCodeMapper;
    @Resource
    private ByOrdersService byOrdersService;

    @Override
    public List<ByChannelCust> findByCondition(ByChannelCust byChannelCust) {
        Example example = new Example(ByChannelCust.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(byChannelCust.getMobile())) {
            criteria.andLike("mobile", StringUtils.fullFuzzy(byChannelCust.getMobile()));
        }
        if(!StringUtils.isEmpty(byChannelCust.getStoreId())){
            criteria.andEqualTo("storeId",byChannelCust.getStoreId());
        }
        if(!StringUtils.isEmpty(byChannelCust.getGoodsId())){
            criteria.andEqualTo("goodsId",byChannelCust.getGoodsId());
        }
        if(!StringUtils.isEmpty(byChannelCust.getChannelSource())){
            criteria.andEqualTo("channelSource",byChannelCust.getChannelSource());
        }
        if(!StringUtils.isEmpty(byChannelCust.getIsGenerateOrder())){
            criteria.andEqualTo("isGenerateOrder",byChannelCust.getIsGenerateOrder());
        }
        criteria.andEqualTo("deleteStatus",false);
        example.orderBy("id").desc();
        return byChannelCustMapper.selectByExample(example);
    }

    @Override
    public ByChannelCust queryById(Object id) {
        return byChannelCustMapper.selectByPrimaryKey(id);
    }

    @Override
    public Boolean addByChannelCust(ByChannelCust byChannelCust) {
        byChannelCust.setGmtCreate(new Date());
        return byChannelCustMapper.insertSelective(byChannelCust) > 0;
    }

    @Override
    public Boolean updateByChannelCust(ByChannelCust byChannelCust) {
        byChannelCust.setGmtModified(new Date());
        return byChannelCustMapper.updateByPrimaryKeySelective(byChannelCust) > 0;
    }

    @Override
    public Boolean removeByChannelCust(Object id) {
        ByChannelCust byChannelCust = byChannelCustMapper.selectByPrimaryKey(id);
        if(byChannelCust.getIsGenerateOrder()){
            throw new CustomException("该用户已生成订单，不能删除");
        }
        byChannelCust.setDeleteStatus(true);
        return byChannelCustMapper.updateByPrimaryKeySelective(byChannelCust) > 0;
    }

    @Override
    public Boolean deleteChannelAllInfo(Object id){
        ByChannelCust byChannelCust = byChannelCustMapper.selectByPrimaryKey(id);
        ByOrders byOrders = byOrdersService.queryByOrdersById(byChannelCust.getOrderId());
        Example example1 = new Example(WriteOffCode.class);
        example1.createCriteria()
                .andEqualTo("orderNo", byOrders.getOrderNo());
        List<WriteOffCode> writeOffCodeList = writeOffCodeMapper.selectByExample(example1);
        if(!writeOffCodeList.isEmpty()){
            for (WriteOffCode writeOffCode : writeOffCodeList) {
                Example example2 = new Example(WriteOffCodeLog.class);
                example2.createCriteria()
                        .andEqualTo("writeOffId", writeOffCode.getId());
                List<WriteOffCodeLog> writeOffCodeLogs = writeOffCodeLogMapper.selectByExample(example2);
                if(!writeOffCodeLogs.isEmpty()){
                    for (WriteOffCodeLog writeOffCodeLog : writeOffCodeLogs) {
                        writeOffCodeLogMapper.deleteByPrimaryKey(writeOffCodeLog.getId());
                    }
                }
                writeOffCodeMapper.deleteByPrimaryKey(writeOffCode.getId());
            }
        }
        byOrdersService.removeByOrders(byChannelCust.getOrderId());
        byChannelCust.setDeleteStatus(true);
        return byChannelCustMapper.updateByPrimaryKeySelective(byChannelCust) > 0;
    }

    @Override
    public List<ChannelCustWriteOffTotalVo> exportChannelCustTotal(ByChannelCust byChannelCust){
        return byChannelCustMapper.exportChannelCustTotal(byChannelCust);
    }
}
