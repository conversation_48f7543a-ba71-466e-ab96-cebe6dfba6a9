package com.wmeimob.fastboot.baoyan.controller.coupon;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.baoyan.service.ByCouponService;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @ClassName ByCouponController
 * @Description 【优惠券表】控制器
 * <AUTHOR>
 * @Date Tue Jul 09 13:58:11 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bycoupon")
@Slf4j
public class ByCouponController {

    @Resource
    private ByCouponService byCouponService;




    /**
     * 优惠券表分页查询
     * @param request
     * @param byCoupon
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByCoupon(HttpServletRequest request, ByCoupon byCoupon){
        PageContext.startPage();
        return new PageInfo<ByCoupon>(byCouponService.findByCondition(byCoupon));
         
    }

    /**
     *优惠券list
     * @param request
     * @param byCoupon
     * @return
     */
    @GetMapping("/couponList")
    public List<ByCoupon> queryByCouponList(HttpServletRequest request, ByCoupon byCoupon){
        return byCouponService.findByCondition(byCoupon);

    }

     /**
     * 优惠券表导出
     * @param request
     * @param byCoupon
     * @return
     */
    @GetMapping("/exports")
    public List<ByCoupon> queryForByCouponexports(HttpServletRequest request, ByCoupon 
    byCoupon){
        return  byCouponService.findByCondition(byCoupon);
    }


    /**
     * 优惠券表查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCoupon queryForByCouponById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byCouponService.queryByCouponById(id);
    }

    /**
     * 优惠券表修改
     * @param request
     * @param byCoupon
     * @return
     */
    @PutMapping("/")
    public void updateForByCoupon(HttpServletRequest request,@RequestBody ByCoupon byCoupon){
        verify(byCoupon);
        byCouponService.modifyCoupon(byCoupon);
    }

    /**
     * 优惠券表删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByCoupon(HttpServletRequest request,@PathVariable("id") Integer id){
            byCouponService.removeByCoupon(id);
    }

    /**
     * 优惠券添加
     * @param request
     * @param coupon
     * @return
     */
    @PostMapping("/")
    public void insertForCoupon(HttpServletRequest request,@RequestBody ByCoupon coupon){
        //效验参数
        verify(coupon);
        byCouponService.addCoupon(coupon);
    }

    /**
     * 效验参数
     * @param coupon
     */
    private void verify(ByCoupon coupon){
        if(coupon.getTempId() == null){
            throw new CustomException("请选择模板");
        }
        if(coupon.getTakeId() == 1){
            if(coupon.getTotalLimit() == null){
                throw new CustomException("请输入每人领取次数");
            }
            coupon.setDayLimit(CommonFinal.ZERO);
        }else{
            if(coupon.getDayLimit() == null){
                throw new CustomException("请输入每人每天领取次数");
            }
            coupon.setTotalLimit(CommonFinal.ZERO);
        }
        if(coupon.getTotal() == null){
            throw new CustomException("请输入优惠券总数");
        }
    }

    /**
     * 优惠券修改
     * @param coupon
     * @return
     */
    @PutMapping("/openClose")
    public void openClose(@RequestBody ByCoupon coupon){
        if(coupon.getId() == null){
            throw new CustomException("参数错误");
        }
        byCouponService.updateByPrimaryKeyeSlective(coupon);
    }
}
