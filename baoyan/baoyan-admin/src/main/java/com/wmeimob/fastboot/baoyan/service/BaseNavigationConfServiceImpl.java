package com.wmeimob.fastboot.baoyan.service;

import com.google.common.base.Objects;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BaseNavigationConfServiceImpl
 * @Description 导航设置 服务类实现
 * <AUTHOR>
 * @Date Tue Jul 30 16:58:36 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseNavigationConfServiceImpl implements BaseNavigationConfService {

    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;
    @Resource
    private ByArticleMapper byArticleMapper;
    @Resource
    private ByGoodsInfoMapper byGoodsInfoMapper;
    @Resource
    private BySubCardGoodsMapper bySubCardGoodsMapper;
    @Resource
    private ByTicketGoodsMapper byTicketGoodsMapper;
    @Resource
    private ByTeamGoodsMapper byTeamGoodsMapper;

    @Override
    public List<BaseNavigationConf> findByCondition(BaseNavigationConf baseNavigationConf) {
        Example example = new Example(BaseNavigationConf.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(baseNavigationConf.getId())) {
            criteria.andEqualTo("id", baseNavigationConf.getId());
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getTitle())) {
            criteria.andLike("title", StringUtils.fullFuzzy(baseNavigationConf.getTitle()));
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getImgUrl())) {
            criteria.andLike("imgUrl", StringUtils.fullFuzzy(baseNavigationConf.getImgUrl()));
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getJumpType())) {
            criteria.andEqualTo("jumpTypeId", baseNavigationConf.getJumpType());
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getTarget())) {
            criteria.andLike("target", StringUtils.fullFuzzy(baseNavigationConf.getTarget()));
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", baseNavigationConf.getGmtCreate());
        }
        if (!StringUtils.isEmpty(baseNavigationConf.getGmtUpdate())) {
            criteria.andEqualTo("gmtUpdate", baseNavigationConf.getGmtUpdate());
        }
        if (baseNavigationConf.getIsHome() != null) {
            criteria.andEqualTo("isHome", baseNavigationConf.getIsHome());
        }
        example.orderBy("status").desc();
        example.orderBy("id").desc();
        List<BaseNavigationConf> baseNavigationConfs = baseNavigationConfMapper.selectByExample(example);
        return baseNavigationConfs;
    }

    @Override
    public BaseNavigationConf queryBaseNavigationConfById(Object id) {
        return baseNavigationConfMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addBaseNavigationConf(BaseNavigationConf baseNavigationConf) {
        baseNavigationConf.setGmtCreate(new Date());
        //处理请求参数
        dealReqParam(baseNavigationConf);
        //效验参数
        checkParam(baseNavigationConf);
        baseNavigationConfMapper.insertSelective(baseNavigationConf);
    }

    @Override
    public void removeBaseNavigationConf(Object id) {
        BaseNavigationConf baseNavigationConf = new BaseNavigationConf();
        baseNavigationConf.setId(Integer.parseInt(id.toString()));
        baseNavigationConfMapper.updateByPrimaryKeySelective(baseNavigationConf);
    }

    @Override
    public void modifyBaseNavigationConf(BaseNavigationConf baseNavigationConf) {

        baseNavigationConf.setGmtUpdate(new Date());
        //处理请求参数
        dealReqParam(baseNavigationConf);
        //效验参数
        checkParam(baseNavigationConf);
        baseNavigationConfMapper.updateByPrimaryKeySelective(baseNavigationConf);

    }


    /**
     * 效验参数
     *
     * @param baseNavigationConf
     */
    private void checkParam(BaseNavigationConf baseNavigationConf) {
        if (null != baseNavigationConf.getJumpType()) {
            if (baseNavigationConf.getJumpType().equals(1)) {
                //判断当前分类id是否存在
                if (null == baseNavigationConf || null == baseNavigationConf.getClassifyId() || StringUtils.isEmpty(baseNavigationConf.getClassifyId())) {
                    throw new CustomException("请选择分类");
                }
            }
            if (baseNavigationConf.getJumpType().equals(2)) {
                if (null == baseNavigationConf || null == baseNavigationConf.getTargetImgUrl()) {
                    throw new CustomException("请上传联票列表图片");
                }
            }
            if (baseNavigationConf.getJumpType().equals(3)) {
                if (null == baseNavigationConf || null == baseNavigationConf.getTargetImgUrl()) {
                    throw new CustomException("请上传次卡列表图片");
                }
            }
            if (baseNavigationConf.getJumpType().equals(4)) {
                if (null == baseNavigationConf || null == baseNavigationConf.getTargetImgUrl()) {
                    throw new CustomException("请上传拼团列表图片");
                }
            }
            if (baseNavigationConf.getJumpType().equals(5)) {
                //判断当前文章id是否存在
                Example example = new Example(ByArticle.class);
                example.createCriteria().andEqualTo("id", baseNavigationConf.getTarget());
                List<ByArticle> byArticles = byArticleMapper.selectByExample(example);
                if (null == byArticles || byArticles.size() <= 0) {
                    throw new CustomException("文章不存在");
                }
            }
            if (baseNavigationConf.getJumpType().equals(6)) {
                //判断当前商品普通商品存在
                Example example = new Example(ByGoodsInfo.class);
                example.createCriteria().andEqualTo("id", baseNavigationConf.getTarget()).andEqualTo("isDel", 0);
                List<ByGoodsInfo> byGoodsInfos = byGoodsInfoMapper.selectByExample(example);
                if (null == byGoodsInfos || byGoodsInfos.size() <= 0) {
                    throw new CustomException("商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (byGoodsInfos.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseNavigationConf.getJumpType().equals(7)) {
                //判断当前联票是否存在
                Example example = new Example(ByTicketGoods.class);
                example.createCriteria().andEqualTo("id", baseNavigationConf.getTarget()).andEqualTo("isDel", 0);
                List<ByTicketGoods> byTicketGoods = byTicketGoodsMapper.selectByExample(example);
                if (null == byTicketGoods || byTicketGoods.size() <= 0) {
                    throw new CustomException("联票商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTicketGoods.get(0).getStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseNavigationConf.getJumpType().equals(8)) {
                //判断当前次卡商品是否存在
                Example example = new Example(BySubCardGoods.class);
                example.createCriteria().andEqualTo("id", baseNavigationConf.getTarget()).andEqualTo("isDel", 0);
                List<BySubCardGoods> bySubCardGoods = bySubCardGoodsMapper.selectByExample(example);
                if (null == bySubCardGoods || bySubCardGoods.size() <= 0) {
                    throw new CustomException("次卡商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (bySubCardGoods.get(0).getStatus().equals(0)) {
                        throw new CustomException("商品未上架");
                    }
                }
            }
            if (baseNavigationConf.getJumpType().equals(9)) {
                //判断当前拼团商品是否存在
                Example example = new Example(ByTeamGoods.class);
                example.createCriteria().andEqualTo("id", baseNavigationConf.getTarget()).andEqualTo("isDel", 0);
                List<ByTeamGoods> byTeamGoods = byTeamGoodsMapper.selectByExample(example);
                if (null == byTeamGoods || byTeamGoods.size() <= 0) {
                    throw new CustomException("拼团商品不存在");
                } else {
                    //效验商品是否是上架状态
                    if (!byTeamGoods.get(0).getTeamStatus()) {
                        throw new CustomException("商品未上架");
                    }
                }
            }

            if (baseNavigationConf.getJumpType().equals(10)) {
                baseNavigationConf.setTarget(null);
                baseNavigationConf.setIsHome(true);
            }

            if (baseNavigationConf.getJumpType().equals(11)) {
                baseNavigationConf.setTarget("");
            }

        }

    }




    /***
     * 处理类型参数
     * @param baseNavigationConf
     */
    @SuppressWarnings("all")
    private void dealReqParam(BaseNavigationConf baseNavigationConf) {
        if (null != baseNavigationConf.getJumpType()) {
            String target;
            switch (baseNavigationConf.getJumpType()) {
                case 1:
                    target = String.valueOf(baseNavigationConf.getClassifyId());
                    break;
                case 2:
                    target = baseNavigationConf.getTargetImgUrl();
                    break;
                case 3:
                    target = baseNavigationConf.getTargetImgUrl();
                    break;
                case 4:
                    target = baseNavigationConf.getTargetImgUrl();
                    break;
                default:
                    target = baseNavigationConf.getTarget();
                    break;
            }
            if (Objects.equal(10,baseNavigationConf.getJumpType()) ||
                    Objects.equal(11,baseNavigationConf.getJumpType())){
                target = "";
            }
            baseNavigationConf.setTarget(target);
        }

    }

    @Override
    public void updateShelf(BaseNavigationConf baseNavigationConf) {
        if (baseNavigationConf.getStatus()) {
            BaseNavigationConf tempBaseNavigationConf = baseNavigationConfMapper.selectByPrimaryKey(baseNavigationConf.getId());
            //上架
            //判断之前是否上架 8个导航上架

            Example example = new Example(BaseNavigationConf.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("status", true);
            Boolean isHome = tempBaseNavigationConf.getIsHome();
            criteria.andEqualTo("isHome", isHome);
            List<BaseNavigationConf> byCondition = baseNavigationConfMapper.selectByExample(example);

//            if (null != byCondition && byCondition.size() >= 4 && isHome)
//                throw new CustomException(" 首页导航只能上架设置4条");
            if (null != byCondition && byCondition.size() >= 20 && (!isHome))
                throw new CustomException(" 分类导航只能上架设置20条");
        }
        baseNavigationConfMapper.updateByPrimaryKeySelective(baseNavigationConf);
    }

}
