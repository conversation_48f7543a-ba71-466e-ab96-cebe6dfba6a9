package com.wmeimob.fastboot.baoyan.controller.coupon;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByCouponTemp;
import com.wmeimob.fastboot.baoyan.service.ByCouponTempService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * @ClassName ByCouponTempController
 * @Description 【优惠券模板】控制器
 * <AUTHOR>
 * @Date Thu Jul 11 17:55:47 CST 2019
 * @version1.0
 **/
@RestController
@RequestMapping("bycoupontemp")
@Slf4j
public class ByCouponTempController {

    @Resource
    private ByCouponTempService byCouponTempService;




    /**
     * 优惠券模板分页查询
     * @param request
     * @param byCouponTemp
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByCouponTemp(HttpServletRequest request, ByCouponTemp byCouponTemp){
        PageContext.startPage();
        return new PageInfo<ByCouponTemp>(byCouponTempService.findByCondition(byCouponTemp));
         
    }

     /**
     * 优惠券模板导出
     * @param request
     * @param byCouponTemp
     * @return
     */
    @GetMapping("/exports")
    public List<ByCouponTemp> queryForByCouponTempexports(HttpServletRequest request, ByCouponTemp 
 byCouponTemp){
        return  byCouponTempService.findByCondition(byCouponTemp);
    }


    /**
     * 优惠券模板查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCouponTemp queryForByCouponTempById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byCouponTempService.queryByCouponTempById(id);
    }


    /**
     * 优惠券模板添加
     * @param request
     * @param byCouponTemp
     * @return
     */
    @PostMapping("/")
    public void insertForByCouponTemp(HttpServletRequest request,@RequestBody ByCouponTemp byCouponTemp){

        if(StringUtils.isEmpty(byCouponTemp.getFull())){
            byCouponTemp.setFull(BigDecimal.ZERO);
        }
        if (byCouponTemp.getEffectiveType().equals(1)) {
            if(StringUtils.isEmpty(byCouponTemp.getStartDate())){
                throw new CustomException("请选择开始日期");
            }
            if(StringUtils.isEmpty(byCouponTemp.getEndDate())){
                throw new CustomException("请选择结束日期");
            }
            if(byCouponTemp.getStartDate().getTime() > byCouponTemp.getEndDate().getTime()){
                throw new CustomException("开始日期不能大于结束日期");
            }
        }else{
            if(StringUtils.isEmpty(byCouponTemp.getDayNum())){
                throw new CustomException("请填写天数");
            }
        }
            byCouponTempService.addByCouponTemp(byCouponTemp);
    }


    /**
     * 优惠券模板修改
     * @param request
     * @param byCouponTemp
     * @return
     */
    @PutMapping("/")
    public void updateForByCouponTemp(HttpServletRequest request,@RequestBody ByCouponTemp byCouponTemp){
            byCouponTempService.modifyByCouponTemp(byCouponTemp);  
    }

    /**
     * 优惠券模板删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByCouponTemp(HttpServletRequest request,@PathVariable("id") Object id){
            byCouponTempService.removeByCouponTemp(id);
    }
}
