package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
import com.wmeimob.fastboot.baoyan.entity.ByCoupon;
import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import com.wmeimob.fastboot.baoyan.entity.ByCustUser;
import com.wmeimob.fastboot.baoyan.entity.TcGoodsShoping;
import com.wmeimob.fastboot.baoyan.mapper.ByCouponMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByCouponUserMapper;
import com.wmeimob.fastboot.baoyan.mapper.ByCustUserMapper;
import com.wmeimob.fastboot.baoyan.service.ByCouponUserService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.starter.security.context.SecurityContext;
import com.wmeimob.fastboot.util.InputValidator;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @ClassName ByCouponUserServiceImpl
 * @Description  优惠券领取 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 17:28:52 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByCouponUserServiceImpl implements ByCouponUserService {

    @Resource
    private ByCouponUserMapper couponUserMapper;

    @Resource
    private ByCouponMapper couponMapper;
    @Resource
    private ByCustUserMapper userInfoMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    /**
     * redis key
      */
    private static final String KEY = "coupon:";



    @Override
    public List<ByCouponUser> findByCondition(ByCouponUser byCouponUser) {
        if(!StringUtils.isEmpty(byCouponUser.getName())){
            if(InputValidator.isMobile(byCouponUser.getName())){
                byCouponUser.setMobile(byCouponUser.getName());
                byCouponUser.setName(null);
            }
        }
        return couponUserMapper.getCouponGetList(byCouponUser);
    }

    @Override
    public ByCouponUser queryByCouponUserById(Object id) {
        return couponUserMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByCouponUser(ByCouponUser byCouponUser) {
	  byCouponUser.setGmtCreate(new Date());
        couponUserMapper.insertSelective(byCouponUser);
    }

    @Override
    public void removeByCouponUser(Object id) {
	  ByCouponUser byCouponUser = new ByCouponUser();
	  byCouponUser.setId(Integer.parseInt(id.toString()));
        couponUserMapper.updateByPrimaryKeySelective(byCouponUser);
    }

    @Override
    public void modifyByCouponUser(ByCouponUser byCouponUser) {
        couponUserMapper.updateByPrimaryKeySelective(byCouponUser);
    }
    @Override
//    @Async
    public void addBitchCouponUser(String batch,ByCouponUser couponUser) {
//        System.out.println(SecurityContext.getUser());
        SysUser sysUser = (SysUser) SecurityContext.getUser();
        couponUser.setIssuerUserId(sysUser.getId());
        ByCoupon coupon = couponMapper.getCouponInfo(couponUser.getCouponId());
        if(couponUser.getUserId().equals(CommonFinal.TWO)){
            handleImport( batch, coupon, couponUser);
        }else{
            //全部用户
            handleImportAll( batch, coupon, couponUser);
        }
    }

    public void handleImport(String batch,ByCoupon coupon,ByCouponUser couponUser){
        Date date = new Date();
        List<ByCouponUser> users = new ArrayList<>();
        String mobiles = couponUser.getName();
        String[] ms = mobiles.split(",");
        Set<String> mst = new HashSet<>();
        for (String m : ms) {
            mst.add(m);
        }
        Example example = new Example(ByCustUser.class);
        example.selectProperties("id");
        for (String s : mst) {
            example.clear();
            example.createCriteria().andEqualTo("mobile",s).andEqualTo("deleteStatus",0);
            ByCustUser userInfo = userInfoMapper.selectOneByExample(example);
            if(userInfo != null){
                ByCouponUser given = packUser(batch, coupon, couponUser, userInfo);
                given.setGmtCreate(date);
                for (int i = 0;i < couponUser.getTargetId();i++){
                    users.add(given);
                }
            }
        }
        if(users.size() > 0){
            couponUserMapper.insertList(users);
        }
        stringRedisTemplate.opsForValue().set(KEY+batch,"20",30, TimeUnit.MINUTES);
    }
    private ByCouponUser packUser(String batch,ByCoupon coupon,ByCouponUser couponUser,ByCustUser userInfo){
        ByCouponUser given = new ByCouponUser();
        given.setIsUse(0);
        given.setUserId(userInfo.getId());
        given.setCouponId(couponUser.getCouponId());
        given.setTargetId(coupon.getTargetId());
        given.setName(coupon.getName());
        given.setDiscount(coupon.getDiscount());
        given.setFull(coupon.getFull());
        given.setStartDate(coupon.getStartDate());
        given.setEndDate(coupon.getEndDate());
        given.setInBatch(batch);
        given.setGmtCreate(new Date());
        given.setGetType(1);
        given.setType(coupon.getType());
        //待审核
        given.setAuditStatus(CommonFinal.ZERO);
        given.setCouponType(coupon.getCouponType());
        given.setIssuerUserId(couponUser.getIssuerUserId());
        given.setSingleGoodsType(coupon.getSingleGoodsType());
        return given;
    }

    public void handleImportAll(String batch,ByCoupon coupon,ByCouponUser couponUser){
        Example example = new Example(ByCouponUser.class);
        example.selectProperties("id");
        List<ByCustUser> userInfos = userInfoMapper.selectByExample(example);
        List<List<ByCustUser>> lists = averageAssign(userInfos, 20);
        //20个线程执行20个list
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(20);
        for (List<ByCustUser> list : lists) {
            //导入
            executor.schedule(() -> handleExecutor(list,batch,coupon,couponUser), 3000, TimeUnit.MILLISECONDS);
        }
    }

    //list 拆分
    public static <T> List<List<T>> averageAssign(List<T> source,int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        int remaider = source.size() % n;  //(先计算出余数)
        int number = source.size() / n;  //然后是商
        int offset = 0;//偏移量
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }
    public void handleExecutor(List<ByCustUser> list,String batch,ByCoupon coupon,ByCouponUser couponUser){
        List<ByCouponUser> users = new ArrayList<>();
        Date date = new Date();
        for (ByCustUser info : list) {
            ByCouponUser given = packUser(batch, coupon, couponUser, info);
            given.setGmtCreate(date);
            for (int i = 0;i < couponUser.getTargetId();i++){
                users.add(given);
            }
            if(users.size() > 5000){
                couponUserMapper.insertList(users);
                users.clear();
            }
        }
        if(users.size() > 0){
            couponUserMapper.insertList(users);
        }
        executor(batch);
    }
    private void executor(String batch){
        synchronized (batch){
            String s = stringRedisTemplate.opsForValue().get(KEY+batch);
            if(s == null){
                stringRedisTemplate.opsForValue().set(KEY+batch,"1",30,TimeUnit.MINUTES);
            }else{
                stringRedisTemplate.opsForValue().set(KEY+batch,(Integer.valueOf(s)+1)+"",30,TimeUnit.MINUTES);
            }
        }
    }

    @Override
    public List<ByCouponUser>  queryAuditListByCouponUser(ByCouponUser byCouponUser) {
       return couponUserMapper.queryAuditListByCouponUser(byCouponUser);
    }

    @Override
    public void  openAdopt(ByCouponUser byCouponUser) {
        SysUser sysUser = (SysUser) SecurityContext.getUser();
        byCouponUser.setAuditTime(new Date());
        byCouponUser.setAuditUserId(sysUser.getId());
        couponUserMapper.updateByPrimaryKeySelective(byCouponUser);
    }


}
