package com.wmeimob.fastboot.baoyan.controller.ych;

import com.wmeimob.fastboot.baoyan.entity.YchGoods;
import com.wmeimob.fastboot.baoyan.service.YchApiService;
import com.wmeimob.fastboot.baoyan.utils.R;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 油菜花渠道商品
 */
@RestController
@RequestMapping("ych/goods")
@Slf4j
public class GoodController {

    @Resource
    private YchApiService ychApiService;

    @GetMapping("/list")
    public R list(){
        return R.ok(ychApiService.getGoodsListAll(2, true));
    }

    @PostMapping("/detail")
    public R detail(@RequestBody YchGoods ychGoods){
        return R.ok(ychApiService.getGoodsDetail(ychGoods));
    }
}
