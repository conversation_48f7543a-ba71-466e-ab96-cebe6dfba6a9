package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.TcBrand;
import com.wmeimob.fastboot.baoyan.entity.TcCate;
import com.wmeimob.fastboot.baoyan.service.TcCateService;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 淘潮玩品类表(TcCate)表控制层
 *
 * <AUTHOR>
 * @since 2021-07-13 14:20:12
 */
@RestController
@RequestMapping("tc/tcCate")
@Slf4j
public class TcCateController {
    /**
     * 服务对象
     */
    @Resource
    private TcCateService tcCateService;

    @GetMapping("/")
    public PageInfo<? extends TcCate> queryPage(TcCate queryObject){
        PageContext.startPage();
        //pageIndex pageSize
        log.info("get  => queryPage [入参]============={}",queryObject);
        return new PageInfo<>(this.tcCateService.queryPage(queryObject));
    }

    @GetMapping("/{id}")
    public TcCate queryById(@PathVariable Integer id){
        return  tcCateService.queryById(id);
    }
    @PostMapping("/add")
    public Boolean addTcCate(@RequestBody TcCate addObject){
        log.info("POST {/add} => addTcCate [入参]============={}",addObject);
        return this.tcCateService.insert(addObject);
    }

    @PostMapping("/update")
    public Boolean updateObject(@RequestBody TcCate object){
        log.info("POST {/update} => updateObject [入参]============={}",object);
        return this.tcCateService.update(object);
    }
    @DeleteMapping("/{id}")
    public Boolean deleteObject(@PathVariable("id") Integer id)
    {
        log.info("deleteObject delete{/} => id [入参]============={}",id);
        return tcCateService.deleteById(id);
    }

}