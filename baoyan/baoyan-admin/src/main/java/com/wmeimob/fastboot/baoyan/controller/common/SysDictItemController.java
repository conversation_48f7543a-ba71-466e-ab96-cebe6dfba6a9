package com.wmeimob.fastboot.baoyan.controller.common;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.api.R;
import com.wmeimob.fastboot.baoyan.entity.SysDictItem;
import com.wmeimob.fastboot.baoyan.mapper.SysDictItemMapper;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("dict")
@Slf4j
public class SysDictItemController {
    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @GetMapping("/")
    public R getList(HttpServletRequest request, SysDictItem sysDictItem){
        Example exampleDictItem = new Example(SysDictItem.class);
        exampleDictItem.createCriteria().andEqualTo("dictId",sysDictItem.getDictId());
        List<SysDictItem> sysDictItemList = sysDictItemMapper.selectByExample(exampleDictItem);
        return R.data(sysDictItemList);

    }
}
