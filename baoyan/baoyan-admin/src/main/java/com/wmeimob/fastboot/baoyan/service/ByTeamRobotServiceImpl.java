package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByTeamRobot;
import com.wmeimob.fastboot.baoyan.mapper.ByTeamRobotMapper;
import com.wmeimob.fastboot.baoyan.service.ByTeamRobotService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByTeamRobotServiceImpl
 * @Description  拼团机器人 服务类实现
 * <AUTHOR>
 * @Date Thu Jul 11 11:18:31 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByTeamRobotServiceImpl implements ByTeamRobotService {

    @Resource
    private ByTeamRobotMapper byTeamRobotMapper;


    @Override
    public List<ByTeamRobot> findByCondition(ByTeamRobot byTeamRobot) {
        Example example = new Example(ByTeamRobot.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byTeamRobot.getId())){
            criteria.andEqualTo("id",byTeamRobot.getId());
	  }
	  if(!StringUtils.isEmpty(byTeamRobot.getObotImg())){
            criteria.andLike("obotImg",StringUtils.fullFuzzy(byTeamRobot.getObotImg()));
	  }
	  if(!StringUtils.isEmpty(byTeamRobot.getObotNickName())){
            criteria.andLike("obotNickName",StringUtils.fullFuzzy(byTeamRobot.getObotNickName()));
	  }
	  if(!StringUtils.isEmpty(byTeamRobot.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byTeamRobot.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byTeamRobot.getGmtUpdate())){
            criteria.andEqualTo("gmtUpdate",byTeamRobot.getGmtUpdate());
	  }
        example.orderBy("id").desc();
        List<ByTeamRobot> byTeamRobots = byTeamRobotMapper.selectByExample(example);
        return byTeamRobots;
    }

    @Override
    public ByTeamRobot queryByTeamRobotById(Object id) {
        return byTeamRobotMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByTeamRobot(ByTeamRobot byTeamRobot) {
	  byTeamRobot.setGmtCreate(new Date());
        byTeamRobotMapper.insertSelective(byTeamRobot);
    }

    @Override
    public void removeByTeamRobot(Object id) {
        byTeamRobotMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void modifyByTeamRobot(ByTeamRobot byTeamRobot) {
        byTeamRobotMapper.updateByPrimaryKeySelective(byTeamRobot);
    }

}
