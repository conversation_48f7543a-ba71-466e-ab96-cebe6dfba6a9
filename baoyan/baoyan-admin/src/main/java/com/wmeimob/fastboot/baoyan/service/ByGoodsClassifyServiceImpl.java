package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.entity.ByGoodsClassify;
import com.wmeimob.fastboot.baoyan.mapper.ByGoodsClassifyMapper;
import com.wmeimob.fastboot.baoyan.service.ByGoodsClassifyService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ByGoodsClassifyServiceImpl
 * @Description  商品类型关联表 服务类实现
 * <AUTHOR>
 * @Date Wed Jul 24 15:32:05 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor={Exception.class})
@Slf4j
public class ByGoodsClassifyServiceImpl implements ByGoodsClassifyService {

    @Resource
    private ByGoodsClassifyMapper byGoodsClassifyMapper;


    @Override
    public List<ByGoodsClassify> findByCondition(ByGoodsClassify byGoodsClassify) {
        Example example = new Example(ByGoodsClassify.class);
        Example.Criteria criteria = example.createCriteria();
	  if(!StringUtils.isEmpty(byGoodsClassify.getId())){
            criteria.andEqualTo("id",byGoodsClassify.getId());
	  }
	  if(!StringUtils.isEmpty(byGoodsClassify.getClassifyId())){
            criteria.andEqualTo("classifyId",byGoodsClassify.getClassifyId());
	  }
	  if(!StringUtils.isEmpty(byGoodsClassify.getGoodsId())){
            criteria.andEqualTo("goodsId",byGoodsClassify.getGoodsId());
	  }
	  if(!StringUtils.isEmpty(byGoodsClassify.getGmtCreate())){
            criteria.andEqualTo("gmtCreate",byGoodsClassify.getGmtCreate());
	  }
	  if(!StringUtils.isEmpty(byGoodsClassify.getGmtModified())){
            criteria.andEqualTo("gmtModified",byGoodsClassify.getGmtModified());
	  }
        example.orderBy("id").desc();
        List<ByGoodsClassify> byGoodsClassifys = byGoodsClassifyMapper.selectByExample(example);
        return byGoodsClassifys;
    }

    @Override
    public ByGoodsClassify queryByGoodsClassifyById(Object id) {
        return byGoodsClassifyMapper.selectByPrimaryKey(id);
    }


	@Override
    public void addByGoodsClassify(ByGoodsClassify byGoodsClassify) {
	  byGoodsClassify.setGmtCreate(new Date());
        byGoodsClassifyMapper.insertSelective(byGoodsClassify);
    }

    @Override
    public void removeByGoodsClassify(Object id) {
	  ByGoodsClassify byGoodsClassify = new ByGoodsClassify();
	  byGoodsClassify.setId(Integer.parseInt(id.toString()));
        byGoodsClassifyMapper.updateByPrimaryKeySelective(byGoodsClassify);
    }

    @Override
    public void modifyByGoodsClassify(ByGoodsClassify byGoodsClassify) {
		byGoodsClassify.setGmtModified(new Date());
        byGoodsClassifyMapper.updateByPrimaryKeySelective(byGoodsClassify);
    }

}
