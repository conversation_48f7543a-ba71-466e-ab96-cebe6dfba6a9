package com.wmeimob.fastboot.baoyan.service;

import com.wmeimob.fastboot.baoyan.constant.BaoYanConstant;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.*;
import com.wmeimob.fastboot.baoyan.service.BaseClassifyService;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BaseClassifyServiceImpl
 * @Description 分类表 服务类实现
 * <AUTHOR>
 * @Date Fri Jul 12 10:13:14 CST 2019
 * @version1.0
 **/

@Service
@Transactional(rollbackFor = {Exception.class})
@Slf4j
public class BaseClassifyServiceImpl implements BaseClassifyService {

    @Resource
    private BaseClassifyMapper baseClassifyMapper;
    @Resource
    private BaseBannerMapper baseBannerMapper;
    @Resource
    private BaseNavigationConfMapper baseNavigationConfMapper;
    @Resource
    private BaseActivityMapper baseActivityMapper;
    @Resource
    private ByGoodsClassifyMapper byGoodsClassifyMapper;
    /**
     * 删除提示信息
     */
    public static final String ERR_MESSAGE = "当前分类 商品、首页推荐中有引用,暂不能删除";


    @Override
    public List<BaseClassify> findByCondition(BaseClassify baseClassify) {
        Example example = new Example(BaseClassify.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(baseClassify.getId())) {
            criteria.andEqualTo("id", baseClassify.getId());
        }
        if (!StringUtils.isEmpty(baseClassify.getClassifyTitle())) {
            criteria.andLike("classifyTitle", StringUtils.fullFuzzy(baseClassify.getClassifyTitle()));
        }
        if (!StringUtils.isEmpty(baseClassify.getClassifyImg())) {
            criteria.andLike("classifyImg", StringUtils.fullFuzzy(baseClassify.getClassifyImg()));
        }
        if (!StringUtils.isEmpty(baseClassify.getIsDel())) {
            criteria.andEqualTo("isDel", baseClassify.getIsDel());
        }
        if (!StringUtils.isEmpty(baseClassify.getGmtCreate())) {
            criteria.andEqualTo("gmtCreate", baseClassify.getGmtCreate());
        }
        if (!StringUtils.isEmpty(baseClassify.getGmtUpdate())) {
            criteria.andEqualTo("gmtUpdate", baseClassify.getGmtUpdate());
        }
        criteria.andEqualTo("isDel", false);
        example.orderBy("id").desc();
        List<BaseClassify> baseClassifys = baseClassifyMapper.selectByExample(example);
        return baseClassifys;
    }

    @Override
    public BaseClassify queryBaseClassifyById(Object id) {
        return baseClassifyMapper.selectByPrimaryKey(id);
    }


    @Override
    public void addBaseClassify(BaseClassify baseClassify) {
        baseClassify.setGmtCreate(new Date());
        baseClassify.setState(0);
        baseClassifyMapper.insertSelective(baseClassify);
    }

    @Override
    public void removeBaseClassify(Integer id) {
        //效验当前分类是否在其他地方引用  商品 前台导航位置
        checkClassIdQuote(id);
        BaseClassify baseClassify = new BaseClassify();
        baseClassify.setId(Integer.parseInt(id.toString()));
        baseClassify.setIsDel(true);
        baseClassifyMapper.updateByPrimaryKeySelective(baseClassify);
    }

    /**
     * 效验当前商品是否在其他地方有引用
     *
     * @param id 商品id
     */
    private void checkClassIdQuote(Integer id) {

        //商品分类是否被引用
        Example goodsClassify = new Example(ByGoodsClassify.class);
        goodsClassify.createCriteria().andEqualTo("classifyId", id);
        List<ByGoodsClassify> byGoodsClassifies = byGoodsClassifyMapper.selectClassifyGoodsContact(id);
        if (null != byGoodsClassifies && byGoodsClassifies.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        //首页推荐位置判断是否有引用
        Example bannerExample = new Example(BaseBanner.class);
        //商品类型
        bannerExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_1).andEqualTo("target", id).andEqualTo("isDel", 0);
        List<BaseBanner> baseBanners = baseBannerMapper.selectByExample(bannerExample);
        if (null != baseBanners && baseBanners.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseNavigationConfExample = new Example(BaseNavigationConf.class);
        baseNavigationConfExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_1).andEqualTo("target", id);
        List<BaseNavigationConf> baseNavigationConfs = baseNavigationConfMapper.selectByExample(baseNavigationConfExample);
        if (null != baseNavigationConfs && baseNavigationConfs.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
        Example baseActivityExample = new Example(BaseActivity.class);
        baseActivityExample.createCriteria().andEqualTo("jumpType", BaoYanConstant.JUMP_TYPE_1).andEqualTo("target", id);
        List<BaseActivity> baseActivities = baseActivityMapper.selectByExample(baseActivityExample);
        if (null != baseActivities && baseActivities.size() > 0) {
            throw new CustomException(ERR_MESSAGE);
        }
    }

    @Override
    public void   modifyBaseClassify(BaseClassify baseClassify) {
        baseClassify.setGmtUpdate(new Date());
        baseClassifyMapper.updateByPrimaryKeySelective(baseClassify);
    }

    @Override
    public void updateState(BaseClassify baseClassify) {
        baseClassifyMapper.updateByPrimaryKeySelective(baseClassify);
    }

}
