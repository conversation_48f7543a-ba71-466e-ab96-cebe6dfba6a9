package com.wmeimob.fastboot.baoyan.config;

import cn.dev33.satoken.strategy.SaStrategy;
import cn.dev33.satoken.util.SaFoxUtil;
import com.wmeimob.fastboot.management.common.entity.SysUser;
import com.wmeimob.fastboot.management.common.mapper.SysUserMapper;
import com.wmeimob.fastboot.starter.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class SaTokenConfigure {

    @Resource
    private JwtAuthenticationFilter filter;

    @Resource
    private SysUserMapper sysUserMapper;
    /**
     * 重写 Sa-Token 框架内部算法策略
     */
    @Autowired
    public void rewriteSaStrategy() {
        // 重写 Token 生成策略
        SaStrategy.instance.createToken = (loginId, loginType) -> {
            SysUser login = this.sysUserMapper.selectByPrimaryKey(loginId);
            String token = this.filter.getJsonWebTokenHandler().generateToken(login);
//            return SaFoxUtil.getRandomString(60);    // 随机60位长度字符串
            return token;
        };
    }
}
