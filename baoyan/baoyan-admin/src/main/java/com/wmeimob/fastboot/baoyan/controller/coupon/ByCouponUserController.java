package com.wmeimob.fastboot.baoyan.controller.coupon;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.core.annotation.Page;
import com.wmeimob.fastboot.core.context.PageContext;
import com.wmeimob.fastboot.core.exception.CustomException;
import com.wmeimob.fastboot.core.rest.RestResult;
import com.wmeimob.fastboot.baoyan.entity.ByCouponUser;
import com.wmeimob.fastboot.baoyan.service.ByCouponUserService;
import com.wmeimob.fastboot.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @ClassName ByCouponUserController
 * @Description 【优惠券领取】控制器
 * <AUTHOR>
 * @Date Thu Jul 11 17:28:52 CST 2019
 * @version 1.0
 **/
@RestController
@RequestMapping("bycouponuser")
@Slf4j
public class ByCouponUserController {

    @Resource
    private ByCouponUserService byCouponUserService;




    /**
     * 优惠券领取分页查询
     * @param request
     * @param byCouponUser
     * @return
     */
    @GetMapping("/")
    public PageInfo queryForByCouponUser(HttpServletRequest request, ByCouponUser byCouponUser){
        PageContext.startPage();
        return new PageInfo<ByCouponUser>(byCouponUserService.findByCondition(byCouponUser));
         
    }

     /**
     * 优惠券领取导出
     * @param request
     * @param byCouponUser
     * @return
     */
    @GetMapping("/exports")
    public List<ByCouponUser> queryForByCouponUserexports(HttpServletRequest request, ByCouponUser 
    byCouponUser){
        return  byCouponUserService.findByCondition(byCouponUser);
    }


    /**
     * 优惠券领取查询-<通过id查询>
     * @param request
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ByCouponUser queryForByCouponUserById(HttpServletRequest request, @PathVariable("id") Object id){
            return  byCouponUserService.queryByCouponUserById(id);
    }


    /**
     * 优惠券领取添加
     * @param request
     * @param byCouponUser
     * @return
     */
    @PostMapping("/")
    public void insertForByCouponUser(HttpServletRequest request,@RequestBody ByCouponUser byCouponUser){
            byCouponUserService.addByCouponUser(byCouponUser);    
    }


    /**
     * 优惠券领取修改
     * @param request
     * @param byCouponUser
     * @return
     */
    @PutMapping("/")
    public void updateForByCouponUser(HttpServletRequest request,@RequestBody ByCouponUser byCouponUser){
            byCouponUserService.modifyByCouponUser(byCouponUser);  
    }

    /**
     * 优惠券领取删除
     * @param request
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public void removeForByCouponUser(HttpServletRequest request,@PathVariable("id") Object id){
            byCouponUserService.removeByCouponUser(id);
    }


    /**
     * 优惠券领取添加
     * @param request
     * @param couponUser
     * @return
     */
    @PostMapping("/bitchGiven")
    public String bitchGiven(HttpServletRequest request,@RequestBody ByCouponUser couponUser){
        if(couponUser.getCouponId() == null){
            throw new CustomException("请选择优惠券");
        }
        if(couponUser.getTargetId() == null){
            throw new CustomException("请输入发放数量");
        }
        if(couponUser.getUserId() == 2){
            if(StringUtils.isEmpty(couponUser.getName())){
                throw new CustomException("请输入发放用户");
            }
        }
        String batch = System.currentTimeMillis()+"";
        byCouponUserService.addBitchCouponUser(batch,couponUser);
        return batch;
    }
    /**
     * 优惠券领取添加
     * @return
     */
    @GetMapping("/bitch/{bt}")
    public PageInfo bitchRest(@PathVariable String bt){
        PageContext.startPage();
        return new PageInfo(byCouponUserService.bitchRest(bt));
    }
    /**
     * 优惠券领取添加
     * @return
     */
    @GetMapping("/print/bitchDone/{bt}")
    public int bitchDone(@PathVariable String bt){
        return byCouponUserService.bitchDone(bt);
    }


    /**
     * 优惠券发放审核列表
     * @param request
     * @param byCouponUser
     * @return
     */
    @GetMapping("/auditList")
    public PageInfo queryAuditListByCouponUser(HttpServletRequest request, ByCouponUser byCouponUser){
        PageContext.startPage();
        return new PageInfo<ByCouponUser>(byCouponUserService.queryAuditListByCouponUser(byCouponUser));

    }
    /**
     * 优惠券审核
     * @param request
     * @param byCouponUser
     * @return
     */
    @PutMapping("/openAdopt")
    public void openAdopt(HttpServletRequest request, @RequestBody ByCouponUser byCouponUser){
         byCouponUserService.openAdopt(byCouponUser);
    }








}
