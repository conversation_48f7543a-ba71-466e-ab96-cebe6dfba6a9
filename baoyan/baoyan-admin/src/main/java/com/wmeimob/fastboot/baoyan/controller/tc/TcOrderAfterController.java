package com.wmeimob.fastboot.baoyan.controller.tc;

import com.github.pagehelper.PageInfo;
import com.wmeimob.fastboot.baoyan.entity.*;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderAfterMapper;
import com.wmeimob.fastboot.baoyan.service.TcOrderAfterService;
import com.wmeimob.fastboot.baoyan.utils.Assert;
import com.wmeimob.fastboot.core.context.PageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/7
 */
@RequestMapping("/tc/orderAfter")
@RestController
@Slf4j
public class TcOrderAfterController {
    @Resource
    private TcOrderAfterMapper tcOrderAfterMapper;

    @Resource
    private TcOrderAfterService tcOrderAfterService;



    /**
     * 退款一个订单商品
     * @param orderGoodsId
     * @return
     */
    @PostMapping("/refundSingeOrder/{orderGoodsId}/{refundCount}")
    public boolean refundSingeOrder(@PathVariable Integer orderGoodsId,@PathVariable Integer refundCount){
        if (refundCount>1){
            throw new RuntimeException("一次只能退款一个");
        }
        return tcOrderAfterService.refundSingeOrder(orderGoodsId, refundCount);
    }
    /**
     * 订单售后导出
     * @param request
     * @param tcOrderAfter
     * @return
     */
    @GetMapping("/exports")
    public List<TcOrderAfter> queryForByOrdersexports(HttpServletRequest request, TcOrderAfter tcOrderAfter){
        //modify by 2019 10.18
        return  tcOrderAfterMapper.findAll(tcOrderAfter);
    }
    /**
     * 同意售后并退款
     * @param id 售后订单id
     * @return
     */
    @PutMapping("/audit/{id}")
    public boolean audit(@PathVariable Integer id){
        return tcOrderAfterService.audit(id);
    }

    /**
     * 查看用户的售后单
     * @return
     */
    @GetMapping
    public PageInfo<TcOrderAfter> findAll(TcOrderAfter tcOrderAfter){
        PageContext.startPage();
        return new PageInfo<TcOrderAfter>(tcOrderAfterMapper.findAll(tcOrderAfter));
    }

    /**
     * 指定用户寄回的地址
     * @param tcOrderAfter
     * @return
     */
    @PutMapping("/appointAddress")
    public boolean appointAddress(@RequestBody TcOrderAfter tcOrderAfter){

        return tcOrderAfterService.appointAddress(tcOrderAfter);
    }

    @PutMapping("/rejectAfter")
    public boolean rejectAfter(@RequestBody TcOrderAfter tcOrderAfter){
        return tcOrderAfterService.rejectAfter(tcOrderAfter);
    }
}
