//package com.wmeimob.fastboot.baoyan;
//
//import com.wmeimob.fastboot.AdminApplication;
//import com.wmeimob.fastboot.autoconfigure.wechat.WechatProperties;
//import com.wmeimob.fastboot.baoyan.constant.CommonFinal;
//import com.wmeimob.fastboot.baoyan.entity.ByOrderLog;
//import com.wmeimob.fastboot.baoyan.entity.TcOrder;
//import com.wmeimob.fastboot.baoyan.mapper.ByOrderLogMapper;
//import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
//import com.wmeimob.fastboot.baoyan.tool.Scheduler;
//import com.wmeimob.fastboot.baoyan.tool.impl.SchedulerImpl;
//import com.wmeimob.fastboot.starter.wechat.service.WepayService;
//import me.hao0.wepay.core.Refunds;
//import me.hao0.wepay.core.Wepay;
//import me.hao0.wepay.model.refund.RefundApplyRequest;
//import me.hao0.wepay.model.refund.RefundApplyResponse;
//import org.apache.commons.lang.StringUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//
//@SpringBootTest(classes = AdminApplication.class)
//@RunWith(SpringRunner.class)
//public class UpdateTest {
//
//
//
//
//    @Autowired
//    private WepayService wepayService;
//    @Autowired
//    private WechatProperties wechatProperties;
//
//    @Resource
//    private ByOrderLogMapper orderLogMapper;
//
//    @Resource
//    private Scheduler scheduler;
//
//    @Resource
//    private TcOrderMapper tcOrderMapper;
//
//    @Test
//    public void orderLogTest(){
//
//    }
//
//
//    /**
//     * 退款
//     */
//    @Test
//    public void refunds(){
//        Wepay wepay = wepayService.getApiComponent(wechatProperties.getAppid());
//        RefundApplyRequest request = new RefundApplyRequest();
//        request.setOutTradeNo("202004837244456965");
//        request.setOutRefundNo(String.valueOf(new Date().getTime()));
//        BigDecimal fee = new BigDecimal(198);
//        request.setTotalFee(fee.multiply(CommonFinal.BIG_100).intValue());
//        request.setRefundFee(fee.multiply(CommonFinal.BIG_100).intValue());
//        request.setOpUserId("1");
//        Refunds refund = wepay.refund();
//        RefundApplyResponse apply = refund.apply(request);
////        return JSON.toJSONString(apply);
//    }
//
//
//
//
//
//}
