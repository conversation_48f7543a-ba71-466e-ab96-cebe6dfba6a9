package com.wmeimob.fastboot.baoyan.tc;

import com.wmeimob.fastboot.AdminApplication;
import com.wmeimob.fastboot.baoyan.entity.TcOrder;
import com.wmeimob.fastboot.baoyan.mapper.TcOrderMapper;
import com.wmeimob.fastboot.baoyan.service.ByGoodsInfoServiceImpl;
import com.wmeimob.fastboot.baoyan.utils.common.QrCodeService;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * @author: wangShun
 * systemName king
 * CreationDate:2021/8/21
 * packageName:com.wmeimob.fastboot.baoyan.tc
 */
@SpringBootTest(classes = AdminApplication.class)
@RunWith(SpringRunner.class)
public class Test {

    @Resource
    private QrCodeService qrCodeService;
    @Resource
    private ByGoodsInfoServiceImpl byGoodsInfoService;
    @Resource
    TcOrderMapper tcOrderMapper;
    @org.junit.Test
    public void codeIMGTest(){
//        TcOrder byPayOrderNo = tcOrderMapper.findById("202130031237026578");
//        System.out.println(byPayOrderNo);

    }
}
