CREATE TABLE by_ych_sync_task (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL COMMENT '用户ID',
    task_type TINYINT(1) NOT NULL COMMENT '任务类型：1会员开卡 2余额查询 3余额变更',
    task_status TINYINT(1) DEFAULT 0 COMMENT '任务状态：0待执行 1执行中 2执行成功 3执行失败',
    retry_count TINYINT(1) DEFAULT 0 COMMENT '重试次数',
    error_msg VARCHAR(255) DEFAULT NULL COMMENT '错误信息',
    ref_id INT DEFAULT NULL COMMENT '关联ID(余额变更记录ID等)',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    UNIQUE KEY `idx_member_type_ref` (`member_id`, `task_type`, `ref_id`)
) COMMENT='油菜花接口同步任务';