   CREATE TABLE by_balance_log (
     id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
     member_id INT(11) NOT NULL COMMENT '用户ID',
     change_balance DECIMAL(10,2) NOT NULL COMMENT '变更金额',
     balance DECIMAL(10,2) NOT NULL COMMENT '变更后余额',
     change_type TINYINT(1) NOT NULL COMMENT '变更类型：1充值 2消费 3退款',
     ych_trans_id VARCHAR(64) DEFAULT NULL COMMENT '油菜花交易ID',
     status TINYINT(1) DEFAULT 0 COMMENT '同步状态：0待同步 1已同步 2同步失败',
     retry_count TINYINT(1) DEFAULT 0 COMMENT '重试次数',
     order_id INT COMMENT '订单ID',
     order_no VARCHAR(64) DEFAULT NULL COMMENT '业务单号',
     summary VARCHAR(255) DEFAULT NULL COMMENT '变更说明',
     create_time DATETIME NOT NULL COMMENT '创建时间',
     update_time DATETIME DEFAULT NULL COMMENT '更新时间'
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员余额变更记录';