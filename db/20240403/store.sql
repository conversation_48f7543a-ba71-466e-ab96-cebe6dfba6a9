alter table base_store
    add store_type int default 0 null comment '门店类型： 0乐园、1餐厅' after name;
alter table base_store
    add store_id int null comment '所属门店（比如餐厅可能是门店下、浴场中存在多个餐厅或按摩等）' after store_type;

alter table base_store
    modify status tinyint(1) default 1 null comment '工作日状态  0:禁用  1:启用';

alter table base_store
    add weekend_status tinyint default 0 null comment '周末状态  0:禁用  1:启用' after status;

create table by_store_table
(
    id              int unsigned auto_increment primary key,
    store_id        int                  null comment '所属门店',
    table_prefix    varchar(50)          null comment '桌码编号前缀',
    table_no        varchar(50)          null comment '桌码编号',
    people_num      varchar(50)          null comment '座位数',
    status          tinyint    default 0 null comment '状态：0启用，,1 禁用',
    is_del          tinyint    default 0 null comment '0-可用,1-删除',
    gmt_create      datetime             null comment '创建时间',
    gmt_modified    datetime             null comment '修改时间',

)
    comment '餐厅桌台表';

