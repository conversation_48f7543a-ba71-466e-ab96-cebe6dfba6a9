create table by_channel_cust
(
    id                int unsigned auto_increment
        primary key,
    cust_id           int                  null comment '会员id',
    mobile            varchar(24)          null comment '手机号',
    store_id          int                  null comment '渠道门店',
    goods_id          int                  null comment '商品ID',
    usable_num        int                  null comment '可使用次数',
    is_generate_order tinyint(1) default 0 null comment '是否生成订单,0未生成，1已生成',
    order_id          int                  null comment '订单ID',
    channel_source    char(10)             null comment '渠道来源(0：大众点评，1：麦淘亲子）',
    gmt_create        datetime             null comment '创建时间',
    gmt_modified      datetime             null comment '修改时间',
    delete_status     int        default 0 null comment '是否删除;0:否,1:是'
)
    comment '渠道会员表' row_format = DYNAMIC;

